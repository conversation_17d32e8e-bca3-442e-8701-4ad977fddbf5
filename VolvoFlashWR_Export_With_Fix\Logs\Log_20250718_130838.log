Log started at 7/18/2025 1:08:38 PM
2025-07-18 13:08:38.691 [Information] LoggingService: Logging service initialized
2025-07-18 13:08:38.702 [Information] App: Starting integrated application initialization
2025-07-18 13:08:38.704 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-18 13:08:38.706 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-18 13:08:38.708 [Information] IntegratedStartupService: Setting up application environment
2025-07-18 13:08:38.708 [Information] IntegratedStartupService: Application environment setup completed
2025-07-18 13:08:38.710 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-18 13:08:38.712 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-18 13:08:38.714 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-18 13:08:38.718 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-18 13:08:38.735 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-18 13:08:38.739 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 13:08:38.743 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 13:08:38.746 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-18 13:08:38.751 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-18 13:08:38.755 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-18 13:08:38.759 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-18 13:08:38.760 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-18 13:08:38.762 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-18 13:08:38.762 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-18 13:08:38.780 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-18 13:08:38.780 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-18 13:08:38.790 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-18 13:08:38.791 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-18 13:08:38.791 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-18 13:08:38.862 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-18 13:08:38.905 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 13:08:38.906 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 13:08:38.906 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-18 13:08:38.906 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-18 13:08:38.907 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-18 13:08:38.907 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-18 13:08:38.912 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-18 13:08:38.912 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-18 13:08:38.912 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-18 13:08:38.917 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-18 13:08:38.918 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-18 13:08:38.919 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-18 13:08:38.921 [Information] LibraryExtractor: Starting library extraction process
2025-07-18 13:08:38.923 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-18 13:08:38.926 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-18 13:08:38.929 [Information] LibraryExtractor: Copying system libraries
2025-07-18 13:08:38.938 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-18 13:08:38.953 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-18 13:13:38.963 [Warning] LibraryExtractor: Failed to download and extract redistributable for msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-18 13:13:38.964 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 13:18:38.972 [Warning] LibraryExtractor: Failed to download and extract redistributable for api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-18 13:18:38.974 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 13:23:38.981 [Warning] LibraryExtractor: Failed to download and extract redistributable for api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-18 13:23:38.982 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-18 13:26:14.759 [Warning] LibraryExtractor: Failed to download and extract redistributable for api-ms-win-crt-string-l1-1-0.dll: Error while copying content to a stream.
2025-07-18 13:26:14.759 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-18 13:28:10.081 [Warning] LibraryExtractor: Failed to download and extract redistributable for api-ms-win-crt-stdio-l1-1-0.dll: Error while copying content to a stream.
2025-07-18 13:28:10.082 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-math-l1-1-0.dll
