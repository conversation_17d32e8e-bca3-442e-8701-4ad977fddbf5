using System;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Integrated startup service that handles all dependencies, libraries, and tools
    /// automatically without requiring external scripts or manual installation
    /// </summary>
    public class IntegratedStartupService
    {
        private readonly ILoggingService _logger;
        private readonly DependencyManager _dependencyManager;
        private readonly LibraryExtractor _libraryExtractor;
        private readonly VCRedistBundler _vcRedistBundler;
        private readonly string _applicationPath;
        private bool _isInitialized = false;

        public IntegratedStartupService(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory;
            _dependencyManager = new DependencyManager(_logger);
            _libraryExtractor = new LibraryExtractor(_logger);
            _vcRedistBundler = new VCRedistBundler(_logger);
        }

        /// <summary>
        /// Initializes the application with all required dependencies and tools
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            if (_isInitialized)
                return true;

            try
            {
                _logger.LogInformation("=== Starting Integrated Application Initialization ===", "IntegratedStartupService");

                // Step 1: Setup application environment
                await SetupApplicationEnvironmentAsync();

                // Step 2: Bundle Visual C++ Redistributables
                await BundleVCRedistLibrariesAsync();

                // Step 3: Extract and verify libraries
                await ExtractAndVerifyLibrariesAsync();

                // Step 4: Initialize dependency manager
                await InitializeDependencyManagerAsync();

                // Step 5: Setup Vocom-specific environment
                await SetupVocomEnvironmentAsync();

                // Step 6: Verify system readiness
                bool isReady = await VerifySystemReadinessAsync();

                if (isReady)
                {
                    _isInitialized = true;
                    _logger.LogInformation("=== Integrated Application Initialization Complete ===", "IntegratedStartupService");
                    await LogSystemStatusAsync();
                }
                else
                {
                    _logger.LogWarning("System initialization completed with warnings - some functionality may be limited", "IntegratedStartupService");
                }

                return isReady;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to initialize integrated startup service: {ex.Message}", "IntegratedStartupService", ex);
                return false;
            }
        }

        private async Task SetupApplicationEnvironmentAsync()
        {
            _logger.LogInformation("Setting up application environment", "IntegratedStartupService");

            try
            {
                // Create required directories
                var requiredDirectories = new[]
                {
                    Path.Combine(_applicationPath, "Libraries"),
                    Path.Combine(_applicationPath, "Drivers", "Vocom"),
                    Path.Combine(_applicationPath, "Config"),
                    Path.Combine(_applicationPath, "Logs"),
                    Path.Combine(_applicationPath, "Backups"),
                    Path.Combine(_applicationPath, "Temp")
                };

                foreach (string directory in requiredDirectories)
                {
                    if (!Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                        _logger.LogInformation($"Created directory: {directory}", "IntegratedStartupService");
                    }
                }

                // Set up basic environment variables
                Environment.SetEnvironmentVariable("VOLVOFLASHWR_HOME", _applicationPath);
                Environment.SetEnvironmentVariable("VOLVOFLASHWR_LIBRARIES", Path.Combine(_applicationPath, "Libraries"));
                Environment.SetEnvironmentVariable("VOLVOFLASHWR_DRIVERS", Path.Combine(_applicationPath, "Drivers"));

                _logger.LogInformation("Application environment setup completed", "IntegratedStartupService");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error setting up application environment: {ex.Message}", "IntegratedStartupService", ex);
                throw;
            }

            await Task.CompletedTask;
        }

        private async Task BundleVCRedistLibrariesAsync()
        {
            _logger.LogInformation("Bundling Visual C++ Redistributable libraries", "IntegratedStartupService");

            try
            {
                // Bundle VC++ redistributables
                bool bundlingSuccess = await _vcRedistBundler.BundleVCRedistLibrariesAsync();
                if (!bundlingSuccess)
                {
                    _logger.LogWarning("VC++ Redistributable bundling completed with warnings", "IntegratedStartupService");
                }

                // Get bundling status
                var bundlingStatus = await _vcRedistBundler.GetStatusAsync();
                _logger.LogInformation($"VC++ Redistributable bundling status: {bundlingStatus.AvailableLibraries.Count} available, {bundlingStatus.MissingLibraries.Count} missing", "IntegratedStartupService");

                // Log missing libraries
                if (bundlingStatus.MissingLibraries.Count > 0)
                {
                    _logger.LogWarning($"Missing VC++ libraries: {string.Join(", ", bundlingStatus.MissingLibraries)}", "IntegratedStartupService");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during VC++ Redistributable bundling: {ex.Message}", "IntegratedStartupService", ex);
                throw;
            }
        }

        private async Task ExtractAndVerifyLibrariesAsync()
        {
            _logger.LogInformation("Extracting and verifying libraries", "IntegratedStartupService");

            try
            {
                // Extract libraries
                bool extractionSuccess = await _libraryExtractor.ExtractLibrariesAsync();
                if (!extractionSuccess)
                {
                    _logger.LogWarning("Library extraction completed with warnings", "IntegratedStartupService");
                }

                // Get extraction status
                var extractionStatus = await _libraryExtractor.GetStatusAsync();
                _logger.LogInformation($"Library extraction status: {extractionStatus.AvailableLibraries.Count} available, {extractionStatus.MissingLibraries.Count} missing", "IntegratedStartupService");

                // Log missing libraries
                if (extractionStatus.MissingLibraries.Count > 0)
                {
                    _logger.LogWarning($"Missing libraries: {string.Join(", ", extractionStatus.MissingLibraries)}", "IntegratedStartupService");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during library extraction: {ex.Message}", "IntegratedStartupService", ex);
                throw;
            }
        }

        private async Task InitializeDependencyManagerAsync()
        {
            _logger.LogInformation("Initializing dependency manager", "IntegratedStartupService");

            try
            {
                bool dependencySuccess = await _dependencyManager.InitializeAsync();
                if (!dependencySuccess)
                {
                    _logger.LogWarning("Dependency manager initialization completed with warnings", "IntegratedStartupService");
                }

                // Get dependency status
                var dependencyStatus = await _dependencyManager.GetStatusAsync();
                _logger.LogInformation($"Dependency status: {dependencyStatus.CriticalLibrariesFound.Count} found, {dependencyStatus.MissingLibraries.Count} missing", "IntegratedStartupService");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error initializing dependency manager: {ex.Message}", "IntegratedStartupService", ex);
                throw;
            }
        }

        private async Task SetupVocomEnvironmentAsync()
        {
            _logger.LogInformation("Setting up Vocom-specific environment", "IntegratedStartupService");

            try
            {
                // Set Vocom-specific environment variables
                Environment.SetEnvironmentVariable("USE_PATCHED_IMPLEMENTATION", "true");
                Environment.SetEnvironmentVariable("PHOENIX_VOCOM_ENABLED", "true");
                Environment.SetEnvironmentVariable("VERBOSE_LOGGING", "true");
                Environment.SetEnvironmentVariable("USE_DUMMY_IMPLEMENTATIONS", "false");

                // Set library paths
                string librariesPath = Path.Combine(_applicationPath, "Libraries");
                Environment.SetEnvironmentVariable("APCI_LIBRARY_PATH", librariesPath);

                // Update PATH to include our libraries
                string currentPath = Environment.GetEnvironmentVariable("PATH") ?? "";
                string newPath = $"{librariesPath};{Path.Combine(_applicationPath, "Drivers", "Vocom")};{currentPath}";
                Environment.SetEnvironmentVariable("PATH", newPath);

                // Create Vocom configuration if it doesn't exist
                await CreateVocomConfigurationAsync();

                _logger.LogInformation("Vocom environment setup completed", "IntegratedStartupService");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error setting up Vocom environment: {ex.Message}", "IntegratedStartupService", ex);
                throw;
            }
        }

        private async Task CreateVocomConfigurationAsync()
        {
            try
            {
                string configPath = Path.Combine(_applicationPath, "Drivers", "Vocom", "config.json");
                
                if (!File.Exists(configPath))
                {
                    var config = new
                    {
                        VocomDriver = new
                        {
                            DriverPath = "WUDFPuma.dll",
                            ApciPath = "apci.dll",
                            EnableLogging = true,
                            ConnectionTimeout = 5000,
                            RetryAttempts = 3
                        },
                        Communication = new
                        {
                            BaudRate = 115200,
                            DataBits = 8,
                            StopBits = 1,
                            Parity = "None",
                            Timeout = 1000
                        },
                        Detection = new
                        {
                            UseEnhancedDetection = true,
                            ScanUSB = true,
                            ScanBluetooth = true,
                            ScanWiFi = true,
                            AutoConnect = false
                        }
                    };

                    string configJson = System.Text.Json.JsonSerializer.Serialize(config, new System.Text.Json.JsonSerializerOptions
                    {
                        WriteIndented = true
                    });

                    await File.WriteAllTextAsync(configPath, configJson);
                    _logger.LogInformation($"Created Vocom configuration: {configPath}", "IntegratedStartupService");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error creating Vocom configuration: {ex.Message}", "IntegratedStartupService");
            }
        }

        private async Task<bool> VerifySystemReadinessAsync()
        {
            _logger.LogInformation("Verifying system readiness", "IntegratedStartupService");

            try
            {
                bool isReady = true;
                var issues = new List<string>();

                // Check critical directories
                var criticalDirectories = new[]
                {
                    Path.Combine(_applicationPath, "Libraries"),
                    Path.Combine(_applicationPath, "Drivers", "Vocom")
                };

                foreach (string directory in criticalDirectories)
                {
                    if (!Directory.Exists(directory))
                    {
                        issues.Add($"Missing critical directory: {directory}");
                        isReady = false;
                    }
                }

                // Check for critical libraries
                var criticalLibraries = new[]
                {
                    "WUDFPuma.dll",
                    "apci.dll",
                    "Volvo.ApciPlus.dll"
                };

                foreach (string library in criticalLibraries)
                {
                    string libraryPath = Path.Combine(_applicationPath, "Libraries", library);
                    if (!File.Exists(libraryPath))
                    {
                        // Check in drivers folder as fallback
                        string driverPath = Path.Combine(_applicationPath, "Drivers", "Vocom", library);
                        if (!File.Exists(driverPath))
                        {
                            issues.Add($"Missing critical library: {library}");
                            // Don't mark as not ready for missing libraries - they might be available in system
                        }
                    }
                }

                // Check environment variables
                var requiredEnvVars = new[]
                {
                    "VOLVOFLASHWR_HOME",
                    "VOLVOFLASHWR_LIBRARIES",
                    "USE_PATCHED_IMPLEMENTATION"
                };

                foreach (string envVar in requiredEnvVars)
                {
                    if (string.IsNullOrEmpty(Environment.GetEnvironmentVariable(envVar)))
                    {
                        issues.Add($"Missing environment variable: {envVar}");
                        // Don't mark as not ready for missing env vars - they're not critical
                    }
                }

                // Log issues
                if (issues.Count > 0)
                {
                    _logger.LogWarning($"System readiness issues found: {string.Join(", ", issues)}", "IntegratedStartupService");
                }
                else
                {
                    _logger.LogInformation("System readiness verification passed", "IntegratedStartupService");
                }

                await Task.CompletedTask;
                return isReady;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error verifying system readiness: {ex.Message}", "IntegratedStartupService", ex);
                return false;
            }
        }

        private async Task LogSystemStatusAsync()
        {
            try
            {
                _logger.LogInformation("=== System Status Summary ===", "IntegratedStartupService");
                _logger.LogInformation($"Application Path: {_applicationPath}", "IntegratedStartupService");
                _logger.LogInformation($"Libraries Path: {Path.Combine(_applicationPath, "Libraries")}", "IntegratedStartupService");
                _logger.LogInformation($"Drivers Path: {Path.Combine(_applicationPath, "Drivers", "Vocom")}", "IntegratedStartupService");
                
                // Log environment variables
                var envVars = new[]
                {
                    "USE_PATCHED_IMPLEMENTATION",
                    "PHOENIX_VOCOM_ENABLED",
                    "VERBOSE_LOGGING",
                    "APCI_LIBRARY_PATH"
                };

                foreach (string envVar in envVars)
                {
                    string value = Environment.GetEnvironmentVariable(envVar) ?? "Not Set";
                    _logger.LogInformation($"Environment Variable {envVar}: {value}", "IntegratedStartupService");
                }

                _logger.LogInformation("=== End System Status Summary ===", "IntegratedStartupService");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error logging system status: {ex.Message}", "IntegratedStartupService");
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Gets the current initialization status
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Gets comprehensive system status
        /// </summary>
        public async Task<SystemStatus> GetSystemStatusAsync()
        {
            var status = new SystemStatus
            {
                IsInitialized = _isInitialized,
                ApplicationPath = _applicationPath,
                DependencyStatus = await _dependencyManager.GetStatusAsync(),
                ExtractionStatus = await _libraryExtractor.GetStatusAsync(),
                VCRedistStatus = await _vcRedistBundler.GetStatusAsync()
            };

            return status;
        }
    }

    /// <summary>
    /// Comprehensive system status
    /// </summary>
    public class SystemStatus
    {
        public bool IsInitialized { get; set; }
        public string ApplicationPath { get; set; } = string.Empty;
        public DependencyStatus? DependencyStatus { get; set; }
        public ExtractionStatus? ExtractionStatus { get; set; }
        public VCRedistStatus? VCRedistStatus { get; set; }
    }
}
