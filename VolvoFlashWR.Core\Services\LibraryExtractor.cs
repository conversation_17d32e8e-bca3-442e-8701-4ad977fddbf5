using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Reflection;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Automatically extracts and manages embedded libraries and dependencies
    /// Ensures all required libraries are available for Vocom communication
    /// </summary>
    public class LibraryExtractor
    {
        private readonly ILoggingService _logger;
        private readonly string _applicationPath;
        private readonly string _librariesPath;
        private readonly string _driversPath;

        // Embedded Visual C++ Redistributable libraries (base64 encoded or as resources)
        private static readonly Dictionary<string, string> EmbeddedLibraries = new()
        {
            // These would be embedded as resources in the application
            // For now, we'll use download URLs as fallback
        };

        // Download URLs for critical libraries (fallback) - Updated for x64 architecture
        private static readonly Dictionary<string, string> LibraryDownloadUrls = new()
        {
            // x86 libraries for legacy support
            ["msvcr120.dll"] = "https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe",

            // x64 libraries for primary process architecture
            ["msvcr140.dll"] = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
            ["msvcp140.dll"] = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
            ["vcruntime140.dll"] = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
            ["api-ms-win-crt-runtime-l1-1-0.dll"] = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
            ["api-ms-win-crt-heap-l1-1-0.dll"] = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
            ["api-ms-win-crt-string-l1-1-0.dll"] = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
            ["api-ms-win-crt-stdio-l1-1-0.dll"] = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
            ["api-ms-win-crt-math-l1-1-0.dll"] = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
            ["api-ms-win-crt-locale-l1-1-0.dll"] = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
        };

        // Critical library information
        private static readonly Dictionary<string, LibraryInfo> CriticalLibraryInfo = new()
        {
            ["WUDFPuma.dll"] = new LibraryInfo
            {
                Name = "WUDFPuma.dll",
                Description = "Vocom 1 Adapter Driver",
                IsRequired = true,
                SourcePaths = new[]
                {
                    @"C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll",
                    @"C:\Windows\System32\drivers\UMDF\WUDFPuma.dll"
                }
            },
            ["apci.dll"] = new LibraryInfo
            {
                Name = "apci.dll",
                Description = "APCI Communication Library",
                IsRequired = true,
                SourcePaths = new[]
                {
                    @"C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\apci.dll",
                    @"C:\Program Files\Phoenix Diag\Flash Editor Plus 2021\apci.dll"
                }
            },
            ["Volvo.ApciPlus.dll"] = new LibraryInfo
            {
                Name = "Volvo.ApciPlus.dll",
                Description = "Volvo APCI Plus Communication",
                IsRequired = true,
                SourcePaths = new[]
                {
                    @"C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\Volvo.ApciPlus.dll",
                    @"C:\Program Files\Phoenix Diag\Flash Editor Plus 2021\Volvo.ApciPlus.dll"
                }
            }
        };

        public LibraryExtractor(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory;
            _librariesPath = Path.Combine(_applicationPath, "Libraries");
            _driversPath = Path.Combine(_applicationPath, "Drivers", "Vocom");
        }

        /// <summary>
        /// Extracts and ensures all required libraries are available
        /// </summary>
        public async Task<bool> ExtractLibrariesAsync()
        {
            try
            {
                _logger.LogInformation("Starting library extraction process", "LibraryExtractor");

                // Create required directories
                await CreateDirectoriesAsync();

                // Priority 1: Copy from pre-bundled Libraries directory
                await CopyPreBundledLibrariesAsync();

                // Priority 2: Extract embedded libraries
                await ExtractEmbeddedLibrariesAsync();

                // Priority 3: Copy system libraries
                await CopySystemLibrariesAsync();

                // Priority 4: Download missing critical libraries (fallback)
                await DownloadMissingLibrariesAsync();

                // Verify extraction results
                bool success = await VerifyExtractionAsync();

                _logger.LogInformation($"Library extraction completed. Success: {success}", "LibraryExtractor");
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during library extraction: {ex.Message}", "LibraryExtractor", ex);
                return false;
            }
        }

        private async Task CreateDirectoriesAsync()
        {
            var directories = new[]
            {
                _librariesPath,
                _driversPath,
                Path.Combine(_librariesPath, "System"),
                Path.Combine(_librariesPath, "VCRedist"),
                Path.Combine(_driversPath, "Backup")
            };

            foreach (string directory in directories)
            {
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    _logger.LogInformation($"Created directory: {directory}", "LibraryExtractor");
                }
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Copies libraries from pre-bundled Libraries directory
        /// </summary>
        private async Task CopyPreBundledLibrariesAsync()
        {
            _logger.LogInformation("Copying pre-bundled libraries", "LibraryExtractor");

            // Check for pre-bundled libraries in Libraries directory
            string librariesPath = Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? "", "Libraries");

            if (!Directory.Exists(librariesPath))
            {
                _logger.LogInformation("Pre-bundled Libraries directory not found, skipping", "LibraryExtractor");
                return;
            }

            // Copy all DLL files from Libraries directory
            var libraryFiles = Directory.GetFiles(librariesPath, "*.dll", SearchOption.TopDirectoryOnly);

            foreach (string libraryFile in libraryFiles)
            {
                string fileName = Path.GetFileName(libraryFile);
                string targetPath = Path.Combine(_librariesPath, fileName);

                if (!File.Exists(targetPath))
                {
                    try
                    {
                        File.Copy(libraryFile, targetPath, true);
                        _logger.LogInformation($"Copied pre-bundled library: {fileName}", "LibraryExtractor");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to copy pre-bundled library {fileName}: {ex.Message}", "LibraryExtractor");
                    }
                }
            }

            await Task.CompletedTask;
        }

        private async Task ExtractEmbeddedLibrariesAsync()
        {
            _logger.LogInformation("Extracting embedded libraries", "LibraryExtractor");

            var assembly = Assembly.GetExecutingAssembly();
            var resourceNames = assembly.GetManifestResourceNames();

            foreach (string resourceName in resourceNames)
            {
                if (resourceName.EndsWith(".dll") || resourceName.EndsWith(".exe"))
                {
                    try
                    {
                        string fileName = Path.GetFileName(resourceName);
                        string targetPath = Path.Combine(_librariesPath, fileName);

                        if (!File.Exists(targetPath))
                        {
                            using var stream = assembly.GetManifestResourceStream(resourceName);
                            if (stream != null)
                            {
                                using var fileStream = File.Create(targetPath);
                                await stream.CopyToAsync(fileStream);
                                _logger.LogInformation($"Extracted embedded library: {fileName}", "LibraryExtractor");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to extract embedded resource {resourceName}: {ex.Message}", "LibraryExtractor");
                    }
                }
            }
        }

        private async Task CopySystemLibrariesAsync()
        {
            _logger.LogInformation("Copying system libraries", "LibraryExtractor");

            foreach (var libraryInfo in CriticalLibraryInfo.Values)
            {
                string targetPath = Path.Combine(_librariesPath, libraryInfo.Name);

                if (!File.Exists(targetPath))
                {
                    bool copied = false;
                    foreach (string sourcePath in libraryInfo.SourcePaths)
                    {
                        if (File.Exists(sourcePath))
                        {
                            try
                            {
                                File.Copy(sourcePath, targetPath, true);
                                _logger.LogInformation($"Copied system library: {libraryInfo.Name} from {sourcePath}", "LibraryExtractor");
                                copied = true;
                                break;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning($"Failed to copy {libraryInfo.Name} from {sourcePath}: {ex.Message}", "LibraryExtractor");
                            }
                        }
                    }

                    if (!copied && libraryInfo.IsRequired)
                    {
                        _logger.LogWarning($"Required library not found in system: {libraryInfo.Name}", "LibraryExtractor");
                    }
                }
            }

            await Task.CompletedTask;
        }

        private async Task DownloadMissingLibrariesAsync()
        {
            // Check if download is disabled via environment variable
            string skipDownload = Environment.GetEnvironmentVariable("SKIP_VCREDIST_DOWNLOAD");
            if (!string.IsNullOrEmpty(skipDownload) && skipDownload.ToLower() == "true")
            {
                _logger.LogInformation("Skipping library download due to SKIP_VCREDIST_DOWNLOAD environment variable", "LibraryExtractor");
                return;
            }

            _logger.LogInformation("Checking for missing libraries to download", "LibraryExtractor");

            // Use shorter timeout to prevent hanging
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(30); // Reduced from 5 minutes to 30 seconds

            int maxRetries = 2;
            int downloadAttempts = 0;
            int maxTotalDownloads = 3; // Limit total download attempts to prevent endless hanging

            foreach (var kvp in LibraryDownloadUrls)
            {
                string libraryName = kvp.Key;
                string downloadUrl = kvp.Value;
                string targetPath = Path.Combine(_librariesPath, libraryName);

                if (!File.Exists(targetPath) && downloadAttempts < maxTotalDownloads)
                {
                    downloadAttempts++;
                    bool downloadSuccessful = false;

                    for (int retry = 0; retry < maxRetries && !downloadSuccessful; retry++)
                    {
                        try
                        {
                            if (retry > 0)
                            {
                                _logger.LogInformation($"Retrying download for {libraryName} (attempt {retry + 1}/{maxRetries})", "LibraryExtractor");
                            }
                            else
                            {
                                _logger.LogInformation($"Downloading missing library: {libraryName}", "LibraryExtractor");
                            }

                            // For redistributable packages, we need to extract the actual DLLs
                            if (downloadUrl.EndsWith(".exe"))
                            {
                                downloadSuccessful = await DownloadAndExtractRedistributableAsync(httpClient, downloadUrl, libraryName);
                            }
                            else
                            {
                                var response = await httpClient.GetAsync(downloadUrl);
                                response.EnsureSuccessStatusCode();

                                using var fileStream = File.Create(targetPath);
                                await response.Content.CopyToAsync(fileStream);

                                _logger.LogInformation($"Downloaded library: {libraryName}", "LibraryExtractor");
                                downloadSuccessful = true;
                            }
                        }
                        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException || ex.Message.Contains("timeout"))
                        {
                            _logger.LogWarning($"Download timeout for {libraryName} (attempt {retry + 1}/{maxRetries}): {ex.Message}", "LibraryExtractor");
                            if (retry == maxRetries - 1)
                            {
                                _logger.LogWarning($"Skipping {libraryName} after {maxRetries} timeout attempts", "LibraryExtractor");
                            }
                        }
                        catch (HttpRequestException ex)
                        {
                            _logger.LogWarning($"Network error downloading {libraryName} (attempt {retry + 1}/{maxRetries}): {ex.Message}", "LibraryExtractor");
                            if (retry == maxRetries - 1)
                            {
                                _logger.LogWarning($"Skipping {libraryName} after {maxRetries} network error attempts", "LibraryExtractor");
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"Failed to download library {libraryName} (attempt {retry + 1}/{maxRetries}): {ex.Message}", "LibraryExtractor");
                            if (retry == maxRetries - 1)
                            {
                                _logger.LogWarning($"Skipping {libraryName} after {maxRetries} failed attempts", "LibraryExtractor");
                            }
                        }

                        // Add small delay between retries
                        if (!downloadSuccessful && retry < maxRetries - 1)
                        {
                            await Task.Delay(1000); // 1 second delay
                        }
                    }
                }
                else if (downloadAttempts >= maxTotalDownloads)
                {
                    _logger.LogWarning($"Reached maximum download attempts ({maxTotalDownloads}), skipping remaining libraries to prevent hanging", "LibraryExtractor");
                    break;
                }
            }

            if (downloadAttempts > 0)
            {
                _logger.LogInformation($"Completed download phase with {downloadAttempts} attempts", "LibraryExtractor");
            }
        }

        private async Task<bool> DownloadAndExtractRedistributableAsync(HttpClient httpClient, string downloadUrl, string targetLibrary)
        {
            string tempPath = "";
            string extractPath = "";

            try
            {
                tempPath = Path.Combine(Path.GetTempPath(), $"vcredist_{Guid.NewGuid()}.exe");
                extractPath = Path.Combine(Path.GetTempPath(), $"vcredist_extract_{Guid.NewGuid()}");

                // Download the redistributable with timeout handling
                var response = await httpClient.GetAsync(downloadUrl);
                response.EnsureSuccessStatusCode();

                using (var fileStream = File.Create(tempPath))
                {
                    await response.Content.CopyToAsync(fileStream);
                }

                // Create extraction directory
                Directory.CreateDirectory(extractPath);

                // Try to extract using built-in Windows tools
                var processInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempPath,
                    Arguments = $"/extract:{extractPath} /quiet",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = System.Diagnostics.Process.Start(processInfo);
                if (process != null)
                {
                    // Add timeout for extraction process (30 seconds)
                    var processTask = process.WaitForExitAsync();
                    var timeoutTask = Task.Delay(TimeSpan.FromSeconds(30));
                    var completedTask = await Task.WhenAny(processTask, timeoutTask);

                    if (completedTask == timeoutTask)
                    {
                        _logger.LogWarning($"Extraction process timed out for {targetLibrary}", "LibraryExtractor");
                        try { process.Kill(); } catch { }
                        return false;
                    }

                    // Look for the target library in extracted files
                    var extractedFiles = Directory.GetFiles(extractPath, "*.dll", SearchOption.AllDirectories);
                    foreach (string file in extractedFiles)
                    {
                        if (Path.GetFileName(file).Equals(targetLibrary, StringComparison.OrdinalIgnoreCase))
                        {
                            string targetPath = Path.Combine(_librariesPath, targetLibrary);
                            File.Copy(file, targetPath, true);
                            _logger.LogInformation($"Extracted and copied: {targetLibrary}", "LibraryExtractor");
                            return true;
                        }
                    }

                    _logger.LogWarning($"Target library {targetLibrary} not found in extracted files", "LibraryExtractor");
                    return false;
                }

                _logger.LogWarning($"Failed to start extraction process for {targetLibrary}", "LibraryExtractor");
                return false;
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException || ex.Message.Contains("timeout"))
            {
                _logger.LogWarning($"Download timeout for redistributable {targetLibrary}: {ex.Message}", "LibraryExtractor");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to download and extract redistributable for {targetLibrary}: {ex.Message}", "LibraryExtractor");
                return false;
            }
            finally
            {
                // Cleanup
                try
                {
                    if (File.Exists(tempPath))
                        File.Delete(tempPath);
                    if (Directory.Exists(extractPath))
                        Directory.Delete(extractPath, true);
                }
                catch (Exception ex)
                {
                    _logger.LogDebug($"Cleanup warning for {targetLibrary}: {ex.Message}", "LibraryExtractor");
                }
            }
        }

        private async Task<bool> VerifyExtractionAsync()
        {
            _logger.LogInformation("Verifying library extraction", "LibraryExtractor");

            int foundCount = 0;
            int requiredCount = 0;

            foreach (var libraryInfo in CriticalLibraryInfo.Values)
            {
                if (libraryInfo.IsRequired)
                {
                    requiredCount++;
                    string targetPath = Path.Combine(_librariesPath, libraryInfo.Name);

                    if (File.Exists(targetPath))
                    {
                        foundCount++;
                        _logger.LogInformation($"✓ Verified: {libraryInfo.Name}", "LibraryExtractor");
                    }
                    else
                    {
                        _logger.LogWarning($"✗ Missing: {libraryInfo.Name}", "LibraryExtractor");
                    }
                }
            }

            double successRate = requiredCount > 0 ? (double)foundCount / requiredCount * 100 : 100;
            _logger.LogInformation($"Library verification: {foundCount}/{requiredCount} ({successRate:F1}%) required libraries found", "LibraryExtractor");

            await Task.CompletedTask;
            return successRate >= 80; // Consider successful if 80% or more libraries are found
        }

        /// <summary>
        /// Gets the extraction status
        /// </summary>
        public async Task<ExtractionStatus> GetStatusAsync()
        {
            var status = new ExtractionStatus
            {
                LibrariesPath = _librariesPath,
                DriversPath = _driversPath,
                AvailableLibraries = new List<string>(),
                MissingLibraries = new List<string>()
            };

            foreach (var libraryInfo in CriticalLibraryInfo.Values)
            {
                string targetPath = Path.Combine(_librariesPath, libraryInfo.Name);
                if (File.Exists(targetPath))
                {
                    var fileInfo = new FileInfo(targetPath);
                    status.AvailableLibraries.Add($"{libraryInfo.Name} ({fileInfo.Length} bytes, {fileInfo.LastWriteTime})");
                }
                else
                {
                    status.MissingLibraries.Add(libraryInfo.Name);
                }
            }

            await Task.CompletedTask;
            return status;
        }
    }

    /// <summary>
    /// Information about a library
    /// </summary>
    public class LibraryInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsRequired { get; set; }
        public string[] SourcePaths { get; set; } = Array.Empty<string>();
    }

    /// <summary>
    /// Status of library extraction
    /// </summary>
    public class ExtractionStatus
    {
        public string LibrariesPath { get; set; } = string.Empty;
        public string DriversPath { get; set; } = string.Empty;
        public List<string> AvailableLibraries { get; set; } = new();
        public List<string> MissingLibraries { get; set; } = new();
    }
}
