using System;
using System.IO;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Enhanced startup service that handles x64 architecture compatibility
    /// and ensures proper library loading for the VolvoFlashWR application
    /// </summary>
    public class EnhancedStartupService
    {
        private readonly ILoggingService _logger;
        private readonly X64LibraryResolver _libraryResolver;
        private readonly VCRedistBundler _vcRedistBundler;
        private readonly DependencyManager _dependencyManager;
        private readonly string _applicationPath;

        public EnhancedStartupService(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _libraryResolver = new X64LibraryResolver(_logger);
            _vcRedistBundler = new VCRedistBundler(_logger);
            _dependencyManager = new DependencyManager(_logger);
            _applicationPath = AppDomain.CurrentDomain.BaseDirectory;
        }

        /// <summary>
        /// Performs comprehensive startup initialization with x64 compatibility
        /// </summary>
        public async Task<StartupResult> InitializeAsync()
        {
            var result = new StartupResult();

            try
            {
                _logger.LogInformation("=== Enhanced Startup Service - x64 Architecture ===", "EnhancedStartupService");
                _logger.LogInformation($"Process Architecture: {(Environment.Is64BitProcess ? "x64" : "x86")}", "EnhancedStartupService");
                _logger.LogInformation($"Application Path: {_applicationPath}", "EnhancedStartupService");

                // Step 1: Resolve library dependencies
                _logger.LogInformation("Step 1: Resolving library dependencies", "EnhancedStartupService");
                var libraryResult = await _libraryResolver.ResolveLibrariesAsync();
                result.LibraryResolution = libraryResult;

                if (!libraryResult.IsSuccessful)
                {
                    _logger.LogWarning("Library resolution completed with issues", "EnhancedStartupService");
                    foreach (var missing in libraryResult.MissingLibraries)
                    {
                        _logger.LogWarning($"Missing library: {missing}", "EnhancedStartupService");
                    }
                }

                // Step 2: Bundle Visual C++ Redistributables
                _logger.LogInformation("Step 2: Bundling Visual C++ Redistributables", "EnhancedStartupService");
                bool vcBundleSuccess = await _vcRedistBundler.BundleVCRedistLibrariesAsync();
                result.VCRedistBundled = vcBundleSuccess;

                if (!vcBundleSuccess)
                {
                    _logger.LogWarning("VC++ Redistributable bundling completed with issues", "EnhancedStartupService");
                }

                // Step 3: Initialize dependency manager
                _logger.LogInformation("Step 3: Initializing dependency manager", "EnhancedStartupService");
                await _dependencyManager.InitializeAsync();
                result.DependencyManagerInitialized = true;

                // Step 4: Configure architecture-specific settings
                _logger.LogInformation("Step 4: Configuring architecture-specific settings", "EnhancedStartupService");
                ConfigureArchitectureSettings(result, libraryResult);

                // Step 5: Verify system readiness
                _logger.LogInformation("Step 5: Verifying system readiness", "EnhancedStartupService");
                await VerifySystemReadinessAsync(result);

                // Step 6: Create startup summary
                CreateStartupSummary(result);

                result.IsSuccessful = true;
                _logger.LogInformation("=== Enhanced Startup Completed Successfully ===", "EnhancedStartupService");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Enhanced startup failed: {ex.Message}", "EnhancedStartupService");
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        private void ConfigureArchitectureSettings(StartupResult result, LibraryResolutionResult libraryResult)
        {
            _logger.LogInformation("Configuring architecture-specific settings", "EnhancedStartupService");

            // Set environment variables based on library resolution
            foreach (var envVar in libraryResult.EnvironmentVariables)
            {
                Environment.SetEnvironmentVariable(envVar.Key, envVar.Value, EnvironmentVariableTarget.Process);
                result.EnvironmentVariables[envVar.Key] = envVar.Value;
                _logger.LogInformation($"Set environment variable: {envVar.Key} = {envVar.Value}", "EnhancedStartupService");
            }

            // Configure architecture bridge if needed
            if (libraryResult.RequiresArchitectureBridge)
            {
                result.RequiresArchitectureBridge = true;
                result.ArchitectureBridgeAvailable = libraryResult.ArchitectureBridgeAvailable;
                result.BridgePath = libraryResult.BridgePath;

                if (libraryResult.ArchitectureBridgeAvailable)
                {
                    _logger.LogInformation($"Architecture bridge configured: {libraryResult.BridgePath}", "EnhancedStartupService");
                    Environment.SetEnvironmentVariable("VOCOM_BRIDGE_PATH", libraryResult.BridgePath, EnvironmentVariableTarget.Process);
                }
                else
                {
                    _logger.LogWarning("Architecture bridge required but not available", "EnhancedStartupService");
                    result.Warnings.Add("Architecture bridge required for x86 library compatibility but not found");
                }
            }

            // Configure library paths
            string librariesPath = Path.Combine(_applicationPath, "Libraries");
            if (Directory.Exists(librariesPath))
            {
                string currentPath = Environment.GetEnvironmentVariable("PATH", EnvironmentVariableTarget.Process) ?? "";
                if (!currentPath.Contains(librariesPath))
                {
                    Environment.SetEnvironmentVariable("PATH", $"{librariesPath};{currentPath}", EnvironmentVariableTarget.Process);
                    _logger.LogInformation($"Added libraries path to PATH: {librariesPath}", "EnhancedStartupService");
                }
            }
        }

        private async Task VerifySystemReadinessAsync(StartupResult result)
        {
            _logger.LogInformation("Verifying system readiness", "EnhancedStartupService");

            // Check critical directories
            var criticalDirectories = new[]
            {
                Path.Combine(_applicationPath, "Libraries"),
                Path.Combine(_applicationPath, "Config"),
                Path.Combine(_applicationPath, "Logs"),
                Path.Combine(_applicationPath, "Drivers", "Vocom")
            };

            foreach (string directory in criticalDirectories)
            {
                if (!Directory.Exists(directory))
                {
                    try
                    {
                        Directory.CreateDirectory(directory);
                        _logger.LogInformation($"Created directory: {directory}", "EnhancedStartupService");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to create directory {directory}: {ex.Message}", "EnhancedStartupService");
                        result.Warnings.Add($"Could not create directory: {directory}");
                    }
                }
                else
                {
                    _logger.LogInformation($"✓ Directory exists: {directory}", "EnhancedStartupService");
                }
            }

            // Check for critical files
            var criticalFiles = new[]
            {
                Path.Combine(_applicationPath, "Drivers", "Vocom", "config.json"),
                Path.Combine(_applicationPath, "Config", "app_config.json")
            };

            foreach (string file in criticalFiles)
            {
                if (File.Exists(file))
                {
                    _logger.LogInformation($"✓ Critical file exists: {Path.GetFileName(file)}", "EnhancedStartupService");
                }
                else
                {
                    _logger.LogWarning($"⚠ Critical file missing: {file}", "EnhancedStartupService");
                    result.Warnings.Add($"Critical file missing: {Path.GetFileName(file)}");
                }
            }

            // Verify VC++ Redistributable status
            var vcStatus = await _vcRedistBundler.GetStatusAsync();
            result.VCRedistStatus = vcStatus;

            _logger.LogInformation($"VC++ Libraries: {vcStatus.AvailableLibraries.Count} available, {vcStatus.MissingLibraries.Count} missing", "EnhancedStartupService");

            await Task.CompletedTask;
        }

        private void CreateStartupSummary(StartupResult result)
        {
            _logger.LogInformation("=== Startup Summary ===", "EnhancedStartupService");
            _logger.LogInformation($"Process Architecture: {(Environment.Is64BitProcess ? "x64" : "x86")}", "EnhancedStartupService");
            _logger.LogInformation($"Application Path: {_applicationPath}", "EnhancedStartupService");
            
            if (result.LibraryResolution != null)
            {
                _logger.LogInformation($"Resolved Libraries: {result.LibraryResolution.ResolvedLibraries.Count}", "EnhancedStartupService");
                _logger.LogInformation($"Missing Libraries: {result.LibraryResolution.MissingLibraries.Count}", "EnhancedStartupService");
                _logger.LogInformation($"Architecture Bridge Required: {result.RequiresArchitectureBridge}", "EnhancedStartupService");
                _logger.LogInformation($"Architecture Bridge Available: {result.ArchitectureBridgeAvailable}", "EnhancedStartupService");
            }

            if (result.VCRedistStatus != null)
            {
                _logger.LogInformation($"VC++ Libraries Available: {result.VCRedistStatus.AvailableLibraries.Count}", "EnhancedStartupService");
                _logger.LogInformation($"VC++ Libraries Missing: {result.VCRedistStatus.MissingLibraries.Count}", "EnhancedStartupService");
            }

            _logger.LogInformation($"Environment Variables Set: {result.EnvironmentVariables.Count}", "EnhancedStartupService");
            _logger.LogInformation($"Warnings: {result.Warnings.Count}", "EnhancedStartupService");

            foreach (var warning in result.Warnings)
            {
                _logger.LogWarning($"⚠ {warning}", "EnhancedStartupService");
            }

            _logger.LogInformation("=== End Startup Summary ===", "EnhancedStartupService");
        }
    }

    /// <summary>
    /// Result of the enhanced startup process
    /// </summary>
    public class StartupResult
    {
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        
        public LibraryResolutionResult? LibraryResolution { get; set; }
        public bool VCRedistBundled { get; set; }
        public bool DependencyManagerInitialized { get; set; }
        
        public bool RequiresArchitectureBridge { get; set; }
        public bool ArchitectureBridgeAvailable { get; set; }
        public string BridgePath { get; set; } = string.Empty;
        
        public VCRedistStatus? VCRedistStatus { get; set; }
        public Dictionary<string, string> EnvironmentVariables { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }
}
