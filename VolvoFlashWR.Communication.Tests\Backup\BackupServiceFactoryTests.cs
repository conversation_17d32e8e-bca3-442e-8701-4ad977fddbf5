using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Backup;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Backup
{
    [TestFixture]
    public class BackupServiceFactoryTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IECUCommunicationService> _mockEcuService;
        private BackupServiceFactory _factory;
        private string _testBackupDirectory;

        [SetUp]
        public void Setup()
        {
            // Create mocks
            _mockLogger = new Mock<ILoggingService>();
            _mockEcuService = new Mock<IECUCommunicationService>();

            // Create a temporary directory for test backups
            _testBackupDirectory = Path.Combine(Path.GetTempPath(), "VolvoFlashWR_Tests", "BackupFactory");
            if (Directory.Exists(_testBackupDirectory))
            {
                Directory.Delete(_testBackupDirectory, true);
            }
            Directory.CreateDirectory(_testBackupDirectory);

            // Setup ECU service mock to return success for initialization
            _mockEcuService.Setup(s => s.IsInitialized).Returns(true);

            // Create the factory
            _factory = new BackupServiceFactory(_mockLogger.Object, _mockEcuService.Object);
        }

        [TearDown]
        public void TearDown()
        {
            // Clean up the test directory
            if (Directory.Exists(_testBackupDirectory))
            {
                Directory.Delete(_testBackupDirectory, true);
            }
        }

        [Test]
        public async Task CreateServiceAsync_WithDefaultSettings_ReturnsInitializedService()
        {
            // Act
            var service = await _factory.CreateServiceAsync();

            // Assert
            Assert.That(service, Is.Not.Null);
            Assert.That(service, Is.InstanceOf<IBackupService>());
        }

        [Test]
        public async Task CreateServiceWithCustomSettingsAsync_ValidSettings_ReturnsInitializedService()
        {
            // Act
            var service = await _factory.CreateServiceWithCustomSettingsAsync(
                _testBackupDirectory,
                useCompression: true,
                useEncryption: false);

            // Assert
            Assert.That(service, Is.Not.Null);
            Assert.That(service, Is.InstanceOf<IBackupService>());
            Assert.That(service.BackupDirectoryPath, Is.EqualTo(_testBackupDirectory));
            Assert.That(service.UseCompression, Is.True);
            Assert.That(service.UseEncryption, Is.False);
        }

        [Test]
        public async Task CreateServiceWithCategoriesAndTagsAsync_ValidSettings_ReturnsInitializedService()
        {
            // Arrange
            var categories = new List<string> { "Production", "Development", "Testing" };
            var tags = new List<string> { "Important", "Verified", "Debug" };

            // Act
            var service = await _factory.CreateServiceWithCategoriesAndTagsAsync(
                categories,
                tags,
                _testBackupDirectory,
                useCompression: true,
                useEncryption: false);

            // Assert
            Assert.That(service, Is.Not.Null);
            Assert.That(service, Is.InstanceOf<IBackupService>());
            Assert.That(service.BackupDirectoryPath, Is.EqualTo(_testBackupDirectory));
            Assert.That(service.UseCompression, Is.True);
            Assert.That(service.UseEncryption, Is.False);

            // Verify template directory was created
            string templateDirectory = Path.Combine(_testBackupDirectory, "Templates");
            Assert.That(Directory.Exists(templateDirectory), Is.True);

            // Verify template files were created
            var templateFiles = Directory.GetFiles(templateDirectory, "*.backup");
            Assert.That(templateFiles.Length, Is.GreaterThanOrEqualTo(categories.Count));

            // Load all backups and verify categories and tags
            var backups = await service.GetAllBackupsAsync();
            
            // Verify categories
            var usedCategories = backups.Select(b => b.Category).Where(c => !string.IsNullOrEmpty(c)).Distinct().ToList();
            foreach (var category in categories)
            {
                Assert.That(usedCategories, Contains.Item(category));
            }

            // Verify tags
            var tagsBackup = backups.FirstOrDefault(b => b.Tags != null && b.Tags.Count > 0);
            Assert.That(tagsBackup, Is.Not.Null);
            foreach (var tag in tags)
            {
                Assert.That(tagsBackup.Tags, Contains.Item(tag));
            }
        }

        [Test]
        public async Task CreateServiceWithCategoriesAndTagsAsync_NullCategories_ReturnsInitializedService()
        {
            // Arrange
            List<string> categories = null;
            var tags = new List<string> { "Important", "Verified", "Debug" };

            // Act
            var service = await _factory.CreateServiceWithCategoriesAndTagsAsync(
                categories,
                tags,
                _testBackupDirectory);

            // Assert
            Assert.That(service, Is.Not.Null);
            Assert.That(service, Is.InstanceOf<IBackupService>());
        }

        [Test]
        public async Task CreateServiceWithCategoriesAndTagsAsync_NullTags_ReturnsInitializedService()
        {
            // Arrange
            var categories = new List<string> { "Production", "Development", "Testing" };
            List<string> tags = null;

            // Act
            var service = await _factory.CreateServiceWithCategoriesAndTagsAsync(
                categories,
                tags,
                _testBackupDirectory);

            // Assert
            Assert.That(service, Is.Not.Null);
            Assert.That(service, Is.InstanceOf<IBackupService>());
        }
    }
}

