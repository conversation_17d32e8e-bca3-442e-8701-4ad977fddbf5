using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Communication.Backup
{
    /// <summary>
    /// Implementation of the backup service for ECU data
    /// </summary>
    public class BackupService : IBackupService
    {
        #region Private Fields

        private readonly ILoggingService _logger;
        private IECUCommunicationService? _ecuService;
        private string _backupDirectoryPath;
        private bool _isInitialized;
        private bool _useCompression;
        private bool _useEncryption;
        private string _encryptionKey;

        #endregion

        #region Events

        /// <summary>
        /// Event triggered when a backup is created
        /// </summary>
        public event EventHandler<BackupData> BackupCreated;

        /// <summary>
        /// Event triggered when a backup is restored
        /// </summary>
        public event EventHandler<BackupData> BackupRestored;

        /// <summary>
        /// Event triggered when an error occurs during backup operations
        /// </summary>
        public event EventHandler<string> BackupError;

        #endregion

        #region Properties

        /// <summary>
        /// Gets the backup directory path
        /// </summary>
        public string BackupDirectoryPath => _backupDirectoryPath;

        /// <summary>
        /// Gets or sets whether to use compression for backups
        /// </summary>
        public bool UseCompression
        {
            get => _useCompression;
            set => _useCompression = value;
        }

        /// <summary>
        /// Gets or sets whether to use encryption for backups
        /// </summary>
        public bool UseEncryption
        {
            get => _useEncryption;
            set => _useEncryption = value;
        }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the BackupService class
        /// </summary>
        /// <param name="logger">The logging service</param>
        public BackupService(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _backupDirectoryPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");
            _isInitialized = false;
            _useCompression = true; // Enable compression by default
            _useEncryption = false; // Disable encryption by default
            _encryptionKey = GenerateEncryptionKey(); // Generate a default encryption key

            // Initialize events to empty handlers to avoid null reference exceptions
            BackupCreated = (sender, data) => { };
            BackupRestored = (sender, data) => { };
            BackupError = (sender, message) => { };
        }

        /// <summary>
        /// Initializes a new instance of the BackupService class with custom settings
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="backupDirectoryPath">The backup directory path</param>
        /// <param name="useCompression">Whether to use compression</param>
        /// <param name="useEncryption">Whether to use encryption</param>
        public BackupService(ILoggingService logger, string backupDirectoryPath, bool useCompression = true, bool useEncryption = false)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _backupDirectoryPath = backupDirectoryPath ?? throw new ArgumentNullException(nameof(backupDirectoryPath));
            _isInitialized = false;
            _useCompression = useCompression;
            _useEncryption = useEncryption;
            _encryptionKey = GenerateEncryptionKey();

            // Initialize events to empty handlers to avoid null reference exceptions
            BackupCreated = (sender, data) => { };
            BackupRestored = (sender, data) => { };
            BackupError = (sender, message) => { };
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Initializes the backup service
        /// </summary>
        /// <param name="ecuService">The ECU communication service to use</param>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync(IECUCommunicationService ecuService)
        {
            try
            {
                _logger?.LogInformation("Initializing backup service", "BackupService");

                if (ecuService == null)
                {
                    _logger?.LogError("ECU communication service is null", "BackupService");
                    BackupError?.Invoke(this, "ECU communication service is null");
                    return false;
                }

                _ecuService = ecuService;

                // Create backup directory if it doesn't exist
                if (!Directory.Exists(_backupDirectoryPath))
                {
                    Directory.CreateDirectory(_backupDirectoryPath);
                    _logger?.LogInformation($"Created backup directory: {_backupDirectoryPath}", "BackupService");
                }

                _isInitialized = true;
                _logger?.LogInformation("Backup service initialized successfully", "BackupService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to initialize backup service", "BackupService", ex);
                BackupError?.Invoke(this, $"Initialization error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Creates a backup of an ECU
        /// </summary>
        /// <param name="ecu">The ECU to backup</param>
        /// <param name="description">Optional description for the backup</param>
        /// <param name="category">Optional category for the backup</param>
        /// <param name="tags">Optional tags for the backup</param>
        /// <param name="includeEEPROM">Whether to include EEPROM data in the backup</param>
        /// <param name="includeMicrocontrollerCode">Whether to include microcontroller code in the backup</param>
        /// <param name="includeParameters">Whether to include parameters in the backup</param>
        /// <returns>The created backup data</returns>
        public async Task<BackupData> CreateBackupAsync(ECUDevice ecu, string description = "", string category = "", List<string>? tags = null, bool includeEEPROM = true, bool includeMicrocontrollerCode = true, bool includeParameters = true)
        {
            try
            {
                _logger?.LogInformation($"Creating backup for ECU {ecu?.Name}", "BackupService");

                if (!ValidateInitialization())
                {
                    return null;
                }

                if (ecu == null)
                {
                    _logger?.LogError("ECU is null", "BackupService");
                    BackupError?.Invoke(this, "ECU is null");
                    return null;
                }

                // Create backup data object
                BackupData backup = new BackupData
                {
                    ECUId = ecu.Id,
                    ECUName = ecu.Name,
                    ECUSerialNumber = ecu.SerialNumber,
                    ECUHardwareVersion = ecu.HardwareVersion,
                    ECUSoftwareVersion = ecu.SoftwareVersion,
                    Description = description,
                    CreatedBy = Environment.UserName,
                    LastModifiedBy = Environment.UserName,
                    Category = category,
                    Tags = tags ?? new List<string>(),
                    Version = 1,
                    IsCompressed = _useCompression,
                    IsEncrypted = _useEncryption,
                    IsComplete = true
                };

                // Read EEPROM data if requested
                if (includeEEPROM)
                {
                    _logger?.LogInformation($"Reading EEPROM data from ECU {ecu.Name}", "BackupService");
                    byte[] eepromData = await _ecuService.ReadEEPROMAsync(ecu);
                    if (eepromData != null)
                    {
                        backup.EEPROMData = eepromData;
                        _logger?.LogInformation($"Read {eepromData.Length} bytes of EEPROM data", "BackupService");
                    }
                    else
                    {
                        _logger?.LogWarning($"Failed to read EEPROM data from ECU {ecu.Name}", "BackupService");
                        backup.IsComplete = false;
                    }
                }

                // Read microcontroller code if requested
                if (includeMicrocontrollerCode)
                {
                    _logger?.LogInformation($"Reading microcontroller code from ECU {ecu.Name}", "BackupService");
                    byte[] mcuCode = await _ecuService.ReadMicrocontrollerCodeAsync(ecu);
                    if (mcuCode != null)
                    {
                        backup.MicrocontrollerCode = mcuCode;
                        _logger?.LogInformation($"Read {mcuCode.Length} bytes of microcontroller code", "BackupService");
                    }
                    else
                    {
                        _logger?.LogWarning($"Failed to read microcontroller code from ECU {ecu.Name}", "BackupService");
                        backup.IsComplete = false;
                    }
                }

                // Read parameters if requested
                if (includeParameters)
                {
                    _logger?.LogInformation($"Reading parameters from ECU {ecu.Name}", "BackupService");
                    Dictionary<string, object> parameters = await _ecuService.ReadParametersAsync(ecu);
                    if (parameters != null)
                    {
                        backup.Parameters = parameters;
                        _logger?.LogInformation($"Read {parameters.Count} parameters", "BackupService");
                    }
                    else
                    {
                        _logger?.LogWarning($"Failed to read parameters from ECU {ecu.Name}", "BackupService");
                        backup.IsComplete = false;
                    }
                }

                // Calculate checksum for integrity verification
                string dataToHash = backup.ECUId + backup.CreationTime.ToString() +
                                   (backup.EEPROMData != null ? DataConversionHelper.BytesToHexString(backup.EEPROMData) : string.Empty) +
                                   (backup.MicrocontrollerCode != null ? DataConversionHelper.BytesToHexString(backup.MicrocontrollerCode) : string.Empty);
                backup.Checksum = DataConversionHelper.CalculateSHA256Checksum(System.Text.Encoding.UTF8.GetBytes(dataToHash));

                // Generate a default file path for the backup
                string fileName = $"{ecu.Name}_{ecu.SerialNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.backup";
                string filePath = Path.Combine(_backupDirectoryPath, fileName);
                backup.FilePath = filePath;

                // Save the backup to a file
                bool saved = await SaveBackupToFileAsync(backup, filePath);
                if (!saved)
                {
                    _logger?.LogWarning($"Failed to save backup to file {filePath}", "BackupService");
                }

                _logger?.LogInformation($"Backup created for ECU {ecu.Name}", "BackupService");
                BackupCreated?.Invoke(this, backup);
                return backup;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error creating backup for ECU {ecu?.Name}", "BackupService", ex);
                BackupError?.Invoke(this, $"Backup creation error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Backs up an ECU with progress reporting
        /// </summary>
        /// <param name="ecu">The ECU to backup</param>
        /// <param name="progress">Progress reporter</param>
        /// <param name="description">Optional description for the backup</param>
        /// <param name="category">Optional category for the backup</param>
        /// <param name="tags">Optional tags for the backup</param>
        /// <returns>The created backup data</returns>
        public async Task<BackupData> BackupECUAsync(ECUDevice ecu, IProgress<int> progress, string description = "", string category = "", List<string>? tags = null)
        {
            try
            {
                _logger?.LogInformation($"Creating backup for ECU {ecu?.Name} with progress reporting", "BackupService");

                if (!ValidateInitialization())
                {
                    return null;
                }

                if (ecu == null)
                {
                    _logger?.LogError("ECU is null", "BackupService");
                    BackupError?.Invoke(this, "ECU is null");
                    return null;
                }

                // Report initial progress
                progress?.Report(0);

                // Create backup data object
                BackupData backup = new BackupData
                {
                    Id = Guid.NewGuid().ToString(),
                    ECUId = ecu.Id,
                    ECUName = ecu.Name,
                    ECUSerialNumber = ecu.SerialNumber,
                    ECUHardwareVersion = ecu.HardwareVersion,
                    ECUSoftwareVersion = ecu.SoftwareVersion,
                    Description = description,
                    CreationTime = DateTime.Now,
                    LastModifiedTime = DateTime.Now,
                    CreatedBy = Environment.UserName,
                    LastModifiedBy = Environment.UserName,
                    Category = category,
                    Tags = tags ?? new List<string>(),
                    Version = 1,
                    IsCompressed = _useCompression,
                    IsEncrypted = _useEncryption,
                    IsComplete = false
                };

                // Report progress after metadata creation
                progress?.Report(10);

                // Read EEPROM data
                _logger?.LogInformation($"Reading EEPROM data from ECU {ecu.Name}", "BackupService");

                // Create a progress transformer for EEPROM reading (10% to 40% of total progress)
                var eepromProgress = new Progress<int>(percent =>
                {
                    int transformedPercent = 10 + (percent * 30 / 100);
                    progress?.Report(transformedPercent);
                });

                byte[] eepromData = await _ecuService.ReadEEPROMAsync(ecu, eepromProgress);
                if (eepromData != null)
                {
                    backup.EEPROMData = eepromData;
                    _logger?.LogInformation($"Read {eepromData.Length} bytes of EEPROM data", "BackupService");
                }
                else
                {
                    _logger?.LogWarning("Failed to read EEPROM data", "BackupService");
                }

                // Report progress after EEPROM reading
                progress?.Report(40);

                // Read microcontroller code
                _logger?.LogInformation($"Reading microcontroller code from ECU {ecu.Name}", "BackupService");

                // Create a progress transformer for microcontroller code reading (40% to 70% of total progress)
                var mcuProgress = new Progress<int>(percent =>
                {
                    int transformedPercent = 40 + (percent * 30 / 100);
                    progress?.Report(transformedPercent);
                });

                byte[] mcuCode = await _ecuService.ReadMicrocontrollerCodeAsync(ecu, mcuProgress);
                if (mcuCode != null)
                {
                    backup.MicrocontrollerCode = mcuCode;
                    _logger?.LogInformation($"Read {mcuCode.Length} bytes of microcontroller code", "BackupService");
                }
                else
                {
                    _logger?.LogWarning("Failed to read microcontroller code", "BackupService");
                }

                // Report progress after microcontroller code reading
                progress?.Report(70);

                // Read parameters
                _logger?.LogInformation($"Reading parameters from ECU {ecu.Name}", "BackupService");

                // Create a progress transformer for parameters reading (70% to 90% of total progress)
                var paramProgress = new Progress<int>(percent =>
                {
                    int transformedPercent = 70 + (percent * 20 / 100);
                    progress?.Report(transformedPercent);
                });

                Dictionary<string, object> parameters = await _ecuService.ReadParametersAsync(ecu, paramProgress);
                if (parameters != null)
                {
                    backup.Parameters = parameters;
                    _logger?.LogInformation($"Read {parameters.Count} parameters", "BackupService");
                }
                else
                {
                    _logger?.LogWarning("Failed to read parameters", "BackupService");
                }

                // Report progress after parameters reading
                progress?.Report(90);

                // Calculate a checksum for the backup
                string dataToHash = backup.ECUId + backup.CreationTime.ToString() +
                                   (backup.EEPROMData != null ? DataConversionHelper.BytesToHexString(backup.EEPROMData) : string.Empty) +
                                   (backup.MicrocontrollerCode != null ? DataConversionHelper.BytesToHexString(backup.MicrocontrollerCode) : string.Empty);
                backup.Checksum = DataConversionHelper.CalculateSHA256Checksum(System.Text.Encoding.UTF8.GetBytes(dataToHash));

                // Mark the backup as complete
                backup.IsComplete = true;

                // Generate a default file path for the backup
                string fileName = $"{ecu.Name}_{ecu.SerialNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.backup";
                string filePath = Path.Combine(_backupDirectoryPath, fileName);
                backup.FilePath = filePath;

                // Save the backup to a file
                bool saved = await SaveBackupToFileAsync(backup, filePath);
                if (!saved)
                {
                    _logger?.LogWarning($"Failed to save backup to file {filePath}", "BackupService");
                }

                // Report final progress
                progress?.Report(100);

                _logger?.LogInformation($"Backup created for ECU {ecu.Name}", "BackupService");
                BackupCreated?.Invoke(this, backup);
                return backup;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error creating backup for ECU {ecu?.Name}", "BackupService", ex);
                BackupError?.Invoke(this, $"Backup creation error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Restores an ECU from a backup with progress reporting
        /// </summary>
        /// <param name="backup">The backup to restore from</param>
        /// <param name="ecu">The ECU to restore</param>
        /// <param name="progress">Progress reporter</param>
        /// <param name="restoreEEPROM">Whether to restore EEPROM data</param>
        /// <param name="restoreMicrocontrollerCode">Whether to restore microcontroller code</param>
        /// <param name="restoreParameters">Whether to restore parameters</param>
        /// <returns>True if restoration is successful, false otherwise</returns>
        public async Task<bool> RestoreECUAsync(BackupData backup, ECUDevice ecu, IProgress<int> progress, bool restoreEEPROM = true, bool restoreMicrocontrollerCode = true, bool restoreParameters = true)
        {
            try
            {
                _logger?.LogInformation($"Restoring backup to ECU {ecu?.Name} with progress reporting", "BackupService");

                if (!ValidateInitialization())
                {
                    return false;
                }

                if (backup == null)
                {
                    _logger?.LogError("Backup is null", "BackupService");
                    BackupError?.Invoke(this, "Backup is null");
                    return false;
                }

                if (ecu == null)
                {
                    _logger?.LogError("ECU is null", "BackupService");
                    BackupError?.Invoke(this, "ECU is null");
                    return false;
                }

                // Report initial progress
                progress?.Report(0);

                // Verify backup integrity
                bool isValid = await VerifyBackupIntegrityAsync(backup);
                if (!isValid)
                {
                    _logger?.LogError($"Backup integrity check failed for backup {backup.Id}", "BackupService");
                    BackupError?.Invoke(this, $"Backup integrity check failed for backup {backup.Id}");
                    return false;
                }

                // Report progress after integrity check
                progress?.Report(10);

                bool success = true;

                // Restore EEPROM data if requested and available
                if (restoreEEPROM && backup.EEPROMData != null && backup.EEPROMData.Length > 0)
                {
                    _logger?.LogInformation($"Restoring EEPROM data to ECU {ecu.Name}", "BackupService");

                    // Create a progress transformer for EEPROM writing (10% to 40% of total progress)
                    var eepromProgress = new Progress<int>(percent =>
                    {
                        int transformedPercent = 10 + (percent * 30 / 100);
                        progress?.Report(transformedPercent);
                    });

                    bool eepromSuccess = await _ecuService.WriteEEPROMAsync(ecu, backup.EEPROMData, eepromProgress);
                    if (eepromSuccess)
                    {
                        _logger?.LogInformation($"EEPROM data restored successfully", "BackupService");
                    }
                    else
                    {
                        _logger?.LogError($"Failed to restore EEPROM data to ECU {ecu.Name}", "BackupService");
                        success = false;
                    }
                }
                else
                {
                    // Skip EEPROM restoration
                    progress?.Report(40);
                }

                // Restore microcontroller code if requested and available
                if (restoreMicrocontrollerCode && backup.MicrocontrollerCode != null && backup.MicrocontrollerCode.Length > 0)
                {
                    _logger?.LogInformation($"Restoring microcontroller code to ECU {ecu.Name}", "BackupService");

                    // Create a progress transformer for microcontroller code writing (40% to 70% of total progress)
                    var mcuProgress = new Progress<int>(percent =>
                    {
                        int transformedPercent = 40 + (percent * 30 / 100);
                        progress?.Report(transformedPercent);
                    });

                    bool mcuSuccess = await _ecuService.WriteMicrocontrollerCodeAsync(ecu, backup.MicrocontrollerCode, mcuProgress);
                    if (mcuSuccess)
                    {
                        _logger?.LogInformation($"Microcontroller code restored successfully", "BackupService");
                    }
                    else
                    {
                        _logger?.LogError($"Failed to restore microcontroller code to ECU {ecu.Name}", "BackupService");
                        success = false;
                    }
                }
                else
                {
                    // Skip microcontroller code restoration
                    progress?.Report(70);
                }

                // Restore parameters if requested and available
                if (restoreParameters && backup.Parameters != null && backup.Parameters.Count > 0)
                {
                    _logger?.LogInformation($"Restoring parameters to ECU {ecu.Name}", "BackupService");

                    // Create a progress transformer for parameters writing (70% to 90% of total progress)
                    var paramProgress = new Progress<int>(percent =>
                    {
                        int transformedPercent = 70 + (percent * 20 / 100);
                        progress?.Report(transformedPercent);
                    });

                    bool paramSuccess = await _ecuService.WriteParametersAsync(ecu, backup.Parameters, paramProgress);
                    if (paramSuccess)
                    {
                        _logger?.LogInformation($"Parameters restored successfully", "BackupService");
                    }
                    else
                    {
                        _logger?.LogError($"Failed to restore parameters to ECU {ecu.Name}", "BackupService");
                        success = false;
                    }
                }
                else
                {
                    // Skip parameters restoration
                    progress?.Report(90);
                }

                // Report final progress
                progress?.Report(100);

                if (success)
                {
                    _logger?.LogInformation($"Backup restored successfully to ECU {ecu.Name}", "BackupService");
                    BackupRestored?.Invoke(this, backup);
                }
                else
                {
                    _logger?.LogWarning($"Backup restored with some errors to ECU {ecu.Name}", "BackupService");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error restoring backup to ECU {ecu?.Name}", "BackupService", ex);
                BackupError?.Invoke(this, $"Backup restoration error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Exports a backup to a specified format
        /// </summary>
        /// <param name="backup">The backup to export</param>
        /// <param name="exportPath">The path to export to</param>
        /// <param name="exportFormat">The format to export to</param>
        /// <returns>True if export is successful, false otherwise</returns>
        public async Task<bool> ExportBackup(BackupData backup, string exportPath, BackupExportFormat exportFormat)
        {
            try
            {
                _logger?.LogInformation($"Exporting backup {backup?.Id} to {exportFormat} format at {exportPath}", "BackupService");

                if (!ValidateInitialization())
                {
                    return false;
                }

                if (backup == null)
                {
                    _logger?.LogError("Backup is null", "BackupService");
                    BackupError?.Invoke(this, "Backup is null");
                    return false;
                }

                if (string.IsNullOrEmpty(exportPath))
                {
                    _logger?.LogError("Export path is null or empty", "BackupService");
                    BackupError?.Invoke(this, "Export path is null or empty");
                    return false;
                }

                // Ensure the directory exists
                string directory = Path.GetDirectoryName(exportPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    _logger?.LogInformation($"Created directory: {directory}", "BackupService");
                }

                switch (exportFormat)
                {
                    case BackupExportFormat.Binary:
                        return await ExportToBinaryFormat(backup, exportPath);

                    case BackupExportFormat.JSON:
                        return await ExportToJsonFormat(backup, exportPath);

                    case BackupExportFormat.XML:
                        return await ExportToXmlFormat(backup, exportPath);

                    case BackupExportFormat.CSV:
                        return await ExportToCsvFormat(backup, exportPath);

                    case BackupExportFormat.Hex:
                        return await ExportToHexFormat(backup, exportPath);

                    case BackupExportFormat.Zip:
                        return await ExportToZipFormat(backup, exportPath);

                    default:
                        _logger?.LogError($"Unsupported export format: {exportFormat}", "BackupService");
                        BackupError?.Invoke(this, $"Unsupported export format: {exportFormat}");
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error exporting backup {backup?.Id}", "BackupService", ex);
                BackupError?.Invoke(this, $"Backup export error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Restores a backup to an ECU
        /// </summary>
        /// <param name="backup">The backup to restore</param>
        /// <param name="ecu">The ECU to restore to</param>
        /// <param name="restoreEEPROM">Whether to restore EEPROM data</param>
        /// <param name="restoreMicrocontrollerCode">Whether to restore microcontroller code</param>
        /// <param name="restoreParameters">Whether to restore parameters</param>
        /// <returns>True if restoration is successful, false otherwise</returns>
        public async Task<bool> RestoreBackupAsync(BackupData backup, ECUDevice ecu, bool restoreEEPROM = true, bool restoreMicrocontrollerCode = true, bool restoreParameters = true)
        {
            try
            {
                _logger?.LogInformation($"Restoring backup to ECU {ecu?.Name}", "BackupService");

                if (!ValidateInitialization())
                {
                    return false;
                }

                if (backup == null)
                {
                    _logger?.LogError("Backup is null", "BackupService");
                    BackupError?.Invoke(this, "Backup is null");
                    return false;
                }

                if (ecu == null)
                {
                    _logger?.LogError("ECU is null", "BackupService");
                    BackupError?.Invoke(this, "ECU is null");
                    return false;
                }

                // Verify the backup integrity before restoring
                bool isValid = await VerifyBackupIntegrityAsync(backup);
                if (!isValid)
                {
                    _logger?.LogWarning("Backup integrity check failed, proceeding with caution", "BackupService");
                    // We still proceed, but with a warning
                }

                bool success = true;

                // Restore EEPROM data if requested and available
                if (restoreEEPROM && backup.EEPROMData != null && backup.EEPROMData.Length > 0)
                {
                    _logger?.LogInformation($"Restoring EEPROM data to ECU {ecu.Name}", "BackupService");
                    bool eepromSuccess = await _ecuService.WriteEEPROMAsync(ecu, backup.EEPROMData);
                    if (eepromSuccess)
                    {
                        _logger?.LogInformation($"EEPROM data restored successfully", "BackupService");
                    }
                    else
                    {
                        _logger?.LogError($"Failed to restore EEPROM data to ECU {ecu.Name}", "BackupService");
                        success = false;
                    }
                }

                // Restore microcontroller code if requested and available
                if (restoreMicrocontrollerCode && backup.MicrocontrollerCode != null && backup.MicrocontrollerCode.Length > 0)
                {
                    _logger?.LogInformation($"Restoring microcontroller code to ECU {ecu.Name}", "BackupService");
                    bool mcuSuccess = await _ecuService.WriteMicrocontrollerCodeAsync(ecu, backup.MicrocontrollerCode);
                    if (mcuSuccess)
                    {
                        _logger?.LogInformation($"Microcontroller code restored successfully", "BackupService");
                    }
                    else
                    {
                        _logger?.LogError($"Failed to restore microcontroller code to ECU {ecu.Name}", "BackupService");
                        success = false;
                    }
                }

                // Restore parameters if requested and available
                if (restoreParameters && backup.Parameters != null && backup.Parameters.Count > 0)
                {
                    _logger?.LogInformation($"Restoring parameters to ECU {ecu.Name}", "BackupService");
                    bool paramSuccess = await _ecuService.WriteParametersAsync(ecu, backup.Parameters);
                    if (paramSuccess)
                    {
                        _logger?.LogInformation($"Parameters restored successfully", "BackupService");
                    }
                    else
                    {
                        _logger?.LogError($"Failed to restore parameters to ECU {ecu.Name}", "BackupService");
                        success = false;
                    }
                }

                if (success)
                {
                    _logger?.LogInformation($"Backup restored successfully to ECU {ecu.Name}", "BackupService");
                    BackupRestored?.Invoke(this, backup);
                }
                else
                {
                    _logger?.LogWarning($"Backup restoration to ECU {ecu.Name} completed with some errors", "BackupService");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error restoring backup to ECU {ecu?.Name}", "BackupService", ex);
                BackupError?.Invoke(this, $"Backup restoration error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets a list of all available backups
        /// </summary>
        /// <returns>List of available backups</returns>
        public async Task<List<BackupData>> GetAllBackupsAsync()
        {
            try
            {
                _logger?.LogInformation("Getting all available backups", "BackupService");

                if (!ValidateInitialization())
                {
                    return new List<BackupData>();
                }

                // Create the backup directory if it doesn't exist
                if (!Directory.Exists(_backupDirectoryPath))
                {
                    Directory.CreateDirectory(_backupDirectoryPath);
                    _logger?.LogInformation($"Created backup directory: {_backupDirectoryPath}", "BackupService");
                    return new List<BackupData>();
                }

                // Get all backup files in the directory
                string[] backupFiles = Directory.GetFiles(_backupDirectoryPath, "*.backup", SearchOption.AllDirectories);
                _logger?.LogInformation($"Found {backupFiles.Length} backup files", "BackupService");

                List<BackupData> backups = new List<BackupData>();

                // Load each backup file
                foreach (string filePath in backupFiles)
                {
                    try
                    {
                        BackupData backup = await LoadBackupFromFileAsync(filePath);
                        if (backup != null)
                        {
                            backups.Add(backup);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning($"Error loading backup from file {filePath}: {ex.Message}", "BackupService");
                        // Continue with the next file
                    }
                }

                // Process version relationships
                ProcessBackupVersionRelationships(backups);

                _logger?.LogInformation($"Successfully loaded {backups.Count} backups", "BackupService");
                return backups;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error getting all backups", "BackupService", ex);
                BackupError?.Invoke(this, $"Error getting all backups: {ex.Message}");
                return new List<BackupData>();
            }
        }

        /// <summary>
        /// Process backup version relationships to ensure consistency
        /// </summary>
        /// <param name="backups">The list of backups to process</param>
        private void ProcessBackupVersionRelationships(List<BackupData> backups)
        {
            try
            {
                _logger?.LogInformation("Processing backup version relationships", "BackupService");

                // Group backups by root backup ID
                var backupGroups = backups
                    .Where(b => !string.IsNullOrEmpty(b.RootBackupId))
                    .GroupBy(b => b.RootBackupId)
                    .ToList();

                foreach (var group in backupGroups)
                {
                    // Sort backups by version
                    var sortedBackups = group.OrderBy(b => b.Version).ToList();

                    // Find the root backup
                    var rootBackup = backups.FirstOrDefault(b => b.Id == group.Key);
                    if (rootBackup == null)
                    {
                        // If the root backup is missing, use the oldest backup as the root
                        rootBackup = sortedBackups.FirstOrDefault();
                        if (rootBackup != null)
                        {
                            rootBackup.ParentBackupId = null;
                            rootBackup.RootBackupId = rootBackup.Id;
                            rootBackup.Version = 1;
                        }
                    }

                    if (rootBackup != null)
                    {
                        // Clear child IDs to rebuild the relationship chain
                        rootBackup.ChildBackupIds.Clear();

                        // Process each backup in the version chain
                        BackupData previousBackup = rootBackup;
                        for (int i = 1; i < sortedBackups.Count; i++)
                        {
                            var currentBackup = sortedBackups[i];

                            // Update parent-child relationship
                            currentBackup.ParentBackupId = previousBackup.Id;
                            currentBackup.Version = i + 1;

                            // Add to parent's child list
                            if (!previousBackup.ChildBackupIds.Contains(currentBackup.Id))
                            {
                                previousBackup.ChildBackupIds.Add(currentBackup.Id);
                            }

                            previousBackup = currentBackup;
                        }

                        // Update latest version flag
                        foreach (var backup in sortedBackups)
                        {
                            backup.IsLatestVersion = (backup == sortedBackups.Last());
                        }
                    }
                }

                _logger?.LogInformation($"Processed {backupGroups.Count} backup version chains", "BackupService");
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error processing backup version relationships", "BackupService", ex);
            }
        }

        /// <summary>
        /// Gets backups for a specific ECU
        /// </summary>
        /// <param name="ecuId">The ID of the ECU</param>
        /// <returns>List of backups for the specified ECU</returns>
        public async Task<List<BackupData>> GetBackupsForECUAsync(string ecuId)
        {
            try
            {
                _logger?.LogInformation($"Getting backups for ECU with ID {ecuId}", "BackupService");

                if (!ValidateInitialization())
                {
                    return new List<BackupData>();
                }

                if (string.IsNullOrEmpty(ecuId))
                {
                    _logger?.LogError("ECU ID is null or empty", "BackupService");
                    BackupError?.Invoke(this, "ECU ID is null or empty");
                    return new List<BackupData>();
                }

                // Get all backups
                List<BackupData> allBackups = await GetAllBackupsAsync();

                // Filter backups by ECU ID
                List<BackupData> ecuBackups = allBackups.Where(b => b.ECUId == ecuId).ToList();

                _logger?.LogInformation($"Found {ecuBackups.Count} backups for ECU with ID {ecuId}", "BackupService");
                return ecuBackups;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error getting backups for ECU with ID {ecuId}", "BackupService", ex);
                BackupError?.Invoke(this, $"Error getting backups for ECU: {ex.Message}");
                return new List<BackupData>();
            }
        }

        /// <summary>
        /// Loads a backup from a file
        /// </summary>
        /// <param name="filePath">The path to the backup file</param>
        /// <returns>The loaded backup data</returns>
        public async Task<BackupData> LoadBackupFromFileAsync(string filePath)
        {
            try
            {
                _logger?.LogInformation($"Loading backup from file {filePath}", "BackupService");

                if (!ValidateInitialization())
                {
                    return null;
                }

                if (string.IsNullOrEmpty(filePath))
                {
                    _logger?.LogError("File path is null or empty", "BackupService");
                    BackupError?.Invoke(this, "File path is null or empty");
                    return null;
                }

                if (!File.Exists(filePath))
                {
                    _logger?.LogError($"Backup file not found: {filePath}", "BackupService");
                    BackupError?.Invoke(this, $"Backup file not found: {filePath}");
                    return null;
                }

                // Read the data from the file
                byte[] fileData = await File.ReadAllBytesAsync(filePath);

                try
                {
                    // First, try to deserialize directly (for backward compatibility with uncompressed/unencrypted backups)
                    string jsonString = Encoding.UTF8.GetString(fileData);
                    BackupData backup = JsonSerializer.Deserialize<BackupData>(jsonString);

                    if (backup != null)
                    {
                        // Set the file path
                        backup.FilePath = filePath;

                        // Verify the backup integrity
                        bool isValid = await VerifyBackupIntegrityAsync(backup);
                        if (!isValid)
                        {
                            _logger?.LogWarning($"Backup integrity check failed for file {filePath}", "BackupService");
                            // We still return the backup, but with a warning
                        }

                        _logger?.LogInformation($"Backup loaded from file {filePath}", "BackupService");
                        return backup;
                    }
                }
                catch
                {
                    // If direct deserialization fails, try to process as compressed/encrypted data
                    _logger?.LogInformation("Direct deserialization failed, trying to process as compressed/encrypted data", "BackupService");
                }

                // Try to decrypt the data if it might be encrypted
                try
                {
                    byte[] decryptedData = DecryptData(fileData, _encryptionKey);
                    fileData = decryptedData;
                    _logger?.LogInformation("Data decrypted successfully", "BackupService");
                }
                catch (Exception ex)
                {
                    _logger?.LogDebug($"Data decryption failed: {ex.Message}. This might not be encrypted data.", "BackupService");
                    // Continue with the original data if decryption fails
                }

                // Try to decompress the data if it might be compressed
                try
                {
                    byte[] decompressedData = DecompressData(fileData);
                    fileData = decompressedData;
                    _logger?.LogInformation("Data decompressed successfully", "BackupService");
                }
                catch (Exception ex)
                {
                    _logger?.LogDebug($"Data decompression failed: {ex.Message}. This might not be compressed data.", "BackupService");
                    // Continue with the current data if decompression fails
                }

                // Try to deserialize the processed data
                try
                {
                    string jsonString = Encoding.UTF8.GetString(fileData);
                    BackupData backup = JsonSerializer.Deserialize<BackupData>(jsonString);

                    if (backup == null)
                    {
                        _logger?.LogError($"Failed to deserialize backup from file {filePath}", "BackupService");
                        BackupError?.Invoke(this, $"Failed to deserialize backup from file {filePath}");
                        return null;
                    }

                    // Set the file path
                    backup.FilePath = filePath;

                    // Verify the backup integrity
                    bool isValid = await VerifyBackupIntegrityAsync(backup);
                    if (!isValid)
                    {
                        _logger?.LogWarning($"Backup integrity check failed for file {filePath}", "BackupService");
                        // We still return the backup, but with a warning
                    }

                    _logger?.LogInformation($"Backup loaded from file {filePath}", "BackupService");
                    return backup;
                }
                catch (Exception ex)
                {
                    _logger?.LogError($"Failed to deserialize backup after processing: {ex.Message}", "BackupService");
                    BackupError?.Invoke(this, $"Failed to deserialize backup after processing: {ex.Message}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error loading backup from file {filePath}", "BackupService", ex);
                BackupError?.Invoke(this, $"Backup load error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Saves a backup to a file
        /// </summary>
        /// <param name="backup">The backup to save</param>
        /// <param name="filePath">The path to save the backup to</param>
        /// <returns>True if save is successful, false otherwise</returns>
        public async Task<bool> SaveBackupToFileAsync(BackupData backup, string filePath)
        {
            try
            {
                _logger?.LogInformation($"Saving backup to file {filePath}", "BackupService");

                if (!ValidateInitialization())
                {
                    return false;
                }

                if (backup == null)
                {
                    _logger?.LogError("Backup is null", "BackupService");
                    BackupError?.Invoke(this, "Backup is null");
                    return false;
                }

                if (string.IsNullOrEmpty(filePath))
                {
                    _logger?.LogError("File path is null or empty", "BackupService");
                    BackupError?.Invoke(this, "File path is null or empty");
                    return false;
                }

                // Ensure the directory exists
                string directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    _logger?.LogInformation($"Created directory: {directory}", "BackupService");
                }

                // Update the file path in the backup object
                backup.FilePath = filePath;

                // Add metadata about compression and encryption
                backup.IsCompressed = _useCompression;
                backup.IsEncrypted = _useEncryption;

                // Serialize the backup data to JSON
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };

                string jsonString = JsonSerializer.Serialize(backup, options);
                byte[] jsonBytes = Encoding.UTF8.GetBytes(jsonString);

                // Apply compression if enabled
                if (_useCompression)
                {
                    _logger?.LogInformation("Compressing backup data", "BackupService");
                    jsonBytes = CompressData(jsonBytes);
                }

                // Apply encryption if enabled
                if (_useEncryption)
                {
                    _logger?.LogInformation("Encrypting backup data", "BackupService");
                    jsonBytes = EncryptData(jsonBytes, _encryptionKey);
                }

                // Write the data to the file
                await File.WriteAllBytesAsync(filePath, jsonBytes);

                // Update the size in bytes
                backup.SizeInBytes = jsonBytes.Length;

                // Update the last modified information
                backup.LastModifiedTime = DateTime.Now;
                backup.LastModifiedBy = Environment.UserName;

                _logger?.LogInformation($"Backup saved to file {filePath} ({backup.SizeInBytes} bytes)", "BackupService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error saving backup to file {filePath}", "BackupService", ex);
                BackupError?.Invoke(this, $"Backup save error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Deletes a backup
        /// </summary>
        /// <param name="backup">The backup to delete</param>
        /// <returns>True if deletion is successful, false otherwise</returns>
        public async Task<bool> DeleteBackupAsync(BackupData backup)
        {
            try
            {
                _logger?.LogInformation($"Deleting backup for ECU {backup?.ECUName}", "BackupService");

                if (!ValidateInitialization())
                {
                    return false;
                }

                if (backup == null)
                {
                    _logger?.LogError("Backup is null", "BackupService");
                    BackupError?.Invoke(this, "Backup is null");
                    return false;
                }

                if (string.IsNullOrEmpty(backup.FilePath))
                {
                    _logger?.LogError("Backup file path is null or empty", "BackupService");
                    BackupError?.Invoke(this, "Backup file path is null or empty");
                    return false;
                }

                if (!File.Exists(backup.FilePath))
                {
                    _logger?.LogWarning($"Backup file not found: {backup.FilePath}", "BackupService");
                    // Consider it a success if the file doesn't exist
                    return true;
                }

                // Delete the file
                File.Delete(backup.FilePath);

                _logger?.LogInformation($"Backup deleted successfully: {backup.FilePath}", "BackupService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error deleting backup: {backup?.FilePath}", "BackupService", ex);
                BackupError?.Invoke(this, $"Backup deletion error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Verifies the integrity of a backup
        /// </summary>
        /// <param name="backup">The backup to verify</param>
        /// <returns>True if the backup is valid, false otherwise</returns>
        public virtual async Task<bool> VerifyBackupIntegrityAsync(BackupData backup)
        {
            try
            {
                _logger?.LogInformation($"Verifying integrity of backup for ECU {backup?.ECUName}", "BackupService");

                if (!ValidateInitialization())
                {
                    return false;
                }

                if (backup == null)
                {
                    _logger?.LogError("Backup is null", "BackupService");
                    BackupError?.Invoke(this, "Backup is null");
                    return false;
                }

                // If the backup doesn't have a checksum, it can't be verified
                if (string.IsNullOrEmpty(backup.Checksum))
                {
                    _logger?.LogWarning("Backup does not have a checksum", "BackupService");

                    // Try to generate a checksum if we have the necessary data
                    if (!string.IsNullOrEmpty(backup.ECUId) && backup.CreationTime != DateTime.MinValue)
                    {
                        _logger?.LogInformation("Attempting to generate a checksum for the backup", "BackupService");

                        string dataToHash = backup.ECUId + backup.CreationTime.ToString() +
                                           (backup.EEPROMData != null ? DataConversionHelper.BytesToHexString(backup.EEPROMData) : string.Empty) +
                                           (backup.MicrocontrollerCode != null ? DataConversionHelper.BytesToHexString(backup.MicrocontrollerCode) : string.Empty);
                        backup.Checksum = DataConversionHelper.CalculateSHA256Checksum(System.Text.Encoding.UTF8.GetBytes(dataToHash));

                        // Save the updated backup with the new checksum
                        if (!string.IsNullOrEmpty(backup.FilePath) && File.Exists(backup.FilePath))
                        {
                            await SaveBackupToFileAsync(backup, backup.FilePath);
                            _logger?.LogInformation("Generated and saved new checksum for backup", "BackupService");
                            return true;
                        }
                    }

                    return false;
                }

                // Recalculate the checksum using the same method as in CreateBackupAsync
                string dataToHashForVerification = backup.ECUId + backup.CreationTime.ToString() +
                                   (backup.EEPROMData != null ? DataConversionHelper.BytesToHexString(backup.EEPROMData) : string.Empty) +
                                   (backup.MicrocontrollerCode != null ? DataConversionHelper.BytesToHexString(backup.MicrocontrollerCode) : string.Empty);
                string calculatedChecksum = DataConversionHelper.CalculateSHA256Checksum(System.Text.Encoding.UTF8.GetBytes(dataToHashForVerification));

                // Compare the calculated checksum with the stored checksum
                bool isValid = calculatedChecksum == backup.Checksum;

                if (isValid)
                {
                    _logger?.LogInformation("Backup integrity verified successfully", "BackupService");
                }
                else
                {
                    _logger?.LogWarning("Backup integrity check failed - checksums do not match", "BackupService");

                    // Try to repair the backup by reloading from file if available
                    if (!string.IsNullOrEmpty(backup.FilePath) && File.Exists(backup.FilePath))
                    {
                        _logger?.LogInformation($"Attempting to repair backup by reloading from file {backup.FilePath}", "BackupService");

                        try
                        {
                            var reloadedBackup = await LoadBackupFromFileAsync(backup.FilePath);
                            if (reloadedBackup != null)
                            {
                                // Copy the reloaded data to the original backup
                                backup.EEPROMData = reloadedBackup.EEPROMData;
                                backup.MicrocontrollerCode = reloadedBackup.MicrocontrollerCode;
                                backup.Parameters = reloadedBackup.Parameters;
                                backup.Checksum = reloadedBackup.Checksum;

                                // Recalculate the checksum
                                string newDataToHash = backup.ECUId + backup.CreationTime.ToString() +
                                                     (backup.EEPROMData != null ? DataConversionHelper.BytesToHexString(backup.EEPROMData) : string.Empty) +
                                                     (backup.MicrocontrollerCode != null ? DataConversionHelper.BytesToHexString(backup.MicrocontrollerCode) : string.Empty);
                                string newCalculatedChecksum = DataConversionHelper.CalculateSHA256Checksum(System.Text.Encoding.UTF8.GetBytes(newDataToHash));

                                if (newCalculatedChecksum == backup.Checksum)
                                {
                                    _logger?.LogInformation($"Successfully repaired backup for ECU {backup.ECUName}", "BackupService");
                                    return true;
                                }
                                else
                                {
                                    _logger?.LogWarning("Repair attempt failed - checksums still do not match", "BackupService");
                                }
                            }
                        }
                        catch (Exception repairEx)
                        {
                            _logger?.LogError($"Failed to repair backup: {repairEx.Message}", "BackupService", repairEx);
                        }
                    }
                }

                // Verify data integrity even if checksum is valid
                if (isValid)
                {
                    // Check for empty data that should not be empty
                    bool hasDataIssues = false;

                    if (backup.EEPROMData != null && backup.EEPROMData.Length == 0)
                    {
                        _logger?.LogWarning($"Backup for ECU {backup.ECUName} has empty EEPROM data", "BackupService");
                        hasDataIssues = true;
                    }

                    if (backup.MicrocontrollerCode != null && backup.MicrocontrollerCode.Length == 0)
                    {
                        _logger?.LogWarning($"Backup for ECU {backup.ECUName} has empty microcontroller code", "BackupService");
                        hasDataIssues = true;
                    }

                    if (backup.Parameters != null && backup.Parameters.Count == 0)
                    {
                        _logger?.LogWarning($"Backup for ECU {backup.ECUName} has no parameters", "BackupService");
                        hasDataIssues = true;
                    }

                    if (hasDataIssues)
                    {
                        _logger?.LogWarning("Backup has data integrity issues despite valid checksum", "BackupService");
                        // We still return true because the checksum is valid
                    }
                }

                return isValid;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error verifying backup integrity", "BackupService", ex);
                BackupError?.Invoke(this, $"Backup integrity verification error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets backups by category
        /// </summary>
        /// <param name="category">The category to filter by</param>
        /// <returns>List of backups in the specified category</returns>
        public async Task<List<BackupData>> GetBackupsByCategoryAsync(string category)
        {
            try
            {
                _logger?.LogInformation($"Getting backups for category {category}", "BackupService");

                if (!ValidateInitialization())
                {
                    return new List<BackupData>();
                }

                if (string.IsNullOrEmpty(category))
                {
                    _logger?.LogError("Category is null or empty", "BackupService");
                    BackupError?.Invoke(this, "Category is null or empty");
                    return new List<BackupData>();
                }

                // Get all backups
                List<BackupData> allBackups = await GetAllBackupsAsync();

                // Filter backups by category
                List<BackupData> categoryBackups = allBackups.Where(b => b.Category == category).ToList();

                _logger?.LogInformation($"Found {categoryBackups.Count} backups for category {category}", "BackupService");
                return categoryBackups;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error getting backups for category {category}", "BackupService", ex);
                BackupError?.Invoke(this, $"Error getting backups for category: {ex.Message}");
                return new List<BackupData>();
            }
        }

        /// <summary>
        /// Gets backups by tag
        /// </summary>
        /// <param name="tag">The tag to filter by</param>
        /// <returns>List of backups with the specified tag</returns>
        public async Task<List<BackupData>> GetBackupsByTagAsync(string tag)
        {
            try
            {
                _logger?.LogInformation($"Getting backups with tag {tag}", "BackupService");

                if (!ValidateInitialization())
                {
                    return new List<BackupData>();
                }

                if (string.IsNullOrEmpty(tag))
                {
                    _logger?.LogError("Tag is null or empty", "BackupService");
                    BackupError?.Invoke(this, "Tag is null or empty");
                    return new List<BackupData>();
                }

                // Get all backups
                List<BackupData> allBackups = await GetAllBackupsAsync();

                // Filter backups by tag
                List<BackupData> taggedBackups = allBackups.Where(b => b.Tags != null && b.Tags.Contains(tag)).ToList();

                _logger?.LogInformation($"Found {taggedBackups.Count} backups with tag {tag}", "BackupService");
                return taggedBackups;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error getting backups with tag {tag}", "BackupService", ex);
                BackupError?.Invoke(this, $"Error getting backups with tag: {ex.Message}");
                return new List<BackupData>();
            }
        }

        /// <summary>
        /// Adds a tag to a backup
        /// </summary>
        /// <param name="backup">The backup to tag</param>
        /// <param name="tag">The tag to add</param>
        /// <returns>True if the tag was added successfully, false otherwise</returns>
        public async Task<bool> AddTagToBackupAsync(BackupData backup, string tag)
        {
            try
            {
                _logger?.LogInformation($"Adding tag {tag} to backup {backup?.Id}", "BackupService");

                if (!ValidateInitialization())
                {
                    return false;
                }

                if (backup == null)
                {
                    _logger?.LogError("Backup is null", "BackupService");
                    BackupError?.Invoke(this, "Backup is null");
                    return false;
                }

                if (string.IsNullOrEmpty(tag))
                {
                    _logger?.LogError("Tag is null or empty", "BackupService");
                    BackupError?.Invoke(this, "Tag is null or empty");
                    return false;
                }

                // Initialize tags list if it's null
                if (backup.Tags == null)
                {
                    backup.Tags = new List<string>();
                }

                // Add the tag if it doesn't already exist
                if (!backup.Tags.Contains(tag))
                {
                    backup.Tags.Add(tag);

                    // Update the last modified information
                    backup.LastModifiedTime = DateTime.Now;
                    backup.LastModifiedBy = Environment.UserName;

                    // Save the updated backup
                    bool saved = await SaveBackupToFileAsync(backup, backup.FilePath);
                    if (!saved)
                    {
                        _logger?.LogError($"Failed to save backup after adding tag {tag}", "BackupService");
                        return false;
                    }

                    _logger?.LogInformation($"Tag {tag} added to backup {backup.Id}", "BackupService");
                    return true;
                }
                else
                {
                    _logger?.LogInformation($"Tag {tag} already exists on backup {backup.Id}", "BackupService");
                    return true; // Tag already exists, consider it a success
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error adding tag {tag} to backup {backup?.Id}", "BackupService", ex);
                BackupError?.Invoke(this, $"Error adding tag: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Removes a tag from a backup
        /// </summary>
        /// <param name="backup">The backup to remove the tag from</param>
        /// <param name="tag">The tag to remove</param>
        /// <returns>True if the tag was removed successfully, false otherwise</returns>
        public async Task<bool> RemoveTagFromBackupAsync(BackupData backup, string tag)
        {
            try
            {
                _logger?.LogInformation($"Removing tag {tag} from backup {backup?.Id}", "BackupService");

                if (!ValidateInitialization())
                {
                    return false;
                }

                if (backup == null)
                {
                    _logger?.LogError("Backup is null", "BackupService");
                    BackupError?.Invoke(this, "Backup is null");
                    return false;
                }

                if (string.IsNullOrEmpty(tag))
                {
                    _logger?.LogError("Tag is null or empty", "BackupService");
                    BackupError?.Invoke(this, "Tag is null or empty");
                    return false;
                }

                // Check if the backup has tags
                if (backup.Tags == null || !backup.Tags.Contains(tag))
                {
                    _logger?.LogInformation($"Tag {tag} not found on backup {backup.Id}", "BackupService");
                    return true; // Tag doesn't exist, consider it a success
                }

                // Remove the tag
                backup.Tags.Remove(tag);

                // Update the last modified information
                backup.LastModifiedTime = DateTime.Now;
                backup.LastModifiedBy = Environment.UserName;

                // Save the updated backup
                bool saved = await SaveBackupToFileAsync(backup, backup.FilePath);
                if (!saved)
                {
                    _logger?.LogError($"Failed to save backup after removing tag {tag}", "BackupService");
                    return false;
                }

                _logger?.LogInformation($"Tag {tag} removed from backup {backup.Id}", "BackupService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error removing tag {tag} from backup {backup?.Id}", "BackupService", ex);
                BackupError?.Invoke(this, $"Error removing tag: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Sets the category of a backup
        /// </summary>
        /// <param name="backup">The backup to categorize</param>
        /// <param name="category">The category to set</param>
        /// <returns>True if the category was set successfully, false otherwise</returns>
        public async Task<bool> SetBackupCategoryAsync(BackupData backup, string category)
        {
            try
            {
                _logger?.LogInformation($"Setting category {category} for backup {backup?.Id}", "BackupService");

                if (!ValidateInitialization())
                {
                    return false;
                }

                if (backup == null)
                {
                    _logger?.LogError("Backup is null", "BackupService");
                    BackupError?.Invoke(this, "Backup is null");
                    return false;
                }

                // Set the category
                backup.Category = category;

                // Update the last modified information
                backup.LastModifiedTime = DateTime.Now;
                backup.LastModifiedBy = Environment.UserName;

                // Save the updated backup
                bool saved = await SaveBackupToFileAsync(backup, backup.FilePath);
                if (!saved)
                {
                    _logger?.LogError($"Failed to save backup after setting category {category}", "BackupService");
                    return false;
                }

                _logger?.LogInformation($"Category {category} set for backup {backup.Id}", "BackupService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error setting category {category} for backup {backup?.Id}", "BackupService", ex);
                BackupError?.Invoke(this, $"Error setting category: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets predefined categories for backups
        /// </summary>
        /// <returns>List of predefined categories</returns>
        public async Task<List<string>> GetPredefinedCategoriesAsync()
        {
            try
            {
                _logger?.LogInformation("Getting predefined backup categories", "BackupService");

                if (!ValidateInitialization())
                {
                    return new List<string>();
                }

                // Return a list of predefined categories
                return new List<string>
                {
                    "Engine",
                    "Transmission",
                    "ABS",
                    "Climate Control",
                    "Infotainment",
                    "Body Control",
                    "Airbag",
                    "Instrument Cluster",
                    "Other"
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error getting predefined backup categories", "BackupService", ex);
                BackupError?.Invoke(this, $"Error getting predefined backup categories: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// Gets all unique categories used across backups
        /// </summary>
        /// <returns>List of unique categories</returns>
        public async Task<List<string>> GetAllCategoriesAsync()
        {
            try
            {
                _logger?.LogInformation("Getting all unique categories", "BackupService");

                if (!ValidateInitialization())
                {
                    return new List<string>();
                }

                // Get all backups
                List<BackupData> allBackups = await GetAllBackupsAsync();

                // Extract unique categories
                HashSet<string> uniqueCategories = new HashSet<string>();
                foreach (var backup in allBackups)
                {
                    if (!string.IsNullOrEmpty(backup.Category))
                    {
                        uniqueCategories.Add(backup.Category);
                    }
                }

                List<string> categories = uniqueCategories.OrderBy(c => c).ToList();
                _logger?.LogInformation($"Found {categories.Count} unique categories", "BackupService");
                return categories;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error getting all categories", "BackupService", ex);
                BackupError?.Invoke(this, $"Error getting categories: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// Gets all categories used in backups
        /// </summary>
        /// <returns>List of categories</returns>
        public async Task<List<string>> GetCategoriesAsync()
        {
            try
            {
                _logger?.LogInformation("Getting all categories", "BackupService");

                if (!ValidateInitialization())
                {
                    return new List<string>();
                }

                // Get all unique categories
                List<string> uniqueCategories = await GetAllCategoriesAsync();

                // Get predefined categories
                List<string> predefinedCategories = await GetPredefinedCategoriesAsync();

                // Combine both lists and remove duplicates
                HashSet<string> allCategories = new HashSet<string>(uniqueCategories);
                foreach (var category in predefinedCategories)
                {
                    allCategories.Add(category);
                }

                List<string> categories = allCategories.OrderBy(c => c).ToList();
                _logger?.LogInformation($"Found {categories.Count} categories", "BackupService");
                return categories;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error getting categories", "BackupService", ex);
                BackupError?.Invoke(this, $"Error getting categories: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// Gets all unique tags used across backups
        /// </summary>
        /// <returns>List of unique tags</returns>
        public async Task<List<string>> GetAllTagsAsync()
        {
            try
            {
                _logger?.LogInformation("Getting all unique tags", "BackupService");

                if (!ValidateInitialization())
                {
                    return new List<string>();
                }

                // Get all backups
                List<BackupData> allBackups = await GetAllBackupsAsync();

                // Extract unique tags
                HashSet<string> uniqueTags = new HashSet<string>();
                foreach (var backup in allBackups)
                {
                    if (backup.Tags != null && backup.Tags.Count > 0)
                    {
                        foreach (var tag in backup.Tags)
                        {
                            if (!string.IsNullOrEmpty(tag))
                            {
                                uniqueTags.Add(tag);
                            }
                        }
                    }
                }

                List<string> tags = uniqueTags.OrderBy(t => t).ToList();
                _logger?.LogInformation($"Found {tags.Count} unique tags", "BackupService");
                return tags;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error getting all tags", "BackupService", ex);
                BackupError?.Invoke(this, $"Error getting tags: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// Creates a new version of an existing backup
        /// </summary>
        /// <param name="parentBackup">The parent backup</param>
        /// <param name="ecu">The ECU to backup</param>
        /// <param name="versionNotes">Notes describing what changed in this version</param>
        /// <param name="includeEEPROM">Whether to include EEPROM data</param>
        /// <param name="includeMicrocontrollerCode">Whether to include microcontroller code</param>
        /// <param name="includeParameters">Whether to include parameters</param>
        /// <returns>The new backup version</returns>
        public async Task<BackupData> CreateBackupVersionAsync(BackupData parentBackup, ECUDevice ecu, string versionNotes = "", bool includeEEPROM = true, bool includeMicrocontrollerCode = true, bool includeParameters = true)
        {
            try
            {
                _logger?.LogInformation($"Creating new version of backup {parentBackup?.Id} for ECU {ecu?.Name}", "BackupService");

                if (!ValidateInitialization())
                {
                    return null;
                }

                if (parentBackup == null)
                {
                    _logger?.LogError("Parent backup is null", "BackupService");
                    BackupError?.Invoke(this, "Parent backup is null");
                    return null;
                }

                if (ecu == null)
                {
                    _logger?.LogError("ECU is null", "BackupService");
                    BackupError?.Invoke(this, "ECU is null");
                    return null;
                }

                // Verify the parent backup integrity before creating a new version
                bool isParentValid = await VerifyBackupIntegrityAsync(parentBackup);
                if (!isParentValid)
                {
                    _logger?.LogWarning($"Parent backup {parentBackup.Id} integrity check failed, attempting to repair", "BackupService");

                    // Try to repair the parent backup by reloading it
                    if (!string.IsNullOrEmpty(parentBackup.FilePath) && File.Exists(parentBackup.FilePath))
                    {
                        var reloadedBackup = await LoadBackupFromFileAsync(parentBackup.FilePath);
                        if (reloadedBackup != null)
                        {
                            _logger?.LogInformation($"Successfully reloaded parent backup {parentBackup.Id}", "BackupService");
                            parentBackup = reloadedBackup;
                        }
                        else
                        {
                            _logger?.LogWarning($"Failed to reload parent backup {parentBackup.Id}, proceeding with caution", "BackupService");
                        }
                    }
                }

                // Determine the root backup ID
                string rootBackupId = parentBackup.RootBackupId ?? parentBackup.Id;

                // Create a new backup with the same metadata as the parent
                BackupData newVersion = new BackupData
                {
                    Id = Guid.NewGuid().ToString(),
                    ECUId = ecu.Id,
                    ECUName = ecu.Name,
                    ECUSerialNumber = ecu.SerialNumber,
                    ECUHardwareVersion = ecu.HardwareVersion,
                    ECUSoftwareVersion = ecu.SoftwareVersion,
                    Description = parentBackup.Description,
                    CreationTime = DateTime.Now,
                    LastModifiedTime = DateTime.Now,
                    CreatedBy = Environment.UserName,
                    LastModifiedBy = Environment.UserName,
                    Category = parentBackup.Category,
                    Tags = new List<string>(parentBackup.Tags ?? new List<string>()),
                    Version = parentBackup.Version + 1,
                    IsCompressed = _useCompression,
                    IsEncrypted = _useEncryption,
                    IsComplete = true,
                    ParentBackupId = parentBackup.Id,
                    RootBackupId = rootBackupId,
                    VersionNotes = versionNotes,
                    VersionCreationTime = DateTime.Now,
                    IsLatestVersion = true,
                    ChildBackupIds = new List<string>()
                };

                // Read EEPROM data if requested
                if (includeEEPROM)
                {
                    _logger?.LogInformation($"Reading EEPROM data from ECU {ecu.Name}", "BackupService");
                    byte[] eepromData = await _ecuService.ReadEEPROMAsync(ecu);
                    if (eepromData != null)
                    {
                        newVersion.EEPROMData = eepromData;
                        _logger?.LogInformation($"Read {eepromData.Length} bytes of EEPROM data", "BackupService");
                    }
                    else
                    {
                        _logger?.LogWarning($"Failed to read EEPROM data from ECU {ecu.Name}", "BackupService");
                        newVersion.IsComplete = false;
                    }
                }

                // Read microcontroller code if requested
                if (includeMicrocontrollerCode)
                {
                    _logger?.LogInformation($"Reading microcontroller code from ECU {ecu.Name}", "BackupService");
                    byte[] mcuCode = await _ecuService.ReadMicrocontrollerCodeAsync(ecu);
                    if (mcuCode != null)
                    {
                        newVersion.MicrocontrollerCode = mcuCode;
                        _logger?.LogInformation($"Read {mcuCode.Length} bytes of microcontroller code", "BackupService");
                    }
                    else
                    {
                        _logger?.LogWarning($"Failed to read microcontroller code from ECU {ecu.Name}", "BackupService");
                        newVersion.IsComplete = false;
                    }
                }

                // Read parameters if requested
                if (includeParameters)
                {
                    _logger?.LogInformation($"Reading parameters from ECU {ecu.Name}", "BackupService");
                    Dictionary<string, object> parameters = await _ecuService.ReadParametersAsync(ecu);
                    if (parameters != null)
                    {
                        newVersion.Parameters = parameters;
                        _logger?.LogInformation($"Read {parameters.Count} parameters", "BackupService");
                    }
                    else
                    {
                        _logger?.LogWarning($"Failed to read parameters from ECU {ecu.Name}", "BackupService");
                        newVersion.IsComplete = false;
                    }
                }

                // Calculate checksum for integrity verification
                string dataToHash = newVersion.ECUId + newVersion.CreationTime.ToString() +
                                   (newVersion.EEPROMData != null ? DataConversionHelper.BytesToHexString(newVersion.EEPROMData) : string.Empty) +
                                   (newVersion.MicrocontrollerCode != null ? DataConversionHelper.BytesToHexString(newVersion.MicrocontrollerCode) : string.Empty);
                newVersion.Checksum = DataConversionHelper.CalculateSHA256Checksum(System.Text.Encoding.UTF8.GetBytes(dataToHash));

                // Generate a default file path for the backup
                string fileName = $"{ecu.Name}_{ecu.SerialNumber}_v{newVersion.Version}_{DateTime.Now:yyyyMMdd_HHmmss}.backup";
                string filePath = Path.Combine(_backupDirectoryPath, fileName);
                newVersion.FilePath = filePath;

                // Create a version-specific subdirectory if it doesn't exist
                string versionDirectory = Path.Combine(_backupDirectoryPath, "Versions", rootBackupId);
                if (!Directory.Exists(versionDirectory))
                {
                    Directory.CreateDirectory(versionDirectory);
                    _logger?.LogInformation($"Created version directory: {versionDirectory}", "BackupService");
                }

                // Create an alternative path in the version-specific directory
                string versionFilePath = Path.Combine(versionDirectory, fileName);

                // Save the backup to both locations for redundancy
                bool saved = await SaveBackupToFileAsync(newVersion, filePath);
                if (!saved)
                {
                    _logger?.LogWarning($"Failed to save backup to file {filePath}", "BackupService");
                }

                bool versionSaved = await SaveBackupToFileAsync(newVersion, versionFilePath);
                if (!versionSaved)
                {
                    _logger?.LogWarning($"Failed to save backup to version file {versionFilePath}", "BackupService");
                }

                // Store version file path (no AlternativeFilePaths property in BackupData)

                // Update the parent backup to reference this new version
                if (parentBackup.ChildBackupIds == null)
                {
                    parentBackup.ChildBackupIds = new List<string>();
                }
                parentBackup.ChildBackupIds.Add(newVersion.Id);
                parentBackup.IsLatestVersion = false;

                // Update the last modified time of the parent
                parentBackup.LastModifiedTime = DateTime.Now;
                parentBackup.LastModifiedBy = Environment.UserName;

                // Save the updated parent backup
                bool parentSaved = await SaveBackupToFileAsync(parentBackup, parentBackup.FilePath);
                if (!parentSaved)
                {
                    _logger?.LogWarning($"Failed to update parent backup {parentBackup.Id}", "BackupService");
                }

                // No AlternativeFilePaths property in BackupData

                // Update all backups in the version chain to reflect the new latest version
                try
                {
                    List<BackupData> allVersions = await GetBackupVersionsAsync(rootBackupId);
                    foreach (var version in allVersions)
                    {
                        if (version.Id != newVersion.Id)
                        {
                            version.IsLatestVersion = false;
                            await SaveBackupToFileAsync(version, version.FilePath);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning($"Error updating version chain: {ex.Message}", "BackupService");
                    // Continue despite this error
                }

                _logger?.LogInformation($"New version {newVersion.Version} created for backup {parentBackup.Id}", "BackupService");
                BackupCreated?.Invoke(this, newVersion);
                return newVersion;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error creating new version of backup {parentBackup?.Id}", "BackupService", ex);
                BackupError?.Invoke(this, $"Backup version creation error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Creates a new backup version based on a schedule
        /// </summary>
        /// <param name="schedule">The backup schedule</param>
        /// <param name="ecu">The ECU to backup</param>
        /// <returns>The created backup data</returns>
        public async Task<BackupData> CreateScheduledBackupVersionAsync(BackupSchedule schedule, ECUDevice ecu)
        {
            try
            {
                _logger?.LogInformation($"Creating scheduled backup version for ECU {ecu?.Name} based on schedule {schedule?.Id}", "BackupService");

                if (!ValidateInitialization())
                {
                    return null;
                }

                if (schedule == null)
                {
                    _logger?.LogError("Schedule is null", "BackupService");
                    BackupError?.Invoke(this, "Schedule is null");
                    return null;
                }

                if (ecu == null)
                {
                    _logger?.LogError("ECU is null", "BackupService");
                    BackupError?.Invoke(this, "ECU is null");
                    return null;
                }

                // Check if there are any existing backups for this ECU
                List<BackupData> allBackups = await GetAllBackupsAsync();
                List<BackupData> ecuBackups = allBackups.Where(b => b.ECUId == ecu.Id).ToList();

                // If there are no existing backups, create a new one
                if (ecuBackups.Count == 0)
                {
                    _logger?.LogInformation($"No existing backups found for ECU {ecu.Name}, creating a new backup", "BackupService");
                    return await CreateBackupAsync(
                        ecu,
                        $"Scheduled backup: {schedule.Name}",
                        schedule.Category,
                        schedule.Tags,
                        schedule.IncludeEEPROM,
                        schedule.IncludeMicrocontrollerCode,
                        schedule.IncludeParameters);
                }

                // Find the latest backup for this ECU
                BackupData latestBackup = ecuBackups.OrderByDescending(b => b.CreationTime).FirstOrDefault();

                // Check if we should create a new version or a new backup
                // Use the MaxBackupAge property from the schedule
                int maxBackupAge = schedule.MaxBackupAge;
                if (latestBackup.CreationTime.AddDays(maxBackupAge) < DateTime.Now)
                {
                    // If the latest backup is older than the maximum age, create a new backup
                    _logger?.LogInformation($"Latest backup for ECU {ecu.Name} is older than {maxBackupAge} days, creating a new backup", "BackupService");
                    return await CreateBackupAsync(
                        ecu,
                        $"Scheduled backup: {schedule.Name}",
                        schedule.Category,
                        schedule.Tags,
                        schedule.IncludeEEPROM,
                        schedule.IncludeMicrocontrollerCode,
                        schedule.IncludeParameters);
                }
                else
                {
                    // Create a new version of the latest backup
                    _logger?.LogInformation($"Creating new version of backup {latestBackup.Id} for ECU {ecu.Name}", "BackupService");
                    return await CreateBackupVersionAsync(
                        latestBackup,
                        ecu,
                        $"Scheduled version created by schedule: {schedule.Name}",
                        schedule.IncludeEEPROM,
                        schedule.IncludeMicrocontrollerCode,
                        schedule.IncludeParameters);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error creating scheduled backup version for ECU {ecu?.Name}", "BackupService", ex);
                BackupError?.Invoke(this, $"Scheduled backup version creation error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets all versions of a backup
        /// </summary>
        /// <param name="backupId">The ID of the backup</param>
        /// <returns>List of all versions of the backup</returns>
        public async Task<List<BackupData>> GetBackupVersionsAsync(string backupId)
        {
            try
            {
                _logger?.LogInformation($"Getting all versions of backup {backupId}", "BackupService");

                if (!ValidateInitialization())
                {
                    return new List<BackupData>();
                }

                if (string.IsNullOrEmpty(backupId))
                {
                    _logger?.LogError("Backup ID is null or empty", "BackupService");
                    BackupError?.Invoke(this, "Backup ID is null or empty");
                    return new List<BackupData>();
                }

                // Get all backups
                List<BackupData> allBackups = await GetAllBackupsAsync();

                // Find the backup
                BackupData backup = allBackups.FirstOrDefault(b => b.Id == backupId);
                if (backup == null)
                {
                    _logger?.LogWarning($"Backup with ID {backupId} not found", "BackupService");
                    return new List<BackupData>();
                }

                // Find the root backup
                string rootId = backup.RootBackupId ?? backup.Id;

                // Find all versions of the backup using the root ID
                List<BackupData> versions = allBackups.Where(b =>
                    b.Id == rootId ||
                    b.RootBackupId == rootId
                ).OrderBy(b => b.Version).ToList();

                _logger?.LogInformation($"Found {versions.Count} versions of backup {backupId}", "BackupService");
                return versions;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error getting versions of backup {backupId}", "BackupService", ex);
                BackupError?.Invoke(this, $"Error getting backup versions: {ex.Message}");
                return new List<BackupData>();
            }
        }

        /// <summary>
        /// Gets the version history of a backup as a tree structure
        /// </summary>
        /// <param name="backupId">The ID of the backup</param>
        /// <returns>The root backup with its version tree</returns>
        public async Task<BackupVersionTree> GetBackupVersionTreeAsync(string backupId)
        {
            try
            {
                _logger?.LogInformation($"Getting version tree for backup {backupId}", "BackupService");

                if (!ValidateInitialization())
                {
                    return null;
                }

                if (string.IsNullOrEmpty(backupId))
                {
                    _logger?.LogError("Backup ID is null or empty", "BackupService");
                    BackupError?.Invoke(this, "Backup ID is null or empty");
                    return null;
                }

                // Get all versions of the backup
                List<BackupData> versions = await GetBackupVersionsAsync(backupId);
                if (versions.Count == 0)
                {
                    _logger?.LogWarning($"No versions found for backup {backupId}", "BackupService");
                    return null;
                }

                // Create the version tree
                BackupVersionTree tree = new BackupVersionTree
                {
                    AllVersions = versions
                };

                // Find the root backup (version 1)
                tree.RootBackup = versions.FirstOrDefault(v => v.Version == 1);
                if (tree.RootBackup == null)
                {
                    _logger?.LogWarning($"Root backup not found for backup {backupId}", "BackupService");
                    return null;
                }

                // Find the latest version
                tree.LatestVersion = versions.OrderByDescending(v => v.Version).FirstOrDefault();

                // Build the tree structure
                Dictionary<string, BackupVersionNode> nodeMap = new Dictionary<string, BackupVersionNode>();

                // Create nodes for all versions
                foreach (var version in versions)
                {
                    var node = new BackupVersionNode
                    {
                        Backup = version,
                        Depth = 0
                    };
                    nodeMap[version.Id] = node;
                }

                // Connect parent-child relationships
                foreach (var version in versions)
                {
                    if (!string.IsNullOrEmpty(version.ParentBackupId) && nodeMap.ContainsKey(version.ParentBackupId))
                    {
                        var node = nodeMap[version.Id];
                        var parentNode = nodeMap[version.ParentBackupId];

                        node.Parent = parentNode;
                        parentNode.Children.Add(node);
                    }
                }

                // Calculate depths
                CalculateNodeDepths(nodeMap[tree.RootBackup.Id], 0);

                // Add all nodes to the tree
                tree.VersionNodes = nodeMap.Values.ToList();

                _logger?.LogInformation($"Created version tree with {versions.Count} nodes for backup {backupId}", "BackupService");
                return tree;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error getting version tree for backup {backupId}", "BackupService", ex);
                BackupError?.Invoke(this, $"Error getting backup version tree: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets the latest version of a backup
        /// </summary>
        /// <param name="backupId">The ID of any version of the backup</param>
        /// <returns>The latest version of the backup</returns>
        public async Task<BackupData> GetLatestBackupVersionAsync(string backupId)
        {
            try
            {
                _logger?.LogInformation($"Getting latest version of backup {backupId}", "BackupService");

                if (!ValidateInitialization())
                {
                    return null;
                }

                if (string.IsNullOrEmpty(backupId))
                {
                    _logger?.LogError("Backup ID is null or empty", "BackupService");
                    BackupError?.Invoke(this, "Backup ID is null or empty");
                    return null;
                }

                // Get all versions of the backup
                List<BackupData> versions = await GetBackupVersionsAsync(backupId);
                if (versions.Count == 0)
                {
                    _logger?.LogWarning($"No versions found for backup {backupId}", "BackupService");
                    return null;
                }

                // Find the latest version
                BackupData latestVersion = versions.OrderByDescending(v => v.Version).FirstOrDefault();

                _logger?.LogInformation($"Found latest version {latestVersion?.Version} for backup {backupId}", "BackupService");
                return latestVersion;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error getting latest version for backup {backupId}", "BackupService", ex);
                BackupError?.Invoke(this, $"Error getting latest backup version: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Merges changes from one backup version into another
        /// </summary>
        /// <param name="sourceBackupId">The source backup ID</param>
        /// <param name="targetBackupId">The target backup ID</param>
        /// <param name="mergeOptions">Options for the merge operation</param>
        /// <returns>The merged backup data</returns>
        public async Task<BackupData> MergeBackupVersionsAsync(string sourceBackupId, string targetBackupId, BackupMergeOptions mergeOptions)
        {
            try
            {
                _logger?.LogInformation($"Merging backup {sourceBackupId} into {targetBackupId}", "BackupService");

                if (!ValidateInitialization())
                {
                    return null;
                }

                if (string.IsNullOrEmpty(sourceBackupId) || string.IsNullOrEmpty(targetBackupId))
                {
                    _logger?.LogError("Source or target backup ID is null or empty", "BackupService");
                    BackupError?.Invoke(this, "Source or target backup ID is null or empty");
                    return null;
                }

                if (mergeOptions == null)
                {
                    mergeOptions = new BackupMergeOptions();
                }

                // Get the source and target backups
                List<BackupData> allBackups = await GetAllBackupsAsync();
                BackupData sourceBackup = allBackups.FirstOrDefault(b => b.Id == sourceBackupId);
                BackupData targetBackup = allBackups.FirstOrDefault(b => b.Id == targetBackupId);

                if (sourceBackup == null)
                {
                    _logger?.LogError($"Source backup {sourceBackupId} not found", "BackupService");
                    BackupError?.Invoke(this, $"Source backup {sourceBackupId} not found");
                    return null;
                }

                if (targetBackup == null)
                {
                    _logger?.LogError($"Target backup {targetBackupId} not found", "BackupService");
                    BackupError?.Invoke(this, $"Target backup {targetBackupId} not found");
                    return null;
                }

                // Create a new backup for the merged result if requested
                BackupData mergedBackup;
                if (mergeOptions.CreateNewVersion)
                {
                    // Find the ECU
                    var ecuDevices = await _ecuService.ScanForECUsAsync();
                    var ecu = ecuDevices.FirstOrDefault(e => e.Id == targetBackup.ECUId);

                    if (ecu == null)
                    {
                        _logger?.LogError($"ECU {targetBackup.ECUId} not found", "BackupService");
                        BackupError?.Invoke(this, $"ECU {targetBackup.ECUId} not found");
                        return null;
                    }

                    // Create a new version of the target backup
                    mergedBackup = await CreateBackupVersionAsync(
                        targetBackup,
                        ecu,
                        mergeOptions.MergeNotes,
                        false, // Don't read EEPROM data yet
                        false, // Don't read microcontroller code yet
                        false  // Don't read parameters yet
                    );

                    if (mergedBackup == null)
                    {
                        _logger?.LogError("Failed to create new version for merged backup", "BackupService");
                        BackupError?.Invoke(this, "Failed to create new version for merged backup");
                        return null;
                    }

                    // Update the description
                    mergedBackup.Description = mergeOptions.MergeDescription;
                }
                else
                {
                    // Use the target backup as the base for merging
                    mergedBackup = targetBackup;
                }

                // Merge EEPROM data if requested
                if (mergeOptions.MergeEEPROM && sourceBackup.EEPROMData != null)
                {
                    mergedBackup.EEPROMData = sourceBackup.EEPROMData;
                    _logger?.LogInformation("Merged EEPROM data", "BackupService");
                }

                // Merge microcontroller code if requested
                if (mergeOptions.MergeMicrocontrollerCode && sourceBackup.MicrocontrollerCode != null)
                {
                    mergedBackup.MicrocontrollerCode = sourceBackup.MicrocontrollerCode;
                    _logger?.LogInformation("Merged microcontroller code", "BackupService");
                }

                // Merge parameters if requested
                if (mergeOptions.MergeParameters && sourceBackup.Parameters != null)
                {
                    if (mergeOptions.ParametersToMerge.Count > 0)
                    {
                        // Merge only specified parameters
                        foreach (var paramName in mergeOptions.ParametersToMerge)
                        {
                            if (sourceBackup.Parameters.ContainsKey(paramName))
                            {
                                if (mergedBackup.Parameters == null)
                                {
                                    mergedBackup.Parameters = new Dictionary<string, object>();
                                }

                                mergedBackup.Parameters[paramName] = sourceBackup.Parameters[paramName];
                                _logger?.LogInformation($"Merged parameter {paramName}", "BackupService");
                            }
                        }
                    }
                    else
                    {
                        // Merge all parameters
                        if (mergedBackup.Parameters == null)
                        {
                            mergedBackup.Parameters = new Dictionary<string, object>();
                        }

                        foreach (var param in sourceBackup.Parameters)
                        {
                            mergedBackup.Parameters[param.Key] = param.Value;
                        }

                        _logger?.LogInformation($"Merged {sourceBackup.Parameters.Count} parameters", "BackupService");
                    }
                }

                // Merge tags if requested
                if (mergeOptions.MergeTags && sourceBackup.Tags != null)
                {
                    if (mergedBackup.Tags == null)
                    {
                        mergedBackup.Tags = new List<string>();
                    }

                    foreach (var tag in sourceBackup.Tags)
                    {
                        if (!mergedBackup.Tags.Contains(tag))
                        {
                            mergedBackup.Tags.Add(tag);
                        }
                    }

                    _logger?.LogInformation("Merged tags", "BackupService");
                }

                // Calculate a new checksum
                string dataToHash = mergedBackup.ECUId + mergedBackup.CreationTime.ToString() +
                                   (mergedBackup.EEPROMData != null ? DataConversionHelper.BytesToHexString(mergedBackup.EEPROMData) : string.Empty) +
                                   (mergedBackup.MicrocontrollerCode != null ? DataConversionHelper.BytesToHexString(mergedBackup.MicrocontrollerCode) : string.Empty);
                mergedBackup.Checksum = DataConversionHelper.CalculateSHA256Checksum(System.Text.Encoding.UTF8.GetBytes(dataToHash));

                // Save the merged backup
                bool saved = await SaveBackupToFileAsync(mergedBackup, mergedBackup.FilePath);
                if (!saved)
                {
                    _logger?.LogError("Failed to save merged backup", "BackupService");
                    BackupError?.Invoke(this, "Failed to save merged backup");
                    return null;
                }

                _logger?.LogInformation($"Successfully merged backup {sourceBackupId} into {targetBackupId}", "BackupService");
                return mergedBackup;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error merging backups {sourceBackupId} and {targetBackupId}", "BackupService", ex);
                BackupError?.Invoke(this, $"Error merging backups: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Compares two backups
        /// </summary>
        /// <param name="backup1">The first backup</param>
        /// <param name="backup2">The second backup</param>
        /// <returns>Dictionary of differences between the backups</returns>
        public async Task<Dictionary<string, object>> CompareBackupsAsync(BackupData backup1, BackupData backup2)
        {
            try
            {
                _logger?.LogInformation($"Comparing backups for ECUs {backup1?.ECUName} and {backup2?.ECUName}", "BackupService");

                if (!ValidateInitialization())
                {
                    return new Dictionary<string, object>();
                }

                if (backup1 == null || backup2 == null)
                {
                    _logger?.LogError("One or both backups are null", "BackupService");
                    BackupError?.Invoke(this, "One or both backups are null");
                    return new Dictionary<string, object>();
                }

                Dictionary<string, object> differences = new Dictionary<string, object>();

                // Compare basic properties
                if (backup1.ECUId != backup2.ECUId)
                {
                    differences.Add("ECUId", new { Backup1 = backup1.ECUId, Backup2 = backup2.ECUId });
                }

                if (backup1.ECUName != backup2.ECUName)
                {
                    differences.Add("ECUName", new { Backup1 = backup1.ECUName, Backup2 = backup2.ECUName });
                }

                if (backup1.ECUSerialNumber != backup2.ECUSerialNumber)
                {
                    differences.Add("ECUSerialNumber", new { Backup1 = backup1.ECUSerialNumber, Backup2 = backup2.ECUSerialNumber });
                }

                if (backup1.ECUHardwareVersion != backup2.ECUHardwareVersion)
                {
                    differences.Add("ECUHardwareVersion", new { Backup1 = backup1.ECUHardwareVersion, Backup2 = backup2.ECUHardwareVersion });
                }

                if (backup1.ECUSoftwareVersion != backup2.ECUSoftwareVersion)
                {
                    differences.Add("ECUSoftwareVersion", new { Backup1 = backup1.ECUSoftwareVersion, Backup2 = backup2.ECUSoftwareVersion });
                }

                // Compare creation times
                if (backup1.CreationTime != backup2.CreationTime)
                {
                    differences.Add("CreationTime", new { Backup1 = backup1.CreationTime, Backup2 = backup2.CreationTime });
                }

                // Compare EEPROM data
                if (!CompareByteArrays(backup1.EEPROMData, backup2.EEPROMData))
                {
                    differences.Add("EEPROMData", new {
                        Backup1Length = backup1.EEPROMData?.Length ?? 0,
                        Backup2Length = backup2.EEPROMData?.Length ?? 0,
                        Message = "EEPROM data differs"
                    });
                }

                // Compare microcontroller code
                if (!CompareByteArrays(backup1.MicrocontrollerCode, backup2.MicrocontrollerCode))
                {
                    differences.Add("MicrocontrollerCode", new {
                        Backup1Length = backup1.MicrocontrollerCode?.Length ?? 0,
                        Backup2Length = backup2.MicrocontrollerCode?.Length ?? 0,
                        Message = "Microcontroller code differs"
                    });
                }

                // Compare parameters
                if (backup1.Parameters != null && backup2.Parameters != null)
                {
                    // Get all unique parameter keys
                    HashSet<string> allKeys = new HashSet<string>(backup1.Parameters.Keys.Union(backup2.Parameters.Keys));

                    foreach (string key in allKeys)
                    {
                        bool backup1HasKey = backup1.Parameters.ContainsKey(key);
                        bool backup2HasKey = backup2.Parameters.ContainsKey(key);

                        if (!backup1HasKey || !backup2HasKey)
                        {
                            differences.Add($"Parameter_{key}", new {
                                ExistsInBackup1 = backup1HasKey,
                                ExistsInBackup2 = backup2HasKey,
                                Backup1Value = backup1HasKey ? backup1.Parameters[key] : null,
                                Backup2Value = backup2HasKey ? backup2.Parameters[key] : null
                            });
                        }
                        else if (!Equals(backup1.Parameters[key], backup2.Parameters[key]))
                        {
                            differences.Add($"Parameter_{key}", new {
                                Backup1Value = backup1.Parameters[key],
                                Backup2Value = backup2.Parameters[key]
                            });
                        }
                    }
                }
                else if (backup1.Parameters != null || backup2.Parameters != null)
                {
                    differences.Add("Parameters", new {
                        Backup1HasParameters = backup1.Parameters != null,
                        Backup2HasParameters = backup2.Parameters != null,
                        Message = "One backup has parameters while the other doesn't"
                    });
                }

                _logger?.LogInformation($"Found {differences.Count} differences between backups", "BackupService");
                return differences;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error comparing backups", "BackupService", ex);
                BackupError?.Invoke(this, $"Backup comparison error: {ex.Message}");
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// Compares two byte arrays for equality
        /// </summary>
        /// <param name="array1">The first array</param>
        /// <param name="array2">The second array</param>
        /// <returns>True if the arrays are equal, false otherwise</returns>
        private bool CompareByteArrays(byte[] array1, byte[] array2)
        {
            // If both are null, they're equal
            if (array1 == null && array2 == null)
                return true;

            // If only one is null, they're not equal
            if (array1 == null || array2 == null)
                return false;

            // If lengths differ, they're not equal
            if (array1.Length != array2.Length)
                return false;

            // Compare each byte
            for (int i = 0; i < array1.Length; i++)
            {
                if (array1[i] != array2[i])
                    return false;
            }

            return true;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Exports a backup to binary format
        /// </summary>
        /// <param name="backup">The backup to export</param>
        /// <param name="exportPath">The path to export to</param>
        /// <returns>True if export is successful, false otherwise</returns>
        private async Task<bool> ExportToBinaryFormat(BackupData backup, string exportPath)
        {
            try
            {
                _logger?.LogInformation($"Exporting backup {backup.Id} to binary format", "BackupService");

                // Serialize the backup data to JSON first
                var options = new JsonSerializerOptions
                {
                    WriteIndented = false
                };

                string jsonString = JsonSerializer.Serialize(backup, options);
                byte[] jsonBytes = Encoding.UTF8.GetBytes(jsonString);

                // Apply compression if enabled
                if (_useCompression)
                {
                    _logger?.LogInformation("Compressing backup data", "BackupService");
                    jsonBytes = CompressData(jsonBytes);
                }

                // Apply encryption if enabled
                if (_useEncryption)
                {
                    _logger?.LogInformation("Encrypting backup data", "BackupService");
                    jsonBytes = EncryptData(jsonBytes, _encryptionKey);
                }

                // Write the data to the file
                await File.WriteAllBytesAsync(exportPath, jsonBytes);

                _logger?.LogInformation($"Backup exported to binary format at {exportPath} ({jsonBytes.Length} bytes)", "BackupService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error exporting backup to binary format", "BackupService", ex);
                BackupError?.Invoke(this, $"Binary export error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Exports a backup to JSON format
        /// </summary>
        /// <param name="backup">The backup to export</param>
        /// <param name="exportPath">The path to export to</param>
        /// <returns>True if export is successful, false otherwise</returns>
        private async Task<bool> ExportToJsonFormat(BackupData backup, string exportPath)
        {
            try
            {
                _logger?.LogInformation($"Exporting backup {backup.Id} to JSON format", "BackupService");

                // Create a copy of the backup with base64-encoded binary data
                var exportBackup = new
                {
                    Metadata = new
                    {
                        Id = backup.Id,
                        ECUId = backup.ECUId,
                        ECUName = backup.ECUName,
                        ECUSerialNumber = backup.ECUSerialNumber,
                        ECUHardwareVersion = backup.ECUHardwareVersion,
                        ECUSoftwareVersion = backup.ECUSoftwareVersion,
                        Description = backup.Description,
                        CreationTime = backup.CreationTime,
                        LastModifiedTime = backup.LastModifiedTime,
                        CreatedBy = backup.CreatedBy,
                        LastModifiedBy = backup.LastModifiedBy,
                        Category = backup.Category,
                        Tags = backup.Tags,
                        Version = backup.Version,
                        ParentBackupId = backup.ParentBackupId,
                        RootBackupId = backup.RootBackupId,
                        VersionNotes = backup.VersionNotes,
                        VersionCreationTime = backup.VersionCreationTime,
                        IsLatestVersion = backup.IsLatestVersion,
                        Checksum = backup.Checksum
                    },
                    EEPROMData = backup.EEPROMData != null ? Convert.ToBase64String(backup.EEPROMData) : null,
                    MicrocontrollerCode = backup.MicrocontrollerCode != null ? Convert.ToBase64String(backup.MicrocontrollerCode) : null,
                    Parameters = backup.Parameters
                };

                // Serialize to JSON with indentation
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };

                string jsonString = JsonSerializer.Serialize(exportBackup, options);

                // Write the JSON to the file
                await File.WriteAllTextAsync(exportPath, jsonString);

                _logger?.LogInformation($"Backup exported to JSON format at {exportPath}", "BackupService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error exporting backup to JSON format", "BackupService", ex);
                BackupError?.Invoke(this, $"JSON export error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Exports a backup to XML format
        /// </summary>
        /// <param name="backup">The backup to export</param>
        /// <param name="exportPath">The path to export to</param>
        /// <returns>True if export is successful, false otherwise</returns>
        private async Task<bool> ExportToXmlFormat(BackupData backup, string exportPath)
        {
            try
            {
                _logger?.LogInformation($"Exporting backup {backup.Id} to XML format", "BackupService");

                // Create a serializable object with base64-encoded binary data
                var exportBackup = new BackupDataXml
                {
                    Id = backup.Id,
                    ECUId = backup.ECUId,
                    ECUName = backup.ECUName,
                    ECUSerialNumber = backup.ECUSerialNumber,
                    ECUHardwareVersion = backup.ECUHardwareVersion,
                    ECUSoftwareVersion = backup.ECUSoftwareVersion,
                    Description = backup.Description,
                    CreationTime = backup.CreationTime,
                    LastModifiedTime = backup.LastModifiedTime,
                    CreatedBy = backup.CreatedBy,
                    LastModifiedBy = backup.LastModifiedBy,
                    Category = backup.Category,
                    Tags = backup.Tags?.ToArray() ?? Array.Empty<string>(),
                    Version = backup.Version,
                    ParentBackupId = backup.ParentBackupId,
                    RootBackupId = backup.RootBackupId,
                    VersionNotes = backup.VersionNotes,
                    VersionCreationTime = backup.VersionCreationTime,
                    IsLatestVersion = backup.IsLatestVersion,
                    Checksum = backup.Checksum,
                    EEPROMData = backup.EEPROMData != null ? Convert.ToBase64String(backup.EEPROMData) : null,
                    MicrocontrollerCode = backup.MicrocontrollerCode != null ? Convert.ToBase64String(backup.MicrocontrollerCode) : null,
                    Parameters = ConvertParametersToXmlFriendly(backup.Parameters)
                };

                // Create XML serializer
                var serializer = new XmlSerializer(typeof(BackupDataXml));

                // Create XML writer settings
                var settings = new XmlWriterSettings
                {
                    Indent = true,
                    IndentChars = "  ",
                    NewLineChars = Environment.NewLine,
                    NewLineHandling = NewLineHandling.Replace
                };

                // Write to file
                using (var stream = new FileStream(exportPath, FileMode.Create))
                using (var writer = XmlWriter.Create(stream, settings))
                {
                    serializer.Serialize(writer, exportBackup);
                }

                _logger?.LogInformation($"Backup exported to XML format at {exportPath}", "BackupService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error exporting backup to XML format", "BackupService", ex);
                BackupError?.Invoke(this, $"XML export error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Exports a backup to CSV format (parameters only)
        /// </summary>
        /// <param name="backup">The backup to export</param>
        /// <param name="exportPath">The path to export to</param>
        /// <returns>True if export is successful, false otherwise</returns>
        private async Task<bool> ExportToCsvFormat(BackupData backup, string exportPath)
        {
            try
            {
                _logger?.LogInformation($"Exporting backup {backup.Id} parameters to CSV format", "BackupService");

                if (backup.Parameters == null || backup.Parameters.Count == 0)
                {
                    _logger?.LogWarning("No parameters to export to CSV", "BackupService");
                    BackupError?.Invoke(this, "No parameters to export to CSV");
                    return false;
                }

                // Create CSV content
                var csvBuilder = new StringBuilder();

                // Add header
                csvBuilder.AppendLine("Parameter,Value,Type");

                // Add parameters
                foreach (var param in backup.Parameters)
                {
                    string paramName = param.Key;
                    string paramValue = param.Value?.ToString() ?? "null";
                    string paramType = param.Value?.GetType().Name ?? "null";

                    // Escape values that contain commas
                    if (paramName.Contains(","))
                    {
                        paramName = $"\"{paramName}\"";
                    }
                    if (paramValue.Contains(","))
                    {
                        paramValue = $"\"{paramValue}\"";
                    }

                    csvBuilder.AppendLine($"{paramName},{paramValue},{paramType}");
                }

                // Write to file
                await File.WriteAllTextAsync(exportPath, csvBuilder.ToString());

                _logger?.LogInformation($"Backup parameters exported to CSV format at {exportPath}", "BackupService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error exporting backup parameters to CSV format", "BackupService", ex);
                BackupError?.Invoke(this, $"CSV export error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Exports a backup to HEX format (binary data only)
        /// </summary>
        /// <param name="backup">The backup to export</param>
        /// <param name="exportPath">The path to export to</param>
        /// <returns>True if export is successful, false otherwise</returns>
        private async Task<bool> ExportToHexFormat(BackupData backup, string exportPath)
        {
            try
            {
                _logger?.LogInformation($"Exporting backup {backup.Id} binary data to HEX format", "BackupService");

                // Create directory for HEX files
                string directory = Path.GetDirectoryName(exportPath);
                string fileNameWithoutExt = Path.GetFileNameWithoutExtension(exportPath);
                string eepromPath = Path.Combine(directory, $"{fileNameWithoutExt}_EEPROM.hex");
                string mcuPath = Path.Combine(directory, $"{fileNameWithoutExt}_MCU.hex");

                // Export EEPROM data if available
                if (backup.EEPROMData != null && backup.EEPROMData.Length > 0)
                {
                    string eepromHex = DataConversionHelper.BytesToHexString(backup.EEPROMData);
                    await File.WriteAllTextAsync(eepromPath, eepromHex);
                    _logger?.LogInformation($"EEPROM data exported to HEX format at {eepromPath}", "BackupService");
                }

                // Export microcontroller code if available
                if (backup.MicrocontrollerCode != null && backup.MicrocontrollerCode.Length > 0)
                {
                    string mcuHex = DataConversionHelper.BytesToHexString(backup.MicrocontrollerCode);
                    await File.WriteAllTextAsync(mcuPath, mcuHex);
                    _logger?.LogInformation($"Microcontroller code exported to HEX format at {mcuPath}", "BackupService");
                }

                // Create a metadata file
                string metadataPath = Path.Combine(directory, $"{fileNameWithoutExt}_metadata.json");
                var metadata = new
                {
                    Id = backup.Id,
                    ECUId = backup.ECUId,
                    ECUName = backup.ECUName,
                    ECUSerialNumber = backup.ECUSerialNumber,
                    ECUHardwareVersion = backup.ECUHardwareVersion,
                    ECUSoftwareVersion = backup.ECUSoftwareVersion,
                    Description = backup.Description,
                    CreationTime = backup.CreationTime,
                    LastModifiedTime = backup.LastModifiedTime,
                    CreatedBy = backup.CreatedBy,
                    LastModifiedBy = backup.LastModifiedBy,
                    Category = backup.Category,
                    Tags = backup.Tags,
                    Version = backup.Version,
                    Checksum = backup.Checksum,
                    EEPROMSize = backup.EEPROMData?.Length ?? 0,
                    MicrocontrollerCodeSize = backup.MicrocontrollerCode?.Length ?? 0
                };

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };

                string metadataJson = JsonSerializer.Serialize(metadata, options);
                await File.WriteAllTextAsync(metadataPath, metadataJson);

                _logger?.LogInformation($"Backup metadata exported to {metadataPath}", "BackupService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error exporting backup to HEX format", "BackupService", ex);
                BackupError?.Invoke(this, $"HEX export error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Exports a backup to ZIP format
        /// </summary>
        /// <param name="backup">The backup to export</param>
        /// <param name="exportPath">The path to export to</param>
        /// <returns>True if export is successful, false otherwise</returns>
        private async Task<bool> ExportToZipFormat(BackupData backup, string exportPath)
        {
            try
            {
                _logger?.LogInformation($"Exporting backup {backup.Id} to ZIP format", "BackupService");

                // Create a temporary directory for files to be zipped
                string tempDir = Path.Combine(Path.GetTempPath(), $"Backup_{backup.Id}_{DateTime.Now:yyyyMMddHHmmss}");
                Directory.CreateDirectory(tempDir);

                try
                {
                    // Export metadata to JSON
                    string metadataPath = Path.Combine(tempDir, "metadata.json");
                    await ExportToJsonFormat(backup, metadataPath);

                    // Export EEPROM data if available
                    if (backup.EEPROMData != null && backup.EEPROMData.Length > 0)
                    {
                        string eepromPath = Path.Combine(tempDir, "eeprom.bin");
                        await File.WriteAllBytesAsync(eepromPath, backup.EEPROMData);
                    }

                    // Export microcontroller code if available
                    if (backup.MicrocontrollerCode != null && backup.MicrocontrollerCode.Length > 0)
                    {
                        string mcuPath = Path.Combine(tempDir, "mcu.bin");
                        await File.WriteAllBytesAsync(mcuPath, backup.MicrocontrollerCode);
                    }

                    // Export parameters if available
                    if (backup.Parameters != null && backup.Parameters.Count > 0)
                    {
                        string paramsPath = Path.Combine(tempDir, "parameters.json");
                        string paramsJson = JsonSerializer.Serialize(backup.Parameters, new JsonSerializerOptions { WriteIndented = true });
                        await File.WriteAllTextAsync(paramsPath, paramsJson);

                        // Also export parameters as CSV for convenience
                        string paramsCsvPath = Path.Combine(tempDir, "parameters.csv");
                        await ExportToCsvFormat(backup, paramsCsvPath);
                    }

                    // Create a readme file
                    string readmePath = Path.Combine(tempDir, "README.txt");
                    string readmeContent = $"Backup ID: {backup.Id}\r\n" +
                                          $"ECU: {backup.ECUName} (SN: {backup.ECUSerialNumber})\r\n" +
                                          $"Created: {backup.CreationTime}\r\n" +
                                          $"Created by: {backup.CreatedBy}\r\n" +
                                          $"Description: {backup.Description}\r\n" +
                                          $"Category: {backup.Category}\r\n" +
                                          $"Tags: {string.Join(", ", backup.Tags ?? new List<string>())}\r\n" +
                                          $"Version: {backup.Version}\r\n" +
                                          $"Checksum: {backup.Checksum}\r\n\r\n" +
                                          $"This backup was exported on {DateTime.Now} using VolvoFlashWR.";
                    await File.WriteAllTextAsync(readmePath, readmeContent);

                    // Create the ZIP file
                    if (File.Exists(exportPath))
                    {
                        File.Delete(exportPath);
                    }

                    ZipFile.CreateFromDirectory(tempDir, exportPath);

                    _logger?.LogInformation($"Backup exported to ZIP format at {exportPath}", "BackupService");
                    return true;
                }
                finally
                {
                    // Clean up temporary directory
                    if (Directory.Exists(tempDir))
                    {
                        Directory.Delete(tempDir, true);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error exporting backup to ZIP format", "BackupService", ex);
                BackupError?.Invoke(this, $"ZIP export error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Converts parameters dictionary to XML-friendly format
        /// </summary>
        /// <param name="parameters">The parameters dictionary</param>
        /// <returns>Array of parameter entries</returns>
        private ParameterEntry[] ConvertParametersToXmlFriendly(Dictionary<string, object> parameters)
        {
            if (parameters == null || parameters.Count == 0)
            {
                return Array.Empty<ParameterEntry>();
            }

            var result = new List<ParameterEntry>();
            foreach (var param in parameters)
            {
                result.Add(new ParameterEntry
                {
                    Name = param.Key,
                    Value = param.Value?.ToString() ?? string.Empty,
                    Type = param.Value?.GetType().Name ?? "null"
                });
            }

            return result.ToArray();
        }

        /// <summary>
        /// Validates that the service is initialized
        /// </summary>
        /// <returns>True if the service is initialized, false otherwise</returns>
        private bool ValidateInitialization()
        {
            if (!_isInitialized)
            {
                _logger?.LogError("Backup service is not initialized", "BackupService");
                BackupError?.Invoke(this, "Backup service is not initialized");
                return false;
            }
            return true;
        }

        /// <summary>
        /// Compresses data using GZip compression
        /// </summary>
        /// <param name="data">The data to compress</param>
        /// <returns>The compressed data</returns>
        private byte[] CompressData(byte[] data)
        {
            if (data == null || data.Length == 0)
                return data;

            using (var memoryStream = new MemoryStream())
            {
                using (var gzipStream = new GZipStream(memoryStream, CompressionLevel.Optimal))
                {
                    gzipStream.Write(data, 0, data.Length);
                }
                return memoryStream.ToArray();
            }
        }

        /// <summary>
        /// Decompresses data using GZip decompression
        /// </summary>
        /// <param name="data">The data to decompress</param>
        /// <returns>The decompressed data</returns>
        private byte[] DecompressData(byte[] data)
        {
            if (data == null || data.Length == 0)
                return data;

            using (var inputStream = new MemoryStream(data))
            using (var outputStream = new MemoryStream())
            using (var gzipStream = new GZipStream(inputStream, CompressionMode.Decompress))
            {
                gzipStream.CopyTo(outputStream);
                return outputStream.ToArray();
            }
        }

        /// <summary>
        /// Encrypts data using AES encryption
        /// </summary>
        /// <param name="data">The data to encrypt</param>
        /// <param name="key">The encryption key</param>
        /// <returns>The encrypted data</returns>
        private byte[] EncryptData(byte[] data, string key)
        {
            if (data == null || data.Length == 0)
                return data;

            if (string.IsNullOrEmpty(key))
                throw new ArgumentNullException(nameof(key));

            // Create an Aes object with the specified key and IV
            using (Aes aes = Aes.Create())
            {
                // Use the key to derive both the key and IV
                byte[] keyBytes = Encoding.UTF8.GetBytes(key);
                byte[] derivedKey = new byte[32]; // 256 bits
                byte[] iv = new byte[16]; // 128 bits

                // Simple key derivation - in a real implementation, use a proper key derivation function
                Array.Copy(keyBytes, 0, derivedKey, 0, Math.Min(keyBytes.Length, derivedKey.Length));
                Array.Copy(keyBytes, 0, iv, 0, Math.Min(keyBytes.Length, iv.Length));

                aes.Key = derivedKey;
                aes.IV = iv;

                // Create an encryptor to perform the stream transform
                ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                // Create the streams used for encryption
                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        csEncrypt.Write(data, 0, data.Length);
                        csEncrypt.FlushFinalBlock();
                    }
                    return msEncrypt.ToArray();
                }
            }
        }

        /// <summary>
        /// Decrypts data using AES decryption
        /// </summary>
        /// <param name="data">The data to decrypt</param>
        /// <param name="key">The decryption key</param>
        /// <returns>The decrypted data</returns>
        private byte[] DecryptData(byte[] data, string key)
        {
            if (data == null || data.Length == 0)
                return data;

            if (string.IsNullOrEmpty(key))
                throw new ArgumentNullException(nameof(key));

            // Create an Aes object with the specified key and IV
            using (Aes aes = Aes.Create())
            {
                // Use the key to derive both the key and IV (same as in encryption)
                byte[] keyBytes = Encoding.UTF8.GetBytes(key);
                byte[] derivedKey = new byte[32]; // 256 bits
                byte[] iv = new byte[16]; // 128 bits

                // Simple key derivation - in a real implementation, use a proper key derivation function
                Array.Copy(keyBytes, 0, derivedKey, 0, Math.Min(keyBytes.Length, derivedKey.Length));
                Array.Copy(keyBytes, 0, iv, 0, Math.Min(keyBytes.Length, iv.Length));

                aes.Key = derivedKey;
                aes.IV = iv;

                // Create a decryptor to perform the stream transform
                ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                // Create the streams used for decryption
                using (MemoryStream msDecrypt = new MemoryStream(data))
                {
                    using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    {
                        using (MemoryStream resultStream = new MemoryStream())
                        {
                            csDecrypt.CopyTo(resultStream);
                            return resultStream.ToArray();
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Generates a random encryption key
        /// </summary>
        /// <returns>A random encryption key</returns>
        private string GenerateEncryptionKey()
        {
            // Generate a random 256-bit key
            byte[] keyBytes = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(keyBytes);
            }
            return Convert.ToBase64String(keyBytes);
        }

        /// <summary>
        /// Recursively calculates the depth of each node in the version tree
        /// </summary>
        /// <param name="node">The current node</param>
        /// <param name="depth">The current depth</param>
        private void CalculateNodeDepths(BackupVersionNode node, int depth)
        {
            node.Depth = depth;
            foreach (var child in node.Children)
            {
                CalculateNodeDepths(child, depth + 1);
            }
        }

        #endregion
    }
}
