Log started at 7/16/2025 3:28:48 PM
2025-07-16 15:28:48.600 [Information] LoggingService: Logging service initialized
2025-07-16 15:28:48.614 [Information] App: Starting integrated application initialization
2025-07-16 15:28:48.615 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-16 15:28:48.618 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-16 15:28:48.619 [Information] IntegratedStartupService: Setting up application environment
2025-07-16 15:28:48.619 [Information] IntegratedStartupService: Application environment setup completed
2025-07-16 15:28:48.621 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-16 15:28:48.623 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-16 15:28:48.625 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-16 15:28:48.628 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-16 15:28:48.634 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.635 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.636 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.637 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-16 15:28:48.639 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.640 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.641 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.642 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 15:28:48.645 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.647 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.648 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.648 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 15:28:48.650 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.651 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.652 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.652 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 15:28:48.654 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.655 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.656 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.656 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 15:28:48.658 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.659 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.660 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.660 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-16 15:28:48.662 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.663 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.664 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.665 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-16 15:28:48.667 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-16 15:28:48.669 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-16 15:28:48.669 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-16 15:28:48.670 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-16 15:28:48.671 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-16 15:28:48.671 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-16 15:28:48.671 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-16 15:28:48.671 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-16 15:28:48.673 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-16 15:28:48.674 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-16 15:28:48.675 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-16 15:28:48.675 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 15:28:48.675 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 15:28:48.675 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 15:28:48.675 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 15:28:48.676 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-16 15:28:48.676 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-16 15:28:48.681 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-16 15:28:48.682 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-16 15:28:48.682 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-16 15:28:48.689 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-16 15:28:48.689 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-16 15:28:48.690 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-16 15:28:48.691 [Information] LibraryExtractor: Starting library extraction process
2025-07-16 15:28:48.694 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-16 15:28:48.698 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-16 15:28:48.700 [Information] LibraryExtractor: Copying system libraries
2025-07-16 15:28:48.705 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-16 15:28:48.710 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-16 15:32:19.776 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 15:36:14.671 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 15:40:38.776 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 15:44:31.390 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 15:48:01.176 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-math-l1-1-0.dll
2025-07-16 08:50:05.056 [Information] LoggingService: Logging service initialized
2025-07-16 08:50:05.067 [Information] App: Starting integrated application initialization
2025-07-16 08:50:05.068 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-16 08:50:05.069 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-16 08:50:05.070 [Information] IntegratedStartupService: Setting up application environment
2025-07-16 08:50:05.071 [Information] IntegratedStartupService: Application environment setup completed
2025-07-16 08:50:05.072 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-16 08:50:05.073 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-16 08:50:05.074 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-16 08:50:05.077 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-16 08:50:05.154 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-16 08:50:05.271 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-16 08:50:05.304 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-16 08:50:05.321 [Information] VCRedistBundler: Copied api-ms-win-crt-runtime-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 08:50:05.348 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-16 08:50:05.364 [Information] VCRedistBundler: Copied api-ms-win-crt-heap-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 08:50:05.390 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-16 08:50:05.406 [Information] VCRedistBundler: Copied api-ms-win-crt-string-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-string-l1-1-0.dll
2025-07-16 08:50:05.434 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-16 08:50:05.450 [Information] VCRedistBundler: Copied api-ms-win-crt-stdio-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 08:50:05.478 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-16 08:50:05.495 [Information] VCRedistBundler: Copied api-ms-win-crt-math-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-math-l1-1-0.dll
2025-07-16 08:50:05.522 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-16 08:50:05.538 [Information] VCRedistBundler: Copied api-ms-win-crt-locale-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-locale-l1-1-0.dll
2025-07-16 08:50:05.540 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-16 08:50:05.541 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-16 08:50:05.541 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-16 08:50:05.547 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-16 08:50:05.548 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-16 08:50:05.552 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-16 08:50:05.552 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-16 08:50:05.553 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-16 08:50:05.565 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-16 08:50:05.565 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-16 08:50:05.571 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-16 08:50:05.572 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 08:50:05.576 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 08:50:05.576 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 08:50:05.581 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 08:50:05.581 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 08:50:05.585 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 08:50:05.585 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 08:50:05.589 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 08:50:05.590 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-16 08:50:05.593 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-math-l1-1-0.dll
2025-07-16 08:50:05.594 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-16 08:50:05.598 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-locale-l1-1-0.dll
2025-07-16 08:50:05.600 [Information] VCRedistBundler: VC++ Redistributable verification: 10/11 (90.9%) required libraries found
2025-07-16 08:50:05.601 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: True
2025-07-16 08:50:05.603 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 10 available, 1 missing
2025-07-16 08:50:05.603 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64))
2025-07-16 08:50:05.604 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-16 08:50:05.605 [Information] LibraryExtractor: Starting library extraction process
2025-07-16 08:50:05.607 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-16 08:50:05.608 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-16 08:50:05.610 [Information] LibraryExtractor: Copying system libraries
2025-07-16 08:50:05.613 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-16 08:50:05.621 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-16 08:50:28.702 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 08:50:47.785 [Warning] LibraryExtractor: Failed to download and extract redistributable for api-ms-win-crt-runtime-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_0f333ada-fc78-4a93-b440-5656ddf681a7.exe' is denied.
2025-07-16 08:50:47.786 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 08:51:09.352 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 08:51:19.625 [Warning] LibraryExtractor: Failed to download and extract redistributable for api-ms-win-crt-string-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_e3fff6ec-bbdd-463b-b833-9307bf5d95c0.exe' is denied.
2025-07-16 08:51:19.625 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 08:51:32.643 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-math-l1-1-0.dll
2025-07-16 08:51:43.134 [Warning] LibraryExtractor: Failed to download and extract redistributable for api-ms-win-crt-math-l1-1-0.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_ae713337-8c75-4a18-b414-30ff6d9ec659.exe' is denied.
2025-07-16 08:51:43.134 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-16 08:51:53.135 [Information] LibraryExtractor: Verifying library extraction
2025-07-16 08:51:53.136 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-16 08:51:53.136 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-16 08:51:53.137 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-16 08:51:53.137 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-16 08:51:53.137 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-16 08:51:53.140 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-16 08:51:53.141 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-16 08:51:53.142 [Information] DependencyManager: Initializing dependency manager
2025-07-16 08:51:53.143 [Information] DependencyManager: Setting up library search paths
2025-07-16 08:51:53.144 [Information] DependencyManager: Added library path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-16 08:51:53.144 [Information] DependencyManager: Added driver path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-16 08:51:53.144 [Information] DependencyManager: Added application path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix
2025-07-16 08:51:53.145 [Information] DependencyManager: Updated PATH environment variable
2025-07-16 08:51:53.146 [Information] DependencyManager: Verifying required directories
2025-07-16 08:51:53.146 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-16 08:51:53.146 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-16 08:51:53.146 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-16 08:51:53.146 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Config
2025-07-16 08:51:53.147 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-16 08:51:53.155 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-16 08:51:53.156 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-16 08:51:53.156 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-16 08:51:53.157 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-16 08:51:53.159 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcr120.dll: Error 193
2025-07-16 08:51:53.159 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr120.dll
2025-07-16 08:51:53.162 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-16 08:51:53.163 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-16 08:51:53.163 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-16 08:51:53.164 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-16 08:51:53.164 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp120.dll: Error 193
2025-07-16 08:51:53.164 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp120.dll
2025-07-16 08:51:53.165 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-16 08:51:53.165 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-16 08:51:53.177 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll (x64)
2025-07-16 08:51:53.177 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll (x64)
2025-07-16 08:51:53.178 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 08:51:53.178 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 08:51:53.179 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 08:51:53.179 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 08:51:53.180 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 08:51:53.180 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 08:51:53.181 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 08:51:53.181 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 08:51:53.181 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-16 08:51:53.182 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-16 08:51:53.182 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-16 08:51:53.182 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-16 08:51:53.183 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-16 08:51:53.183 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-16 08:51:53.183 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-16 08:51:53.184 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-16 08:51:53.184 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-16 08:51:53.184 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-16 08:51:53.185 [Information] DependencyManager: VC++ Redistributable library loading: 2/14 (14.3%) libraries loaded
2025-07-16 08:51:53.185 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-16 08:51:53.186 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-16 08:51:53.191 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-16 08:51:53.252 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-16 08:51:53.253 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-16 08:51:53.254 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-16 08:51:53.301 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-16 08:51:53.301 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-16 08:51:53.302 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-16 08:51:53.493 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-16 08:51:53.494 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-16 08:51:53.494 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-16 08:51:53.566 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-16 08:51:53.566 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-16 08:51:53.567 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-16 08:51:53.613 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-16 08:51:53.613 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-16 08:51:53.613 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-16 08:51:53.614 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-16 08:51:53.614 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-16 08:51:53.614 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-16 08:51:53.615 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-16 08:51:53.615 [Warning] DependencyManager: Failed to load Critical library msvcr120.dll: Error 193
2025-07-16 08:51:53.615 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-16 08:51:53.615 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-16 08:51:53.616 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-16 08:51:53.616 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-16 08:51:53.616 [Warning] DependencyManager: Failed to load Critical library msvcp120.dll: Error 193
2025-07-16 08:51:53.617 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-16 08:51:53.617 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll (x64)
2025-07-16 08:51:53.618 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll (x64)
2025-07-16 08:51:53.618 [Information] DependencyManager: Setting up environment variables
2025-07-16 08:51:53.618 [Information] DependencyManager: Environment variables configured
2025-07-16 08:51:53.620 [Information] DependencyManager: Verifying library loading status
2025-07-16 08:51:53.726 [Information] DependencyManager: Library loading verification: 4/11 (36.4%) critical libraries loaded
2025-07-16 08:51:53.726 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-16 08:51:53.727 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-16 08:51:53.729 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-16 08:51:53.730 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-16 08:51:53.738 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-16 08:51:53.739 [Information] IntegratedStartupService: Verifying system readiness
2025-07-16 08:51:53.740 [Information] IntegratedStartupService: System readiness verification passed
2025-07-16 08:51:53.740 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-16 08:51:53.741 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-16 08:51:53.741 [Information] IntegratedStartupService: Application Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix
2025-07-16 08:51:53.741 [Information] IntegratedStartupService: Libraries Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-16 08:51:53.741 [Information] IntegratedStartupService: Drivers Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-16 08:51:53.742 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-16 08:51:53.742 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-16 08:51:53.742 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-16 08:51:53.743 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-16 08:51:53.743 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-16 08:51:53.743 [Information] App: Integrated startup completed successfully
2025-07-16 08:51:53.745 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-16 08:51:53.886 [Information] App: Initializing application services
2025-07-16 08:51:53.887 [Information] AppConfigurationService: Initializing configuration service
2025-07-16 08:51:53.888 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Config
2025-07-16 08:51:53.931 [Information] AppConfigurationService: Configuration loaded from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-16 08:51:53.932 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-16 08:51:53.933 [Information] App: Configuration service initialized successfully
2025-07-16 08:51:53.933 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-16 08:51:53.934 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-16 08:51:53.936 [Information] App: Environment variable exists: True, not 'false': False
2025-07-16 08:51:53.937 [Information] App: Final useDummyImplementations value: False
2025-07-16 08:51:53.937 [Information] App: Updating config to NOT use dummy implementations
2025-07-16 08:51:53.938 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-16 08:51:53.947 [Information] AppConfigurationService: Configuration saved to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-16 08:51:53.948 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-16 08:51:53.948 [Information] App: usePatchedImplementation flag is: True
2025-07-16 08:51:53.948 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-16 08:51:53.949 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-16 08:51:53.949 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-16 08:51:53.949 [Information] App: verboseLogging flag is: True
2025-07-16 08:51:53.950 [Information] App: Verifying real hardware requirements...
2025-07-16 08:51:53.951 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-16 08:51:53.951 [Information] App: ✓ Found critical library: apci.dll
2025-07-16 08:51:53.951 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-16 08:51:53.951 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-16 08:51:53.952 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-16 08:51:53.952 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-16 08:51:53.952 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-16 08:51:53.952 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-16 08:51:53.964 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-16 08:51:53.966 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-16 08:51:53.966 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-16 08:51:53.967 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-16 08:51:53.971 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-07-16 08:52:07.569 [Warning] RuntimeDependencyResolver: Failed to download msvcr140.dll: Access to the path 'C:\Users\<USER>\AppData\Local\Temp\vcredist_6dad5c38-8abe-4da4-955a-5ecf3e8efbf0.exe' is denied.
2025-07-16 08:52:07.570 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-16 08:52:07.570 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-16 08:52:07.570 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-16 08:52:07.571 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 08:52:07.571 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 08:52:07.571 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 08:52:07.571 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 08:52:07.571 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-16 08:52:07.572 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-16 08:52:07.572 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-16 08:52:07.574 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-16 08:52:07.575 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-16 08:52:07.575 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-16 08:52:07.575 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-16 08:52:07.576 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-16 08:52:07.576 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-16 08:52:07.576 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-16 08:52:07.577 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-16 08:52:07.578 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-16 08:52:07.578 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-16 08:52:07.580 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-16 08:52:07.580 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-16 08:52:07.581 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-16 08:52:07.581 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-16 08:52:07.582 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-16 08:52:07.582 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct service first, then bridge if needed
2025-07-16 08:52:07.583 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service despite architecture mismatch
2025-07-16 08:52:07.584 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-16 08:52:07.585 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-07-16 08:52:07.585 [Information] PatchedVocomServiceFactory: Assembly location: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-07-16 08:52:07.601 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-16 08:52:07.602 [Information] PatchedVocomServiceFactory: Created marker file at C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-07-16 08:52:07.604 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-16 08:52:07.604 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-16 08:52:07.604 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-16 08:52:07.605 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-16 08:52:07.606 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-16 08:52:07.606 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-16 08:52:07.606 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-16 08:52:07.606 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 08:52:07.607 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-16 08:52:07.607 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-16 08:52:07.607 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-16 08:52:07.609 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-16 08:52:07.611 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-16 08:52:07.614 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-16 08:52:07.615 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-16 08:52:07.615 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-16 08:52:07.621 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll
2025-07-16 08:52:07.624 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-16 08:52:07.626 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apcidb.dll
2025-07-16 08:52:07.627 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-07-16 08:52:07.631 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.ApciPlus.dll
2025-07-16 08:52:07.634 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-07-16 08:52:07.637 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.ApciPlusData.dll
2025-07-16 08:52:07.638 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-07-16 08:52:07.639 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.ApciPlusTea2Data.dll
2025-07-16 08:52:07.640 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusTea2Data.dll
2025-07-16 08:52:07.642 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interface.dll
2025-07-16 08:52:07.644 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Volvo.NAMS.AC.Services.Interface.dll
2025-07-16 08:52:07.645 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-16 08:52:07.647 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-16 08:52:07.648 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.NVS.Core.dll
2025-07-16 08:52:07.649 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Volvo.NVS.Core.dll
2025-07-16 08:52:07.650 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.NVS.Logging.dll
2025-07-16 08:52:07.651 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Volvo.NVS.Logging.dll
2025-07-16 08:52:07.653 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.NVS.Persistence.dll
2025-07-16 08:52:07.654 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Volvo.NVS.Persistence.dll
2025-07-16 08:52:07.655 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.NVS.Persistence.NHibernate.dll
2025-07-16 08:52:07.656 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Volvo.NVS.Persistence.NHibernate.dll
2025-07-16 08:52:07.658 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\VolvoIt.Baf.Utility.dll
2025-07-16 08:52:07.660 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\VolvoIt.Baf.Utility.dll
2025-07-16 08:52:07.661 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-16 08:52:07.662 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-16 08:52:07.664 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\VolvoIt.Waf.ServiceContract.dll
2025-07-16 08:52:07.665 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\VolvoIt.Waf.ServiceContract.dll
2025-07-16 08:52:07.667 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\VolvoIt.Waf.Utility.dll
2025-07-16 08:52:07.668 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\VolvoIt.Waf.Utility.dll
2025-07-16 08:52:07.669 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Config\Volvo.ApciPlus.dll.config
2025-07-16 08:52:07.670 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll.config
2025-07-16 08:52:07.671 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Config\Volvo.ApciPlusData.dll.config
2025-07-16 08:52:07.672 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll.config
2025-07-16 08:52:07.675 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\NHibernate.dll
2025-07-16 08:52:07.678 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\NHibernate.dll
2025-07-16 08:52:07.680 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\NHibernate.Caches.SysCache2.dll
2025-07-16 08:52:07.681 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\NHibernate.Caches.SysCache2.dll
2025-07-16 08:52:07.683 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\Iesi.Collections.dll
2025-07-16 08:52:07.684 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Iesi.Collections.dll
2025-07-16 08:52:07.685 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\Ionic.Zip.Reduced.dll
2025-07-16 08:52:07.686 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Ionic.Zip.Reduced.dll
2025-07-16 08:52:07.688 [Information] PhoenixVocomAdapter: Copied SharpCompress.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\SharpCompress.dll
2025-07-16 08:52:07.691 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\DotNetZip.dll
2025-07-16 08:52:07.692 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\DotNetZip.dll
2025-07-16 08:52:07.693 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\ICSharpCode.SharpZipLib.dll
2025-07-16 08:52:07.694 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\ICSharpCode.SharpZipLib.dll
2025-07-16 08:52:07.696 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Vodia.CommonDomain.Model.dll
2025-07-16 08:52:07.697 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Vodia.CommonDomain.Model.dll
2025-07-16 08:52:07.698 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Vodia.Contracts.Common.dll
2025-07-16 08:52:07.700 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Vodia.Contracts.Common.dll
2025-07-16 08:52:07.702 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Vodia.UtilityComponent.dll
2025-07-16 08:52:07.703 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Vodia.UtilityComponent.dll
2025-07-16 08:52:07.704 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\log4net.dll
2025-07-16 08:52:07.705 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\log4net.dll
2025-07-16 08:52:07.707 [Information] PhoenixVocomAdapter: Copied Newtonsoft.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\Newtonsoft.Json.dll
2025-07-16 08:52:07.709 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\AutoMapper.dll
2025-07-16 08:52:07.710 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\AutoMapper.dll
2025-07-16 08:52:07.710 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-16 08:52:07.712 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.AppContext.dll
2025-07-16 08:52:07.713 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.AppContext.dll
2025-07-16 08:52:07.714 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Buffers.dll
2025-07-16 08:52:07.715 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Buffers.dll
2025-07-16 08:52:07.716 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Collections.Concurrent.dll
2025-07-16 08:52:07.717 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Collections.Concurrent.dll
2025-07-16 08:52:07.718 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Collections.dll
2025-07-16 08:52:07.720 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Collections.dll
2025-07-16 08:52:07.721 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Collections.NonGeneric.dll
2025-07-16 08:52:07.722 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Collections.NonGeneric.dll
2025-07-16 08:52:07.723 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Collections.Specialized.dll
2025-07-16 08:52:07.724 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Collections.Specialized.dll
2025-07-16 08:52:07.725 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.ComponentModel.dll
2025-07-16 08:52:07.726 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.ComponentModel.dll
2025-07-16 08:52:07.727 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.ComponentModel.EventBasedAsync.dll
2025-07-16 08:52:07.728 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.ComponentModel.EventBasedAsync.dll
2025-07-16 08:52:07.729 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.ComponentModel.Primitives.dll
2025-07-16 08:52:07.731 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.ComponentModel.Primitives.dll
2025-07-16 08:52:07.732 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.ComponentModel.TypeConverter.dll
2025-07-16 08:52:07.733 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.ComponentModel.TypeConverter.dll
2025-07-16 08:52:07.734 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Console.dll
2025-07-16 08:52:07.735 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Console.dll
2025-07-16 08:52:07.736 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Data.Common.dll
2025-07-16 08:52:07.737 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Data.Common.dll
2025-07-16 08:52:07.739 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Data.SQLite.dll
2025-07-16 08:52:07.740 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Data.SQLite.dll
2025-07-16 08:52:07.742 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Data.SqlServerCe.dll
2025-07-16 08:52:07.743 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Data.SqlServerCe.dll
2025-07-16 08:52:07.744 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.Contracts.dll
2025-07-16 08:52:07.745 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Diagnostics.Contracts.dll
2025-07-16 08:52:07.746 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.Debug.dll
2025-07-16 08:52:07.747 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Diagnostics.Debug.dll
2025-07-16 08:52:07.748 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.FileVersionInfo.dll
2025-07-16 08:52:07.749 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Diagnostics.FileVersionInfo.dll
2025-07-16 08:52:07.750 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.Process.dll
2025-07-16 08:52:07.751 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Diagnostics.Process.dll
2025-07-16 08:52:07.752 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.StackTrace.dll
2025-07-16 08:52:07.753 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Diagnostics.StackTrace.dll
2025-07-16 08:52:07.755 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.TextWriterTraceListener.dll
2025-07-16 08:52:07.756 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Diagnostics.TextWriterTraceListener.dll
2025-07-16 08:52:07.758 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.Tools.dll
2025-07-16 08:52:07.759 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Diagnostics.Tools.dll
2025-07-16 08:52:07.760 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.TraceSource.dll
2025-07-16 08:52:07.761 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Diagnostics.TraceSource.dll
2025-07-16 08:52:07.762 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.Tracing.dll
2025-07-16 08:52:07.763 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Diagnostics.Tracing.dll
2025-07-16 08:52:07.764 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Drawing.Primitives.dll
2025-07-16 08:52:07.765 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Drawing.Primitives.dll
2025-07-16 08:52:07.766 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Dynamic.Runtime.dll
2025-07-16 08:52:07.767 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Dynamic.Runtime.dll
2025-07-16 08:52:07.768 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Globalization.Calendars.dll
2025-07-16 08:52:07.769 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Globalization.Calendars.dll
2025-07-16 08:52:07.771 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Globalization.dll
2025-07-16 08:52:07.772 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Globalization.dll
2025-07-16 08:52:07.773 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Globalization.Extensions.dll
2025-07-16 08:52:07.775 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Globalization.Extensions.dll
2025-07-16 08:52:07.777 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.Compression.dll
2025-07-16 08:52:07.778 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.IO.Compression.dll
2025-07-16 08:52:07.779 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.Compression.ZipFile.dll
2025-07-16 08:52:07.780 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.IO.Compression.ZipFile.dll
2025-07-16 08:52:07.781 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.dll
2025-07-16 08:52:07.782 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.IO.dll
2025-07-16 08:52:07.783 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.FileSystem.dll
2025-07-16 08:52:07.784 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.IO.FileSystem.dll
2025-07-16 08:52:07.785 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.FileSystem.DriveInfo.dll
2025-07-16 08:52:07.786 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.IO.FileSystem.DriveInfo.dll
2025-07-16 08:52:07.789 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.FileSystem.Primitives.dll
2025-07-16 08:52:07.790 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.IO.FileSystem.Primitives.dll
2025-07-16 08:52:07.791 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.FileSystem.Watcher.dll
2025-07-16 08:52:07.792 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.IO.FileSystem.Watcher.dll
2025-07-16 08:52:07.793 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.IsolatedStorage.dll
2025-07-16 08:52:07.794 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.IO.IsolatedStorage.dll
2025-07-16 08:52:07.795 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.MemoryMappedFiles.dll
2025-07-16 08:52:07.796 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.IO.MemoryMappedFiles.dll
2025-07-16 08:52:07.798 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.Pipes.dll
2025-07-16 08:52:07.798 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.IO.Pipes.dll
2025-07-16 08:52:07.799 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.UnmanagedMemoryStream.dll
2025-07-16 08:52:07.800 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.IO.UnmanagedMemoryStream.dll
2025-07-16 08:52:07.801 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Linq.dll
2025-07-16 08:52:07.802 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Linq.dll
2025-07-16 08:52:07.803 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Linq.Expressions.dll
2025-07-16 08:52:07.804 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Linq.Expressions.dll
2025-07-16 08:52:07.805 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Linq.Parallel.dll
2025-07-16 08:52:07.806 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Linq.Parallel.dll
2025-07-16 08:52:07.808 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Linq.Queryable.dll
2025-07-16 08:52:07.809 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Linq.Queryable.dll
2025-07-16 08:52:07.810 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Memory.dll
2025-07-16 08:52:07.811 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Memory.dll
2025-07-16 08:52:07.812 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.Http.dll
2025-07-16 08:52:07.813 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Net.Http.dll
2025-07-16 08:52:07.814 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.NameResolution.dll
2025-07-16 08:52:07.815 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Net.NameResolution.dll
2025-07-16 08:52:07.816 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.NetworkInformation.dll
2025-07-16 08:52:07.817 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Net.NetworkInformation.dll
2025-07-16 08:52:07.818 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.Ping.dll
2025-07-16 08:52:07.819 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Net.Ping.dll
2025-07-16 08:52:07.820 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.Primitives.dll
2025-07-16 08:52:07.821 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Net.Primitives.dll
2025-07-16 08:52:07.822 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.Requests.dll
2025-07-16 08:52:07.823 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Net.Requests.dll
2025-07-16 08:52:07.824 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.Security.dll
2025-07-16 08:52:07.825 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Net.Security.dll
2025-07-16 08:52:07.826 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.Sockets.dll
2025-07-16 08:52:07.827 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Net.Sockets.dll
2025-07-16 08:52:07.828 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.WebHeaderCollection.dll
2025-07-16 08:52:07.829 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Net.WebHeaderCollection.dll
2025-07-16 08:52:07.830 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.WebSockets.Client.dll
2025-07-16 08:52:07.831 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Net.WebSockets.Client.dll
2025-07-16 08:52:07.832 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.WebSockets.dll
2025-07-16 08:52:07.833 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Net.WebSockets.dll
2025-07-16 08:52:07.834 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Numerics.Vectors.dll
2025-07-16 08:52:07.835 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Numerics.Vectors.dll
2025-07-16 08:52:07.836 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.ObjectModel.dll
2025-07-16 08:52:07.837 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.ObjectModel.dll
2025-07-16 08:52:07.838 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Reflection.dll
2025-07-16 08:52:07.839 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Reflection.dll
2025-07-16 08:52:07.840 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Reflection.Extensions.dll
2025-07-16 08:52:07.841 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Reflection.Extensions.dll
2025-07-16 08:52:07.842 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Reflection.Primitives.dll
2025-07-16 08:52:07.843 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Reflection.Primitives.dll
2025-07-16 08:52:07.845 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Resources.Reader.dll
2025-07-16 08:52:07.845 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Resources.Reader.dll
2025-07-16 08:52:07.846 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Resources.ResourceManager.dll
2025-07-16 08:52:07.847 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Resources.ResourceManager.dll
2025-07-16 08:52:07.848 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Resources.Writer.dll
2025-07-16 08:52:07.849 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Resources.Writer.dll
2025-07-16 08:52:07.850 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.CompilerServices.Unsafe.dll
2025-07-16 08:52:07.851 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Runtime.CompilerServices.Unsafe.dll
2025-07-16 08:52:07.852 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.CompilerServices.VisualC.dll
2025-07-16 08:52:07.853 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Runtime.CompilerServices.VisualC.dll
2025-07-16 08:52:07.854 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.dll
2025-07-16 08:52:07.855 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Runtime.dll
2025-07-16 08:52:07.856 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Extensions.dll
2025-07-16 08:52:07.857 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Runtime.Extensions.dll
2025-07-16 08:52:07.859 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Handles.dll
2025-07-16 08:52:07.860 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Runtime.Handles.dll
2025-07-16 08:52:07.861 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.InteropServices.dll
2025-07-16 08:52:07.861 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Runtime.InteropServices.dll
2025-07-16 08:52:07.862 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-16 08:52:07.863 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-16 08:52:07.864 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Numerics.dll
2025-07-16 08:52:07.865 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Runtime.Numerics.dll
2025-07-16 08:52:07.866 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Serialization.Formatters.dll
2025-07-16 08:52:07.867 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Runtime.Serialization.Formatters.dll
2025-07-16 08:52:07.868 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Serialization.Json.dll
2025-07-16 08:52:07.869 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Runtime.Serialization.Json.dll
2025-07-16 08:52:07.870 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Serialization.Primitives.dll
2025-07-16 08:52:07.871 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Runtime.Serialization.Primitives.dll
2025-07-16 08:52:07.872 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Serialization.Xml.dll
2025-07-16 08:52:07.873 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Runtime.Serialization.Xml.dll
2025-07-16 08:52:07.874 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Claims.dll
2025-07-16 08:52:07.875 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Security.Claims.dll
2025-07-16 08:52:07.876 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Cryptography.Algorithms.dll
2025-07-16 08:52:07.876 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Security.Cryptography.Algorithms.dll
2025-07-16 08:52:07.878 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Cryptography.Csp.dll
2025-07-16 08:52:07.879 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Security.Cryptography.Csp.dll
2025-07-16 08:52:07.880 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Cryptography.Encoding.dll
2025-07-16 08:52:07.881 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Security.Cryptography.Encoding.dll
2025-07-16 08:52:07.882 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Cryptography.Primitives.dll
2025-07-16 08:52:07.883 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Security.Cryptography.Primitives.dll
2025-07-16 08:52:07.884 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Cryptography.X509Certificates.dll
2025-07-16 08:52:07.885 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Security.Cryptography.X509Certificates.dll
2025-07-16 08:52:07.886 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Principal.dll
2025-07-16 08:52:07.888 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Security.Principal.dll
2025-07-16 08:52:07.890 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.SecureString.dll
2025-07-16 08:52:07.891 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Security.SecureString.dll
2025-07-16 08:52:07.893 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Text.Encoding.dll
2025-07-16 08:52:07.894 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Text.Encoding.dll
2025-07-16 08:52:07.895 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Text.Encoding.Extensions.dll
2025-07-16 08:52:07.896 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Text.Encoding.Extensions.dll
2025-07-16 08:52:07.897 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Text.RegularExpressions.dll
2025-07-16 08:52:07.898 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Text.RegularExpressions.dll
2025-07-16 08:52:07.900 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.dll
2025-07-16 08:52:07.901 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Threading.dll
2025-07-16 08:52:07.902 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.Overlapped.dll
2025-07-16 08:52:07.903 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Threading.Overlapped.dll
2025-07-16 08:52:07.904 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.Tasks.dll
2025-07-16 08:52:07.905 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Threading.Tasks.dll
2025-07-16 08:52:07.907 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.Tasks.Extensions.dll
2025-07-16 08:52:07.908 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Threading.Tasks.Extensions.dll
2025-07-16 08:52:07.909 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.Tasks.Parallel.dll
2025-07-16 08:52:07.910 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Threading.Tasks.Parallel.dll
2025-07-16 08:52:07.911 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.Thread.dll
2025-07-16 08:52:07.912 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Threading.Thread.dll
2025-07-16 08:52:07.913 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.ThreadPool.dll
2025-07-16 08:52:07.914 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Threading.ThreadPool.dll
2025-07-16 08:52:07.916 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.Timer.dll
2025-07-16 08:52:07.917 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Threading.Timer.dll
2025-07-16 08:52:07.918 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.ValueTuple.dll
2025-07-16 08:52:07.919 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.ValueTuple.dll
2025-07-16 08:52:07.920 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Xml.ReaderWriter.dll
2025-07-16 08:52:07.921 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Xml.ReaderWriter.dll
2025-07-16 08:52:07.923 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Xml.XDocument.dll
2025-07-16 08:52:07.924 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Xml.XDocument.dll
2025-07-16 08:52:07.926 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Xml.XmlDocument.dll
2025-07-16 08:52:07.928 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Xml.XmlDocument.dll
2025-07-16 08:52:07.929 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Xml.XmlSerializer.dll
2025-07-16 08:52:07.930 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Xml.XmlSerializer.dll
2025-07-16 08:52:07.931 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Xml.XPath.dll
2025-07-16 08:52:07.932 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Xml.XPath.dll
2025-07-16 08:52:07.933 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Xml.XPath.XDocument.dll
2025-07-16 08:52:07.934 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\System.Xml.XPath.XDocument.dll
2025-07-16 08:52:07.936 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\SystemInterface.dll
2025-07-16 08:52:07.937 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\SystemInterface.dll
2025-07-16 08:52:07.937 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-16 08:52:07.938 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-16 08:52:07.938 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-16 08:52:08.001 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-16 08:52:08.001 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-07-16 08:52:08.002 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-16 08:52:08.002 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-07-16 08:52:08.060 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-16 08:52:08.060 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-16 08:52:08.061 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-16 08:52:08.061 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-16 08:52:08.061 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-16 08:52:08.062 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-16 08:52:08.062 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-16 08:52:08.063 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 5:58:52 AM
2025-07-16 08:52:08.065 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-16 08:52:08.066 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-16 08:52:08.066 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-16 08:52:08.066 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-16 08:52:08.066 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-16 08:52:08.068 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-16 08:52:08.068 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-16 08:52:08.069 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-16 08:52:08.069 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-16 08:52:08.069 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-16 08:52:08.069 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-16 08:52:08.070 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-16 08:52:08.070 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-16 08:52:08.071 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr120.dll
2025-07-16 08:52:08.072 [Warning] VocomDiagnosticTool: ✗ Missing: msvcp120.dll
2025-07-16 08:52:08.073 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-16 08:52:08.074 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-16 08:52:08.074 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-16 08:52:08.074 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 08:52:08.075 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-16 08:52:08.075 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-16 08:52:08.076 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-16 08:52:08.076 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-16 08:52:08.076 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-16 08:52:08.077 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-16 08:52:08.077 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-16 08:52:08.077 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-16 08:52:08.078 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-16 08:52:08.080 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-16 08:52:08.080 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-16 08:52:08.091 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-16 08:52:08.091 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-16 08:52:08.091 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-16 08:52:08.092 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-16 08:52:08.093 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-16 08:52:08.094 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-16 08:52:08.094 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-16 08:52:08.094 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-16 08:52:08.139 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-16 08:52:08.140 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-16 08:52:08.140 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-16 08:52:08.288 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-16 08:52:08.289 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-16 08:52:08.366 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-16 08:52:08.366 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-16 08:52:08.442 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-16 08:52:08.442 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-16 08:52:08.442 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-16 08:52:08.516 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-16 08:52:08.594 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-16 08:52:08.594 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-16 08:52:08.656 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\
2025-07-16 08:52:08.805 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\
2025-07-16 08:52:08.929 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-16 08:52:09.018 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-16 08:52:09.018 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-16 08:52:09.019 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-16 08:52:09.201 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-16 08:52:09.202 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-16 08:52:09.278 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-16 08:52:09.342 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-16 08:52:09.342 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-16 08:52:09.450 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-16 08:52:09.556 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-16 08:52:09.556 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-16 08:52:09.659 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\
2025-07-16 08:52:09.666 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\
2025-07-16 08:52:09.713 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\
2025-07-16 08:52:09.715 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-16 08:52:09.715 [Error] VocomNativeInterop_Patch: DLL Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-16 08:52:09.716 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 5:58:52 AM
2025-07-16 08:52:09.716 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-16 08:52:09.716 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-16 08:52:09.717 [Information] VocomDriver: Initializing Vocom driver
2025-07-16 08:52:09.718 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-16 08:52:09.720 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-16 08:52:09.720 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-16 08:52:09.720 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-16 08:52:09.721 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-16 08:52:09.721 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-16 08:52:09.723 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr120.dll
2025-07-16 08:52:09.724 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp120.dll
2025-07-16 08:52:09.725 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-16 08:52:09.726 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-16 08:52:09.726 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-16 08:52:09.726 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 08:52:09.746 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-07-16 08:52:09.767 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-07-16 08:52:09.781 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-07-16 08:52:09.784 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-16 08:52:09.784 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-16 08:52:09.785 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-16 08:52:09.785 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-16 08:52:09.786 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-16 08:52:09.786 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-16 08:52:09.786 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-16 08:52:09.786 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-16 08:52:09.786 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-16 08:52:09.787 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-16 08:52:09.787 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-16 08:52:09.787 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-16 08:52:09.787 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-16 08:52:09.787 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-16 08:52:09.788 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-16 08:52:09.788 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-16 08:52:09.788 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-16 08:52:09.788 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-16 08:52:09.788 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-16 08:52:09.789 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-16 08:52:09.789 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-16 08:52:09.789 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-16 08:52:09.789 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-16 08:52:09.789 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-16 08:52:09.790 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-16 08:52:09.790 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-16 08:52:09.790 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-16 08:52:09.790 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-16 08:52:09.790 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-16 08:52:09.791 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-16 08:52:09.791 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-16 08:52:09.791 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-16 08:52:09.792 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-16 08:52:09.792 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-16 08:52:09.792 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-16 08:52:09.792 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-16 08:52:09.792 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-16 08:52:09.792 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-16 08:52:09.793 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-16 08:52:09.793 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-16 08:52:09.793 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-16 08:52:09.793 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-16 08:52:09.793 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-16 08:52:09.793 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-16 08:52:09.794 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-16 08:52:09.794 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-16 08:52:09.794 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-16 08:52:09.795 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-16 08:52:09.795 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-16 08:52:09.795 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-16 08:52:09.796 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-16 08:52:09.796 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-16 08:52:09.796 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-16 08:52:09.796 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-16 08:52:09.798 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-16 08:52:09.799 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-16 08:52:09.799 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-16 08:52:09.800 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-16 08:52:09.836 [Information] WiFiCommunicationService: WiFi is available
2025-07-16 08:52:09.836 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-16 08:52:09.837 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-16 08:52:09.838 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-16 08:52:09.839 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-16 08:52:09.840 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-16 08:52:09.841 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-16 08:52:09.842 [Information] VocomService: Initializing enhanced Vocom services
2025-07-16 08:52:09.842 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-16 08:52:09.843 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-16 08:52:09.846 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-16 08:52:09.846 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-16 08:52:09.846 [Information] VocomService: Native USB communication service initialized
2025-07-16 08:52:09.847 [Information] VocomService: Enhanced device detection service initialized
2025-07-16 08:52:09.847 [Information] VocomService: Connection recovery service initialized
2025-07-16 08:52:09.847 [Information] VocomService: Enhanced services initialization completed
2025-07-16 08:52:09.848 [Information] VocomService: Checking if PTT application is running
2025-07-16 08:52:09.858 [Information] VocomService: PTT application is not running
2025-07-16 08:52:09.859 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-16 08:52:09.861 [Debug] VocomService: Bluetooth is enabled
2025-07-16 08:52:09.861 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-16 08:52:09.861 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-16 08:52:09.861 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-07-16 08:52:09.863 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-16 08:52:09.863 [Information] VocomService: Using new enhanced device detection service
2025-07-16 08:52:09.864 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-16 08:52:09.865 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-16 08:52:10.216 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-16 08:52:10.217 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-16 08:52:10.218 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-16 08:52:10.219 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-16 08:52:10.219 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-16 08:52:10.220 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-16 08:52:10.221 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-16 08:52:10.223 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-16 08:52:10.397 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-16 08:52:10.399 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-16 08:52:10.400 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-16 08:52:10.400 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-16 08:52:10.401 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-16 08:52:10.401 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-16 08:52:10.402 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-16 08:52:10.402 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-16 08:52:10.402 [Debug] VocomService: Bluetooth is enabled
2025-07-16 08:52:10.404 [Debug] VocomService: Checking if WiFi is available
2025-07-16 08:52:10.405 [Debug] VocomService: WiFi is available
2025-07-16 08:52:10.405 [Information] VocomService: Found 3 Vocom devices
2025-07-16 08:52:10.405 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 devices
2025-07-16 08:52:10.406 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real devices - using direct service
2025-07-16 08:52:10.406 [Information] ArchitectureAwareVocomServiceFactory: Direct service created successfully despite architecture mismatch
2025-07-16 08:52:10.407 [Information] App: Architecture-aware Vocom service created successfully
2025-07-16 08:52:10.407 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-16 08:52:10.407 [Information] VocomService: Initializing enhanced Vocom services
2025-07-16 08:52:10.408 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-16 08:52:10.408 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-16 08:52:10.409 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-16 08:52:10.409 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-16 08:52:10.409 [Information] VocomService: Native USB communication service initialized
2025-07-16 08:52:10.409 [Information] VocomService: Enhanced device detection service initialized
2025-07-16 08:52:10.410 [Information] VocomService: Connection recovery service initialized
2025-07-16 08:52:10.410 [Information] VocomService: Enhanced services initialization completed
2025-07-16 08:52:10.410 [Information] VocomService: Checking if PTT application is running
2025-07-16 08:52:10.419 [Information] VocomService: PTT application is not running
2025-07-16 08:52:10.419 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-16 08:52:10.419 [Debug] VocomService: Bluetooth is enabled
2025-07-16 08:52:10.420 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-16 08:52:10.420 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-16 08:52:10.420 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-16 08:52:10.420 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-16 08:52:10.448 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-16 08:52:10.448 [Information] VocomService: Using new enhanced device detection service
2025-07-16 08:52:10.448 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-16 08:52:10.448 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-16 08:52:10.608 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-16 08:52:10.608 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-16 08:52:10.609 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-16 08:52:10.609 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-16 08:52:10.609 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-16 08:52:10.609 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-16 08:52:10.609 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-16 08:52:10.610 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-16 08:52:10.774 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-16 08:52:10.774 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-16 08:52:10.774 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-16 08:52:10.775 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-16 08:52:10.775 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-16 08:52:10.775 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-16 08:52:10.776 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-16 08:52:10.776 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-16 08:52:10.776 [Debug] VocomService: Bluetooth is enabled
2025-07-16 08:52:10.776 [Debug] VocomService: Checking if WiFi is available
2025-07-16 08:52:10.777 [Debug] VocomService: WiFi is available
2025-07-16 08:52:10.777 [Information] VocomService: Found 3 Vocom devices
2025-07-16 08:52:10.777 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-16 08:52:10.779 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-16 08:52:10.779 [Information] VocomService: Checking if PTT application is running
2025-07-16 08:52:10.787 [Information] VocomService: PTT application is not running
2025-07-16 08:52:10.790 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-16 08:52:10.791 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-16 08:52:10.791 [Information] VocomService: Checking if PTT application is running
2025-07-16 08:52:10.798 [Information] VocomService: PTT application is not running
2025-07-16 08:52:10.799 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-16 08:52:10.800 [Information] VocomService: Native USB connection attempt 1/3 to WUDFPuma Driver
2025-07-16 08:52:10.801 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-16 08:52:10.802 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-16 08:52:10.804 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-16 08:52:10.804 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-16 08:52:12.806 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-16 08:52:13.806 [Information] VocomService: Native USB connection attempt 2/3 to WUDFPuma Driver
2025-07-16 08:52:13.807 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-16 08:52:13.807 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-16 08:52:13.807 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-16 08:52:13.807 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-16 08:52:15.807 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-16 08:52:16.807 [Information] VocomService: Native USB connection attempt 3/3 to WUDFPuma Driver
2025-07-16 08:52:16.808 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-16 08:52:16.808 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-16 08:52:16.808 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-16 08:52:16.808 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-16 08:52:18.810 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-16 08:52:18.811 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-16 08:52:18.811 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-16 08:52:18.838 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-16 08:52:18.839 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-16 08:52:18.840 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-16 08:52:18.840 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-16 08:52:18.840 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-16 08:52:18.841 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-16 08:52:18.841 [Information] ModernUSBCommunicationService: CLR Version: 8.0.16
2025-07-16 08:52:18.842 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-16 08:52:18.843 [Information] ModernUSBCommunicationService: Found 9 HID devices total
2025-07-16 08:52:18.844 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-07-16 08:52:18.845 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-16 08:52:18.845 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-16 08:52:18.845 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-16 08:52:18.845 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-16 08:52:18.846 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-16 08:52:18.846 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-16 08:52:18.848 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-16 08:52:18.850 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-16 08:52:18.850 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-16 08:52:18.852 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-16 08:52:18.853 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-16 08:52:18.854 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-16 08:52:18.854 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-16 08:52:18.854 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-07-16 08:52:18.855 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-16 08:52:18.855 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-16 08:52:18.855 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-16 08:52:18.856 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-16 08:52:18.857 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-16 08:52:18.857 [Information] VocomService: Using new enhanced device detection service
2025-07-16 08:52:18.857 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-16 08:52:18.857 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-16 08:52:19.026 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-16 08:52:19.026 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-16 08:52:19.026 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-16 08:52:19.027 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-16 08:52:19.027 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-16 08:52:19.027 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-16 08:52:19.027 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-16 08:52:19.027 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-16 08:52:19.187 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-16 08:52:19.188 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-16 08:52:19.188 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-16 08:52:19.188 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-16 08:52:19.189 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-16 08:52:19.189 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-16 08:52:19.189 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-16 08:52:19.189 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-16 08:52:19.189 [Debug] VocomService: Bluetooth is enabled
2025-07-16 08:52:19.190 [Debug] VocomService: Checking if WiFi is available
2025-07-16 08:52:19.190 [Debug] VocomService: WiFi is available
2025-07-16 08:52:19.190 [Information] VocomService: Found 3 Vocom devices
2025-07-16 08:52:19.191 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-16 08:52:19.191 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-16 08:52:19.192 [Information] VocomService: Checking if PTT application is running
2025-07-16 08:52:19.199 [Information] VocomService: PTT application is not running
2025-07-16 08:52:19.200 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-16 08:52:19.200 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-16 08:52:19.200 [Information] VocomService: Checking if PTT application is running
2025-07-16 08:52:19.208 [Information] VocomService: PTT application is not running
2025-07-16 08:52:19.209 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-16 08:52:19.209 [Information] VocomService: Native USB connection attempt 1/3 to WUDFPuma Driver
2025-07-16 08:52:19.209 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-16 08:52:19.209 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-16 08:52:19.210 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-16 08:52:19.210 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-16 08:52:21.210 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-16 08:52:22.211 [Information] VocomService: Native USB connection attempt 2/3 to WUDFPuma Driver
2025-07-16 08:52:22.212 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-16 08:52:22.212 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-16 08:52:22.212 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-16 08:52:22.212 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-16 08:52:24.213 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-16 08:52:25.213 [Information] VocomService: Native USB connection attempt 3/3 to WUDFPuma Driver
2025-07-16 08:52:25.213 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-16 08:52:25.213 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-16 08:52:25.214 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-16 08:52:25.214 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-16 08:52:27.214 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-16 08:52:27.214 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-16 08:52:27.215 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-16 08:52:27.215 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-16 08:52:27.215 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-16 08:52:27.215 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-16 08:52:27.216 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-16 08:52:27.216 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-16 08:52:27.216 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-16 08:52:27.216 [Information] ModernUSBCommunicationService: CLR Version: 8.0.16
2025-07-16 08:52:27.216 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-16 08:52:27.217 [Information] ModernUSBCommunicationService: Found 9 HID devices total
2025-07-16 08:52:27.217 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-07-16 08:52:27.217 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-16 08:52:27.217 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-16 08:52:27.218 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-16 08:52:27.218 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-16 08:52:27.218 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-16 08:52:27.219 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-16 08:52:27.219 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-16 08:52:27.219 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-16 08:52:27.219 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-16 08:52:27.220 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-16 08:52:27.220 [Information] VocomService: Checking if PTT application is running
2025-07-16 08:52:27.228 [Information] VocomService: PTT application is not running
2025-07-16 08:52:27.230 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-16 08:52:27.230 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-16 08:52:27.230 [Debug] VocomService: Bluetooth is enabled
2025-07-16 08:52:27.231 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-16 08:52:28.033 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-16 08:52:28.034 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-16 08:52:28.034 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-16 08:52:28.034 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-16 08:52:28.036 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-16 08:52:28.037 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-16 08:52:28.049 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-16 08:52:28.050 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-16 08:52:28.062 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-16 08:52:28.066 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-16 08:52:28.067 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-16 08:52:28.078 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-16 08:52:28.079 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-16 08:52:28.079 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-16 08:52:28.080 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-16 08:52:28.080 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-16 08:52:28.080 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-16 08:52:28.080 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-16 08:52:28.081 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-16 08:52:28.081 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-16 08:52:28.082 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-16 08:52:28.082 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-16 08:52:28.082 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-16 08:52:28.082 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-16 08:52:28.083 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-16 08:52:28.083 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-16 08:52:28.083 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-16 08:52:28.083 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-16 08:52:28.085 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-16 08:52:28.090 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-16 08:52:28.091 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-16 08:52:28.093 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-16 08:52:28.094 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-16 08:52:28.101 [Information] CANRegisterAccess: Read value 0xF9 from register 0x0141 (simulated)
2025-07-16 08:52:28.102 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-16 08:52:28.102 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-16 08:52:28.102 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-16 08:52:28.109 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-16 08:52:28.109 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-16 08:52:28.115 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-16 08:52:28.115 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-16 08:52:28.115 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-16 08:52:28.122 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-16 08:52:28.122 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-16 08:52:28.122 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-16 08:52:28.127 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-16 08:52:28.127 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-16 08:52:28.133 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-16 08:52:28.133 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-16 08:52:28.139 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-16 08:52:28.139 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-16 08:52:28.145 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-16 08:52:28.145 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-16 08:52:28.150 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-16 08:52:28.150 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-16 08:52:28.156 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-16 08:52:28.156 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-16 08:52:28.161 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-16 08:52:28.162 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-16 08:52:28.167 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-16 08:52:28.167 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-16 08:52:28.172 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-16 08:52:28.173 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-16 08:52:28.178 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-16 08:52:28.179 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-16 08:52:28.183 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-16 08:52:28.184 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-16 08:52:28.189 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-16 08:52:28.190 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-16 08:52:28.195 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-16 08:52:28.196 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-16 08:52:28.200 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-16 08:52:28.201 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-16 08:52:28.206 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-16 08:52:28.207 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-16 08:52:28.212 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-16 08:52:28.213 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-16 08:52:28.218 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-16 08:52:28.219 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-16 08:52:28.219 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-16 08:52:28.223 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-16 08:52:28.224 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-16 08:52:28.224 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-16 08:52:28.224 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-16 08:52:28.230 [Information] CANRegisterAccess: Read value 0x34 from register 0x0141 (simulated)
2025-07-16 08:52:28.231 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-16 08:52:28.231 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-16 08:52:28.231 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-16 08:52:28.231 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-16 08:52:28.237 [Information] CANRegisterAccess: Read value 0xCE from register 0x0140 (simulated)
2025-07-16 08:52:28.243 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-16 08:52:28.249 [Information] CANRegisterAccess: Read value 0x76 from register 0x0140 (simulated)
2025-07-16 08:52:28.250 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-16 08:52:28.250 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-16 08:52:28.251 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-16 08:52:28.252 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-16 08:52:28.262 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-16 08:52:28.263 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-16 08:52:28.263 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-16 08:52:28.266 [Information] VocomService: Sending data and waiting for response
2025-07-16 08:52:28.266 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-16 08:52:28.317 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-16 08:52:28.318 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-16 08:52:28.319 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-16 08:52:28.320 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-16 08:52:28.320 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-16 08:52:28.331 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-16 08:52:28.331 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-16 08:52:28.332 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-16 08:52:28.343 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-16 08:52:28.354 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-16 08:52:28.365 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-16 08:52:28.375 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-16 08:52:28.386 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-16 08:52:28.388 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-16 08:52:28.388 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-16 08:52:28.399 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-16 08:52:28.400 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-16 08:52:28.400 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-16 08:52:28.411 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-16 08:52:28.422 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-16 08:52:28.432 [Information] IICProtocolHandler: Enabling IIC module
2025-07-16 08:52:28.443 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-16 08:52:28.454 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-16 08:52:28.465 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-16 08:52:28.466 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-16 08:52:28.467 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-16 08:52:28.476 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-16 08:52:28.477 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-16 08:52:28.478 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-16 08:52:28.478 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-16 08:52:28.478 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-16 08:52:28.478 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-16 08:52:28.479 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-16 08:52:28.479 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-16 08:52:28.479 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-16 08:52:28.479 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-16 08:52:28.479 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-16 08:52:28.480 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-16 08:52:28.480 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-16 08:52:28.480 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-16 08:52:28.480 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-16 08:52:28.481 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-16 08:52:28.481 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-16 08:52:28.581 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-16 08:52:28.582 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-16 08:52:28.584 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-16 08:52:28.584 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-16 08:52:28.585 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-16 08:52:28.585 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-16 08:52:28.585 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-16 08:52:28.585 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-16 08:52:28.586 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-16 08:52:28.586 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-16 08:52:28.586 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-16 08:52:28.586 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-16 08:52:28.587 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-16 08:52:28.587 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-16 08:52:28.587 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-16 08:52:28.588 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-16 08:52:28.589 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-16 08:52:28.589 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-16 08:52:28.591 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-16 08:52:28.592 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-16 08:52:28.595 [Information] BackupService: Initializing backup service
2025-07-16 08:52:28.595 [Information] BackupService: Backup service initialized successfully
2025-07-16 08:52:28.596 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-16 08:52:28.596 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-16 08:52:28.597 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-16 08:52:28.621 [Information] BackupService: Compressing backup data
2025-07-16 08:52:28.631 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (447 bytes)
2025-07-16 08:52:28.632 [Information] BackupServiceFactory: Created template for category: Production
2025-07-16 08:52:28.632 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-16 08:52:28.632 [Information] BackupService: Compressing backup data
2025-07-16 08:52:28.633 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (449 bytes)
2025-07-16 08:52:28.633 [Information] BackupServiceFactory: Created template for category: Development
2025-07-16 08:52:28.634 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-16 08:52:28.634 [Information] BackupService: Compressing backup data
2025-07-16 08:52:28.635 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (446 bytes)
2025-07-16 08:52:28.635 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-16 08:52:28.635 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-16 08:52:28.636 [Information] BackupService: Compressing backup data
2025-07-16 08:52:28.636 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-07-16 08:52:28.636 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-16 08:52:28.637 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-16 08:52:28.637 [Information] BackupService: Compressing backup data
2025-07-16 08:52:28.638 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (450 bytes)
2025-07-16 08:52:28.638 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-16 08:52:28.638 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-16 08:52:28.639 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-16 08:52:28.639 [Information] BackupService: Compressing backup data
2025-07-16 08:52:28.640 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (510 bytes)
2025-07-16 08:52:28.640 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-16 08:52:28.640 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-16 08:52:28.642 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-16 08:52:28.645 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-16 08:52:28.647 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-16 08:52:28.695 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-16 08:52:28.695 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-16 08:52:28.696 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-16 08:52:28.696 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-16 08:52:28.696 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-16 08:52:28.697 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-16 08:52:28.698 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-16 08:52:28.700 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-16 08:52:28.700 [Information] App: Flash operation monitor service initialized successfully
2025-07-16 08:52:28.706 [Information] LicensingService: Initializing licensing service
2025-07-16 08:52:28.741 [Warning] LicensingService: Failed to decrypt license data (possibly corrupted or wrong key): Padding is invalid and cannot be removed.
2025-07-16 08:52:28.741 [Warning] LicensingService: License file is corrupted or incompatible, creating new trial license: License file is corrupted or was created with a different encryption key
2025-07-16 08:52:28.742 [Information] LicensingService: Deleted corrupted license file
2025-07-16 08:52:28.771 [Information] LicensingService: License information saved successfully
2025-07-16 08:52:28.772 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-16 08:52:28.772 [Information] App: Licensing service initialized successfully
2025-07-16 08:52:28.773 [Information] App: License status: Trial
2025-07-16 08:52:28.773 [Information] App: Trial period: 30 days remaining
2025-07-16 08:52:28.773 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-16 08:52:28.794 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-16 08:52:28.926 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-16 08:52:28.927 [Information] VocomService: Initializing enhanced Vocom services
2025-07-16 08:52:28.927 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-16 08:52:28.927 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-16 08:52:28.928 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-16 08:52:28.928 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-16 08:52:28.928 [Information] VocomService: Native USB communication service initialized
2025-07-16 08:52:28.928 [Information] VocomService: Enhanced device detection service initialized
2025-07-16 08:52:28.928 [Information] VocomService: Connection recovery service initialized
2025-07-16 08:52:28.929 [Information] VocomService: Enhanced services initialization completed
2025-07-16 08:52:28.929 [Information] VocomService: Checking if PTT application is running
2025-07-16 08:52:28.936 [Information] VocomService: PTT application is not running
2025-07-16 08:52:28.937 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-16 08:52:28.937 [Debug] VocomService: Bluetooth is enabled
2025-07-16 08:52:28.938 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-16 08:52:28.988 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-16 08:52:28.988 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-16 08:52:28.989 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-16 08:52:28.989 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-16 08:52:28.989 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-16 08:52:28.989 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: c1265ccc-962e-466c-807a-553a5381cd03
2025-07-16 08:52:28.990 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-16 08:52:28.990 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-16 08:52:28.991 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-16 08:52:28.991 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-16 08:52:28.992 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-16 08:52:28.992 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-16 08:52:28.993 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-16 08:52:28.993 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-16 08:52:28.993 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-16 08:52:29.003 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-16 08:52:29.004 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-16 08:52:29.004 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-16 08:52:29.004 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-16 08:52:29.004 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-16 08:52:29.004 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-16 08:52:29.004 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-16 08:52:29.005 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-16 08:52:29.005 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-16 08:52:29.005 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-16 08:52:29.005 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-16 08:52:29.005 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-16 08:52:29.006 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-16 08:52:29.006 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-16 08:52:29.006 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-16 08:52:29.006 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-16 08:52:29.006 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-16 08:52:29.007 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-16 08:52:29.012 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-16 08:52:29.012 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-16 08:52:29.012 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-16 08:52:29.013 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-16 08:52:29.018 [Information] CANRegisterAccess: Read value 0x2F from register 0x0141 (simulated)
2025-07-16 08:52:29.019 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-16 08:52:29.019 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-16 08:52:29.019 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-16 08:52:29.026 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-16 08:52:29.027 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-16 08:52:29.032 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-16 08:52:29.033 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-16 08:52:29.033 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-16 08:52:29.039 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-16 08:52:29.040 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-16 08:52:29.040 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-16 08:52:29.046 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-16 08:52:29.046 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-16 08:52:29.052 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-16 08:52:29.052 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-16 08:52:29.058 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-16 08:52:29.059 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-16 08:52:29.065 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-16 08:52:29.065 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-16 08:52:29.071 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-16 08:52:29.071 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-16 08:52:29.077 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-16 08:52:29.077 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-16 08:52:29.083 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-16 08:52:29.083 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-16 08:52:29.090 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-16 08:52:29.090 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-16 08:52:29.095 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-16 08:52:29.095 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-16 08:52:29.101 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-16 08:52:29.101 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-16 08:52:29.107 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-16 08:52:29.107 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-16 08:52:29.113 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-16 08:52:29.113 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-16 08:52:29.118 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-16 08:52:29.118 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-16 08:52:29.124 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-16 08:52:29.124 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-16 08:52:29.130 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-16 08:52:29.130 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-16 08:52:29.136 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-16 08:52:29.136 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-16 08:52:29.142 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-16 08:52:29.143 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-16 08:52:29.143 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-16 08:52:29.148 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-16 08:52:29.148 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-16 08:52:29.149 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-16 08:52:29.149 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-16 08:52:29.155 [Information] CANRegisterAccess: Read value 0xB6 from register 0x0141 (simulated)
2025-07-16 08:52:29.155 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-16 08:52:29.155 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-16 08:52:29.156 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-16 08:52:29.156 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-16 08:52:29.161 [Information] CANRegisterAccess: Read value 0x6C from register 0x0140 (simulated)
2025-07-16 08:52:29.167 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-16 08:52:29.173 [Information] CANRegisterAccess: Read value 0x7D from register 0x0140 (simulated)
2025-07-16 08:52:29.173 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-16 08:52:29.173 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-16 08:52:29.174 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-16 08:52:29.174 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-16 08:52:29.185 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-16 08:52:29.186 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-16 08:52:29.186 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-16 08:52:29.186 [Information] VocomService: Sending data and waiting for response
2025-07-16 08:52:29.187 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-16 08:52:29.237 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-16 08:52:29.237 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-16 08:52:29.238 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-16 08:52:29.238 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-16 08:52:29.238 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-16 08:52:29.249 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-16 08:52:29.249 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-16 08:52:29.249 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-16 08:52:29.260 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-16 08:52:29.270 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-16 08:52:29.281 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-16 08:52:29.291 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-16 08:52:29.302 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-16 08:52:29.302 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-16 08:52:29.303 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-16 08:52:29.314 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-16 08:52:29.314 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-16 08:52:29.315 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-16 08:52:29.326 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-16 08:52:29.336 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-16 08:52:29.347 [Information] IICProtocolHandler: Enabling IIC module
2025-07-16 08:52:29.357 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-16 08:52:29.367 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-16 08:52:29.378 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-16 08:52:29.378 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-16 08:52:29.378 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-16 08:52:29.390 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-16 08:52:29.390 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-16 08:52:29.391 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-16 08:52:29.391 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-16 08:52:29.391 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-16 08:52:29.391 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-16 08:52:29.391 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-16 08:52:29.392 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-16 08:52:29.392 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-16 08:52:29.392 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-16 08:52:29.392 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-16 08:52:29.392 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-16 08:52:29.392 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-16 08:52:29.393 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-16 08:52:29.393 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-16 08:52:29.393 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-16 08:52:29.393 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-16 08:52:29.495 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-16 08:52:29.495 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-16 08:52:29.495 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-16 08:52:29.496 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-16 08:52:29.496 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-16 08:52:29.496 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-16 08:52:29.496 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-16 08:52:29.496 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-16 08:52:29.497 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-16 08:52:29.497 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-16 08:52:29.497 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-16 08:52:29.497 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-16 08:52:29.497 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-16 08:52:29.498 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-16 08:52:29.498 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-16 08:52:29.549 [Information] BackupService: Initializing backup service
2025-07-16 08:52:29.549 [Information] BackupService: Backup service initialized successfully
2025-07-16 08:52:29.600 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-16 08:52:29.601 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-16 08:52:29.602 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_Export_With_Fix (1)\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-16 08:52:29.602 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-16 08:52:29.653 [Information] BackupService: Getting predefined backup categories
2025-07-16 08:52:29.704 [Information] MainViewModel: Services initialized successfully
2025-07-16 08:52:29.706 [Information] MainViewModel: Scanning for Vocom devices
2025-07-16 08:52:29.707 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-16 08:52:29.707 [Information] VocomService: Using new enhanced device detection service
2025-07-16 08:52:29.707 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-16 08:52:29.708 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-16 08:52:29.877 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-16 08:52:29.878 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-16 08:52:29.878 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-16 08:52:29.878 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-16 08:52:29.878 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-16 08:52:29.878 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-16 08:52:29.879 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-16 08:52:29.879 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-16 08:52:30.044 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-16 08:52:30.044 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-16 08:52:30.044 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-16 08:52:30.044 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-16 08:52:30.045 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-16 08:52:30.045 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-16 08:52:30.045 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-16 08:52:30.045 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-16 08:52:30.046 [Debug] VocomService: Bluetooth is enabled
2025-07-16 08:52:30.047 [Debug] VocomService: Checking if WiFi is available
2025-07-16 08:52:30.047 [Debug] VocomService: WiFi is available
2025-07-16 08:52:30.047 [Information] VocomService: Found 3 Vocom devices
2025-07-16 08:52:30.048 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-16 08:52:46.359 [Information] MainViewModel: Scanning for Vocom devices
2025-07-16 08:52:46.360 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-16 08:52:46.360 [Information] VocomService: Using new enhanced device detection service
2025-07-16 08:52:46.360 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-16 08:52:46.361 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-16 08:52:46.544 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-16 08:52:46.545 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-16 08:52:46.545 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-16 08:52:46.545 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-16 08:52:46.545 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-16 08:52:46.640 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-16 08:52:46.641 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-16 08:52:46.641 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-16 08:52:46.642 [Information] MainViewModel: Found 1 Vocom device(s)
2025-07-16 08:52:49.197 [Information] MainViewModel: Connecting to Vocom device 88890300
2025-07-16 08:52:49.198 [Information] VocomService: Connecting to Vocom device 88890300 via USB
2025-07-16 08:52:49.199 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-16 08:52:49.200 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-16 08:52:49.620 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-16 08:52:49.620 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-16 08:52:49.621 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-16 08:52:49.622 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-16 08:52:49.623 [Information] ECUCommunicationService: No ECUs are connected
2025-07-16 08:52:49.623 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-16 08:52:49.623 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-16 08:52:49.623 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-16 08:52:49.624 [Information] ECUCommunicationService: No ECUs are connected
2025-07-16 08:52:49.624 [Information] VocomService: Checking if PTT application is running
2025-07-16 08:52:49.633 [Information] VocomService: PTT application is not running
2025-07-16 08:52:49.633 [Information] VocomService: Connecting to Vocom device 88890300 via USB with enhanced capabilities
2025-07-16 08:52:49.634 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:49.634 [Information] VocomService: Checking if PTT application is running
2025-07-16 08:52:49.642 [Information] VocomService: PTT application is not running
2025-07-16 08:52:49.642 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:49.642 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:49.642 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:49.643 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:49.644 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:49.645 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:49.645 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:49.645 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:49.645 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:49.645 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:49.646 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:49.646 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:49.646 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-16 08:52:49.647 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:49.647 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-07-16 08:52:49.647 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-16 08:52:49.647 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-07-16 08:52:49.647 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-16 08:52:49.648 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:49.648 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:49.648 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-16 08:52:50.658 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:50.659 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:50.659 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:50.660 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:50.660 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:50.660 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:50.660 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:50.661 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:50.661 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:50.661 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:50.662 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:50.662 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-16 08:52:50.662 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:50.662 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-07-16 08:52:50.663 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-16 08:52:50.663 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-07-16 08:52:50.663 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-16 08:52:50.663 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:50.664 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:50.664 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-16 08:52:51.669 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:51.669 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:51.670 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:51.670 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:51.671 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:51.671 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:51.671 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:51.672 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:51.672 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:51.672 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:51.672 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:52:51.672 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-16 08:52:51.673 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:51.673 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-07-16 08:52:51.673 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-16 08:52:51.673 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-07-16 08:52:51.674 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-16 08:52:51.674 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:51.674 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:51.674 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-16 08:52:51.675 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-16 08:52:51.675 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:51.675 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-16 08:52:51.675 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:52:51.676 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-16 08:52:51.676 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-16 08:52:51.676 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-16 08:52:51.676 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-16 08:52:51.677 [Information] ModernUSBCommunicationService: CLR Version: 8.0.16
2025-07-16 08:52:51.677 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-16 08:52:51.677 [Information] ModernUSBCommunicationService: Found 9 HID devices total
2025-07-16 08:52:51.677 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-07-16 08:52:51.678 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-16 08:52:51.678 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-16 08:52:51.678 [Error] VocomService: Standard USB connection failed for device 88890300
2025-07-16 08:52:51.678 [Error] VocomService: All USB connection methods failed for device 88890300
2025-07-16 08:52:51.679 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-07-16 08:52:51.679 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300
2025-07-16 08:52:51.679 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300
2025-07-16 08:52:51.680 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-07-16 08:52:51.680 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300
2025-07-16 08:52:51.680 [Error] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-07-16 08:52:51.681 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-16 08:52:51.681 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-16 08:52:51.681 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-16 08:52:51.681 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-16 08:52:51.681 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-16 08:52:51.682 [Error] MainViewModel: Failed to connect to Vocom device 88890300
2025-07-16 08:53:00.433 [Information] MainViewModel: Connecting to Vocom device 88890300
2025-07-16 08:53:00.433 [Information] VocomService: Connecting to Vocom device 88890300 via USB
2025-07-16 08:53:00.434 [Information] VocomService: Checking if PTT application is running
2025-07-16 08:53:00.442 [Information] VocomService: PTT application is not running
2025-07-16 08:53:00.442 [Information] VocomService: Connecting to Vocom device 88890300 via USB with enhanced capabilities
2025-07-16 08:53:00.442 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:00.442 [Information] VocomService: Checking if PTT application is running
2025-07-16 08:53:00.451 [Information] VocomService: PTT application is not running
2025-07-16 08:53:00.451 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:00.451 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:00.452 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:00.452 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:00.452 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:00.453 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:00.453 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:00.453 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:00.453 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:00.453 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:00.454 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:00.454 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:00.454 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-16 08:53:00.455 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:00.455 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-07-16 08:53:00.455 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-16 08:53:00.455 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-07-16 08:53:00.455 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-16 08:53:00.456 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:00.456 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:00.456 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-16 08:53:01.468 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:01.469 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:01.469 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:01.470 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:01.470 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:01.470 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:01.470 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:01.470 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:01.471 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:01.471 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:01.471 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:01.471 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-16 08:53:01.472 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:01.472 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-07-16 08:53:01.472 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-16 08:53:01.472 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-07-16 08:53:01.473 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-16 08:53:01.473 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:01.473 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:01.473 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-16 08:53:02.488 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:02.489 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:02.489 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:02.490 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col01#3&36a7043c&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:02.490 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#intc816&col02#3&36a7043c&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:02.490 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col01#5&4e40498&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:02.490 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col02#5&379854aa&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:02.491 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col03#5&379854aa&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:02.491 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col02#5&4e40498&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:02.491 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col03#5&4e40498&0&0002#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:02.491 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#dll07bf&col04#5&4e40498&0&0003#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-16 08:53:02.492 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#converteddevice&col01#5&379854aa&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-16 08:53:02.492 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:02.492 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#0000007658
2025-07-16 08:53:02.493 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-16 08:53:02.493 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#0000007658
2025-07-16 08:53:02.493 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-16 08:53:02.493 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:02.493 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:02.494 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-16 08:53:02.494 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-16 08:53:02.494 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:02.494 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-16 08:53:02.495 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-16 08:53:02.495 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-16 08:53:02.495 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-16 08:53:02.495 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-16 08:53:02.495 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-16 08:53:02.496 [Information] ModernUSBCommunicationService: CLR Version: 8.0.16
2025-07-16 08:53:02.496 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-16 08:53:02.496 [Information] ModernUSBCommunicationService: Found 9 HID devices total
2025-07-16 08:53:02.496 [Debug] ModernUSBCommunicationService: Checking HID device: VID=06CB, PID=7A13, Name=HIDI2C Device
2025-07-16 08:53:02.497 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-16 08:53:02.497 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-16 08:53:02.497 [Error] VocomService: Standard USB connection failed for device 88890300
2025-07-16 08:53:02.497 [Error] VocomService: All USB connection methods failed for device 88890300
2025-07-16 08:53:02.498 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-07-16 08:53:02.498 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300
2025-07-16 08:53:02.498 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300
2025-07-16 08:53:02.498 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300
2025-07-16 08:53:02.499 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300
2025-07-16 08:53:02.499 [Error] VocomService: Failed to connect to Vocom device 88890300 via USB
2025-07-16 08:53:02.499 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-16 08:53:02.500 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-16 08:53:02.500 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-16 08:53:02.500 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-16 08:53:02.500 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300 via USB
2025-07-16 08:53:02.500 [Error] MainViewModel: Failed to connect to Vocom device 88890300
