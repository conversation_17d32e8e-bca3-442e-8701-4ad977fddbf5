[2025-03-19 14:51:33.049-07]	[  1]	[DEBUG]	VolvoIt.Baf.Core.Ui.CrashMessage - >>> FLIGHT RECORDER START >>>
[2025-03-19 14:51:33.049-07]	[  1]	[DEBUG]	VolvoIt.Baf.Core.Ui.CrashMessage - ProcessCrashedMessageBox.InitializeStyles  Entered
[2025-03-19 14:51:33.205-07]	[  1]	[DEBUG]	VolvoIt.Baf.Core.Ui.CrashMessage - ProcessCrashedMessageBox.InitializeStyles  Leaving
[2025-03-19 14:51:33.205-07]	[  1]	[DEBUG]	VolvoIt.Baf.Core.Ui.CrashMessage - ProcessCrashedMessageBox.SetCurrentUICulture  Setting argument en-GB language.
[2025-03-19 14:51:33.399-07]	[  1]	[DEBUG]	VolvoIt.Baf.Core.Ui.CrashMessage - ProcessCrashedMessageBox.ConnectToServiceMonitor  Entered
[2025-03-19 14:51:33.440-07]	[  1]	[ERROR]	VolvoIt.Baf.Core.Ui.CrashMessage - ProcessCrashedMessageBox.ConnectToServiceMonitor  
Level: 0
Source: mscorlib
EndpointNotFoundException: There was no endpoint listening at net.pipe://localhost/ServiceMonitorService that could accept the message. This is often caused by an incorrect address or SOAP action. See InnerException, if present, for more details.
StackTrace:
Server stack trace: 
   at System.ServiceModel.Channels.PipeConnectionInitiator.GetPipeName(Uri uri, IPipeTransportFactorySettings transportFactorySettings)
   at System.ServiceModel.Channels.NamedPipeConnectionPoolRegistry.NamedPipeConnectionPool.GetPoolKey(EndpointAddress address, Uri via)
   at System.ServiceModel.Channels.CommunicationPool`2.TakeConnection(EndpointAddress address, Uri via, TimeSpan timeout, TKey& key)
   at System.ServiceModel.Channels.ConnectionPoolHelper.EstablishConnection(TimeSpan timeout)
   at System.ServiceModel.Channels.ClientFramingDuplexSessionChannel.OnOpen(TimeSpan timeout)
   at System.ServiceModel.Channels.CommunicationObject.Open(TimeSpan timeout)
   at System.ServiceModel.Channels.ServiceChannel.OnOpen(TimeSpan timeout)
   at System.ServiceModel.Channels.CommunicationObject.Open(TimeSpan timeout)
   at System.ServiceModel.Channels.ServiceChannel.CallOpenOnce.System.ServiceModel.Channels.ServiceChannel.ICallOnce.Call(ServiceChannel channel, TimeSpan timeout)
   at System.ServiceModel.Channels.ServiceChannel.CallOnceManager.CallOnce(TimeSpan timeout, CallOnceManager cascade)
   at System.ServiceModel.Channels.ServiceChannel.Call(String action, Boolean oneway, ProxyOperationRuntime operation, Object[] ins, Object[] outs, TimeSpan timeout)
   at System.ServiceModel.Channels.ServiceChannelProxy.InvokeService(IMethodCallMessage methodCall, ProxyOperationRuntime operation)
   at System.ServiceModel.Channels.ServiceChannelProxy.Invoke(IMessage message)
Exception rethrown at [0]: 
   at System.Runtime.Remoting.Proxies.RealProxy.HandleReturnMessage(IMessage reqMsg, IMessage retMsg)
   at System.Runtime.Remoting.Proxies.RealProxy.PrivateInvoke(MessageData& msgData, Int32 type)
   at VolvoIt.Baf.Utility.ServiceContracts.ServiceMonitorService.IServiceMonitorService.GetCurrentStatus()
   at VolvoIt.Baf.Core.Ui.CrashMessage.ProcessCrashedMessageBox.ConnectToServiceMonitor()

   Level: 1
   Source: 
   PipeException: The pipe endpoint 'net.pipe://localhost/ServiceMonitorService' could not be found on your local machine. 
   StackTrace:<empty>

[2025-03-19 14:51:33.452-07]	[  2]	[DEBUG]	VolvoIt.Baf.Core.Ui.CrashMessage - ProcessCrashedMessageBox.EvaluateServiceStatus  ServiceMonitor found an irrecoverable error. Closing...
[2025-03-19 14:54:03.053-07]	[  2]	[DEBUG]	VolvoIt.Baf.Core.Ui.CrashMessage - ProcessCrashedMessageBox.ConnectToServiceMonitor  Leaving
[2025-03-19 14:54:03.053-07]	[  2]	[DEBUG]	VolvoIt.Baf.Core.Ui.CrashMessage - <<< FLIGHT RECORDER END   <<<
