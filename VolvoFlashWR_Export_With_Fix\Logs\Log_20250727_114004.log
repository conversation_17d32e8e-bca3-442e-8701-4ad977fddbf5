Log started at 7/27/2025 11:40:04 AM
2025-07-27 11:40:04.381 [Information] LoggingService: Logging service initialized
2025-07-27 11:40:04.398 [Information] App: Starting integrated application initialization
2025-07-27 11:40:04.400 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-27 11:40:04.403 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-27 11:40:04.405 [Information] IntegratedStartupService: Setting up application environment
2025-07-27 11:40:04.406 [Information] IntegratedStartupService: Application environment setup completed
2025-07-27 11:40:04.409 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-27 11:40:04.411 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-27 11:40:04.414 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-27 11:40:04.419 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-27 11:40:04.432 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-27 11:40:04.442 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:40:04.450 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 11:40:04.459 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 11:40:04.467 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 11:40:04.476 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 11:40:04.483 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 11:40:04.485 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-27 11:40:04.492 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-27 11:40:04.492 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-27 11:40:04.494 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-27 11:40:04.495 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-27 11:40:04.495 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-27 11:40:04.496 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-27 11:40:04.496 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-27 11:40:04.500 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-27 11:40:04.501 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:40:04.502 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 11:40:04.502 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 11:40:04.502 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 11:40:04.503 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 11:40:04.503 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 11:40:04.511 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-27 11:40:04.512 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-27 11:40:04.512 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-27 11:40:04.519 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-27 11:40:04.519 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-27 11:40:04.522 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-27 11:40:04.524 [Information] LibraryExtractor: Starting library extraction process
2025-07-27 11:40:04.527 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-27 11:40:04.531 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-27 11:40:04.533 [Information] LibraryExtractor: Copying system libraries
2025-07-27 11:40:04.542 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-27 11:40:04.550 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-27 11:40:34.562 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 11:40:35.566 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-27 11:41:05.571 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 11:41:05.572 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:41:35.575 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 11:41:36.576 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-27 11:42:06.580 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 11:42:06.581 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 11:42:36.583 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 11:42:37.584 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-27 11:43:07.591 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 11:43:07.592 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-27 11:43:07.593 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-27 11:43:07.597 [Information] LibraryExtractor: Verifying library extraction
2025-07-27 11:43:07.598 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-27 11:43:07.598 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-27 11:43:07.599 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-27 11:43:07.599 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-27 11:43:07.600 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-27 11:43:07.606 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-27 11:43:07.609 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-27 11:43:07.610 [Information] DependencyManager: Initializing dependency manager
2025-07-27 11:43:07.611 [Information] DependencyManager: Setting up library search paths
2025-07-27 11:43:07.613 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 11:43:07.613 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 11:43:07.614 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 11:43:07.614 [Information] DependencyManager: Updated PATH environment variable
2025-07-27 11:43:07.616 [Information] DependencyManager: Verifying required directories
2025-07-27 11:43:07.618 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 11:43:07.618 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 11:43:07.619 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-27 11:43:07.620 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 11:43:07.622 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-27 11:43:07.628 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 11:43:07.630 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 11:43:07.630 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 11:43:07.632 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 11:43:07.634 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 11:43:07.635 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 11:43:07.636 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 11:43:07.639 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll from C:\Windows\system32\msvcp140.dll: Error 193
2025-07-27 11:43:07.639 [Warning] DependencyManager: Architecture mismatch detected for msvcp140.dll. Expected: x64
2025-07-27 11:43:07.640 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp140.dll
2025-07-27 11:43:07.640 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll: Error 193
2025-07-27 11:43:07.641 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp140.dll
2025-07-27 11:43:07.641 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 11:43:07.642 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 11:43:07.643 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:43:07.644 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:43:07.645 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 11:43:07.645 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 11:43:07.647 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 11:43:07.647 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 11:43:07.648 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 11:43:07.648 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 11:43:07.649 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 11:43:07.649 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 11:43:07.650 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 11:43:07.651 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 11:43:07.652 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 11:43:07.653 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 11:43:07.654 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 11:43:07.654 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 11:43:07.655 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 11:43:07.656 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 11:43:07.656 [Information] DependencyManager: VC++ Redistributable library loading: 3/14 (21.4%) libraries loaded
2025-07-27 11:43:07.657 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-27 11:43:07.658 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-27 11:43:07.659 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-27 11:43:07.660 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-27 11:43:07.661 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 11:43:07.661 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-27 11:43:07.662 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-27 11:43:07.663 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-07-27 11:43:07.663 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-27 11:43:07.664 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-27 11:43:08.005 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-07-27 11:43:08.007 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-27 11:43:08.008 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-27 11:43:08.009 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-07-27 11:43:08.010 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-27 11:43:08.011 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 11:43:08.012 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 11:43:08.012 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 11:43:08.013 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 11:43:08.013 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 11:43:08.014 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 11:43:08.015 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-27 11:43:08.016 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 11:43:08.018 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-27 11:43:08.018 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 11:43:08.019 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 11:43:08.020 [Information] DependencyManager: Setting up environment variables
2025-07-27 11:43:08.020 [Information] DependencyManager: Environment variables configured
2025-07-27 11:43:08.022 [Information] DependencyManager: Verifying library loading status
2025-07-27 11:43:08.377 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-27 11:43:08.378 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-27 11:43:08.378 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-27 11:43:08.380 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-27 11:43:08.382 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-27 11:43:08.387 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-27 11:43:08.389 [Information] IntegratedStartupService: Verifying system readiness
2025-07-27 11:43:08.390 [Information] IntegratedStartupService: System readiness verification passed
2025-07-27 11:43:08.390 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-27 11:43:08.391 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-27 11:43:08.392 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 11:43:08.392 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 11:43:08.393 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 11:43:08.393 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-27 11:43:08.394 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-27 11:43:08.394 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-27 11:43:08.394 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 11:43:08.394 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-27 11:43:08.395 [Information] App: Integrated startup completed successfully
2025-07-27 11:43:08.398 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-27 11:43:08.661 [Information] App: Initializing application services
2025-07-27 11:43:08.663 [Information] AppConfigurationService: Initializing configuration service
2025-07-27 11:43:08.663 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 11:43:08.713 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 11:43:08.714 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-27 11:43:08.715 [Information] App: Configuration service initialized successfully
2025-07-27 11:43:08.717 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-27 11:43:08.717 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-27 11:43:08.724 [Information] App: Environment variable exists: True, not 'false': False
2025-07-27 11:43:08.725 [Information] App: Final useDummyImplementations value: False
2025-07-27 11:43:08.726 [Information] App: Updating config to NOT use dummy implementations
2025-07-27 11:43:08.747 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 11:43:08.748 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-27 11:43:08.749 [Information] App: usePatchedImplementation flag is: True
2025-07-27 11:43:08.749 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-27 11:43:08.749 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-27 11:43:08.750 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-27 11:43:08.750 [Information] App: verboseLogging flag is: True
2025-07-27 11:43:08.752 [Information] App: Verifying real hardware requirements...
2025-07-27 11:43:08.753 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-27 11:43:08.753 [Information] App: ✓ Found critical library: apci.dll
2025-07-27 11:43:08.754 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-27 11:43:08.754 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-27 11:43:08.755 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 11:43:08.756 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-27 11:43:08.756 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-27 11:43:08.757 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-27 11:43:08.768 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-27 11:43:08.771 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-27 11:43:08.772 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-27 11:43:08.774 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-27 11:43:08.780 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
