Log started at 7/16/2025 9:28:10 PM
2025-07-16 21:28:10.299 [Information] LoggingService: Logging service initialized
2025-07-16 21:28:10.316 [Information] App: Starting integrated application initialization
2025-07-16 21:28:10.317 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-16 21:28:10.320 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-16 21:28:10.323 [Information] IntegratedStartupService: Setting up application environment
2025-07-16 21:28:10.323 [Information] IntegratedStartupService: Application environment setup completed
2025-07-16 21:28:10.325 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-16 21:28:10.327 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-16 21:28:10.329 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-16 21:28:10.333 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-16 21:28:10.339 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 21:28:10.342 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.344 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.344 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-16 21:28:10.346 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 21:28:10.347 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.349 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.349 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 21:28:10.351 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 21:28:10.352 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.353 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.353 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 21:28:10.355 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 21:28:10.356 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.357 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.357 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 21:28:10.359 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 21:28:10.361 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.362 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.362 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 21:28:10.364 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 21:28:10.365 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.366 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.367 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-16 21:28:10.368 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 21:28:10.369 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.370 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 21:28:10.371 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-16 21:28:10.372 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-16 21:28:10.374 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-16 21:28:10.374 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-16 21:28:10.454 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-16 21:28:10.454 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-16 21:28:10.455 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-16 21:28:10.455 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-16 21:28:10.456 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-16 21:28:10.513 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-16 21:28:10.513 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-16 21:28:10.515 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-16 21:28:10.516 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 21:28:10.516 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 21:28:10.517 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 21:28:10.517 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 21:28:10.517 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-16 21:28:10.517 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-16 21:28:10.523 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-16 21:28:10.524 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-16 21:28:10.524 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-16 21:28:10.528 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-16 21:28:10.528 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-16 21:28:10.530 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-16 21:28:10.532 [Information] LibraryExtractor: Starting library extraction process
2025-07-16 21:28:10.534 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-16 21:28:10.537 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-16 21:28:10.538 [Information] LibraryExtractor: Copying system libraries
2025-07-16 21:28:10.542 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-16 21:28:10.548 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-16 21:33:10.558 [Warning] LibraryExtractor: Failed to download and extract redistributable for msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-16 21:33:10.560 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 21:38:10.562 [Warning] LibraryExtractor: Failed to download and extract redistributable for api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-16 21:38:10.563 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
