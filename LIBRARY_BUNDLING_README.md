# VolvoFlashWR Library Bundling System

## Overview

This system allows you to pre-download and bundle all required Visual C++ runtime libraries and dependencies with the VolvoFlashWR application, eliminating the need to download them every time the application starts.

## Benefits

- **Faster Startup**: No more 5-7 minute download delays on first run
- **Offline Operation**: Application works without internet connection
- **System Stability**: Avoids potential system restarts during library downloads
- **Reliability**: Pre-tested libraries ensure compatibility

## Quick Start

### Option 1: Automatic Bundling (Recommended)

1. **Run the bundling script:**
   ```batch
   Bundle_All_Libraries.bat
   ```

2. **Start the application:**
   ```batch
   VolvoFlashWR_Export_With_Fix\Run_Normal_Mode.bat
   ```

### Option 2: Manual Bundling

1. **Download Visual C++ Redistributables manually:**
   - [Visual C++ 2015-2022 Redistributable (x64)](https://aka.ms/vs/17/release/vc_redist.x64.exe)
   - [Visual C++ 2013 Redistributable (x64)](https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x64.exe)

2. **Run the PowerShell script:**
   ```powershell
   .\Download_And_Bundle_Libraries.ps1 -OutputPath ".\VolvoFlashWR_Export_With_Fix\Libraries"
   ```

## Required Libraries

The system bundles the following critical libraries:

### Visual C++ 2013 Runtime
- `msvcr120.dll` - Microsoft Visual C++ 2013 Runtime
- `msvcp120.dll` - Microsoft Visual C++ 2013 C++ Runtime

### Visual C++ 2015-2022 Runtime
- `msvcr140.dll` - Microsoft Visual C++ 2015-2022 Runtime
- `msvcp140.dll` - Microsoft Visual C++ 2015-2022 C++ Runtime
- `vcruntime140.dll` - Microsoft Visual C++ 2015-2022 Runtime

### Universal C Runtime (UCRT)
- `api-ms-win-crt-runtime-l1-1-0.dll` - Universal CRT Runtime
- `api-ms-win-crt-heap-l1-1-0.dll` - Universal CRT Heap
- `api-ms-win-crt-string-l1-1-0.dll` - Universal CRT String
- `api-ms-win-crt-stdio-l1-1-0.dll` - Universal CRT Standard I/O
- `api-ms-win-crt-math-l1-1-0.dll` - Universal CRT Math
- `api-ms-win-crt-locale-l1-1-0.dll` - Universal CRT Locale

### Vocom Communication Libraries
- `WUDFPuma.dll` - Vocom 1 Adapter Driver
- `apci.dll` - APCI Communication Library

## Directory Structure

```
S.A.H.VolvoFlashWR/
├── Bundle_All_Libraries.bat              # Easy-to-use bundling script
├── Download_And_Bundle_Libraries.ps1     # PowerShell bundling script
├── Bundle_VCRedist_Libraries.ps1         # Legacy bundling script
├── VolvoFlashWR_Export_With_Fix/
│   ├── Libraries/                        # Bundled libraries directory
│   │   ├── msvcr120.dll
│   │   ├── msvcr140.dll
│   │   ├── api-ms-win-crt-*.dll
│   │   └── ...
│   ├── Run_Normal_Mode.bat              # Application launcher
│   └── VolvoFlashWR.exe                 # Main application
└── LIBRARY_BUNDLING_README.md           # This file
```

## How It Works

### Application Priority Order

The application now uses the following priority order when loading libraries:

1. **Pre-bundled Libraries** - From `Libraries/` directory (fastest)
2. **System Libraries** - From Windows system directories
3. **Embedded Resources** - From application resources
4. **Download Fallback** - Download from internet (slowest)

### Bundling Process

1. **Download**: Downloads Visual C++ Redistributable packages
2. **Extract**: Extracts individual DLL files from packages
3. **Copy**: Copies system libraries if available
4. **Verify**: Verifies all required libraries are present

## Troubleshooting

### PowerShell Execution Policy

If you get an execution policy error:

```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Missing Libraries

If some libraries cannot be bundled automatically:

1. Install Visual C++ Redistributables manually
2. Run the bundling script again with `-Force` parameter
3. Check the application logs for specific missing libraries

### Verification

Check if bundling was successful:

1. Look for `Libraries/` directory in `VolvoFlashWR_Export_With_Fix/`
2. Verify DLL files are present
3. Run the application and check logs for "Copied pre-bundled library" messages

## Advanced Usage

### PowerShell Script Parameters

```powershell
.\Download_And_Bundle_Libraries.ps1 [parameters]

Parameters:
  -OutputPath <string>    # Output directory for bundled libraries
  -Force                  # Force re-download and overwrite existing files
  -DownloadOnly          # Only download redistributables, don't extract
  -Verbose               # Show detailed progress information
```

### Environment Variables

- `SKIP_VCREDIST_DOWNLOAD=true` - Disables library downloading entirely

## Support

If you encounter issues:

1. Check the application logs in `VolvoFlashWR_Export_With_Fix\Logs\`
2. Verify all required Visual C++ Redistributables are installed
3. Run the bundling script with `-Verbose` flag for detailed output
4. Ensure you have sufficient disk space and internet connectivity

## Notes

- The bundling process requires internet connectivity for initial download
- Bundled libraries are architecture-specific (x64)
- Some libraries may require administrator privileges to access
- The application will fall back to downloading if bundled libraries are corrupted or missing
