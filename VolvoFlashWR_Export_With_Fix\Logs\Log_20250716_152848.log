Log started at 7/16/2025 3:28:48 PM
2025-07-16 15:28:48.600 [Information] LoggingService: Logging service initialized
2025-07-16 15:28:48.614 [Information] App: Starting integrated application initialization
2025-07-16 15:28:48.615 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-16 15:28:48.618 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-16 15:28:48.619 [Information] IntegratedStartupService: Setting up application environment
2025-07-16 15:28:48.619 [Information] IntegratedStartupService: Application environment setup completed
2025-07-16 15:28:48.621 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-16 15:28:48.623 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-16 15:28:48.625 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-16 15:28:48.628 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-16 15:28:48.634 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.635 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.636 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.637 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-16 15:28:48.639 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.640 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.641 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.642 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 15:28:48.645 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.647 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.648 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.648 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 15:28:48.650 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.651 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.652 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.652 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 15:28:48.654 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.655 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.656 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.656 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 15:28:48.658 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.659 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.660 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.660 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-16 15:28:48.662 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-16 15:28:48.663 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.664 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-16 15:28:48.665 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-16 15:28:48.667 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-16 15:28:48.669 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-16 15:28:48.669 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-16 15:28:48.670 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-16 15:28:48.671 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-16 15:28:48.671 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-16 15:28:48.671 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-16 15:28:48.671 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-16 15:28:48.673 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-16 15:28:48.674 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-16 15:28:48.675 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-16 15:28:48.675 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 15:28:48.675 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 15:28:48.675 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 15:28:48.675 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 15:28:48.676 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-16 15:28:48.676 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-16 15:28:48.681 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-16 15:28:48.682 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-16 15:28:48.682 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-16 15:28:48.689 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-16 15:28:48.689 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-16 15:28:48.690 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-16 15:28:48.691 [Information] LibraryExtractor: Starting library extraction process
2025-07-16 15:28:48.694 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-16 15:28:48.698 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-16 15:28:48.700 [Information] LibraryExtractor: Copying system libraries
2025-07-16 15:28:48.705 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-16 15:28:48.710 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-16 15:32:19.776 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-16 15:36:14.671 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-16 15:40:38.776 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-16 15:44:31.390 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-16 15:48:01.176 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-math-l1-1-0.dll
