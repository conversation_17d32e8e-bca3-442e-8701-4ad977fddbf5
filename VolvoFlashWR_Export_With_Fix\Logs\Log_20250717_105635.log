Log started at 7/17/2025 10:56:35 AM
2025-07-17 10:56:35.410 [Information] LoggingService: Logging service initialized
2025-07-17 10:56:35.424 [Information] App: Starting integrated application initialization
2025-07-17 10:56:35.426 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-17 10:56:35.428 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-17 10:56:35.430 [Information] IntegratedStartupService: Setting up application environment
2025-07-17 10:56:35.431 [Information] IntegratedStartupService: Application environment setup completed
2025-07-17 10:56:35.432 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-17 10:56:35.434 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-17 10:56:35.436 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-17 10:56:35.441 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-17 10:56:35.448 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-17 10:56:35.452 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.453 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.454 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-17 10:56:35.455 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-17 10:56:35.457 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.458 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.458 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-17 10:56:35.461 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-17 10:56:35.463 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.464 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.465 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-17 10:56:35.467 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-17 10:56:35.468 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.469 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.470 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-17 10:56:35.472 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-17 10:56:35.473 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.474 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.474 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-17 10:56:35.476 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-17 10:56:35.478 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.479 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.480 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-17 10:56:35.483 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-17 10:56:35.484 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.485 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-17 10:56:35.485 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-17 10:56:35.487 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-17 10:56:35.489 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-17 10:56:35.489 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-17 10:56:35.490 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-17 10:56:35.490 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-17 10:56:35.491 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-17 10:56:35.491 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-17 10:56:35.491 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-17 10:56:35.495 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-17 10:56:35.496 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-17 10:56:35.497 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-17 10:56:35.498 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-17 10:56:35.498 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-17 10:56:35.498 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-17 10:56:35.499 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-17 10:56:35.499 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-17 10:56:35.499 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-17 10:56:35.505 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-17 10:56:35.505 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-17 10:56:35.505 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-17 10:56:35.511 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-17 10:56:35.512 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-17 10:56:35.515 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-17 10:56:35.518 [Information] LibraryExtractor: Starting library extraction process
2025-07-17 10:56:35.522 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-17 10:56:35.527 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-17 10:56:35.529 [Information] LibraryExtractor: Copying system libraries
2025-07-17 10:56:35.537 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-17 10:56:35.559 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-17 11:01:35.576 [Warning] LibraryExtractor: Failed to download and extract redistributable for msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-17 11:01:35.577 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-17 11:06:35.580 [Warning] LibraryExtractor: Failed to download and extract redistributable for api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-17 11:06:35.580 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-17 11:11:35.584 [Warning] LibraryExtractor: Failed to download and extract redistributable for api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-17 11:11:35.585 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-17 11:16:35.588 [Warning] LibraryExtractor: Failed to download and extract redistributable for api-ms-win-crt-string-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-17 11:16:35.588 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-stdio-l1-1-0.dll
