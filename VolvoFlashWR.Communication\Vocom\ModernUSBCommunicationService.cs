using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using HidSharp;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Modern USB communication service for Vocom adapters using LibUsbDotNet and HidSharp
    /// Replaces legacy Phoenix APCI libraries with modern .NET 8.0 compatible packages
    /// </summary>
    public class ModernUSBCommunicationService : IUSBCommunicationService
    {
        private readonly ILoggingService _logger;
        private bool _isConnected;
        private HidDevice? _hidDevice;
        private HidStream? _hidStream;

        // Vocom adapter USB identifiers (updated for real hardware)
        private const int VOCOM_VENDOR_ID = 0x178E;  // Real Vocom adapter VID from logs
        private const int VOCOM_PRODUCT_ID = 0x0024; // Real Vocom adapter PID from logs

        // Alternative identifiers to try (based on real Vocom hardware)
        private static readonly (int VendorId, int ProductId)[] VocomIdentifiers = new[]
        {
            (0x178E, 0x0024), // Real Vocom adapter (from logs - primary)
            (0x1A12, 0x0001), // CSR based (Vocom 1 - alternative)
            (0x0BDA, 0x8150), // Realtek based (alternative)
            (0x04B4, 0x1004), // Cypress based (alternative)
            (0x0403, 0x6001), // FTDI based (alternative)
            (0x10C4, 0xEA60), // Silicon Labs CP210x (alternative)
            (0x067B, 0x2303), // Prolific PL2303 (alternative)
        };

        // Vocom device names to search for
        private static readonly string[] VocomDeviceNames = new[]
        {
            "Vocom - 88890300",
            "88890300",
            "Vocom",
            "Communication Unit",
            "Bluetooth Adapter",
            "CSR8510"
        };

        public ModernUSBCommunicationService(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public bool IsConnected => _isConnected;

        public async Task<bool> ConnectAsync(VocomConnectionSettings settings)
        {
            try
            {
                _logger.LogInformation("Attempting modern USB connection to Vocom adapter", "ModernUSBCommunicationService");

                // Log system information for debugging
                LogSystemInformation();

                // Try HidSharp for device connection
                if (await TryConnectWithHidSharpAsync())
                {
                    _isConnected = true;
                    _logger.LogInformation("USB connection established successfully using HidSharp", "ModernUSBCommunicationService");
                    return true;
                }

                _logger.LogWarning("No Vocom adapter found via USB", "ModernUSBCommunicationService");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to establish USB connection", "ModernUSBCommunicationService", ex);
                return false;
            }
        }

        private void LogSystemInformation()
        {
            try
            {
                _logger.LogInformation($"Operating System: {Environment.OSVersion}", "ModernUSBCommunicationService");
                _logger.LogInformation($"Is 64-bit OS: {Environment.Is64BitOperatingSystem}", "ModernUSBCommunicationService");
                _logger.LogInformation($"Is 64-bit Process: {Environment.Is64BitProcess}", "ModernUSBCommunicationService");
                _logger.LogInformation($"CLR Version: {Environment.Version}", "ModernUSBCommunicationService");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to log system information: {ex.Message}", "ModernUSBCommunicationService");
            }
        }



        private async Task<bool> TryConnectWithHidSharpAsync()
        {
            try
            {
                _logger.LogInformation("Searching for Vocom adapter using HidSharp", "ModernUSBCommunicationService");

                // Find HID devices that might be Vocom adapters
                var devices = DeviceList.Local.GetHidDevices();
                _logger.LogInformation($"Found {devices.Count()} HID devices total", "ModernUSBCommunicationService");

                foreach (var device in devices)
                {
                    _logger.LogDebug($"Checking HID device: VID={device.VendorID:X4}, PID={device.ProductID:X4}, Name={device.GetProductName()}", "ModernUSBCommunicationService");

                    // Check against known Vocom identifiers
                    foreach (var (vendorId, productId) in VocomIdentifiers)
                    {
                        if (device.VendorID == vendorId && device.ProductID == productId)
                        {
                            try
                            {
                                _logger.LogInformation($"Found matching Vocom device: VID={vendorId:X4}, PID={productId:X4}", "ModernUSBCommunicationService");

                                // Try to open the device
                                try
                                {
                                    _hidDevice = device;
                                    _hidStream = device.Open();

                                    _logger.LogInformation($"Successfully connected to Vocom HID device: {device.GetProductName()}", "ModernUSBCommunicationService");
                                    return true;
                                }
                                catch (UnauthorizedAccessException)
                                {
                                    _logger.LogWarning($"Vocom device found but access denied (may be in use): {device.GetProductName()}", "ModernUSBCommunicationService");
                                }
                            }
                            catch (UnauthorizedAccessException ex)
                            {
                                _logger.LogWarning($"Access denied to HID device (may be in use by another application): {ex.Message}", "ModernUSBCommunicationService");
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning($"Failed to open HID device: {ex.Message}", "ModernUSBCommunicationService");
                            }
                        }
                    }
                }

                // Try alternative device enumeration methods
                await TryAlternativeDeviceEnumeration();

                _logger.LogInformation("No compatible Vocom HID device found", "ModernUSBCommunicationService");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"HidSharp connection failed: {ex.Message}", "ModernUSBCommunicationService");
                return false;
            }
        }

        private async Task TryAlternativeDeviceEnumeration()
        {
            try
            {
                _logger.LogInformation("Trying alternative device enumeration methods", "ModernUSBCommunicationService");

                // Method 1: Try to refresh device list
                DeviceList.Local.Changed += (sender, e) => {
                    _logger.LogDebug("Device list changed, refreshing", "ModernUSBCommunicationService");
                };

                // Method 2: Try different device access patterns
                var allDevices = DeviceList.Local.GetHidDevices();
                foreach (var device in allDevices)
                {
                    if (device.VendorID == VOCOM_VENDOR_ID && device.ProductID == VOCOM_PRODUCT_ID)
                    {
                        _logger.LogInformation($"Found Vocom device via alternative enumeration: {device.DevicePath}", "ModernUSBCommunicationService");

                        // Try to open with different access modes
                        await TryOpenDeviceWithDifferentModes(device);
                    }
                }

                // Method 3: Check for devices that might be enumerated differently
                await CheckForVocomDeviceVariants();
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Alternative device enumeration failed: {ex.Message}", "ModernUSBCommunicationService");
            }
        }

        private async Task TryOpenDeviceWithDifferentModes(HidDevice device)
        {
            try
            {
                // Try to open the device
                try
                {
                    _logger.LogDebug("Trying to open device", "ModernUSBCommunicationService");

                    var stream = device.Open();
                    if (stream != null)
                    {
                        _hidDevice = device;
                        _hidStream = stream;
                        _logger.LogInformation("Successfully opened device", "ModernUSBCommunicationService");
                        return;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug($"Failed to open device: {ex.Message}", "ModernUSBCommunicationService");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error trying different open modes: {ex.Message}", "ModernUSBCommunicationService");
            }
        }

        private async Task CheckForVocomDeviceVariants()
        {
            try
            {
                // Check for different Vocom device variants that might have different IDs
                var vocomVariants = new[]
                {
                    (0x178E, 0x0024), // Standard Vocom
                    (0x178E, 0x0020), // Alternative Vocom
                    (0x178E, 0x0025), // Another variant
                    (0x0403, 0x6001), // FTDI-based Vocom
                    (0x0403, 0x6014)  // Another FTDI variant
                };

                var devices = DeviceList.Local.GetHidDevices();
                foreach (var device in devices)
                {
                    foreach (var (vid, pid) in vocomVariants)
                    {
                        if (device.VendorID == vid && device.ProductID == pid)
                        {
                            _logger.LogInformation($"Found potential Vocom variant: VID={vid:X4}, PID={pid:X4}, Name={device.GetProductName()}", "ModernUSBCommunicationService");

                            // Try to connect to this variant
                            try
                            {
                                var stream = device.Open();
                                _hidDevice = device;
                                _hidStream = stream;
                                _logger.LogInformation($"Connected to Vocom variant device", "ModernUSBCommunicationService");
                                return;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogDebug($"Failed to connect to variant device: {ex.Message}", "ModernUSBCommunicationService");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error checking for Vocom device variants: {ex.Message}", "ModernUSBCommunicationService");
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                if (_isConnected)
                {
                    _logger.LogInformation("Disconnecting USB connection", "ModernUSBCommunicationService");

                    // Close HidSharp resources
                    _hidStream?.Close();
                    _hidStream = null;
                    _hidDevice = null;

                    _isConnected = false;
                    _logger.LogInformation("USB connection disconnected successfully", "ModernUSBCommunicationService");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error during USB disconnection", "ModernUSBCommunicationService", ex);
            }
        }

        public async Task<byte[]> SendCommandAsync(byte[] command)
        {
            if (!_isConnected)
            {
                throw new InvalidOperationException("USB connection is not established");
            }

            try
            {
                _logger.LogInformation($"Sending USB command: {BitConverter.ToString(command)}", "ModernUSBCommunicationService");

                byte[] response = Array.Empty<byte>();

                // Use HidSharp for communication
                if (_hidStream != null)
                {
                    response = await SendCommandWithHidSharpAsync(command);
                }
                else
                {
                    throw new InvalidOperationException("No valid USB connection available");
                }

                _logger.LogInformation($"Received USB response: {BitConverter.ToString(response)}", "ModernUSBCommunicationService");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error sending USB command", "ModernUSBCommunicationService", ex);
                throw;
            }
        }



        private async Task<byte[]> SendCommandWithHidSharpAsync(byte[] command)
        {
            if (_hidStream == null)
                throw new InvalidOperationException("HID stream not available");

            // Send command
            await _hidStream.WriteAsync(command, 0, command.Length);

            // Read response
            byte[] buffer = new byte[1024];
            int bytesRead = await _hidStream.ReadAsync(buffer, 0, buffer.Length);

            // Return only the actual data
            byte[] response = new byte[bytesRead];
            Array.Copy(buffer, response, bytesRead);
            return response;
        }

        public void Dispose()
        {
            DisconnectAsync().Wait();
        }

        // Additional methods required by IUSBCommunicationService interface
        public async Task<bool> InitializeAsync()
        {
            _logger.LogInformation("Initializing modern USB communication service", "ModernUSBCommunicationService");
            return true;
        }

        public Task<bool> IsUSBAvailableAsync()
        {
            try
            {
                // Check if any USB devices are available
                var devices = DeviceList.Local.GetHidDevices();
                bool available = devices.Any();

                _logger.LogInformation($"USB availability check: {available}", "ModernUSBCommunicationService");
                return Task.FromResult(available);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error checking USB availability", "ModernUSBCommunicationService", ex);
                return Task.FromResult(false);
            }
        }

        // Events required by IUSBCommunicationService
        public event EventHandler<string>? USBConnected;
        public event EventHandler<string>? USBDisconnected;
        public event EventHandler<string>? USBError;

        // Additional methods to match the interface
        public async Task<List<string>> DetectVocomDevicesAsync()
        {
            try
            {
                _logger.LogInformation("Detecting Vocom devices using modern USB service", "ModernUSBCommunicationService");

                var devices = new List<string>();

                // Method 1: Try to find devices using HidSharp
                await DetectHidDevicesAsync(devices);

                // Method 2: Try to find devices using WMI (Windows only)
                if (OperatingSystem.IsWindows())
                {
                    await DetectWmiDevicesAsync(devices);
                }

                // Method 3: Try to find devices using Serial Port enumeration
                await DetectSerialPortDevicesAsync(devices);

                // Method 4: Check for Vocom driver installation and add potential devices
                await CheckVocomDriverAndAddDevicesAsync(devices);

                _logger.LogInformation($"Found {devices.Count} Vocom devices", "ModernUSBCommunicationService");
                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error detecting Vocom devices: {ex.Message}", "ModernUSBCommunicationService", ex);
                return new List<string>();
            }
        }

        private async Task DetectHidDevicesAsync(List<string> devices)
        {
            try
            {
                var hidDevices = DeviceList.Local.GetHidDevices();

                foreach (var device in hidDevices)
                {
                    // Check against known Vocom identifiers
                    foreach (var (vendorId, productId) in VocomIdentifiers)
                    {
                        if (device.VendorID == vendorId && device.ProductID == productId)
                        {
                            string devicePath = $"HID:{device.DevicePath}";
                            if (!devices.Contains(devicePath))
                            {
                                devices.Add(devicePath);
                                _logger.LogInformation($"Found Vocom HID device: {device.GetProductName()} at {devicePath}", "ModernUSBCommunicationService");
                            }
                        }
                    }

                    // Also check by device name
                    try
                    {
                        string productName = device.GetProductName();
                        if (!string.IsNullOrEmpty(productName))
                        {
                            foreach (string vocomName in VocomDeviceNames)
                            {
                                if (productName.Contains(vocomName, StringComparison.OrdinalIgnoreCase))
                                {
                                    string devicePath = $"HID:{device.DevicePath}";
                                    if (!devices.Contains(devicePath))
                                    {
                                        devices.Add(devicePath);
                                        _logger.LogInformation($"Found Vocom HID device by name: {productName} at {devicePath}", "ModernUSBCommunicationService");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug($"Could not get product name for HID device: {ex.Message}", "ModernUSBCommunicationService");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error detecting HID devices: {ex.Message}", "ModernUSBCommunicationService");
            }

            await Task.CompletedTask;
        }

        private async Task DetectWmiDevicesAsync(List<string> devices)
        {
            try
            {
#if WINDOWS
                using var searcher = new System.Management.ManagementObjectSearcher(
                    "SELECT * FROM Win32_PnPEntity WHERE " +
                    "(PNPDeviceID LIKE '%VID_178E%' AND PNPDeviceID LIKE '%PID_0024%') OR " +
                    "(PNPDeviceID LIKE '%VID_1A12%' AND PNPDeviceID LIKE '%PID_0001%') OR " +
                    "Caption LIKE '%Vocom%' OR Caption LIKE '%88890300%' OR Caption LIKE '%Communication Unit%'");

                foreach (System.Management.ManagementObject queryObj in searcher.Get())
                {
                    if (queryObj["Caption"] != null && queryObj["PNPDeviceID"] != null)
                    {
                        string caption = queryObj["Caption"].ToString();
                        string deviceId = queryObj["PNPDeviceID"].ToString();

                        string devicePath = $"WMI:{deviceId}";
                        if (!devices.Contains(devicePath))
                        {
                            devices.Add(devicePath);
                            _logger.LogInformation($"Found Vocom WMI device: {caption} ({deviceId})", "ModernUSBCommunicationService");
                        }
                    }
                }
#endif
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error detecting WMI devices: {ex.Message}", "ModernUSBCommunicationService");
            }

            await Task.CompletedTask;
        }

        private async Task DetectSerialPortDevicesAsync(List<string> devices)
        {
            try
            {
                string[] portNames = System.IO.Ports.SerialPort.GetPortNames();

                foreach (string portName in portNames)
                {
                    try
                    {
                        // Try to get more information about the port
                        bool isVocomPort = false;

#if WINDOWS
                        using var searcher = new System.Management.ManagementObjectSearcher(
                            $"SELECT * FROM Win32_PnPEntity WHERE Caption LIKE '%{portName}%'");

                        foreach (System.Management.ManagementObject queryObj in searcher.Get())
                        {
                            if (queryObj["Caption"] != null)
                            {
                                string caption = queryObj["Caption"].ToString();

                                foreach (string vocomName in VocomDeviceNames)
                                {
                                    if (caption.Contains(vocomName, StringComparison.OrdinalIgnoreCase))
                                    {
                                        isVocomPort = true;
                                        break;
                                    }
                                }

                                if (isVocomPort)
                                {
                                    string devicePath = $"COM:{portName}";
                                    if (!devices.Contains(devicePath))
                                    {
                                        devices.Add(devicePath);
                                        _logger.LogInformation($"Found Vocom COM device: {caption} on {portName}", "ModernUSBCommunicationService");
                                    }
                                    break;
                                }
                            }
                        }
#else
                        // For non-Windows platforms, add all COM ports as potential Vocom devices
                        string devicePath = $"COM:{portName}";
                        if (!devices.Contains(devicePath))
                        {
                            devices.Add(devicePath);
                            _logger.LogInformation($"Found potential Vocom COM device on {portName}", "ModernUSBCommunicationService");
                        }
#endif
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug($"Error checking COM port {portName}: {ex.Message}", "ModernUSBCommunicationService");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error detecting serial port devices: {ex.Message}", "ModernUSBCommunicationService");
            }

            await Task.CompletedTask;
        }

        private async Task CheckVocomDriverAndAddDevicesAsync(List<string> devices)
        {
            try
            {
                // Check if Vocom driver is installed
                string vocomDriverPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86),
                    "88890020 Adapter", "UMDF", "WUDFPuma.dll");

                if (File.Exists(vocomDriverPath))
                {
                    _logger.LogInformation("Vocom driver found, checking for connected devices", "ModernUSBCommunicationService");

                    // If driver exists but no devices found, add a generic device entry
                    if (devices.Count == 0)
                    {
                        string devicePath = "DRIVER:WUDFPuma";
                        devices.Add(devicePath);
                        _logger.LogInformation("Added Vocom driver device entry", "ModernUSBCommunicationService");
                    }
                }
                else
                {
                    _logger.LogWarning("Vocom driver not found at expected location", "ModernUSBCommunicationService");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error checking Vocom driver: {ex.Message}", "ModernUSBCommunicationService");
            }

            await Task.CompletedTask;
        }

        public async Task<bool> ConnectToDeviceAsync(string portName, int baudRate = 115200, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Connecting to device: {portName}", "ModernUSBCommunicationService");

                // For modern implementation, we use the ConnectAsync method
                var settings = new VocomConnectionSettings();

                bool connected = await ConnectAsync(settings);
                if (connected)
                {
                    USBConnected?.Invoke(this, portName);
                }

                return connected;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error connecting to device {portName}", "ModernUSBCommunicationService", ex);
                USBError?.Invoke(this, $"Connection error: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DisconnectFromDeviceAsync(string portName)
        {
            try
            {
                _logger.LogInformation($"Disconnecting from device: {portName}", "ModernUSBCommunicationService");

                await DisconnectAsync();
                USBDisconnected?.Invoke(this, portName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error disconnecting from device {portName}", "ModernUSBCommunicationService", ex);
                USBError?.Invoke(this, $"Disconnection error: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> SendDataAsync(string portName, byte[] data)
        {
            try
            {
                _logger.LogInformation($"Sending data to device {portName}: {data.Length} bytes", "ModernUSBCommunicationService");

                var response = await SendCommandAsync(data);
                return response.Length > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending data to device {portName}", "ModernUSBCommunicationService", ex);
                USBError?.Invoke(this, $"Send data error: {ex.Message}");
                return false;
            }
        }

        public async Task<byte[]> ReceiveDataAsync(string portName, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Receiving data from device {portName}", "ModernUSBCommunicationService");

                // For this implementation, we'll return empty data as receiving is handled in SendCommandAsync
                return Array.Empty<byte>();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error receiving data from device {portName}", "ModernUSBCommunicationService", ex);
                USBError?.Invoke(this, $"Receive data error: {ex.Message}");
                return Array.Empty<byte>();
            }
        }
    }
}
