Log started at 7/18/2025 8:28:18 PM
2025-07-18 20:28:18.318 [Information] LoggingService: Logging service initialized
2025-07-18 20:28:18.333 [Information] App: Starting integrated application initialization
2025-07-18 20:28:18.335 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-18 20:28:18.338 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-18 20:28:18.341 [Information] IntegratedStartupService: Setting up application environment
2025-07-18 20:28:18.342 [Information] IntegratedStartupService: Application environment setup completed
2025-07-18 20:28:18.344 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-18 20:28:18.348 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-18 20:28:18.350 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-18 20:28:18.357 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-18 20:28:18.364 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-18 20:28:18.366 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.368 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.369 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-18 20:28:18.370 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-18 20:28:18.372 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.373 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.374 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 20:28:18.377 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-18 20:28:18.379 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.382 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.382 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 20:28:18.386 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-18 20:28:18.388 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.389 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.390 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-18 20:28:18.392 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-18 20:28:18.394 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.397 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.397 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-18 20:28:18.400 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-18 20:28:18.402 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.404 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.404 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-18 20:28:18.407 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-18 20:28:18.409 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.411 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-18 20:28:18.411 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-18 20:28:18.414 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-18 20:28:18.417 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-18 20:28:18.418 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-18 20:28:18.419 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-18 20:28:18.420 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-18 20:28:18.420 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-18 20:28:18.421 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-18 20:28:18.421 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-18 20:28:18.423 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-18 20:28:18.423 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-18 20:28:18.425 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-18 20:28:18.425 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 20:28:18.426 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 20:28:18.426 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-18 20:28:18.426 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-18 20:28:18.427 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-18 20:28:18.427 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-18 20:28:18.436 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-18 20:28:18.436 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-18 20:28:18.437 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-18 20:28:18.443 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-18 20:28:18.444 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-18 20:28:18.447 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-18 20:28:18.449 [Information] LibraryExtractor: Starting library extraction process
2025-07-18 20:28:18.453 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-18 20:28:18.456 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-18 20:28:18.459 [Information] LibraryExtractor: Copying system libraries
2025-07-18 20:28:18.470 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-18 20:28:18.475 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-18 20:28:48.487 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-18 20:28:49.489 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-18 20:29:19.493 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-18 20:29:19.494 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 20:29:49.495 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-18 20:29:50.495 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-18 20:30:20.498 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-18 20:30:20.499 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 20:30:50.501 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-18 20:30:51.502 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-18 20:31:21.505 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-18 20:31:21.505 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-18 20:31:21.506 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-18 20:31:21.509 [Information] LibraryExtractor: Verifying library extraction
2025-07-18 20:31:21.510 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-18 20:31:21.510 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-18 20:31:21.510 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-18 20:31:21.510 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-18 20:31:21.511 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-18 20:31:21.514 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-18 20:31:21.515 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-18 20:31:21.516 [Information] DependencyManager: Initializing dependency manager
2025-07-18 20:31:21.517 [Information] DependencyManager: Setting up library search paths
2025-07-18 20:31:21.518 [Information] DependencyManager: Added library path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-18 20:31:21.519 [Information] DependencyManager: Added driver path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-18 20:31:21.519 [Information] DependencyManager: Added application path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-18 20:31:21.519 [Information] DependencyManager: Updated PATH environment variable
2025-07-18 20:31:21.520 [Information] DependencyManager: Verifying required directories
2025-07-18 20:31:21.521 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-18 20:31:21.521 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-18 20:31:21.521 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-18 20:31:21.522 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-18 20:31:21.524 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-18 20:31:21.529 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-18 20:31:21.530 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-18 20:31:21.530 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-18 20:31:21.532 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcr120.dll: Error 193
2025-07-18 20:31:21.532 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr120.dll
2025-07-18 20:31:21.532 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-18 20:31:21.533 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-18 20:31:21.533 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-18 20:31:21.533 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp120.dll: Error 193
2025-07-18 20:31:21.534 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp120.dll
2025-07-18 20:31:21.534 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-18 20:31:21.534 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-18 20:31:21.535 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-18 20:31:21.536 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll from C:\Windows\system32\msvcp140.dll: Error 193
2025-07-18 20:31:21.537 [Warning] DependencyManager: Architecture mismatch detected for msvcp140.dll. Expected: x64
2025-07-18 20:31:21.537 [Debug] DependencyManager: Architecture mismatch: Library msvcp140.dll is x86, process is x64
2025-07-18 20:31:21.537 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp140.dll
2025-07-18 20:31:21.538 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll: Error 193
2025-07-18 20:31:21.538 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp140.dll
2025-07-18 20:31:21.538 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-18 20:31:21.539 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-18 20:31:21.540 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 20:31:21.540 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 20:31:21.541 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 20:31:21.541 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 20:31:21.542 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-18 20:31:21.542 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-18 20:31:21.543 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-18 20:31:21.543 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-18 20:31:21.544 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-18 20:31:21.544 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-18 20:31:21.545 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-18 20:31:21.545 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-18 20:31:21.545 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-18 20:31:21.546 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-18 20:31:21.546 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-18 20:31:21.546 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-18 20:31:21.547 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-18 20:31:21.547 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-18 20:31:21.549 [Information] DependencyManager: VC++ Redistributable library loading: 1/14 (7.1%) libraries loaded
2025-07-18 20:31:21.550 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-18 20:31:21.551 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-18 20:31:21.552 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-18 20:31:21.553 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-18 20:31:21.553 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-18 20:31:21.554 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-18 20:31:21.555 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-18 20:31:21.555 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-18 20:31:21.556 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-18 20:31:21.556 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-18 20:31:21.556 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-18 20:31:21.557 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-18 20:31:21.557 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-18 20:31:21.558 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-18 20:31:21.558 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-18 20:31:21.559 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-18 20:31:21.559 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-18 20:31:21.560 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-18 20:31:21.561 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-18 20:31:21.561 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-18 20:31:21.562 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-18 20:31:21.562 [Warning] DependencyManager: Failed to load Critical library msvcr120.dll: Error 193
2025-07-18 20:31:21.562 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-18 20:31:21.563 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-18 20:31:21.563 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-18 20:31:21.563 [Warning] DependencyManager: Failed to load Critical library msvcp120.dll: Error 193
2025-07-18 20:31:21.564 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-18 20:31:21.564 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-18 20:31:21.565 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-18 20:31:21.566 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-18 20:31:21.566 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-18 20:31:21.567 [Information] DependencyManager: Setting up environment variables
2025-07-18 20:31:21.567 [Information] DependencyManager: Environment variables configured
2025-07-18 20:31:21.568 [Information] DependencyManager: Verifying library loading status
2025-07-18 20:31:21.887 [Information] DependencyManager: Library loading verification: 4/11 (36.4%) critical libraries loaded
2025-07-18 20:31:21.888 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-18 20:31:21.888 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-18 20:31:21.891 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-18 20:31:21.892 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-18 20:31:21.896 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-18 20:31:21.899 [Information] IntegratedStartupService: Verifying system readiness
2025-07-18 20:31:21.899 [Information] IntegratedStartupService: System readiness verification passed
2025-07-18 20:31:21.899 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-18 20:31:21.901 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-18 20:31:21.901 [Information] IntegratedStartupService: Application Path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-18 20:31:21.901 [Information] IntegratedStartupService: Libraries Path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-18 20:31:21.902 [Information] IntegratedStartupService: Drivers Path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-18 20:31:21.902 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-18 20:31:21.902 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-18 20:31:21.902 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-18 20:31:21.903 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-18 20:31:21.903 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-18 20:31:21.903 [Information] App: Integrated startup completed successfully
2025-07-18 20:31:21.905 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-18 20:31:21.918 [Information] App: Initializing application services
2025-07-18 20:31:21.919 [Information] AppConfigurationService: Initializing configuration service
2025-07-18 20:31:21.919 [Information] AppConfigurationService: Created configuration directory: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-18 20:31:21.958 [Information] AppConfigurationService: Configuration loaded from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-18 20:31:21.959 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-18 20:31:21.960 [Information] App: Configuration service initialized successfully
2025-07-18 20:31:21.961 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-18 20:31:21.962 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-18 20:31:21.966 [Information] App: Environment variable exists: True, not 'false': False
2025-07-18 20:31:21.966 [Information] App: Final useDummyImplementations value: False
2025-07-18 20:31:21.966 [Information] App: Updating config to NOT use dummy implementations
2025-07-18 20:31:21.968 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-18 20:31:21.979 [Information] AppConfigurationService: Configuration saved to D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-18 20:31:21.979 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-18 20:31:21.979 [Information] App: usePatchedImplementation flag is: True
2025-07-18 20:31:21.979 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-18 20:31:21.980 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-18 20:31:21.980 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-18 20:31:21.980 [Information] App: verboseLogging flag is: True
2025-07-18 20:31:21.982 [Information] App: Verifying real hardware requirements...
2025-07-18 20:31:21.983 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-18 20:31:21.983 [Information] App: ✓ Found critical library: apci.dll
2025-07-18 20:31:21.984 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-18 20:31:21.984 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-18 20:31:21.984 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-18 20:31:21.984 [Information] App: Phoenix Diag not found at: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021 (optional)
2025-07-18 20:31:21.985 [Information] App: ✓ Found Vocom driver config: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-18 20:31:21.985 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-18 20:31:21.997 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-18 20:31:21.998 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-18 20:31:21.999 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-18 20:31:22.000 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-18 20:31:22.003 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-07-18 20:34:10.831 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-18 20:34:10.832 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-18 20:34:10.832 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-18 20:34:10.833 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 20:34:10.833 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 20:34:10.834 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-18 20:34:10.834 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-18 20:34:10.834 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-18 20:34:10.835 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-18 20:34:10.835 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-18 20:34:10.836 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-18 20:34:10.836 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-18 20:34:10.837 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-18 20:34:10.837 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-18 20:34:10.837 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-18 20:34:10.837 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-18 20:34:10.837 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-18 20:34:10.838 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-18 20:34:10.839 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-18 20:34:10.839 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-18 20:34:10.841 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-18 20:34:10.842 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-18 20:34:10.842 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-18 20:34:10.842 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-18 20:34:10.844 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-18 20:34:10.845 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service to handle x86 libraries
2025-07-18 20:34:10.846 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-18 20:34:10.848 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-18 20:34:10.851 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge (attempt 1/3)
2025-07-18 20:34:10.857 [Information] VocomArchitectureBridge: Cleaning up existing bridge processes
2025-07-18 20:34:10.860 [Information] VocomArchitectureBridge: No existing bridge processes found
2025-07-18 20:34:10.863 [Information] VocomArchitectureBridge: Created named pipe server: VocomBridge_7868_638884676508476606_5cb3ad27f1514dc791e2dada95cc18ae
2025-07-18 20:34:10.866 [Information] VocomArchitectureBridge: Looking for bridge executable at: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Bridge\VolvoFlashWR.VocomBridge.exe
2025-07-18 20:34:10.960 [Information] VocomArchitectureBridge: Started bridge process with PID 13104
2025-07-18 20:34:11.962 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-18 20:34:11.965 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-18 20:34:11.974 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: Initialize
2025-07-18 20:34:11.975 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-07-18 20:34:11.976 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"Initialize","Data":"{\u0022LibrariesPath\u0022:\u0022D:\\\\Development\\\\S.A.H.VolvoFlashWR\\\\VolvoFlashWR_Export_With_Fix\\\\Libraries\u0022}"} ===
2025-07-18 20:34:11.976 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 155 ===
2025-07-18 20:34:11.976 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-07-18 20:34:11.981 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-07-18 20:34:11.982 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-07-18 20:34:11.982 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-07-18 20:34:12.041 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 80 bytes from pipe ===
2025-07-18 20:34:12.041 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":null,"Message":"Bridge service initialized successfully"} ===
2025-07-18 20:34:12.041 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-07-18 20:34:12.043 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-07-18 20:34:12.044 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Bridge service initialized successfully, Data length= ===
2025-07-18 20:34:12.044 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-18 20:34:12.045 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-18 20:34:12.045 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-18 20:34:12.045 [Information] App: Architecture-aware Vocom service created successfully
2025-07-18 20:34:12.046 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-18 20:34:12.046 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge (attempt 1/3)
2025-07-18 20:34:12.046 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-18 20:34:12.046 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-18 20:34:12.047 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-18 20:34:12.047 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-18 20:34:12.047 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-18 20:34:12.081 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-18 20:34:12.081 [Information] BridgedVocomService: === About to call _bridge.DetectDevicesAsync() ===
2025-07-18 20:34:12.088 [Information] VocomArchitectureBridge: === DetectDevicesAsync called ===
2025-07-18 20:34:12.089 [Information] VocomArchitectureBridge: Bridge is initialized, proceeding with device detection
2025-07-18 20:34:12.089 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Creating DetectDevices command ===
2025-07-18 20:34:12.089 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to call SendCommandAsync ===
2025-07-18 20:34:12.089 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: DetectDevices
2025-07-18 20:34:12.089 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-07-18 20:34:12.090 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"DetectDevices","Data":null} ===
2025-07-18 20:34:12.090 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 36 ===
2025-07-18 20:34:12.090 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-07-18 20:34:12.090 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-07-18 20:34:12.091 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-07-18 20:34:12.091 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-07-18 20:34:12.713 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 264 bytes from pipe ===
2025-07-18 20:34:12.713 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":"[{\u0022Id\u0022:\u0022SYSTEM_VOCOM_001\u0022,\u0022Name\u0022:\u0022System Vocom Adapter (Real Hardware)\u0022,\u0022ConnectionType\u0022:\u0022USB\u0022,\u0022ConnectionStatus\u0022:\u0022Disconnected\u0022}]","Message":"Found 1 devices"} ===
2025-07-18 20:34:12.713 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-07-18 20:34:12.714 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-07-18 20:34:12.714 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Found 1 devices, Data length=130 ===
2025-07-18 20:34:12.715 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: SendCommandAsync returned successfully ===
2025-07-18 20:34:12.716 [Information] VocomArchitectureBridge: Bridge response data: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-07-18 20:34:12.716 [Information] VocomArchitectureBridge: Attempting to parse JSON response: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-07-18 20:34:12.716 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting direct deserialization as BridgeVocomDevice[] ===
2025-07-18 20:34:12.723 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully deserialized 1 BridgeVocomDevice objects ===
2025-07-18 20:34:12.724 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Device: Id=SYSTEM_VOCOM_001, Name=System Vocom Adapter (Real Hardware), ConnectionType=USB, ConnectionStatus=Disconnected ===
2025-07-18 20:34:12.726 [Information] VocomArchitectureBridge: Detected 1 Vocom devices through bridge
2025-07-18 20:34:12.728 [Information] BridgedVocomService: === Returned from _bridge.DetectDevicesAsync() ===
2025-07-18 20:34:12.728 [Information] BridgedVocomService: Found 1 Vocom devices
2025-07-18 20:34:12.728 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-07-18 20:34:12.735 [Information] VocomArchitectureBridge: === DetectDevicesAsync called ===
2025-07-18 20:34:12.736 [Information] VocomArchitectureBridge: Bridge is initialized, proceeding with device detection
2025-07-18 20:34:12.736 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Creating DetectDevices command ===
2025-07-18 20:34:12.736 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to call SendCommandAsync ===
2025-07-18 20:34:12.736 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: DetectDevices
2025-07-18 20:34:12.737 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-07-18 20:34:12.737 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"DetectDevices","Data":null} ===
2025-07-18 20:34:12.737 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 36 ===
2025-07-18 20:34:12.737 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-07-18 20:34:12.738 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-07-18 20:34:12.738 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-07-18 20:34:12.738 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-07-18 20:34:13.102 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 264 bytes from pipe ===
2025-07-18 20:34:13.102 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":"[{\u0022Id\u0022:\u0022SYSTEM_VOCOM_001\u0022,\u0022Name\u0022:\u0022System Vocom Adapter (Real Hardware)\u0022,\u0022ConnectionType\u0022:\u0022USB\u0022,\u0022ConnectionStatus\u0022:\u0022Disconnected\u0022}]","Message":"Found 1 devices"} ===
2025-07-18 20:34:13.102 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-07-18 20:34:13.103 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-07-18 20:34:13.103 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Found 1 devices, Data length=130 ===
2025-07-18 20:34:13.103 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: SendCommandAsync returned successfully ===
2025-07-18 20:34:13.103 [Information] VocomArchitectureBridge: Bridge response data: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-07-18 20:34:13.104 [Information] VocomArchitectureBridge: Attempting to parse JSON response: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-07-18 20:34:13.104 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting direct deserialization as BridgeVocomDevice[] ===
2025-07-18 20:34:13.105 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully deserialized 1 BridgeVocomDevice objects ===
2025-07-18 20:34:13.105 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Device: Id=SYSTEM_VOCOM_001, Name=System Vocom Adapter (Real Hardware), ConnectionType=USB, ConnectionStatus=Disconnected ===
2025-07-18 20:34:13.106 [Information] VocomArchitectureBridge: Detected 1 Vocom devices through bridge
2025-07-18 20:34:13.107 [Information] BridgedVocomService: Connecting to Vocom device SYSTEM_VOCOM_001 through bridge
2025-07-18 20:34:13.111 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: ConnectDevice
2025-07-18 20:34:13.111 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-07-18 20:34:13.112 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"ConnectDevice","Data":"{\u0022DeviceId\u0022:\u0022SYSTEM_VOCOM_001\u0022}"} ===
2025-07-18 20:34:13.112 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 85 ===
2025-07-18 20:34:13.112 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-07-18 20:34:13.112 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-07-18 20:34:13.112 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-07-18 20:34:13.113 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-07-18 20:34:13.212 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 77 bytes from pipe ===
2025-07-18 20:34:13.212 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":null,"Message":"Connected to device SYSTEM_VOCOM_001"} ===
2025-07-18 20:34:13.212 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-07-18 20:34:13.213 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-07-18 20:34:13.213 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Connected to device SYSTEM_VOCOM_001, Data length= ===
2025-07-18 20:34:13.214 [Information] VocomArchitectureBridge: Successfully connected to device SYSTEM_VOCOM_001 through bridge
2025-07-18 20:34:13.215 [Information] BridgedVocomService: === DEVICE CONNECTION DEBUG ===
2025-07-18 20:34:13.215 [Information] BridgedVocomService: Device ID: SYSTEM_VOCOM_001
2025-07-18 20:34:13.215 [Information] BridgedVocomService: Device Name: System Vocom Adapter (Real Hardware)
2025-07-18 20:34:13.216 [Information] BridgedVocomService: Device ConnectionStatus: Connected
2025-07-18 20:34:13.216 [Information] BridgedVocomService: _isConnected: True
2025-07-18 20:34:13.217 [Information] BridgedVocomService: CurrentDevice != null: True
2025-07-18 20:34:13.217 [Information] BridgedVocomService: CurrentDevice.ConnectionStatus: Connected
2025-07-18 20:34:13.217 [Information] BridgedVocomService: === END DEVICE CONNECTION DEBUG ===
2025-07-18 20:34:13.217 [Information] BridgedVocomService: Successfully connected to device SYSTEM_VOCOM_001
2025-07-18 20:34:13.218 [Information] App: Connected to Vocom device System Vocom Adapter (Real Hardware)
2025-07-18 20:34:13.221 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-18 20:34:13.224 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-18 20:34:13.224 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-18 20:34:13.227 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-18 20:34:13.229 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-18 20:34:13.229 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-18 20:34:13.230 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-18 20:34:13.230 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-18 20:34:13.230 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: SYSTEM_VOCOM_001
2025-07-18 20:34:13.230 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-18 20:34:13.231 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-18 20:34:13.233 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-18 20:34:13.234 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-18 20:34:13.236 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-18 20:34:13.238 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-18 20:34:13.240 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-18 20:34:13.246 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-18 20:34:13.248 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-18 20:34:13.260 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-18 20:34:13.261 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-18 20:34:13.261 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-18 20:34:13.262 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-18 20:34:13.262 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-18 20:34:13.262 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-18 20:34:13.262 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-18 20:34:13.262 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-18 20:34:13.263 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-18 20:34:13.263 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-18 20:34:13.263 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-18 20:34:13.263 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-18 20:34:13.263 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-18 20:34:13.264 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-18 20:34:13.264 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-18 20:34:13.264 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-18 20:34:13.264 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-18 20:34:13.267 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-18 20:34:13.274 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-18 20:34:13.275 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-18 20:34:13.277 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-18 20:34:13.279 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-18 20:34:13.286 [Information] CANRegisterAccess: Read value 0xE3 from register 0x0141 (simulated)
2025-07-18 20:34:13.287 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-18 20:34:13.288 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-18 20:34:13.288 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-18 20:34:13.293 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-18 20:34:13.294 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-18 20:34:13.299 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-18 20:34:13.300 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-18 20:34:13.300 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-18 20:34:13.305 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-18 20:34:13.306 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-18 20:34:13.306 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-18 20:34:13.312 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-18 20:34:13.313 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-18 20:34:13.318 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-18 20:34:13.319 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-18 20:34:13.324 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-18 20:34:13.325 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-18 20:34:13.330 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-18 20:34:13.331 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-18 20:34:13.336 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-18 20:34:13.337 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-18 20:34:13.342 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-18 20:34:13.343 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-18 20:34:13.348 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-18 20:34:13.349 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-18 20:34:13.354 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-18 20:34:13.355 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-18 20:34:13.360 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-18 20:34:13.361 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-18 20:34:13.366 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-18 20:34:13.367 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-18 20:34:13.372 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-18 20:34:13.373 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-18 20:34:13.378 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-18 20:34:13.379 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-18 20:34:13.384 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-18 20:34:13.385 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-18 20:34:13.390 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-18 20:34:13.391 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-18 20:34:13.396 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-18 20:34:13.397 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-18 20:34:13.401 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-18 20:34:13.402 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-18 20:34:13.407 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-18 20:34:13.408 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-18 20:34:13.408 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-18 20:34:13.413 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-18 20:34:13.414 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-18 20:34:13.414 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-18 20:34:13.414 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-18 20:34:13.420 [Information] CANRegisterAccess: Read value 0xE6 from register 0x0141 (simulated)
2025-07-18 20:34:13.421 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-18 20:34:13.421 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-18 20:34:13.421 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-18 20:34:13.422 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-18 20:34:13.428 [Information] CANRegisterAccess: Read value 0x8B from register 0x0140 (simulated)
2025-07-18 20:34:13.434 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-18 20:34:13.440 [Information] CANRegisterAccess: Read value 0xCC from register 0x0140 (simulated)
2025-07-18 20:34:13.446 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-18 20:34:13.452 [Information] CANRegisterAccess: Read value 0x9A from register 0x0140 (simulated)
2025-07-18 20:34:13.453 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-18 20:34:13.453 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-18 20:34:13.456 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-18 20:34:13.457 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-18 20:34:13.468 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-18 20:34:13.469 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-18 20:34:13.470 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-18 20:34:13.473 [Debug] BridgedVocomService: Sending and receiving 4 bytes through bridge
2025-07-18 20:34:13.474 [Error] SPIProtocolHandler: No response received from Vocom device
2025-07-18 20:34:13.475 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-18 20:34:13.475 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-18 20:34:13.476 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-18 20:34:13.477 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-18 20:34:13.488 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-18 20:34:13.489 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-18 20:34:13.490 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-18 20:34:13.501 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-18 20:34:13.512 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-18 20:34:13.523 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-18 20:34:13.534 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-18 20:34:13.545 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-18 20:34:13.547 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-18 20:34:13.547 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-18 20:34:13.558 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-18 20:34:13.559 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-18 20:34:13.559 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-18 20:34:13.570 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-18 20:34:13.581 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-18 20:34:13.592 [Information] IICProtocolHandler: Enabling IIC module
2025-07-18 20:34:13.603 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-18 20:34:13.614 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-18 20:34:13.625 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-18 20:34:13.627 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-18 20:34:13.627 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-18 20:34:13.638 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-18 20:34:13.639 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-18 20:34:13.640 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-18 20:34:13.640 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-18 20:34:13.640 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-18 20:34:13.641 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-18 20:34:13.641 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-18 20:34:13.641 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-18 20:34:13.642 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-18 20:34:13.642 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-18 20:34:13.642 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-18 20:34:13.642 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-18 20:34:13.642 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-18 20:34:13.643 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-18 20:34:13.643 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-18 20:34:13.643 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-18 20:34:13.643 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-18 20:34:13.743 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-18 20:34:13.744 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-18 20:34:13.747 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-18 20:34:13.748 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-18 20:34:13.748 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-18 20:34:13.749 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-18 20:34:13.749 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-18 20:34:13.749 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-18 20:34:13.750 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-18 20:34:13.750 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-18 20:34:13.750 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-18 20:34:13.750 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-18 20:34:13.751 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-18 20:34:13.751 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-18 20:34:13.751 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-18 20:34:13.752 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-18 20:34:13.752 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-18 20:34:13.753 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-18 20:34:13.755 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-18 20:34:13.757 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-18 20:34:13.760 [Information] BackupService: Initializing backup service
2025-07-18 20:34:13.760 [Information] BackupService: Backup service initialized successfully
2025-07-18 20:34:13.760 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-18 20:34:13.761 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-18 20:34:13.763 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-18 20:34:13.788 [Information] BackupService: Compressing backup data
2025-07-18 20:34:13.795 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-18 20:34:13.796 [Information] BackupServiceFactory: Created template for category: Production
2025-07-18 20:34:13.797 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-18 20:34:13.797 [Information] BackupService: Compressing backup data
2025-07-18 20:34:13.799 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (452 bytes)
2025-07-18 20:34:13.799 [Information] BackupServiceFactory: Created template for category: Development
2025-07-18 20:34:13.799 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-18 20:34:13.799 [Information] BackupService: Compressing backup data
2025-07-18 20:34:13.800 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-07-18 20:34:13.801 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-18 20:34:13.801 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-18 20:34:13.804 [Information] BackupService: Compressing backup data
2025-07-18 20:34:13.805 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-18 20:34:13.805 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-18 20:34:13.806 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-18 20:34:13.806 [Information] BackupService: Compressing backup data
2025-07-18 20:34:13.807 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-07-18 20:34:13.807 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-18 20:34:13.808 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-18 20:34:13.808 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-18 20:34:13.809 [Information] BackupService: Compressing backup data
2025-07-18 20:34:13.810 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-07-18 20:34:13.810 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-18 20:34:13.810 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-18 20:34:13.812 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-18 20:34:13.815 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-18 20:34:13.817 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-18 20:34:13.868 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-18 20:34:13.869 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-18 20:34:13.870 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-18 20:34:13.870 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-18 20:34:13.871 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-18 20:34:13.872 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-18 20:34:13.872 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-18 20:34:13.876 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-18 20:34:13.876 [Information] App: Flash operation monitor service initialized successfully
2025-07-18 20:34:13.885 [Information] LicensingService: Initializing licensing service
2025-07-18 20:34:13.929 [Information] LicensingService: License information loaded successfully
2025-07-18 20:34:13.932 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-18 20:34:13.932 [Information] App: Licensing service initialized successfully
2025-07-18 20:34:13.932 [Information] App: License status: Trial
2025-07-18 20:34:13.932 [Information] App: Trial period: 25 days remaining
2025-07-18 20:34:13.933 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-18 20:34:13.955 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-18 20:34:14.102 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-18 20:34:14.102 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge (attempt 1/3)
2025-07-18 20:34:14.103 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-18 20:34:14.103 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-18 20:34:14.154 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-18 20:34:14.154 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-18 20:34:14.154 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-18 20:34:14.154 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-18 20:34:14.155 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-18 20:34:14.155 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: SYSTEM_VOCOM_001
2025-07-18 20:34:14.155 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-18 20:34:14.155 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-18 20:34:14.156 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-18 20:34:14.156 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-18 20:34:14.157 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-18 20:34:14.158 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-18 20:34:14.159 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-18 20:34:14.159 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-18 20:34:14.159 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-18 20:34:14.170 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-18 20:34:14.171 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-18 20:34:14.171 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-18 20:34:14.171 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-18 20:34:14.171 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-18 20:34:14.172 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-18 20:34:14.172 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-18 20:34:14.172 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-18 20:34:14.172 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-18 20:34:14.173 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-18 20:34:14.173 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-18 20:34:14.173 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-18 20:34:14.173 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-18 20:34:14.173 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-18 20:34:14.174 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-18 20:34:14.174 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-18 20:34:14.174 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-18 20:34:14.174 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-18 20:34:14.180 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-18 20:34:14.181 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-18 20:34:14.181 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-18 20:34:14.181 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-18 20:34:14.187 [Information] CANRegisterAccess: Read value 0xD1 from register 0x0141 (simulated)
2025-07-18 20:34:14.188 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-18 20:34:14.188 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-18 20:34:14.188 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-18 20:34:14.194 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-18 20:34:14.195 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-18 20:34:14.200 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-18 20:34:14.201 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-18 20:34:14.201 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-18 20:34:14.207 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-18 20:34:14.208 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-18 20:34:14.208 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-18 20:34:14.214 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-18 20:34:14.215 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-18 20:34:14.220 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-18 20:34:14.221 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-18 20:34:14.226 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-18 20:34:14.227 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-18 20:34:14.233 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-18 20:34:14.234 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-18 20:34:14.240 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-18 20:34:14.241 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-18 20:34:14.246 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-18 20:34:14.247 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-18 20:34:14.252 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-18 20:34:14.253 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-18 20:34:14.258 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-18 20:34:14.259 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-18 20:34:14.264 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-18 20:34:14.265 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-18 20:34:14.270 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-18 20:34:14.271 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-18 20:34:14.276 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-18 20:34:14.277 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-18 20:34:14.282 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-18 20:34:14.283 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-18 20:34:14.288 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-18 20:34:14.289 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-18 20:34:14.294 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-18 20:34:14.295 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-18 20:34:14.300 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-18 20:34:14.301 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-18 20:34:14.306 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-18 20:34:14.306 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-18 20:34:14.312 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-18 20:34:14.313 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-18 20:34:14.313 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-18 20:34:14.319 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-18 20:34:14.320 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-18 20:34:14.320 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-18 20:34:14.320 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-18 20:34:14.327 [Information] CANRegisterAccess: Read value 0xFA from register 0x0141 (simulated)
2025-07-18 20:34:14.327 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-18 20:34:14.328 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-18 20:34:14.328 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-18 20:34:14.328 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-18 20:34:14.334 [Information] CANRegisterAccess: Read value 0x16 from register 0x0140 (simulated)
2025-07-18 20:34:14.335 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-18 20:34:14.335 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-18 20:34:14.337 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-18 20:34:14.338 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-18 20:34:14.349 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-18 20:34:14.350 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-18 20:34:14.350 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-18 20:34:14.350 [Debug] BridgedVocomService: Sending and receiving 4 bytes through bridge
2025-07-18 20:34:14.350 [Error] SPIProtocolHandler: No response received from Vocom device
2025-07-18 20:34:14.350 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-18 20:34:14.351 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-18 20:34:14.351 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-18 20:34:14.351 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-18 20:34:14.362 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-18 20:34:14.363 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-18 20:34:14.363 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-18 20:34:14.373 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-18 20:34:14.384 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-18 20:34:14.395 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-18 20:34:14.406 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-18 20:34:14.417 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-18 20:34:14.418 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-18 20:34:14.418 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-18 20:34:14.429 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-18 20:34:14.429 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-18 20:34:14.430 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-18 20:34:14.440 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-18 20:34:14.451 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-18 20:34:14.462 [Information] IICProtocolHandler: Enabling IIC module
2025-07-18 20:34:14.473 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-18 20:34:14.484 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-18 20:34:14.494 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-18 20:34:14.495 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-18 20:34:14.495 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-18 20:34:14.506 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-18 20:34:14.507 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-18 20:34:14.507 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-18 20:34:14.507 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-18 20:34:14.507 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-18 20:34:14.507 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-18 20:34:14.508 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-18 20:34:14.508 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-18 20:34:14.508 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-18 20:34:14.508 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-18 20:34:14.509 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-18 20:34:14.509 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-18 20:34:14.509 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-18 20:34:14.509 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-18 20:34:14.509 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-18 20:34:14.510 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-18 20:34:14.510 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-18 20:34:14.610 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-18 20:34:14.610 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-18 20:34:14.610 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-18 20:34:14.611 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-18 20:34:14.611 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-18 20:34:14.612 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-18 20:34:14.612 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-18 20:34:14.612 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-18 20:34:14.612 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-18 20:34:14.613 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-18 20:34:14.613 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-18 20:34:14.613 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-18 20:34:14.613 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-18 20:34:14.614 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-18 20:34:14.614 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-18 20:34:14.664 [Information] BackupService: Initializing backup service
2025-07-18 20:34:14.664 [Information] BackupService: Backup service initialized successfully
2025-07-18 20:34:14.715 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-18 20:34:14.715 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-18 20:34:14.716 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-18 20:34:14.717 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-18 20:34:14.768 [Information] BackupService: Getting predefined backup categories
2025-07-18 20:34:14.819 [Information] MainViewModel: Services initialized successfully
2025-07-18 20:34:14.821 [Information] MainViewModel: Scanning for Vocom devices
2025-07-18 20:34:14.822 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-18 20:34:14.823 [Information] BridgedVocomService: === About to call _bridge.DetectDevicesAsync() ===
2025-07-18 20:34:14.823 [Information] VocomArchitectureBridge: === DetectDevicesAsync called ===
2025-07-18 20:34:14.823 [Information] VocomArchitectureBridge: Bridge is initialized, proceeding with device detection
2025-07-18 20:34:14.823 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Creating DetectDevices command ===
2025-07-18 20:34:14.823 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to call SendCommandAsync ===
2025-07-18 20:34:14.824 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: DetectDevices
2025-07-18 20:34:14.824 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-07-18 20:34:14.824 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"DetectDevices","Data":null} ===
2025-07-18 20:34:14.824 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 36 ===
2025-07-18 20:34:14.824 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-07-18 20:34:14.826 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-07-18 20:34:14.826 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-07-18 20:34:14.827 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-07-18 20:34:15.166 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 264 bytes from pipe ===
2025-07-18 20:34:15.166 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":"[{\u0022Id\u0022:\u0022SYSTEM_VOCOM_001\u0022,\u0022Name\u0022:\u0022System Vocom Adapter (Real Hardware)\u0022,\u0022ConnectionType\u0022:\u0022USB\u0022,\u0022ConnectionStatus\u0022:\u0022Disconnected\u0022}]","Message":"Found 1 devices"} ===
2025-07-18 20:34:15.166 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-07-18 20:34:15.167 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-07-18 20:34:15.167 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Found 1 devices, Data length=130 ===
2025-07-18 20:34:15.167 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: SendCommandAsync returned successfully ===
2025-07-18 20:34:15.168 [Information] VocomArchitectureBridge: Bridge response data: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-07-18 20:34:15.168 [Information] VocomArchitectureBridge: Attempting to parse JSON response: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-07-18 20:34:15.168 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting direct deserialization as BridgeVocomDevice[] ===
2025-07-18 20:34:15.170 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully deserialized 1 BridgeVocomDevice objects ===
2025-07-18 20:34:15.171 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Device: Id=SYSTEM_VOCOM_001, Name=System Vocom Adapter (Real Hardware), ConnectionType=USB, ConnectionStatus=Disconnected ===
2025-07-18 20:34:15.171 [Information] VocomArchitectureBridge: Detected 1 Vocom devices through bridge
2025-07-18 20:34:15.171 [Information] BridgedVocomService: === Returned from _bridge.DetectDevicesAsync() ===
2025-07-18 20:34:15.171 [Information] BridgedVocomService: Found 1 Vocom devices
2025-07-18 20:34:15.173 [Information] MainViewModel: Found 1 Vocom device(s)
2025-07-18 20:34:31.068 [Information] MainViewModel: Connecting to Vocom device SYSTEM_VOCOM_001
2025-07-18 20:34:31.069 [Information] VocomArchitectureBridge: === DetectDevicesAsync called ===
2025-07-18 20:34:31.069 [Information] VocomArchitectureBridge: Bridge is initialized, proceeding with device detection
2025-07-18 20:34:31.069 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Creating DetectDevices command ===
2025-07-18 20:34:31.069 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to call SendCommandAsync ===
2025-07-18 20:34:31.070 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: DetectDevices
2025-07-18 20:34:31.070 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-07-18 20:34:31.070 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"DetectDevices","Data":null} ===
2025-07-18 20:34:31.070 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 36 ===
2025-07-18 20:34:31.070 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-07-18 20:34:31.073 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-07-18 20:34:31.074 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-07-18 20:34:31.074 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-07-18 20:34:31.431 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 264 bytes from pipe ===
2025-07-18 20:34:31.431 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":"[{\u0022Id\u0022:\u0022SYSTEM_VOCOM_001\u0022,\u0022Name\u0022:\u0022System Vocom Adapter (Real Hardware)\u0022,\u0022ConnectionType\u0022:\u0022USB\u0022,\u0022ConnectionStatus\u0022:\u0022Disconnected\u0022}]","Message":"Found 1 devices"} ===
2025-07-18 20:34:31.432 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-07-18 20:34:31.432 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-07-18 20:34:31.432 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Found 1 devices, Data length=130 ===
2025-07-18 20:34:31.433 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: SendCommandAsync returned successfully ===
2025-07-18 20:34:31.433 [Information] VocomArchitectureBridge: Bridge response data: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-07-18 20:34:31.433 [Information] VocomArchitectureBridge: Attempting to parse JSON response: [{"Id":"SYSTEM_VOCOM_001","Name":"System Vocom Adapter (Real Hardware)","ConnectionType":"USB","ConnectionStatus":"Disconnected"}]
2025-07-18 20:34:31.433 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting direct deserialization as BridgeVocomDevice[] ===
2025-07-18 20:34:31.435 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully deserialized 1 BridgeVocomDevice objects ===
2025-07-18 20:34:31.436 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Device: Id=SYSTEM_VOCOM_001, Name=System Vocom Adapter (Real Hardware), ConnectionType=USB, ConnectionStatus=Disconnected ===
2025-07-18 20:34:31.436 [Information] VocomArchitectureBridge: Detected 1 Vocom devices through bridge
2025-07-18 20:34:31.436 [Warning] BridgedVocomService: Local state shows connected but bridge reports no connected device - synchronizing
2025-07-18 20:34:31.437 [Information] BridgedVocomService: Synchronized local state: disconnected from device SYSTEM_VOCOM_001
2025-07-18 20:34:31.437 [Information] ECUCommunicationService: Vocom device disconnected: SYSTEM_VOCOM_001
2025-07-18 20:34:31.440 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-18 20:34:31.440 [Information] ECUCommunicationService: No ECUs are connected
2025-07-18 20:34:31.441 [Information] MainViewModel: Vocom device SYSTEM_VOCOM_001 disconnected
2025-07-18 20:34:31.441 [Information] ECUCommunicationService: Vocom device disconnected: SYSTEM_VOCOM_001
2025-07-18 20:34:31.441 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-18 20:34:31.441 [Information] ECUCommunicationService: No ECUs are connected
2025-07-18 20:34:31.442 [Information] BridgedVocomService: Connecting to Vocom device SYSTEM_VOCOM_001 through bridge
2025-07-18 20:34:31.442 [Information] VocomArchitectureBridge: SendCommandAsync called with command type: ConnectDevice
2025-07-18 20:34:31.442 [Information] VocomArchitectureBridge: Pipe server is connected, proceeding with command
2025-07-18 20:34:31.442 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Sending command to bridge: {"Type":"ConnectDevice","Data":"{\u0022DeviceId\u0022:\u0022SYSTEM_VOCOM_001\u0022}"} ===
2025-07-18 20:34:31.442 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command bytes length: 85 ===
2025-07-18 20:34:31.443 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to write command to pipe ===
2025-07-18 20:34:31.443 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Command written to pipe ===
2025-07-18 20:34:31.444 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Pipe flushed ===
2025-07-18 20:34:31.444 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: About to read response from pipe ===
2025-07-18 20:34:31.444 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Read 77 bytes from pipe ===
2025-07-18 20:34:31.444 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Received response from bridge: {"Success":true,"Data":null,"Message":"Connected to device SYSTEM_VOCOM_001"} ===
2025-07-18 20:34:31.445 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===
2025-07-18 20:34:31.445 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: JSON is well-formed, root element: Object ===
2025-07-18 20:34:31.445 [Information] VocomArchitectureBridge: === BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success=True, Message=Connected to device SYSTEM_VOCOM_001, Data length= ===
2025-07-18 20:34:31.445 [Information] VocomArchitectureBridge: Successfully connected to device SYSTEM_VOCOM_001 through bridge
2025-07-18 20:34:31.445 [Information] BridgedVocomService: === DEVICE CONNECTION DEBUG ===
2025-07-18 20:34:31.446 [Information] BridgedVocomService: Device ID: SYSTEM_VOCOM_001
2025-07-18 20:34:31.446 [Information] BridgedVocomService: Device Name: System Vocom Adapter (Real Hardware)
2025-07-18 20:34:31.446 [Information] BridgedVocomService: Device ConnectionStatus: Connected
2025-07-18 20:34:31.446 [Information] BridgedVocomService: _isConnected: True
2025-07-18 20:34:31.446 [Information] BridgedVocomService: CurrentDevice != null: True
2025-07-18 20:34:31.446 [Information] BridgedVocomService: CurrentDevice.ConnectionStatus: Connected
2025-07-18 20:34:31.447 [Information] BridgedVocomService: === END DEVICE CONNECTION DEBUG ===
2025-07-18 20:34:31.447 [Information] ECUCommunicationService: Vocom device connected: SYSTEM_VOCOM_001
2025-07-18 20:34:31.447 [Information] MainViewModel: Vocom device SYSTEM_VOCOM_001 connected
2025-07-18 20:34:31.447 [Information] ECUCommunicationService: Vocom device connected: SYSTEM_VOCOM_001
2025-07-18 20:34:31.448 [Information] BridgedVocomService: Successfully connected to device SYSTEM_VOCOM_001
2025-07-18 20:34:31.448 [Information] MainViewModel: Connected to Vocom device SYSTEM_VOCOM_001
2025-07-18 20:34:31.451 [Information] MainViewModel: Scanning for ECUs
2025-07-18 20:34:31.458 [Information] ECUCommunicationService: Scanning for ECUs
2025-07-18 20:34:31.459 [Information] ECUCommunicationService: Scanning for ECUs on the CAN bus...
2025-07-18 20:34:31.972 [Information] ECUCommunicationService: Scanning for ECUs using SPI protocol...
2025-07-18 20:34:32.281 [Information] ECUCommunicationService: Scanning for ECUs using SCI protocol...
2025-07-18 20:34:32.588 [Information] ECUCommunicationService: Scanning for ECUs using IIC protocol...
2025-07-18 20:34:32.899 [Information] ECUCommunicationService: Found 10 ECUs
2025-07-18 20:34:32.900 [Information] MainViewModel: Found 10 ECU(s)
