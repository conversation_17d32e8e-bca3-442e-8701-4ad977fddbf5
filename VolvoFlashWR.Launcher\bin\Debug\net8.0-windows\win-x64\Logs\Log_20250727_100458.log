Log started at 7/27/2025 10:04:58 AM
2025-07-27 10:04:59.077 [Information] LoggingService: Logging service initialized
2025-07-27 10:04:59.100 [Information] App: Starting integrated application initialization
2025-07-27 10:04:59.102 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-27 10:04:59.107 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-27 10:04:59.110 [Information] IntegratedStartupService: Setting up application environment
2025-07-27 10:04:59.111 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Libraries
2025-07-27 10:04:59.113 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Config
2025-07-27 10:04:59.113 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Backups
2025-07-27 10:04:59.115 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Temp
2025-07-27 10:04:59.116 [Information] IntegratedStartupService: Application environment setup completed
2025-07-27 10:04:59.119 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-27 10:04:59.122 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-27 10:04:59.123 [Information] VCRedistBundler: Created VCRedist directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Libraries\VCRedist
2025-07-27 10:04:59.127 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-27 10:04:59.134 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-27 10:04:59.183 [Information] VCRedistBundler: Copied msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-27 10:04:59.188 [Information] VCRedistBundler: Copied msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-27 10:04:59.216 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-27 10:04:59.305 [Information] VCRedistBundler: Copied msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-27 10:04:59.313 [Information] VCRedistBundler: Copied vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-27 10:04:59.324 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 10:04:59.335 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 10:04:59.346 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 10:04:59.356 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 10:04:59.368 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 10:04:59.411 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 10:04:59.414 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-27 10:04:59.417 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-27 10:04:59.418 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-27 10:04:59.737 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-27 10:04:59.847 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-27 10:04:59.848 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-27 10:04:59.956 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-27 10:05:00.036 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 10:05:00.036 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 10:05:00.037 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 10:05:00.037 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 10:05:00.038 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 10:05:00.038 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 10:05:00.047 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-27 10:05:00.048 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-27 10:05:00.048 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-27 10:05:00.057 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-27 10:05:00.058 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-27 10:05:00.060 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-27 10:05:00.063 [Information] LibraryExtractor: Starting library extraction process
2025-07-27 10:05:00.066 [Information] LibraryExtractor: Created directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Libraries\System
2025-07-27 10:05:00.067 [Information] LibraryExtractor: Created directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Debug\net8.0-windows\win-x64\Drivers\Vocom\Backup
2025-07-27 10:05:00.069 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-27 10:05:00.072 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-27 10:05:00.075 [Information] LibraryExtractor: Copying system libraries
2025-07-27 10:05:00.143 [Information] LibraryExtractor: Copied system library: WUDFPuma.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 10:05:00.152 [Information] LibraryExtractor: Copied system library: apci.dll from C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\apci.dll
2025-07-27 10:05:00.167 [Information] LibraryExtractor: Copied system library: Volvo.ApciPlus.dll from C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\Volvo.ApciPlus.dll
2025-07-27 10:05:00.260 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-27 10:05:00.455 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-27 10:05:30.497 [Warning] LibraryExtractor: Download timeout for redistributable msvcr120.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 10:05:31.500 [Information] LibraryExtractor: Retrying download for msvcr120.dll (attempt 2/2)
2025-07-27 10:06:01.506 [Warning] LibraryExtractor: Download timeout for redistributable msvcr120.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 10:06:01.507 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-27 10:06:31.511 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 10:06:32.512 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
