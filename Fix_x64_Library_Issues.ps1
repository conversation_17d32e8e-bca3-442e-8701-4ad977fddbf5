# VolvoFlashWR x64 Architecture Library Fix Script
# This script resolves library loading issues while maintaining x64 architecture

param(
    [switch]$Force,
    [switch]$Verbose,
    [string]$OutputPath = ".\VolvoFlashWR_Export_With_Fix\Libraries"
)

# Set error action preference
$ErrorActionPreference = "Continue"

# Enable verbose output if requested
if ($Verbose) {
    $VerbosePreference = "Continue"
}

Write-Host "=== VolvoFlashWR x64 Architecture Library Fix ===" -ForegroundColor Green
Write-Host "This script will fix library loading issues while maintaining x64 architecture" -ForegroundColor Yellow
Write-Host ""

# Create output directory
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "Created output directory: $OutputPath" -ForegroundColor Green
}

# Function to download file with progress
function Download-FileWithProgress {
    param(
        [string]$Url,
        [string]$OutputPath,
        [string]$Description
    )
    
    try {
        Write-Host "Downloading $Description..." -ForegroundColor Yellow
        $webClient = New-Object System.Net.WebClient
        $webClient.DownloadFile($Url, $OutputPath)
        Write-Host "✓ Downloaded: $Description" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ Failed to download $Description`: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    finally {
        if ($webClient) { $webClient.Dispose() }
    }
}

# Function to check if file exists and get architecture
function Get-FileArchitecture {
    param([string]$FilePath)
    
    if (!(Test-Path $FilePath)) {
        return "NotFound"
    }
    
    try {
        $bytes = [System.IO.File]::ReadAllBytes($FilePath)
        if ($bytes.Length -lt 64) { return "Invalid" }
        
        # Get PE header offset
        $peOffset = [BitConverter]::ToInt32($bytes, 60)
        if ($peOffset + 24 -ge $bytes.Length) { return "Invalid" }
        
        # Get machine type
        $machineType = [BitConverter]::ToUInt16($bytes, $peOffset + 4)
        
        switch ($machineType) {
            0x014c { return "x86" }
            0x8664 { return "x64" }
            default { return "Unknown" }
        }
    }
    catch {
        return "Error"
    }
}

# Step 1: Download and install Visual C++ Redistributables
Write-Host "Step 1: Installing Visual C++ Redistributables" -ForegroundColor Cyan

$vcRedistUrls = @{
    "VC2015-2022_x64" = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
    "VC2013_x64" = "https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x64.exe"
}

$tempDir = [System.IO.Path]::GetTempPath()

foreach ($redist in $vcRedistUrls.GetEnumerator()) {
    $tempFile = Join-Path $tempDir "$($redist.Key).exe"
    
    if (Download-FileWithProgress -Url $redist.Value -OutputPath $tempFile -Description $redist.Key) {
        Write-Host "Installing $($redist.Key)..." -ForegroundColor Yellow
        try {
            $process = Start-Process -FilePath $tempFile -ArgumentList "/quiet", "/norestart" -Wait -PassThru
            if ($process.ExitCode -eq 0) {
                Write-Host "✓ Installed: $($redist.Key)" -ForegroundColor Green
            } else {
                Write-Host "⚠ Installation completed with exit code: $($process.ExitCode)" -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "✗ Failed to install $($redist.Key): $($_.Exception.Message)" -ForegroundColor Red
        }
        finally {
            if (Test-Path $tempFile) { Remove-Item $tempFile -Force }
        }
    }
}

# Step 2: Copy system libraries to application directory
Write-Host "`nStep 2: Copying system libraries" -ForegroundColor Cyan

$requiredLibraries = @(
    "msvcr140.dll",
    "msvcp140.dll", 
    "vcruntime140.dll",
    "api-ms-win-crt-runtime-l1-1-0.dll",
    "api-ms-win-crt-heap-l1-1-0.dll",
    "api-ms-win-crt-string-l1-1-0.dll",
    "api-ms-win-crt-stdio-l1-1-0.dll",
    "api-ms-win-crt-math-l1-1-0.dll",
    "api-ms-win-crt-locale-l1-1-0.dll"
)

$systemPaths = @(
    "$env:SystemRoot\System32",
    "$env:SystemRoot\SysWOW64"
)

foreach ($library in $requiredLibraries) {
    $found = $false
    $targetPath = Join-Path $OutputPath $library
    
    # Skip if already exists and not forcing
    if ((Test-Path $targetPath) -and !$Force) {
        $arch = Get-FileArchitecture -FilePath $targetPath
        Write-Host "✓ Already exists: $library ($arch)" -ForegroundColor Green
        continue
    }
    
    foreach ($systemPath in $systemPaths) {
        $sourcePath = Join-Path $systemPath $library
        if (Test-Path $sourcePath) {
            $arch = Get-FileArchitecture -FilePath $sourcePath
            
            # Prefer x64 libraries for x64 application
            if ($arch -eq "x64" -or !$found) {
                try {
                    Copy-Item -Path $sourcePath -Destination $targetPath -Force
                    Write-Host "✓ Copied: $library ($arch) from $systemPath" -ForegroundColor Green
                    $found = $true
                    if ($arch -eq "x64") { break } # Prefer x64, so break if found
                }
                catch {
                    Write-Host "✗ Failed to copy $library from $systemPath`: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
    }
    
    if (!$found) {
        Write-Host "⚠ Not found: $library" -ForegroundColor Yellow
    }
}

Write-Host "`nStep 3: Analyzing architecture compatibility" -ForegroundColor Cyan

# Check critical APCI libraries
$apciLibraries = @(
    "apci.dll",
    "apcidb.dll", 
    "Volvo.ApciPlus.dll",
    "Volvo.ApciPlusData.dll",
    "WUDFPuma.dll"
)

$appPath = ".\VolvoFlashWR_Export_With_Fix"
$librariesPath = Join-Path $appPath "Libraries"

Write-Host "Checking APCI library architectures:" -ForegroundColor Yellow

foreach ($library in $apciLibraries) {
    $paths = @(
        (Join-Path $appPath $library),
        (Join-Path $librariesPath $library)
    )
    
    foreach ($path in $paths) {
        if (Test-Path $path) {
            $arch = Get-FileArchitecture -FilePath $path
            if ($arch -eq "x86") {
                Write-Host "⚠ Architecture mismatch: $library is x86 (needs x64 process compatibility)" -ForegroundColor Yellow
            } elseif ($arch -eq "x64") {
                Write-Host "✓ Compatible: $library is x64" -ForegroundColor Green
            } else {
                Write-Host "? Unknown architecture: $library ($arch)" -ForegroundColor Gray
            }
            break
        }
    }
}

Write-Host "`nStep 4: Creating architecture compatibility solution" -ForegroundColor Cyan

# The application already has architecture bridge implementation
# We need to ensure it's properly configured and the bridge service is available

$bridgeExePath = Join-Path $appPath "Bridge\VolvoFlashWR.VocomBridge.exe"
$bridgeConfigPath = Join-Path $appPath "Bridge\bridge_config.json"

if (Test-Path $bridgeExePath) {
    $bridgeArch = Get-FileArchitecture -FilePath $bridgeExePath
    Write-Host "✓ Found Vocom Bridge: $bridgeExePath ($bridgeArch)" -ForegroundColor Green

    # Ensure bridge is x86 for compatibility with x86 APCI libraries
    if ($bridgeArch -ne "x86") {
        Write-Host "⚠ Bridge should be x86 for APCI library compatibility" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ Vocom Bridge not found at: $bridgeExePath" -ForegroundColor Yellow
    Write-Host "  The application will use fallback mechanisms" -ForegroundColor Gray
}

# Step 5: Update application configuration for x64 compatibility
Write-Host "`nStep 5: Updating application configuration" -ForegroundColor Cyan

$configPath = Join-Path $appPath "Config\app_config.json"
if (Test-Path $configPath) {
    try {
        $config = Get-Content $configPath | ConvertFrom-Json

        # Ensure proper architecture settings
        if (!$config.Application) { $config.Application = @{} }
        $config.Application.ForceArchitectureBridge = $true
        $config.Application.PreferX64Libraries = $true
        $config.Application.EnableVerboseLogging = $true

        # Save updated configuration
        $config | ConvertTo-Json -Depth 10 | Set-Content $configPath
        Write-Host "✓ Updated application configuration" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠ Could not update configuration: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ Configuration file not found: $configPath" -ForegroundColor Yellow
}

# Step 6: Set environment variables for proper library loading
Write-Host "`nStep 6: Setting environment variables" -ForegroundColor Cyan

$envVars = @{
    "USE_PATCHED_IMPLEMENTATION" = "true"
    "PHOENIX_VOCOM_ENABLED" = "true"
    "VERBOSE_LOGGING" = "true"
    "APCI_LIBRARY_PATH" = $librariesPath
    "FORCE_ARCHITECTURE_BRIDGE" = "true"
}

foreach ($envVar in $envVars.GetEnumerator()) {
    [Environment]::SetEnvironmentVariable($envVar.Key, $envVar.Value, "Process")
    Write-Host "✓ Set environment variable: $($envVar.Key) = $($envVar.Value)" -ForegroundColor Green
}

# Step 7: Create a startup script with proper environment
Write-Host "`nStep 7: Creating enhanced startup script" -ForegroundColor Cyan

$startupScript = @"
@echo off
echo === VolvoFlashWR x64 Enhanced Startup ===
echo Setting up environment for x64 architecture compatibility...

REM Set environment variables for proper library loading
set USE_PATCHED_IMPLEMENTATION=true
set PHOENIX_VOCOM_ENABLED=true
set VERBOSE_LOGGING=true
set APCI_LIBRARY_PATH=%~dp0Libraries
set FORCE_ARCHITECTURE_BRIDGE=true

REM Add Libraries directory to PATH
set PATH=%~dp0Libraries;%PATH%

echo Environment configured for x64 compatibility
echo Starting VolvoFlashWR...
echo.

REM Start the application
"%~dp0VolvoFlashWR.Launcher.exe"

if errorlevel 1 (
    echo.
    echo === Application Error Detected ===
    echo Check the logs in the Logs directory for details
    echo Common issues:
    echo - Missing Visual C++ Redistributables
    echo - Architecture compatibility problems
    echo - Hardware connection issues
    echo.
    pause
)
"@

$startupScriptPath = Join-Path $appPath "Run_x64_Compatible.bat"
$startupScript | Set-Content $startupScriptPath -Encoding ASCII
Write-Host "✓ Created enhanced startup script: Run_x64_Compatible.bat" -ForegroundColor Green

# Step 8: Verification and summary
Write-Host "`nStep 8: Verification Summary" -ForegroundColor Cyan

Write-Host "`n=== VERIFICATION RESULTS ===" -ForegroundColor Green

# Check Visual C++ libraries
$vcLibsFound = 0
$vcLibsTotal = $requiredLibraries.Count
foreach ($library in $requiredLibraries) {
    $targetPath = Join-Path $OutputPath $library
    if (Test-Path $targetPath) {
        $vcLibsFound++
    }
}

Write-Host "Visual C++ Libraries: $vcLibsFound/$vcLibsTotal found" -ForegroundColor $(if ($vcLibsFound -eq $vcLibsTotal) { "Green" } else { "Yellow" })

# Check APCI libraries
$apciLibsFound = 0
foreach ($library in $apciLibraries) {
    $found = $false
    $paths = @(
        (Join-Path $appPath $library),
        (Join-Path $librariesPath $library)
    )

    foreach ($path in $paths) {
        if (Test-Path $path) {
            $apciLibsFound++
            $found = $true
            break
        }
    }
}

Write-Host "APCI Libraries: $apciLibsFound/$($apciLibraries.Count) found" -ForegroundColor $(if ($apciLibsFound -gt 0) { "Green" } else { "Yellow" })

Write-Host "`n=== NEXT STEPS ===" -ForegroundColor Cyan
Write-Host "1. Use the enhanced startup script: Run_x64_Compatible.bat" -ForegroundColor White
Write-Host "2. The application will use architecture bridge for x86 library compatibility" -ForegroundColor White
Write-Host "3. Check logs in the Logs directory if issues persist" -ForegroundColor White
Write-Host "4. Ensure Vocom hardware is properly connected" -ForegroundColor White

Write-Host "`n=== FIX COMPLETED ===" -ForegroundColor Green
Write-Host "The x64 architecture library issues have been addressed." -ForegroundColor Green
Write-Host "Run the application using: .\VolvoFlashWR_Export_With_Fix\Run_x64_Compatible.bat" -ForegroundColor Yellow
