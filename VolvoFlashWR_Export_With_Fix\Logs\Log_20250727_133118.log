Log started at 7/27/2025 1:31:18 PM
2025-07-27 13:31:18.896 [Information] LoggingService: Logging service initialized
2025-07-27 13:31:18.919 [Information] App: Starting integrated application initialization
2025-07-27 13:31:18.931 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-27 13:31:18.935 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-27 13:31:18.937 [Information] IntegratedStartupService: Setting up application environment
2025-07-27 13:31:18.941 [Information] IntegratedStartupService: Application environment setup completed
2025-07-27 13:31:18.944 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-27 13:31:18.948 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-27 13:31:18.953 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-27 13:31:18.963 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-27 13:31:18.977 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 13:31:18.984 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:18.987 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:18.991 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-27 13:31:18.996 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 13:31:19.000 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:19.003 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:19.004 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 13:31:19.011 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 13:31:19.015 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:19.018 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:19.019 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 13:31:19.025 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 13:31:19.028 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:19.032 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:19.033 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 13:31:19.037 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 13:31:19.044 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:19.048 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:19.049 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 13:31:19.063 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 13:31:19.067 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:19.077 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:19.081 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 13:31:19.086 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 13:31:19.097 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:19.111 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 13:31:19.121 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 13:31:19.142 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-27 13:31:19.145 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-27 13:31:19.146 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-27 13:31:19.151 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-27 13:31:19.152 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-27 13:31:19.154 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-27 13:31:19.154 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-27 13:31:19.155 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-27 13:31:19.172 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-27 13:31:19.172 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-27 13:31:19.177 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-27 13:31:19.178 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 13:31:19.178 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 13:31:19.179 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 13:31:19.179 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 13:31:19.180 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 13:31:19.180 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 13:31:19.191 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-27 13:31:19.192 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-27 13:31:19.193 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-27 13:31:19.219 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-27 13:31:19.242 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-27 13:31:19.252 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-27 13:31:19.269 [Information] LibraryExtractor: Starting library extraction process
2025-07-27 13:31:19.309 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-27 13:31:19.335 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-27 13:31:19.343 [Information] LibraryExtractor: Copying system libraries
2025-07-27 13:31:19.359 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-27 13:31:19.375 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-27 13:31:49.414 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 13:31:50.420 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-27 13:32:20.425 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 13:32:20.425 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 13:32:50.429 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 13:32:51.429 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-27 13:33:21.434 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 13:33:21.436 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 13:33:51.440 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 13:33:52.441 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-27 13:34:22.446 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 13:34:22.448 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-27 13:34:22.448 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-27 13:34:22.453 [Information] LibraryExtractor: Verifying library extraction
2025-07-27 13:34:22.453 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-27 13:34:22.454 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-27 13:34:22.454 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-27 13:34:22.455 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-27 13:34:22.455 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-27 13:34:22.459 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-27 13:34:22.461 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-27 13:34:22.463 [Information] DependencyManager: Initializing dependency manager
2025-07-27 13:34:22.464 [Information] DependencyManager: Setting up library search paths
2025-07-27 13:34:22.465 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 13:34:22.466 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 13:34:22.466 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 13:34:22.467 [Information] DependencyManager: Updated PATH environment variable
2025-07-27 13:34:22.469 [Information] DependencyManager: Verifying required directories
2025-07-27 13:34:22.469 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 13:34:22.470 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 13:34:22.470 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-27 13:34:22.471 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 13:34:22.473 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-27 13:34:22.481 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 13:34:22.490 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 13:34:22.492 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 13:34:22.500 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 13:34:22.503 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 13:34:22.503 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 13:34:22.506 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 13:34:22.511 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll from C:\Windows\system32\msvcp140.dll: Error 193
2025-07-27 13:34:22.511 [Warning] DependencyManager: Architecture mismatch detected for msvcp140.dll. Expected: x64
2025-07-27 13:34:22.512 [Debug] DependencyManager: Architecture mismatch: Library msvcp140.dll is x86, process is x64
2025-07-27 13:34:22.512 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp140.dll
2025-07-27 13:34:22.513 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll: Error 193
2025-07-27 13:34:22.514 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp140.dll
2025-07-27 13:34:22.516 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 13:34:22.518 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 13:34:22.519 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 13:34:22.520 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 13:34:22.522 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 13:34:22.523 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 13:34:22.524 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 13:34:22.524 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 13:34:22.526 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 13:34:22.527 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 13:34:22.528 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 13:34:22.528 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 13:34:22.529 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 13:34:22.529 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 13:34:22.530 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 13:34:22.531 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 13:34:22.531 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 13:34:22.532 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 13:34:22.533 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 13:34:22.533 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 13:34:22.534 [Information] DependencyManager: VC++ Redistributable library loading: 3/14 (21.4%) libraries loaded
2025-07-27 13:34:22.534 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-27 13:34:22.536 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-27 13:34:22.540 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-27 13:34:22.541 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 13:34:22.542 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-27 13:34:22.543 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 13:34:22.545 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 13:34:22.548 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-27 13:34:22.549 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-27 13:34:22.550 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-27 13:34:22.551 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-27 13:34:22.551 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-07-27 13:34:22.552 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-27 13:34:22.553 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-27 13:34:22.554 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-27 13:34:22.556 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-27 13:34:22.557 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-07-27 13:34:22.558 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-27 13:34:22.559 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-27 13:34:22.560 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-27 13:34:22.561 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-27 13:34:22.561 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-07-27 13:34:22.563 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-27 13:34:22.564 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-27 13:34:22.565 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 13:34:22.567 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 13:34:22.567 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 13:34:22.568 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 13:34:22.569 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 13:34:22.569 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 13:34:22.571 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-27 13:34:22.572 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 13:34:22.574 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-27 13:34:22.576 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 13:34:22.577 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 13:34:22.578 [Information] DependencyManager: Setting up environment variables
2025-07-27 13:34:22.578 [Information] DependencyManager: Environment variables configured
2025-07-27 13:34:22.580 [Information] DependencyManager: Verifying library loading status
2025-07-27 13:34:22.974 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-27 13:34:22.974 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-27 13:34:22.974 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-27 13:34:22.977 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-27 13:34:22.979 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-27 13:34:22.984 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-27 13:34:22.986 [Information] IntegratedStartupService: Verifying system readiness
2025-07-27 13:34:22.987 [Information] IntegratedStartupService: System readiness verification passed
2025-07-27 13:34:22.987 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-27 13:34:22.990 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-27 13:34:22.992 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 13:34:22.993 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 13:34:22.993 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 13:34:22.993 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-27 13:34:22.993 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-27 13:34:22.994 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-27 13:34:22.994 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 13:34:22.994 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-27 13:34:22.995 [Information] App: Integrated startup completed successfully
2025-07-27 13:34:22.998 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-27 13:34:23.022 [Information] App: Initializing application services
2025-07-27 13:34:23.024 [Information] AppConfigurationService: Initializing configuration service
2025-07-27 13:34:23.025 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 13:34:23.085 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 13:34:23.086 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-27 13:34:23.087 [Information] App: Configuration service initialized successfully
2025-07-27 13:34:23.090 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-27 13:34:23.090 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-27 13:34:23.097 [Information] App: Environment variable exists: True, not 'false': False
2025-07-27 13:34:23.098 [Information] App: Final useDummyImplementations value: False
2025-07-27 13:34:23.098 [Information] App: Updating config to NOT use dummy implementations
2025-07-27 13:34:23.100 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-27 13:34:23.119 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 13:34:23.121 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-27 13:34:23.122 [Information] App: usePatchedImplementation flag is: True
2025-07-27 13:34:23.122 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-27 13:34:23.123 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-27 13:34:23.123 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-27 13:34:23.123 [Information] App: verboseLogging flag is: True
2025-07-27 13:34:23.125 [Information] App: Verifying real hardware requirements...
2025-07-27 13:34:23.127 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-27 13:34:23.127 [Information] App: ✓ Found critical library: apci.dll
2025-07-27 13:34:23.128 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-27 13:34:23.128 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-27 13:34:23.129 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 13:34:23.130 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-27 13:34:23.130 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-27 13:34:23.130 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-27 13:34:23.141 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-27 13:34:23.144 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-27 13:34:23.144 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-27 13:34:23.147 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-27 13:34:23.151 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-07-27 13:39:23.153 [Warning] RuntimeDependencyResolver: Failed to download msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-27 13:39:23.156 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-27 13:39:23.156 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-27 13:39:23.156 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-27 13:39:23.157 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 13:39:23.158 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 13:39:23.159 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 13:39:23.162 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 13:39:23.163 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 13:39:23.163 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 13:39:23.164 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-27 13:39:23.166 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-27 13:39:23.166 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-27 13:39:23.166 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 13:39:23.167 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-27 13:39:23.168 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-27 13:39:23.168 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-27 13:39:23.169 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-27 13:39:23.169 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-27 13:39:23.173 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-27 13:39:23.173 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-27 13:39:23.176 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-27 13:39:23.180 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-27 13:39:23.181 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-27 13:39:23.181 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-27 13:39:23.183 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-27 13:39:23.184 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-07-27 13:39:23.186 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-07-27 13:39:23.187 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-27 13:39:23.189 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-07-27 13:39:23.190 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-07-27 13:39:23.241 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-27 13:39:23.246 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-07-27 13:39:23.268 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-27 13:39:23.269 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-27 13:39:23.270 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-27 13:39:23.271 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-27 13:39:23.287 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-27 13:39:23.289 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-27 13:39:23.289 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-27 13:39:23.289 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 13:39:23.290 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-27 13:39:23.290 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-27 13:39:23.291 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-27 13:39:23.295 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-27 13:39:23.300 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-27 13:39:23.324 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-27 13:39:23.336 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 13:39:23.336 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-27 13:39:23.363 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 13:39:23.401 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-27 13:39:23.402 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-27 13:39:23.403 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-27 13:39:23.405 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 13:39:23.405 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-07-27 13:39:23.406 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 13:39:23.406 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-07-27 13:39:23.407 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 13:39:23.412 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-27 13:39:23.414 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-27 13:39:23.414 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-27 13:39:23.415 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-27 13:39:23.418 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-27 13:39:23.418 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-27 13:39:23.418 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-27 13:39:23.422 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-27 13:39:23.424 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-27 13:39:23.425 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 13:39:23.426 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-27 13:39:23.426 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-27 13:39:23.432 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-27 13:39:23.433 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-27 13:39:23.434 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-27 13:39:23.434 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-27 13:39:23.435 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-27 13:39:23.435 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-27 13:39:23.436 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-27 13:39:23.437 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-27 13:39:23.438 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-27 13:39:23.438 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-27 13:39:23.440 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-27 13:39:23.441 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-27 13:39:23.441 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-27 13:39:23.441 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 13:39:23.443 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-27 13:39:23.447 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-27 13:39:23.448 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 13:39:23.449 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-27 13:39:23.449 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-27 13:39:23.451 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-27 13:39:23.452 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-27 13:39:23.452 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-27 13:39:23.456 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-27 13:39:23.461 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-27 13:39:23.461 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-27 13:39:23.473 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-27 13:39:23.474 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-27 13:39:23.474 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 13:39:23.475 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 13:39:23.485 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-27 13:39:23.486 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 13:39:23.489 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 13:39:23.490 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-27 13:39:23.491 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 13:39:23.492 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 13:39:23.492 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-27 13:39:23.496 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 13:39:23.496 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-27 13:39:23.498 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 13:39:23.498 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-27 13:39:23.500 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 13:39:23.502 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 13:39:23.502 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-27 13:39:23.504 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 13:39:23.505 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 13:39:23.506 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-27 13:39:23.508 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 13:39:23.511 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 13:39:23.513 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 13:39:23.515 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 13:39:23.515 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-27 13:39:23.516 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 13:39:23.518 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 13:39:23.519 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-27 13:39:23.520 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 13:39:23.522 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 13:39:23.522 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-27 13:39:23.524 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 13:39:23.525 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 13:39:23.525 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-27 13:39:23.532 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 13:39:23.534 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 13:39:23.536 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 13:39:23.541 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-27 13:39:23.542 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 13:39:23.542 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-27 13:39:23.545 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-27 13:39:23.546 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-27 13:39:23.550 [Information] VocomDriver: Initializing Vocom driver
2025-07-27 13:39:23.552 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-27 13:39:23.558 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-27 13:39:23.559 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 13:39:23.559 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 13:39:23.563 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 13:39:23.563 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 13:39:23.571 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-27 13:39:23.572 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-27 13:39:23.575 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-27 13:39:23.577 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-27 13:39:23.579 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-27 13:39:23.580 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 13:39:23.585 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-07-27 13:39:23.589 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-07-27 13:39:23.592 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-07-27 13:39:23.594 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-27 13:39:23.595 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-27 13:39:23.596 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-27 13:39:23.598 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-27 13:39:23.599 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-27 13:39:23.600 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-27 13:39:23.600 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-27 13:39:23.600 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-27 13:39:23.601 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-27 13:39:23.601 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-27 13:39:23.601 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-27 13:39:23.602 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-27 13:39:23.602 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-27 13:39:23.602 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-27 13:39:23.603 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-27 13:39:23.603 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-27 13:39:23.603 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-27 13:39:23.604 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-27 13:39:23.604 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-27 13:39:23.604 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-27 13:39:23.605 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-27 13:39:23.605 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-27 13:39:23.606 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-27 13:39:23.606 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-27 13:39:23.606 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-27 13:39:23.607 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-27 13:39:23.607 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-27 13:39:23.607 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-27 13:39:23.617 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-27 13:39:23.617 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-27 13:39:23.618 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-27 13:39:23.619 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-27 13:39:23.619 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-27 13:39:23.619 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-27 13:39:23.620 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-27 13:39:23.620 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-27 13:39:23.620 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-27 13:39:23.621 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-27 13:39:23.621 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-27 13:39:23.621 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-27 13:39:23.622 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-27 13:39:23.622 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-27 13:39:23.622 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-27 13:39:23.623 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-27 13:39:23.623 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-27 13:39:23.623 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-27 13:39:23.625 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-27 13:39:23.627 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-27 13:39:23.629 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-27 13:39:23.630 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-27 13:39:23.630 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-27 13:39:23.631 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-27 13:39:23.631 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-27 13:39:23.632 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-27 13:39:23.639 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-27 13:39:23.641 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-27 13:39:23.644 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-27 13:39:23.646 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-27 13:39:23.807 [Information] WiFiCommunicationService: WiFi is available
2025-07-27 13:39:23.817 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-27 13:39:23.821 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-27 13:39:23.830 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-27 13:39:23.837 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-27 13:39:23.841 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-27 13:39:23.845 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 13:39:23.847 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 13:39:23.850 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 13:39:23.854 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 13:39:23.862 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 13:39:23.863 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 13:39:23.863 [Information] VocomService: Native USB communication service initialized
2025-07-27 13:39:23.864 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 13:39:23.864 [Information] VocomService: Connection recovery service initialized
2025-07-27 13:39:23.865 [Information] VocomService: Enhanced services initialization completed
2025-07-27 13:39:23.869 [Information] VocomService: Checking if PTT application is running
2025-07-27 13:39:23.904 [Information] VocomService: PTT application is not running
2025-07-27 13:39:23.906 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 13:39:23.913 [Debug] VocomService: Bluetooth is enabled
2025-07-27 13:39:23.914 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 13:39:23.915 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-27 13:39:23.916 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-07-27 13:39:23.921 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 13:39:23.921 [Information] VocomService: Using new enhanced device detection service
2025-07-27 13:39:23.926 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 13:39:23.932 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 13:39:24.870 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 13:39:24.872 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 13:39:24.874 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 13:39:24.881 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 13:39:24.882 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 13:39:24.885 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 13:39:24.891 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 13:39:24.898 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 13:39:25.510 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 13:39:25.515 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 13:39:25.519 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 13:39:25.520 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 13:39:25.522 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 13:39:25.543 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 13:39:25.552 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 13:39:25.552 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 13:39:25.553 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 13:39:25.554 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 13:39:25.556 [Debug] VocomService: Bluetooth is enabled
2025-07-27 13:39:25.562 [Debug] VocomService: Checking if WiFi is available
2025-07-27 13:39:25.567 [Debug] VocomService: WiFi is available
2025-07-27 13:39:25.568 [Information] VocomService: Found 3 Vocom devices
2025-07-27 13:39:25.570 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-07-27 13:39:25.573 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: 88cc077d-4b83-4c8f-93db-d2d4335ab16b, Type: USB)
2025-07-27 13:39:25.573 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: a580caba-5112-4493-901b-d9a3186e3dc5, Type: Bluetooth)
2025-07-27 13:39:25.573 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: bd682183-5ee4-4308-a944-969d41936576, Type: WiFi)
2025-07-27 13:39:25.576 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-07-27 13:39:25.577 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-07-27 13:39:25.579 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-07-27 13:39:25.580 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-07-27 13:39:25.580 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-07-27 13:39:25.581 [Information] App: Architecture-aware Vocom service created successfully
2025-07-27 13:39:25.582 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 13:39:25.582 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 13:39:25.583 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 13:39:25.583 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 13:39:25.585 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 13:39:25.586 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 13:39:25.586 [Information] VocomService: Native USB communication service initialized
2025-07-27 13:39:25.587 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 13:39:25.587 [Information] VocomService: Connection recovery service initialized
2025-07-27 13:39:25.587 [Information] VocomService: Enhanced services initialization completed
2025-07-27 13:39:25.588 [Information] VocomService: Checking if PTT application is running
2025-07-27 13:39:25.637 [Information] VocomService: PTT application is not running
2025-07-27 13:39:25.638 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 13:39:25.639 [Debug] VocomService: Bluetooth is enabled
2025-07-27 13:39:25.640 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 13:39:25.640 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-27 13:39:25.640 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-27 13:39:25.641 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-27 13:39:25.732 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 13:39:25.733 [Information] VocomService: Using new enhanced device detection service
2025-07-27 13:39:25.733 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 13:39:25.734 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 13:39:26.649 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 13:39:26.650 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 13:39:26.651 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 13:39:26.652 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 13:39:26.652 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 13:39:26.653 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 13:39:26.653 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 13:39:26.654 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 13:39:27.233 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 13:39:27.236 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 13:39:27.238 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 13:39:27.238 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 13:39:27.240 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 13:39:27.259 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 13:39:27.260 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 13:39:27.260 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 13:39:27.261 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 13:39:27.263 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 13:39:27.265 [Debug] VocomService: Bluetooth is enabled
2025-07-27 13:39:27.265 [Debug] VocomService: Checking if WiFi is available
2025-07-27 13:39:27.265 [Debug] VocomService: WiFi is available
2025-07-27 13:39:27.266 [Information] VocomService: Found 3 Vocom devices
2025-07-27 13:39:27.266 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-27 13:39:27.272 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 13:39:27.272 [Information] VocomService: Checking if PTT application is running
2025-07-27 13:39:27.305 [Information] VocomService: PTT application is not running
2025-07-27 13:39:27.316 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 13:39:27.319 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-27 13:39:27.320 [Information] VocomService: Checking if PTT application is running
2025-07-27 13:39:27.365 [Information] VocomService: PTT application is not running
2025-07-27 13:39:27.365 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-27 13:39:27.371 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-27 13:39:27.373 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 13:39:27.377 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 13:39:27.380 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:27.382 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:27.382 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:27.383 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:27.383 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:27.384 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:27.384 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:27.385 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:27.385 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:27.385 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 13:39:27.386 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:27.386 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:27.386 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:27.387 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:27.387 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:27.388 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 13:39:27.389 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:39:27.389 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 13:39:27.390 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:39:27.390 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 13:39:27.390 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 13:39:27.391 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 13:39:27.391 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 13:39:28.393 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-27 13:39:28.393 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 13:39:28.393 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 13:39:28.394 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:28.395 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:28.396 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:28.396 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:28.397 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:28.397 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:28.398 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:28.399 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:28.399 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:28.400 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 13:39:28.400 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:28.401 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:28.402 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:28.402 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:28.403 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:28.403 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 13:39:28.404 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:39:28.404 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 13:39:28.404 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:39:28.405 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 13:39:28.405 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 13:39:28.405 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 13:39:28.406 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 13:39:29.406 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-27 13:39:29.409 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 13:39:29.409 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 13:39:29.413 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:29.413 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:29.414 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:29.414 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:29.415 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:29.415 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:29.415 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:29.416 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:29.416 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:29.416 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 13:39:29.417 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:29.419 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:29.419 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:29.419 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:29.420 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:29.420 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 13:39:29.421 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:39:29.421 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 13:39:29.421 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:39:29.422 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 13:39:29.422 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 13:39:29.422 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 13:39:29.423 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 13:39:29.424 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 13:39:29.424 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-27 13:39:29.501 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 13:39:29.503 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-27 13:39:29.505 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 13:39:29.506 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 13:39:29.507 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 13:39:29.507 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 13:39:29.508 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 13:39:29.513 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 13:39:29.515 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 13:39:29.521 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-27 13:39:29.523 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 13:39:29.523 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 13:39:29.524 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 13:39:29.524 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 13:39:29.525 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 13:39:29.526 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-27 13:39:29.536 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-27 13:39:29.541 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 13:39:29.543 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-27 13:39:29.557 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 13:39:29.565 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 13:39:29.567 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 13:39:29.572 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 13:39:29.573 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-07-27 13:39:29.573 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 13:39:29.574 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-27 13:39:29.574 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-27 13:39:29.580 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-27 13:39:29.581 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 13:39:29.581 [Information] VocomService: Using new enhanced device detection service
2025-07-27 13:39:29.582 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 13:39:29.582 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 13:39:30.209 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 13:39:30.210 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 13:39:30.211 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 13:39:30.214 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 13:39:30.214 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 13:39:30.214 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 13:39:30.215 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 13:39:30.216 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 13:39:30.739 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 13:39:30.739 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 13:39:30.740 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 13:39:30.741 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 13:39:30.742 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 13:39:30.755 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 13:39:30.756 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 13:39:30.756 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 13:39:30.756 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 13:39:30.757 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 13:39:30.760 [Debug] VocomService: Bluetooth is enabled
2025-07-27 13:39:30.765 [Debug] VocomService: Checking if WiFi is available
2025-07-27 13:39:30.766 [Debug] VocomService: WiFi is available
2025-07-27 13:39:30.766 [Information] VocomService: Found 3 Vocom devices
2025-07-27 13:39:30.769 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 13:39:30.769 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 13:39:30.769 [Information] VocomService: Checking if PTT application is running
2025-07-27 13:39:30.801 [Information] VocomService: PTT application is not running
2025-07-27 13:39:30.804 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 13:39:30.815 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-27 13:39:30.815 [Information] VocomService: Checking if PTT application is running
2025-07-27 13:39:30.853 [Information] VocomService: PTT application is not running
2025-07-27 13:39:30.855 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-27 13:39:30.856 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-27 13:39:30.856 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 13:39:30.858 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 13:39:30.860 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:30.861 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:30.863 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:30.866 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:30.866 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:30.867 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:30.869 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:30.869 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:30.869 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:30.870 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 13:39:30.870 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:30.871 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:30.872 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:30.873 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:30.873 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:30.874 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 13:39:30.874 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:39:30.875 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 13:39:30.875 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:39:30.875 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 13:39:30.876 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 13:39:30.876 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 13:39:30.877 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 13:39:31.882 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-27 13:39:31.883 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 13:39:31.883 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 13:39:31.884 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:31.885 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:31.885 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:31.885 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:31.886 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:31.886 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:31.886 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:31.887 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:31.887 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:31.888 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 13:39:31.888 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:31.888 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:31.889 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:31.889 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:31.889 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:31.890 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 13:39:31.890 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:39:31.891 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 13:39:31.891 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:39:31.891 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 13:39:31.891 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 13:39:31.892 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 13:39:31.892 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 13:39:32.893 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-27 13:39:32.894 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 13:39:32.894 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 13:39:32.895 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:32.898 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:32.898 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:32.899 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:32.899 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:32.900 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:32.900 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:32.901 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:32.901 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:32.902 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 13:39:32.902 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:32.902 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:32.903 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:39:32.904 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:39:32.904 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:39:32.905 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 13:39:32.905 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:39:32.906 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 13:39:32.906 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:39:32.906 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 13:39:32.907 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 13:39:32.907 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 13:39:32.910 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 13:39:32.910 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 13:39:32.911 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-27 13:39:32.911 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 13:39:32.915 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-27 13:39:32.915 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 13:39:32.916 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 13:39:32.916 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 13:39:32.916 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 13:39:32.917 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 13:39:32.919 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 13:39:32.919 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 13:39:32.920 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-27 13:39:32.921 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 13:39:32.921 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 13:39:32.921 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 13:39:32.922 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 13:39:32.923 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 13:39:32.923 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 13:39:32.924 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 13:39:32.924 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 13:39:32.925 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 13:39:32.926 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 13:39:32.927 [Information] VocomService: Checking if PTT application is running
2025-07-27 13:39:32.953 [Information] VocomService: PTT application is not running
2025-07-27 13:39:32.956 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 13:39:32.957 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 13:39:32.959 [Debug] VocomService: Bluetooth is enabled
2025-07-27 13:39:32.962 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-27 13:39:33.773 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 13:39:33.774 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 13:39:33.775 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-27 13:39:33.777 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 13:39:33.791 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 13:39:33.795 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 13:39:33.807 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 13:39:33.811 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 13:39:33.818 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 13:39:33.831 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 13:39:33.835 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 13:39:33.851 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 13:39:33.852 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 13:39:33.854 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 13:39:33.855 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 13:39:33.855 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 13:39:33.856 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 13:39:33.856 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 13:39:33.856 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 13:39:33.857 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 13:39:33.857 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 13:39:33.858 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 13:39:33.858 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 13:39:33.859 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 13:39:33.859 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 13:39:33.859 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 13:39:33.860 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 13:39:33.860 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 13:39:33.867 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 13:39:33.874 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 13:39:33.876 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 13:39:33.885 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 13:39:33.888 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 13:39:33.895 [Information] CANRegisterAccess: Read value 0xD8 from register 0x0141 (simulated)
2025-07-27 13:39:33.904 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 13:39:33.910 [Information] CANRegisterAccess: Read value 0x85 from register 0x0141 (simulated)
2025-07-27 13:39:33.910 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 13:39:33.911 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 13:39:33.912 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 13:39:33.918 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 13:39:33.919 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 13:39:33.925 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 13:39:33.925 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 13:39:33.926 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 13:39:33.932 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 13:39:33.933 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 13:39:33.933 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 13:39:33.939 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 13:39:33.939 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 13:39:33.949 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 13:39:33.950 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 13:39:33.956 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 13:39:33.957 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 13:39:33.964 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 13:39:33.965 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 13:39:33.971 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 13:39:33.971 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 13:39:33.977 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 13:39:33.978 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 13:39:33.984 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 13:39:33.984 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 13:39:33.990 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 13:39:33.990 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 13:39:33.999 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 13:39:33.999 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 13:39:34.006 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 13:39:34.006 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 13:39:34.012 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 13:39:34.013 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 13:39:34.020 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 13:39:34.020 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 13:39:34.026 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 13:39:34.026 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 13:39:34.032 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 13:39:34.032 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 13:39:34.038 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 13:39:34.038 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 13:39:34.045 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 13:39:34.045 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 13:39:34.054 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 13:39:34.054 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 13:39:34.055 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 13:39:34.062 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 13:39:34.062 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 13:39:34.063 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 13:39:34.064 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 13:39:34.071 [Information] CANRegisterAccess: Read value 0x8C from register 0x0141 (simulated)
2025-07-27 13:39:34.071 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 13:39:34.072 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 13:39:34.072 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 13:39:34.072 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 13:39:34.077 [Information] CANRegisterAccess: Read value 0x9A from register 0x0140 (simulated)
2025-07-27 13:39:34.078 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 13:39:34.079 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 13:39:34.084 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 13:39:34.084 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 13:39:34.096 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 13:39:34.099 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 13:39:34.100 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 13:39:34.107 [Information] VocomService: Sending data and waiting for response
2025-07-27 13:39:34.107 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 13:39:34.160 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 13:39:34.162 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 13:39:34.162 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 13:39:34.166 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 13:39:34.166 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 13:39:34.178 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 13:39:34.181 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 13:39:34.182 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 13:39:34.193 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 13:39:34.204 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 13:39:34.215 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 13:39:34.226 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 13:39:34.237 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 13:39:34.241 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 13:39:34.241 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 13:39:34.252 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 13:39:34.253 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 13:39:34.253 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 13:39:34.264 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 13:39:34.276 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 13:39:34.287 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 13:39:34.299 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 13:39:34.310 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 13:39:34.321 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 13:39:34.323 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 13:39:34.324 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 13:39:34.335 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 13:39:34.336 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 13:39:34.336 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 13:39:34.337 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 13:39:34.337 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 13:39:34.337 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 13:39:34.338 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 13:39:34.338 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 13:39:34.338 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 13:39:34.339 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 13:39:34.339 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 13:39:34.339 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 13:39:34.340 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 13:39:34.340 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 13:39:34.340 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 13:39:34.341 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 13:39:34.341 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 13:39:34.441 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 13:39:34.441 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 13:39:34.445 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 13:39:34.449 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 13:39:34.450 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 13:39:34.451 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 13:39:34.451 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 13:39:34.452 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 13:39:34.452 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 13:39:34.453 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 13:39:34.453 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 13:39:34.453 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 13:39:34.454 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 13:39:34.455 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 13:39:34.455 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 13:39:34.457 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-27 13:39:34.459 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-27 13:39:34.459 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-27 13:39:34.467 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-27 13:39:34.470 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-27 13:39:34.476 [Information] BackupService: Initializing backup service
2025-07-27 13:39:34.476 [Information] BackupService: Backup service initialized successfully
2025-07-27 13:39:34.477 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-27 13:39:34.477 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-27 13:39:34.483 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-27 13:39:34.570 [Information] BackupService: Compressing backup data
2025-07-27 13:39:34.592 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-07-27 13:39:34.595 [Information] BackupServiceFactory: Created template for category: Production
2025-07-27 13:39:34.601 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-27 13:39:34.603 [Information] BackupService: Compressing backup data
2025-07-27 13:39:34.606 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-07-27 13:39:34.606 [Information] BackupServiceFactory: Created template for category: Development
2025-07-27 13:39:34.607 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-27 13:39:34.608 [Information] BackupService: Compressing backup data
2025-07-27 13:39:34.612 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (449 bytes)
2025-07-27 13:39:34.614 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-27 13:39:34.614 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-27 13:39:34.615 [Information] BackupService: Compressing backup data
2025-07-27 13:39:34.616 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-27 13:39:34.616 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-27 13:39:34.617 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-27 13:39:34.617 [Information] BackupService: Compressing backup data
2025-07-27 13:39:34.621 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-07-27 13:39:34.622 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-27 13:39:34.623 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-27 13:39:34.624 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-27 13:39:34.625 [Information] BackupService: Compressing backup data
2025-07-27 13:39:34.626 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (515 bytes)
2025-07-27 13:39:34.626 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-27 13:39:34.627 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-27 13:39:34.629 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-27 13:39:34.635 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 13:39:34.637 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 13:39:34.773 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 13:39:34.774 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 13:39:34.776 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-27 13:39:34.777 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-27 13:39:34.777 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-27 13:39:34.782 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-27 13:39:34.783 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-27 13:39:34.790 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-27 13:39:34.790 [Information] App: Flash operation monitor service initialized successfully
2025-07-27 13:39:34.817 [Information] LicensingService: Initializing licensing service
2025-07-27 13:39:34.952 [Information] LicensingService: License information loaded successfully
2025-07-27 13:39:34.957 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-27 13:39:34.957 [Information] App: Licensing service initialized successfully
2025-07-27 13:39:34.958 [Information] App: License status: Trial
2025-07-27 13:39:34.959 [Information] App: Trial period: 30 days remaining
2025-07-27 13:39:34.960 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-27 13:39:35.017 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-27 13:39:35.239 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 13:39:35.239 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 13:39:35.240 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 13:39:35.240 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 13:39:35.241 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 13:39:35.241 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 13:39:35.242 [Information] VocomService: Native USB communication service initialized
2025-07-27 13:39:35.242 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 13:39:35.242 [Information] VocomService: Connection recovery service initialized
2025-07-27 13:39:35.243 [Information] VocomService: Enhanced services initialization completed
2025-07-27 13:39:35.243 [Information] VocomService: Checking if PTT application is running
2025-07-27 13:39:35.267 [Information] VocomService: PTT application is not running
2025-07-27 13:39:35.268 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 13:39:35.271 [Debug] VocomService: Bluetooth is enabled
2025-07-27 13:39:35.272 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 13:39:35.323 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 13:39:35.324 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 13:39:35.324 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 13:39:35.325 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 13:39:35.325 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-27 13:39:35.325 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: 13b1a1ed-bb68-43c0-8d1a-f37a5781081b
2025-07-27 13:39:35.328 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-27 13:39:35.329 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 13:39:35.329 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 13:39:35.330 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 13:39:35.336 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 13:39:35.336 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 13:39:35.339 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 13:39:35.340 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 13:39:35.340 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 13:39:35.352 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 13:39:35.352 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 13:39:35.353 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 13:39:35.353 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 13:39:35.353 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 13:39:35.354 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 13:39:35.354 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 13:39:35.355 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 13:39:35.355 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 13:39:35.356 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 13:39:35.356 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 13:39:35.356 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 13:39:35.357 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 13:39:35.358 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 13:39:35.358 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 13:39:35.359 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 13:39:35.359 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 13:39:35.359 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 13:39:35.367 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 13:39:35.367 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 13:39:35.368 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 13:39:35.368 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 13:39:35.374 [Information] CANRegisterAccess: Read value 0x03 from register 0x0141 (simulated)
2025-07-27 13:39:35.375 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 13:39:35.375 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 13:39:35.376 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 13:39:35.383 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 13:39:35.383 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 13:39:35.389 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 13:39:35.390 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 13:39:35.391 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 13:39:35.399 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 13:39:35.400 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 13:39:35.401 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 13:39:35.407 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 13:39:35.408 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 13:39:35.415 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 13:39:35.416 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 13:39:35.422 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 13:39:35.423 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 13:39:35.430 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 13:39:35.431 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 13:39:35.438 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 13:39:35.439 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 13:39:35.445 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 13:39:35.446 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 13:39:35.453 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 13:39:35.453 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 13:39:35.459 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 13:39:35.460 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 13:39:35.466 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 13:39:35.467 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 13:39:35.473 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 13:39:35.474 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 13:39:35.494 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 13:39:35.495 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 13:39:35.501 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 13:39:35.502 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 13:39:35.511 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 13:39:35.512 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 13:39:35.519 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 13:39:35.519 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 13:39:35.525 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 13:39:35.526 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 13:39:35.533 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 13:39:35.534 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 13:39:35.540 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 13:39:35.541 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 13:39:35.541 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 13:39:35.550 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 13:39:35.550 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 13:39:35.551 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 13:39:35.551 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 13:39:35.559 [Information] CANRegisterAccess: Read value 0xF2 from register 0x0141 (simulated)
2025-07-27 13:39:35.560 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 13:39:35.561 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 13:39:35.561 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 13:39:35.561 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 13:39:35.572 [Information] CANRegisterAccess: Read value 0xCF from register 0x0140 (simulated)
2025-07-27 13:39:35.578 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 13:39:35.584 [Information] CANRegisterAccess: Read value 0x5B from register 0x0140 (simulated)
2025-07-27 13:39:35.585 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 13:39:35.585 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 13:39:35.586 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 13:39:35.586 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 13:39:35.601 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 13:39:35.602 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 13:39:35.602 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 13:39:35.602 [Information] VocomService: Sending data and waiting for response
2025-07-27 13:39:35.603 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 13:39:35.654 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 13:39:35.655 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 13:39:35.655 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 13:39:35.656 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 13:39:35.656 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 13:39:35.667 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 13:39:35.668 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 13:39:35.668 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 13:39:35.679 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 13:39:35.690 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 13:39:35.701 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 13:39:35.712 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 13:39:35.723 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 13:39:35.724 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 13:39:35.724 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 13:39:35.735 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 13:39:35.736 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 13:39:35.736 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 13:39:35.751 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 13:39:35.762 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 13:39:35.773 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 13:39:35.784 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 13:39:35.795 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 13:39:35.806 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 13:39:35.807 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 13:39:35.807 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 13:39:35.819 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 13:39:35.819 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 13:39:35.820 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 13:39:35.820 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 13:39:35.821 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 13:39:35.821 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 13:39:35.821 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 13:39:35.822 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 13:39:35.822 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 13:39:35.822 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 13:39:35.823 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 13:39:35.823 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 13:39:35.823 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 13:39:35.824 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 13:39:35.824 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 13:39:35.824 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 13:39:35.825 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 13:39:35.926 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 13:39:35.926 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 13:39:35.927 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 13:39:35.928 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 13:39:35.928 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 13:39:35.929 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 13:39:35.929 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 13:39:35.930 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 13:39:35.931 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 13:39:35.931 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 13:39:35.932 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 13:39:35.932 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 13:39:35.933 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 13:39:35.933 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 13:39:35.934 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 13:39:35.995 [Information] BackupService: Initializing backup service
2025-07-27 13:39:35.996 [Information] BackupService: Backup service initialized successfully
2025-07-27 13:39:36.050 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 13:39:36.051 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 13:39:36.053 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 13:39:36.053 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 13:39:36.106 [Information] BackupService: Getting predefined backup categories
2025-07-27 13:39:36.159 [Information] MainViewModel: Services initialized successfully
2025-07-27 13:39:36.163 [Information] MainViewModel: Scanning for Vocom devices
2025-07-27 13:39:36.166 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 13:39:36.167 [Information] VocomService: Using new enhanced device detection service
2025-07-27 13:39:36.167 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 13:39:36.167 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 13:39:36.679 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 13:39:36.680 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 13:39:36.680 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 13:39:36.682 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 13:39:36.683 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 13:39:36.683 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 13:39:36.683 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 13:39:36.684 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 13:39:37.274 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 13:39:37.275 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 13:39:37.276 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 13:39:37.276 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 13:39:37.279 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 13:39:37.293 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 13:39:37.294 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 13:39:37.294 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 13:39:37.295 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 13:39:37.295 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 13:39:37.300 [Debug] VocomService: Bluetooth is enabled
2025-07-27 13:39:37.301 [Debug] VocomService: Checking if WiFi is available
2025-07-27 13:39:37.301 [Debug] VocomService: WiFi is available
2025-07-27 13:39:37.302 [Information] VocomService: Found 3 Vocom devices
2025-07-27 13:39:37.304 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-27 13:43:45.414 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-07-27 13:43:45.415 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 13:43:45.417 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-27 13:43:45.419 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-27 13:43:45.821 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-27 13:43:45.823 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-27 13:43:45.823 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-27 13:43:45.826 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-27 13:43:45.828 [Information] ECUCommunicationService: No ECUs are connected
2025-07-27 13:43:45.828 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-27 13:43:45.829 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-27 13:43:45.829 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-27 13:43:45.829 [Information] ECUCommunicationService: No ECUs are connected
2025-07-27 13:43:45.830 [Information] VocomService: Checking if PTT application is running
2025-07-27 13:43:45.846 [Information] VocomService: PTT application is not running
2025-07-27 13:43:45.846 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 13:43:45.847 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-27 13:43:45.847 [Information] VocomService: Checking if PTT application is running
2025-07-27 13:43:45.866 [Information] VocomService: PTT application is not running
2025-07-27 13:43:45.866 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-27 13:43:45.867 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-27 13:43:45.868 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 13:43:45.868 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 13:43:45.869 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:43:45.869 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:45.870 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:45.870 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:43:45.870 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:45.871 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:45.871 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:43:45.875 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:45.875 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:45.876 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 13:43:45.876 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:45.876 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:45.877 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:43:45.877 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:45.878 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:45.878 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 13:43:45.879 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:43:45.879 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 13:43:45.879 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:43:45.879 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 13:43:45.880 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 13:43:45.880 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 13:43:45.880 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 13:43:46.880 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-27 13:43:46.880 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 13:43:46.881 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 13:43:46.881 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:43:46.882 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:46.882 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:46.882 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:43:46.883 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:46.883 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:46.883 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:43:46.884 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:46.884 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:46.884 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 13:43:46.885 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:46.885 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:46.885 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:43:46.886 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:46.886 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:46.886 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 13:43:46.887 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:43:46.887 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 13:43:46.888 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:43:46.888 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 13:43:46.888 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 13:43:46.889 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 13:43:46.889 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 13:43:47.889 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-27 13:43:47.889 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 13:43:47.890 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 13:43:47.890 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:43:47.891 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:47.891 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:47.891 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:43:47.892 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:47.892 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:47.892 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:43:47.893 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:47.893 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:47.893 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 13:43:47.893 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:47.894 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:47.894 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 13:43:47.894 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 13:43:47.895 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 13:43:47.895 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 13:43:47.895 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:43:47.896 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 13:43:47.896 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 13:43:47.896 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 13:43:47.896 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 13:43:47.897 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 13:43:47.897 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 13:43:47.898 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 13:43:47.898 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-27 13:43:47.898 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 13:43:47.899 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-27 13:43:47.899 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 13:43:47.899 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 13:43:47.899 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 13:43:47.899 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 13:43:47.900 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 13:43:47.900 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 13:43:47.900 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 13:43:47.901 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-27 13:43:47.901 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 13:43:47.902 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 13:43:47.902 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 13:43:47.902 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 13:43:47.903 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 13:43:47.903 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 13:43:47.904 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 13:43:47.904 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 13:43:47.905 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 13:43:47.905 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 13:43:47.905 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 13:43:47.906 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 13:43:47.906 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 13:43:47.906 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 13:43:47.907 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 13:43:47.908 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
