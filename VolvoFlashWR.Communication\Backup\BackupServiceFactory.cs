using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Backup
{
    /// <summary>
    /// Factory for creating and initializing backup services
    /// </summary>
    public class BackupServiceFactory
    {
        private readonly ILoggingService _logger;
        private readonly IECUCommunicationService _ecuService;

        /// <summary>
        /// Initializes a new instance of the BackupServiceFactory class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="ecuService">The ECU communication service</param>
        public BackupServiceFactory(ILoggingService logger, IECUCommunicationService ecuService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _ecuService = ecuService ?? throw new ArgumentNullException(nameof(ecuService));
        }

        /// <summary>
        /// Creates and initializes a new backup service with default settings
        /// </summary>
        /// <returns>The initialized backup service</returns>
        public async Task<IBackupService> CreateServiceAsync()
        {
            try
            {
                _logger.LogInformation("Creating backup service with default settings", "BackupServiceFactory");

                // Create the service with default settings
                var service = new BackupService(_logger);

                // Initialize the service
                bool initialized = await service.InitializeAsync(_ecuService);
                if (!initialized)
                {
                    _logger.LogError("Failed to initialize backup service", "BackupServiceFactory");
                    return null;
                }

                _logger.LogInformation("Backup service created and initialized successfully", "BackupServiceFactory");
                return service;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error creating backup service", "BackupServiceFactory", ex);
                return null;
            }
        }

        /// <summary>
        /// Creates and initializes a new backup service with custom settings
        /// </summary>
        /// <param name="backupDirectoryPath">Custom backup directory path (null for default)</param>
        /// <param name="useCompression">Whether to use compression for backups</param>
        /// <param name="useEncryption">Whether to use encryption for backups</param>
        /// <returns>The initialized backup service</returns>
        public async Task<IBackupService?> CreateServiceWithCustomSettingsAsync(
            string? backupDirectoryPath = null,
            bool useCompression = true,
            bool useEncryption = false)
        {
            try
            {
                _logger.LogInformation("Creating backup service with custom settings", "BackupServiceFactory");

                // Use default directory if none provided
                if (string.IsNullOrEmpty(backupDirectoryPath))
                {
                    backupDirectoryPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");
                }

                // Create the service with custom settings
                var service = new BackupService(_logger, backupDirectoryPath, useCompression, useEncryption);

                // Initialize the service
                bool initialized = await service.InitializeAsync(_ecuService);
                if (!initialized)
                {
                    _logger.LogError("Failed to initialize backup service with custom settings", "BackupServiceFactory");
                    return null;
                }

                _logger.LogInformation($"Backup service created with custom settings (Directory: {backupDirectoryPath}, Compression: {useCompression}, Encryption: {useEncryption}) and initialized successfully", "BackupServiceFactory");
                return service;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error creating backup service with custom settings", "BackupServiceFactory", ex);
                return null;
            }
        }

        /// <summary>
        /// Creates and initializes a backup service and sets up predefined categories and tags
        /// </summary>
        /// <param name="predefinedCategories">List of predefined categories to set up</param>
        /// <param name="predefinedTags">List of predefined tags to set up</param>
        /// <param name="backupDirectoryPath">Custom backup directory path (null for default)</param>
        /// <param name="useCompression">Whether to use compression for backups</param>
        /// <param name="useEncryption">Whether to use encryption for backups</param>
        /// <returns>The initialized backup service</returns>
        public async Task<IBackupService?> CreateServiceWithCategoriesAndTagsAsync(
            List<string>? predefinedCategories = null,
            List<string>? predefinedTags = null,
            string? backupDirectoryPath = null,
            bool useCompression = true,
            bool useEncryption = false)
        {
            try
            {
                _logger.LogInformation("Creating backup service with predefined categories and tags", "BackupServiceFactory");

                // Create service with custom settings
                var service = await CreateServiceWithCustomSettingsAsync(backupDirectoryPath, useCompression, useEncryption);
                if (service == null)
                {
                    return null;
                }

                // Create a sample backup for each predefined category if provided
                if (predefinedCategories != null && predefinedCategories.Count > 0)
                {
                    _logger.LogInformation($"Setting up {predefinedCategories.Count} predefined categories", "BackupServiceFactory");

                    // Create a directory to store category templates
                    string templateDirectory = Path.Combine(
                        string.IsNullOrEmpty(backupDirectoryPath)
                            ? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups")
                            : backupDirectoryPath,
                        "Templates");

                    if (!Directory.Exists(templateDirectory))
                    {
                        Directory.CreateDirectory(templateDirectory);
                    }

                    // Create a template backup for each category
                    foreach (var category in predefinedCategories)
                    {
                        if (!string.IsNullOrEmpty(category))
                        {
                            var templateBackup = new BackupData
                            {
                                Id = $"template_{Guid.NewGuid()}",
                                ECUId = "template",
                                ECUName = "Template",
                                Category = category,
                                Description = $"Template for {category} category",
                                CreatedBy = "System",
                                IsComplete = false // Mark as template
                            };

                            string filePath = Path.Combine(templateDirectory, $"template_{category}.backup");
                            await service.SaveBackupToFileAsync(templateBackup, filePath);
                            _logger.LogInformation($"Created template for category: {category}", "BackupServiceFactory");
                        }
                    }
                }

                // Create a sample backup with each predefined tag if provided
                if (predefinedTags != null && predefinedTags.Count > 0)
                {
                    _logger.LogInformation($"Setting up {predefinedTags.Count} predefined tags", "BackupServiceFactory");

                    // Create a directory to store tag templates
                    string templateDirectory = Path.Combine(
                        string.IsNullOrEmpty(backupDirectoryPath)
                            ? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups")
                            : backupDirectoryPath,
                        "Templates");

                    if (!Directory.Exists(templateDirectory))
                    {
                        Directory.CreateDirectory(templateDirectory);
                    }

                    // Create a template backup with all tags
                    var templateBackup = new BackupData
                    {
                        Id = $"tags_template_{Guid.NewGuid()}",
                        ECUId = "template",
                        ECUName = "Tags Template",
                        Category = "Templates",
                        Description = "Template for predefined tags",
                        CreatedBy = "System",
                        Tags = new List<string>(predefinedTags),
                        IsComplete = false // Mark as template
                    };

                    string filePath = Path.Combine(templateDirectory, "template_tags.backup");
                    await service.SaveBackupToFileAsync(templateBackup, filePath);
                    _logger.LogInformation($"Created template with predefined tags", "BackupServiceFactory");
                }

                _logger.LogInformation("Backup service with predefined categories and tags created successfully", "BackupServiceFactory");
                return service;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error creating backup service with predefined categories and tags", "BackupServiceFactory", ex);
                return null;
            }
        }
    }
}
