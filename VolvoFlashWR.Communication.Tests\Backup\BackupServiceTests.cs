using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Backup;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Backup
{
    [TestFixture]
    public class BackupServiceTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IECUCommunicationService> _mockEcuService;
        private BackupService _backupService;
        private string _testBackupDirectory;

        [SetUp]
        public void Setup()
        {
            // Create mocks
            _mockLogger = new Mock<ILoggingService>();
            _mockEcuService = new Mock<IECUCommunicationService>();

            // Create a temporary directory for test backups
            _testBackupDirectory = Path.Combine(Path.GetTempPath(), "VolvoFlashWR_Tests", "Backups");
            if (Directory.Exists(_testBackupDirectory))
            {
                Directory.Delete(_testBackupDirectory, true);
            }
            Directory.CreateDirectory(_testBackupDirectory);

            // Create the backup service
            _backupService = new BackupService(_mockLogger.Object);

            // Use reflection to set the backup directory to our test directory
            var directoryField = typeof(BackupService).GetField("_backupDirectoryPath", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            directoryField.SetValue(_backupService, _testBackupDirectory);
        }

        [TearDown]
        public void TearDown()
        {
            // Clean up the test directory
            if (Directory.Exists(_testBackupDirectory))
            {
                Directory.Delete(_testBackupDirectory, true);
            }
        }

        [Test]
        public async Task InitializeAsync_ValidEcuService_ReturnsTrue()
        {
            // Arrange

            // Act
            bool result = await _backupService.InitializeAsync(_mockEcuService.Object);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task InitializeAsync_NullEcuService_ReturnsFalse()
        {
            // Arrange

            // Act
            bool result = await _backupService.InitializeAsync(null);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public async Task CreateBackupAsync_ValidEcu_ReturnsBackupData()
        {
            // Arrange
            await _backupService.InitializeAsync(_mockEcuService.Object);

            var ecu = new ECUDevice
            {
                Id = "test-ecu-id",
                Name = "Test ECU",
                SerialNumber = "SN12345",
                HardwareVersion = "1.0",
                SoftwareVersion = "2.0",
                MicrocontrollerType = "MC9S12XEP100"
            };

            byte[] eepromData = new byte[1024];
            byte[] mcuCode = new byte[4096];
            var parameters = new Dictionary<string, object>
            {
                { "Param1", 123 },
                { "Param2", "Value" }
            };

            _mockEcuService.Setup(s => s.ReadEEPROMAsync(ecu)).ReturnsAsync(eepromData);
            _mockEcuService.Setup(s => s.ReadMicrocontrollerCodeAsync(ecu)).ReturnsAsync(mcuCode);
            _mockEcuService.Setup(s => s.ReadParametersAsync(ecu)).ReturnsAsync(parameters);

            // Act
            var backup = await _backupService.CreateBackupAsync(ecu, "Test backup");

            // Assert
            Assert.That(backup, Is.Not.Null);
            Assert.That(backup.ECUId, Is.EqualTo(ecu.Id));
            Assert.That(backup.ECUName, Is.EqualTo(ecu.Name));
            Assert.That(backup.ECUSerialNumber, Is.EqualTo(ecu.SerialNumber));
            Assert.That(backup.ECUHardwareVersion, Is.EqualTo(ecu.HardwareVersion));
            Assert.That(backup.ECUSoftwareVersion, Is.EqualTo(ecu.SoftwareVersion));
            Assert.That(backup.Description, Is.EqualTo("Test backup"));
            Assert.That(backup.EEPROMData, Is.EqualTo(eepromData));
            Assert.That(backup.MicrocontrollerCode, Is.EqualTo(mcuCode));
            Assert.That(backup.Parameters, Is.EqualTo(parameters));
            Assert.That(backup.IsComplete, Is.True);
            Assert.That(backup.Checksum, Is.Not.Null.And.Not.Empty);
            Assert.That(File.Exists(backup.FilePath), Is.True);
        }

        [Test]
        public async Task RestoreBackupAsync_ValidBackupAndEcu_ReturnsTrue()
        {
            // Arrange
            await _backupService.InitializeAsync(_mockEcuService.Object);

            var ecu = new ECUDevice
            {
                Id = "test-ecu-id",
                Name = "Test ECU",
                SerialNumber = "SN12345"
            };

            var backup = new BackupData
            {
                ECUId = ecu.Id,
                ECUName = ecu.Name,
                ECUSerialNumber = ecu.SerialNumber,
                EEPROMData = new byte[1024],
                MicrocontrollerCode = new byte[4096],
                Parameters = new Dictionary<string, object>
                {
                    { "Param1", 123 },
                    { "Param2", "Value" }
                },
                Checksum = "test-checksum" // This would normally be calculated
            };

            _mockEcuService.Setup(s => s.WriteEEPROMAsync(ecu, backup.EEPROMData)).ReturnsAsync(true);
            _mockEcuService.Setup(s => s.WriteMicrocontrollerCodeAsync(ecu, backup.MicrocontrollerCode)).ReturnsAsync(true);
            _mockEcuService.Setup(s => s.WriteParametersAsync(ecu, backup.Parameters)).ReturnsAsync(true);

            // Save the backup to a file so it can be verified
            string filePath = Path.Combine(_testBackupDirectory, "test-backup.backup");
            await _backupService.SaveBackupToFileAsync(backup, filePath);
            backup.FilePath = filePath;

            // Act
            bool result = await _backupService.RestoreBackupAsync(backup, ecu);

            // Assert
            Assert.That(result, Is.True);
            _mockEcuService.Verify(s => s.WriteEEPROMAsync(ecu, backup.EEPROMData), Times.Once);
            _mockEcuService.Verify(s => s.WriteMicrocontrollerCodeAsync(ecu, backup.MicrocontrollerCode), Times.Once);
            _mockEcuService.Verify(s => s.WriteParametersAsync(ecu, backup.Parameters), Times.Once);
        }

        [Test]
        public async Task GetAllBackupsAsync_WithBackups_ReturnsAllBackups()
        {
            // Arrange
            await _backupService.InitializeAsync(_mockEcuService.Object);

            // Create and save some test backups
            var backup1 = new BackupData
            {
                Id = "backup1",
                ECUId = "ecu1",
                ECUName = "ECU 1",
                Checksum = "checksum1"
            };

            var backup2 = new BackupData
            {
                Id = "backup2",
                ECUId = "ecu2",
                ECUName = "ECU 2",
                Checksum = "checksum2"
            };

            await _backupService.SaveBackupToFileAsync(backup1, Path.Combine(_testBackupDirectory, "backup1.backup"));
            await _backupService.SaveBackupToFileAsync(backup2, Path.Combine(_testBackupDirectory, "backup2.backup"));

            // Act
            var backups = await _backupService.GetAllBackupsAsync();

            // Assert
            Assert.That(backups, Is.Not.Null);
            Assert.That(backups.Count, Is.EqualTo(2));
            Assert.That(backups.Any(b => b.Id == "backup1"), Is.True);
            Assert.That(backups.Any(b => b.Id == "backup2"), Is.True);
        }

        [Test]
        public async Task GetBackupsForECUAsync_WithMatchingBackups_ReturnsFilteredBackups()
        {
            // Arrange
            await _backupService.InitializeAsync(_mockEcuService.Object);

            // Create and save some test backups
            var backup1 = new BackupData
            {
                Id = "backup1",
                ECUId = "ecu1",
                ECUName = "ECU 1",
                Checksum = "checksum1"
            };

            var backup2 = new BackupData
            {
                Id = "backup2",
                ECUId = "ecu1", // Same ECU ID
                ECUName = "ECU 1",
                Checksum = "checksum2"
            };

            var backup3 = new BackupData
            {
                Id = "backup3",
                ECUId = "ecu2", // Different ECU ID
                ECUName = "ECU 2",
                Checksum = "checksum3"
            };

            await _backupService.SaveBackupToFileAsync(backup1, Path.Combine(_testBackupDirectory, "backup1.backup"));
            await _backupService.SaveBackupToFileAsync(backup2, Path.Combine(_testBackupDirectory, "backup2.backup"));
            await _backupService.SaveBackupToFileAsync(backup3, Path.Combine(_testBackupDirectory, "backup3.backup"));

            // Act
            var backups = await _backupService.GetBackupsForECUAsync("ecu1");

            // Assert
            Assert.That(backups, Is.Not.Null);
            Assert.That(backups.Count, Is.EqualTo(2));
            Assert.That(backups.All(b => b.ECUId == "ecu1"), Is.True);
        }

        [Test]
        public async Task DeleteBackupAsync_ExistingBackup_ReturnsTrue()
        {
            // Arrange
            await _backupService.InitializeAsync(_mockEcuService.Object);

            var backup = new BackupData
            {
                Id = "backup1",
                ECUId = "ecu1",
                ECUName = "ECU 1",
                Checksum = "checksum1"
            };

            string filePath = Path.Combine(_testBackupDirectory, "backup1.backup");
            await _backupService.SaveBackupToFileAsync(backup, filePath);
            backup.FilePath = filePath;

            // Act
            bool result = await _backupService.DeleteBackupAsync(backup);

            // Assert
            Assert.That(result, Is.True);
            Assert.That(File.Exists(filePath), Is.False);
        }

        [Test]
        public async Task CompareBackupsAsync_DifferentBackups_ReturnsDifferences()
        {
            // Arrange
            await _backupService.InitializeAsync(_mockEcuService.Object);

            var backup1 = new BackupData
            {
                ECUId = "ecu1",
                ECUName = "ECU 1",
                ECUSerialNumber = "SN1",
                ECUHardwareVersion = "HW1",
                ECUSoftwareVersion = "SW1",
                EEPROMData = new byte[] { 1, 2, 3 },
                MicrocontrollerCode = new byte[] { 4, 5, 6 },
                Parameters = new Dictionary<string, object>
                {
                    { "Param1", 123 },
                    { "Param2", "Value1" }
                },
                CreationTime = new DateTime(2023, 1, 1),
                Checksum = "checksum1"
            };

            var backup2 = new BackupData
            {
                ECUId = "ecu1", // Same
                ECUName = "ECU 1", // Same
                ECUSerialNumber = "SN2", // Different
                ECUHardwareVersion = "HW1", // Same
                ECUSoftwareVersion = "SW2", // Different
                EEPROMData = new byte[] { 1, 2, 4 }, // Different
                MicrocontrollerCode = new byte[] { 4, 5, 6 }, // Same
                Parameters = new Dictionary<string, object>
                {
                    { "Param1", 456 }, // Different value
                    { "Param3", "Value3" } // Different key
                },
                CreationTime = new DateTime(2023, 2, 1), // Different
                Checksum = "checksum2" // Different
            };

            // Act
            var differences = await _backupService.CompareBackupsAsync(backup1, backup2);

            // Assert
            Assert.That(differences, Is.Not.Null);
            Assert.That(differences.Count, Is.GreaterThan(0));
            Assert.That(differences.ContainsKey("ECUSerialNumber"), Is.True);
            Assert.That(differences.ContainsKey("ECUSoftwareVersion"), Is.True);
            Assert.That(differences.ContainsKey("EEPROMData"), Is.True);
            Assert.That(differences.ContainsKey("CreationTime"), Is.True);
            Assert.That(differences.ContainsKey("Parameter_Param1"), Is.True);
            Assert.That(differences.ContainsKey("Parameter_Param2"), Is.True);
            Assert.That(differences.ContainsKey("Parameter_Param3"), Is.True);
        }

        [Test]
        public async Task GetBackupsByCategoryAsync_WithMatchingBackups_ReturnsFilteredBackups()
        {
            // Arrange
            await _backupService.InitializeAsync(_mockEcuService.Object);

            // Create and save some test backups with different categories
            var backup1 = new BackupData
            {
                Id = "backup1",
                ECUId = "ecu1",
                ECUName = "ECU 1",
                Category = "Production",
                Checksum = "checksum1"
            };

            var backup2 = new BackupData
            {
                Id = "backup2",
                ECUId = "ecu2",
                ECUName = "ECU 2",
                Category = "Production", // Same category
                Checksum = "checksum2"
            };

            var backup3 = new BackupData
            {
                Id = "backup3",
                ECUId = "ecu3",
                ECUName = "ECU 3",
                Category = "Development", // Different category
                Checksum = "checksum3"
            };

            await _backupService.SaveBackupToFileAsync(backup1, Path.Combine(_testBackupDirectory, "backup1.backup"));
            await _backupService.SaveBackupToFileAsync(backup2, Path.Combine(_testBackupDirectory, "backup2.backup"));
            await _backupService.SaveBackupToFileAsync(backup3, Path.Combine(_testBackupDirectory, "backup3.backup"));

            // Act
            var backups = await _backupService.GetBackupsByCategoryAsync("Production");

            // Assert
            Assert.That(backups, Is.Not.Null);
            Assert.That(backups.Count, Is.EqualTo(2));
            Assert.That(backups.All(b => b.Category == "Production"), Is.True);
        }

        [Test]
        public async Task GetBackupsByTagAsync_WithMatchingBackups_ReturnsFilteredBackups()
        {
            // Arrange
            await _backupService.InitializeAsync(_mockEcuService.Object);

            // Create and save some test backups with different tags
            var backup1 = new BackupData
            {
                Id = "backup1",
                ECUId = "ecu1",
                ECUName = "ECU 1",
                Tags = new List<string> { "Important", "Verified" },
                Checksum = "checksum1"
            };

            var backup2 = new BackupData
            {
                Id = "backup2",
                ECUId = "ecu2",
                ECUName = "ECU 2",
                Tags = new List<string> { "Important" }, // Has the tag
                Checksum = "checksum2"
            };

            var backup3 = new BackupData
            {
                Id = "backup3",
                ECUId = "ecu3",
                ECUName = "ECU 3",
                Tags = new List<string> { "Test" }, // Doesn't have the tag
                Checksum = "checksum3"
            };

            await _backupService.SaveBackupToFileAsync(backup1, Path.Combine(_testBackupDirectory, "backup1.backup"));
            await _backupService.SaveBackupToFileAsync(backup2, Path.Combine(_testBackupDirectory, "backup2.backup"));
            await _backupService.SaveBackupToFileAsync(backup3, Path.Combine(_testBackupDirectory, "backup3.backup"));

            // Act
            var backups = await _backupService.GetBackupsByTagAsync("Important");

            // Assert
            Assert.That(backups, Is.Not.Null);
            Assert.That(backups.Count, Is.EqualTo(2));
            Assert.That(backups.All(b => b.Tags.Contains("Important")), Is.True);
        }

        [Test]
        public async Task AddTagToBackupAsync_ValidBackupAndTag_ReturnsTrue()
        {
            // Arrange
            await _backupService.InitializeAsync(_mockEcuService.Object);

            var backup = new BackupData
            {
                Id = "backup1",
                ECUId = "ecu1",
                ECUName = "ECU 1",
                Tags = new List<string> { "Existing" },
                Checksum = "checksum1"
            };

            string filePath = Path.Combine(_testBackupDirectory, "backup1.backup");
            await _backupService.SaveBackupToFileAsync(backup, filePath);
            backup.FilePath = filePath;

            // Act
            bool result = await _backupService.AddTagToBackupAsync(backup, "NewTag");

            // Assert
            Assert.That(result, Is.True);
            Assert.That(backup.Tags, Contains.Item("NewTag"));

            // Verify the tag was saved to the file
            var loadedBackup = await _backupService.LoadBackupFromFileAsync(filePath);
            Assert.That(loadedBackup.Tags, Contains.Item("NewTag"));
        }

        [Test]
        public async Task RemoveTagFromBackupAsync_ExistingTag_ReturnsTrue()
        {
            // Arrange
            await _backupService.InitializeAsync(_mockEcuService.Object);

            var backup = new BackupData
            {
                Id = "backup1",
                ECUId = "ecu1",
                ECUName = "ECU 1",
                Tags = new List<string> { "TagToRemove", "KeepThisTag" },
                Checksum = "checksum1"
            };

            string filePath = Path.Combine(_testBackupDirectory, "backup1.backup");
            await _backupService.SaveBackupToFileAsync(backup, filePath);
            backup.FilePath = filePath;

            // Act
            bool result = await _backupService.RemoveTagFromBackupAsync(backup, "TagToRemove");

            // Assert
            Assert.That(result, Is.True);
            Assert.That(backup.Tags, Does.Not.Contain("TagToRemove"));
            Assert.That(backup.Tags, Contains.Item("KeepThisTag"));

            // Verify the tag was removed from the file
            var loadedBackup = await _backupService.LoadBackupFromFileAsync(filePath);
            Assert.That(loadedBackup.Tags, Does.Not.Contain("TagToRemove"));
        }

        [Test]
        public async Task SetBackupCategoryAsync_ValidBackupAndCategory_ReturnsTrue()
        {
            // Arrange
            await _backupService.InitializeAsync(_mockEcuService.Object);

            var backup = new BackupData
            {
                Id = "backup1",
                ECUId = "ecu1",
                ECUName = "ECU 1",
                Category = "OldCategory",
                Checksum = "checksum1"
            };

            string filePath = Path.Combine(_testBackupDirectory, "backup1.backup");
            await _backupService.SaveBackupToFileAsync(backup, filePath);
            backup.FilePath = filePath;

            // Act
            bool result = await _backupService.SetBackupCategoryAsync(backup, "NewCategory");

            // Assert
            Assert.That(result, Is.True);
            Assert.That(backup.Category, Is.EqualTo("NewCategory"));

            // Verify the category was saved to the file
            var loadedBackup = await _backupService.LoadBackupFromFileAsync(filePath);
            Assert.That(loadedBackup.Category, Is.EqualTo("NewCategory"));
        }

        [Test]
        public async Task GetAllCategoriesAsync_WithMultipleCategories_ReturnsUniqueCategories()
        {
            // Arrange
            await _backupService.InitializeAsync(_mockEcuService.Object);

            // Create and save some test backups with different categories
            var backup1 = new BackupData
            {
                Id = "backup1",
                ECUId = "ecu1",
                ECUName = "ECU 1",
                Category = "Production",
                Checksum = "checksum1"
            };

            var backup2 = new BackupData
            {
                Id = "backup2",
                ECUId = "ecu2",
                ECUName = "ECU 2",
                Category = "Development",
                Checksum = "checksum2"
            };

            var backup3 = new BackupData
            {
                Id = "backup3",
                ECUId = "ecu3",
                ECUName = "ECU 3",
                Category = "Production", // Duplicate category
                Checksum = "checksum3"
            };

            await _backupService.SaveBackupToFileAsync(backup1, Path.Combine(_testBackupDirectory, "backup1.backup"));
            await _backupService.SaveBackupToFileAsync(backup2, Path.Combine(_testBackupDirectory, "backup2.backup"));
            await _backupService.SaveBackupToFileAsync(backup3, Path.Combine(_testBackupDirectory, "backup3.backup"));

            // Act
            var categories = await _backupService.GetAllCategoriesAsync();

            // Assert
            Assert.That(categories, Is.Not.Null);
            Assert.That(categories.Count, Is.EqualTo(2)); // Only unique categories
            Assert.That(categories, Contains.Item("Production"));
            Assert.That(categories, Contains.Item("Development"));
        }

        [Test]
        public async Task GetAllTagsAsync_WithMultipleTags_ReturnsUniqueTags()
        {
            // Arrange
            await _backupService.InitializeAsync(_mockEcuService.Object);

            // Create and save some test backups with different tags
            var backup1 = new BackupData
            {
                Id = "backup1",
                ECUId = "ecu1",
                ECUName = "ECU 1",
                Tags = new List<string> { "Important", "Verified" },
                Checksum = "checksum1"
            };

            var backup2 = new BackupData
            {
                Id = "backup2",
                ECUId = "ecu2",
                ECUName = "ECU 2",
                Tags = new List<string> { "Important", "Test" },
                Checksum = "checksum2"
            };

            var backup3 = new BackupData
            {
                Id = "backup3",
                ECUId = "ecu3",
                ECUName = "ECU 3",
                Tags = new List<string> { "Test", "Debug" },
                Checksum = "checksum3"
            };

            await _backupService.SaveBackupToFileAsync(backup1, Path.Combine(_testBackupDirectory, "backup1.backup"));
            await _backupService.SaveBackupToFileAsync(backup2, Path.Combine(_testBackupDirectory, "backup2.backup"));
            await _backupService.SaveBackupToFileAsync(backup3, Path.Combine(_testBackupDirectory, "backup3.backup"));

            // Act
            var tags = await _backupService.GetAllTagsAsync();

            // Assert
            Assert.That(tags, Is.Not.Null);
            Assert.That(tags.Count, Is.EqualTo(4)); // Only unique tags
            Assert.That(tags, Contains.Item("Important"));
            Assert.That(tags, Contains.Item("Verified"));
            Assert.That(tags, Contains.Item("Test"));
            Assert.That(tags, Contains.Item("Debug"));
        }
    }
}

