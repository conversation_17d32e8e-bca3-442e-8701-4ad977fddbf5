Log started at 7/22/2025 12:28:55 AM
2025-07-22 00:28:55.905 [Information] LoggingService: Logging service initialized
2025-07-22 00:28:55.917 [Information] App: Starting integrated application initialization
2025-07-22 00:28:55.918 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-22 00:28:55.922 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-22 00:28:55.924 [Information] IntegratedStartupService: Setting up application environment
2025-07-22 00:28:55.925 [Information] IntegratedStartupService: Application environment setup completed
2025-07-22 00:28:55.927 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-22 00:28:55.929 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-22 00:28:55.932 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-22 00:28:55.936 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-22 00:28:55.943 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-22 00:28:55.945 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.947 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.948 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-22 00:28:55.950 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-22 00:28:55.952 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.953 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.954 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-22 00:28:55.957 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-22 00:28:55.959 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.961 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.961 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-22 00:28:55.964 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-22 00:28:55.966 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.967 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.968 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-22 00:28:55.971 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-22 00:28:55.973 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.974 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.975 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-22 00:28:55.978 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-22 00:28:55.979 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.981 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.982 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-22 00:28:55.984 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-22 00:28:55.986 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.988 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-22 00:28:55.989 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-22 00:28:55.991 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-22 00:28:55.993 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-22 00:28:55.993 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-22 00:28:55.995 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-22 00:28:55.995 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-22 00:28:55.996 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-22 00:28:55.996 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-22 00:28:55.997 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-22 00:28:55.999 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-22 00:28:55.999 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-22 00:28:56.001 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-22 00:28:56.001 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-22 00:28:56.002 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-22 00:28:56.002 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-22 00:28:56.003 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-22 00:28:56.003 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-22 00:28:56.003 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-22 00:28:56.010 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-22 00:28:56.010 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-22 00:28:56.011 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-22 00:28:56.015 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-22 00:28:56.016 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-22 00:28:56.017 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-22 00:28:56.018 [Information] LibraryExtractor: Starting library extraction process
2025-07-22 00:28:56.020 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-22 00:28:56.024 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-22 00:28:56.025 [Information] LibraryExtractor: Copying system libraries
2025-07-22 00:28:56.030 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-22 00:28:56.036 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-22 00:29:26.047 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-22 00:29:27.048 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-22 00:29:57.050 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-22 00:29:57.051 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-22 00:30:27.054 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-22 00:30:28.055 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-22 00:30:58.058 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-22 00:30:58.059 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-22 00:31:28.061 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-22 00:31:29.062 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-22 00:31:59.064 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-22 00:31:59.065 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-22 00:31:59.065 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-22 00:31:59.068 [Information] LibraryExtractor: Verifying library extraction
2025-07-22 00:31:59.069 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-22 00:31:59.069 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-22 00:31:59.069 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-22 00:31:59.070 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-22 00:31:59.070 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-22 00:31:59.075 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-22 00:31:59.077 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-22 00:31:59.078 [Information] DependencyManager: Initializing dependency manager
2025-07-22 00:31:59.079 [Information] DependencyManager: Setting up library search paths
2025-07-22 00:31:59.080 [Information] DependencyManager: Added library path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-22 00:31:59.081 [Information] DependencyManager: Added driver path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-22 00:31:59.081 [Information] DependencyManager: Added application path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-22 00:31:59.081 [Information] DependencyManager: Updated PATH environment variable
2025-07-22 00:31:59.083 [Information] DependencyManager: Verifying required directories
2025-07-22 00:31:59.083 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-22 00:31:59.084 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-22 00:31:59.084 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-22 00:31:59.084 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-22 00:31:59.086 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-22 00:31:59.091 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-22 00:31:59.092 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-22 00:31:59.092 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-22 00:31:59.094 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcr120.dll: Error 193
2025-07-22 00:31:59.094 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr120.dll
2025-07-22 00:31:59.095 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-22 00:31:59.095 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-22 00:31:59.095 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-22 00:31:59.096 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp120.dll: Error 193
2025-07-22 00:31:59.096 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp120.dll
2025-07-22 00:31:59.097 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-22 00:31:59.097 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-22 00:31:59.097 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-22 00:31:59.099 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll from C:\Windows\system32\msvcp140.dll: Error 193
2025-07-22 00:31:59.099 [Warning] DependencyManager: Architecture mismatch detected for msvcp140.dll. Expected: x64
2025-07-22 00:31:59.099 [Debug] DependencyManager: Architecture mismatch: Library msvcp140.dll is x86, process is x64
2025-07-22 00:31:59.100 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp140.dll
2025-07-22 00:31:59.100 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll: Error 193
2025-07-22 00:31:59.101 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp140.dll
2025-07-22 00:31:59.101 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-22 00:31:59.102 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-22 00:31:59.103 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-22 00:31:59.103 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-22 00:31:59.104 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-22 00:31:59.104 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-22 00:31:59.105 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-22 00:31:59.105 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-22 00:31:59.106 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-22 00:31:59.106 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-22 00:31:59.106 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-22 00:31:59.107 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-22 00:31:59.107 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-22 00:31:59.107 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-22 00:31:59.108 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-22 00:31:59.108 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-22 00:31:59.109 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-22 00:31:59.109 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-22 00:31:59.109 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-22 00:31:59.110 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-22 00:31:59.110 [Information] DependencyManager: VC++ Redistributable library loading: 1/14 (7.1%) libraries loaded
2025-07-22 00:31:59.110 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-22 00:31:59.111 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-22 00:31:59.112 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-22 00:31:59.112 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-22 00:31:59.113 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-22 00:31:59.113 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-22 00:31:59.114 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-22 00:31:59.114 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-22 00:31:59.115 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-22 00:31:59.115 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-22 00:31:59.115 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-22 00:31:59.116 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-22 00:31:59.117 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-22 00:31:59.117 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-22 00:31:59.117 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-22 00:31:59.118 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-22 00:31:59.118 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-22 00:31:59.119 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-22 00:31:59.119 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-22 00:31:59.120 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-22 00:31:59.120 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-22 00:31:59.121 [Warning] DependencyManager: Failed to load Critical library msvcr120.dll: Error 193
2025-07-22 00:31:59.122 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-22 00:31:59.122 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-22 00:31:59.122 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-22 00:31:59.123 [Warning] DependencyManager: Failed to load Critical library msvcp120.dll: Error 193
2025-07-22 00:31:59.123 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-22 00:31:59.124 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-22 00:31:59.125 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-22 00:31:59.126 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-22 00:31:59.126 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-22 00:31:59.127 [Information] DependencyManager: Setting up environment variables
2025-07-22 00:31:59.127 [Information] DependencyManager: Environment variables configured
2025-07-22 00:31:59.128 [Information] DependencyManager: Verifying library loading status
2025-07-22 00:31:59.435 [Information] DependencyManager: Library loading verification: 4/11 (36.4%) critical libraries loaded
2025-07-22 00:31:59.436 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-22 00:31:59.436 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-22 00:31:59.439 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-22 00:31:59.440 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-22 00:31:59.443 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-22 00:31:59.444 [Information] IntegratedStartupService: Verifying system readiness
2025-07-22 00:31:59.444 [Information] IntegratedStartupService: System readiness verification passed
2025-07-22 00:31:59.445 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-22 00:31:59.446 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-22 00:31:59.446 [Information] IntegratedStartupService: Application Path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-22 00:31:59.446 [Information] IntegratedStartupService: Libraries Path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-22 00:31:59.447 [Information] IntegratedStartupService: Drivers Path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-22 00:31:59.447 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-22 00:31:59.447 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-22 00:31:59.447 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-22 00:31:59.448 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-22 00:31:59.448 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-22 00:31:59.448 [Information] App: Integrated startup completed successfully
2025-07-22 00:31:59.452 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-22 00:31:59.465 [Information] App: Initializing application services
2025-07-22 00:31:59.467 [Information] AppConfigurationService: Initializing configuration service
2025-07-22 00:31:59.467 [Information] AppConfigurationService: Created configuration directory: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-22 00:31:59.507 [Information] AppConfigurationService: Configuration loaded from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-22 00:31:59.508 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-22 00:31:59.508 [Information] App: Configuration service initialized successfully
2025-07-22 00:31:59.510 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-22 00:31:59.510 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-22 00:31:59.517 [Information] App: Environment variable exists: True, not 'false': False
2025-07-22 00:31:59.518 [Information] App: Final useDummyImplementations value: False
2025-07-22 00:31:59.518 [Information] App: Updating config to NOT use dummy implementations
2025-07-22 00:31:59.520 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-22 00:31:59.535 [Information] AppConfigurationService: Configuration saved to D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-22 00:31:59.536 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-22 00:31:59.536 [Information] App: usePatchedImplementation flag is: True
2025-07-22 00:31:59.536 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-22 00:31:59.536 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-22 00:31:59.537 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-22 00:31:59.537 [Information] App: verboseLogging flag is: True
2025-07-22 00:31:59.539 [Information] App: Verifying real hardware requirements...
2025-07-22 00:31:59.540 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-22 00:31:59.540 [Information] App: ✓ Found critical library: apci.dll
2025-07-22 00:31:59.540 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-22 00:31:59.540 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-22 00:31:59.541 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-22 00:31:59.541 [Information] App: Phoenix Diag not found at: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021 (optional)
2025-07-22 00:31:59.541 [Information] App: ✓ Found Vocom driver config: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-22 00:31:59.542 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-22 00:31:59.553 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-22 00:31:59.556 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-22 00:31:59.556 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-22 00:31:59.558 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-22 00:31:59.561 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-07-22 00:35:00.861 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-22 00:35:00.861 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-22 00:35:00.861 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-22 00:35:00.862 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-22 00:35:00.862 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-22 00:35:00.863 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-22 00:35:00.863 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-22 00:35:00.864 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-22 00:35:00.864 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-22 00:35:00.864 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-22 00:35:00.867 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-22 00:35:00.867 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-22 00:35:00.867 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-22 00:35:00.867 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-22 00:35:00.868 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-22 00:35:00.868 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-22 00:35:00.868 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-22 00:35:00.868 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-22 00:35:00.869 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-22 00:35:00.869 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-22 00:35:00.871 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-22 00:35:00.871 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-22 00:35:00.871 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-22 00:35:00.872 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-22 00:35:00.873 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-22 00:35:00.874 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-07-22 00:35:00.875 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-07-22 00:35:00.876 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-22 00:35:00.876 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-07-22 00:35:00.877 [Information] PatchedVocomServiceFactory: Assembly location: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-07-22 00:35:00.901 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-22 00:35:00.902 [Information] PatchedVocomServiceFactory: Created marker file at D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-07-22 00:35:00.905 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-22 00:35:00.905 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-22 00:35:00.906 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-22 00:35:00.906 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-22 00:35:00.908 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-22 00:35:00.908 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-22 00:35:00.909 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-22 00:35:00.909 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-22 00:35:00.909 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-22 00:35:00.909 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-22 00:35:00.910 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-22 00:35:00.912 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-22 00:35:00.915 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-22 00:35:00.917 [Error] PhoenixVocomAdapter: Phoenix Diag path not found at C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-22 00:35:00.918 [Error] PhoenixVocomAdapter: Required Phoenix libraries are not available
2025-07-22 00:35:00.919 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-22 00:35:00.919 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-22 00:35:00.919 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-22 00:35:00.920 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-22 00:35:00.922 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-22 00:35:00.922 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-22 00:35:00.926 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-22 00:35:00.926 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-22 00:35:00.927 [Information] VocomNativeInterop_Patch: Searching for apcidb.dll...
2025-07-22 00:35:00.927 [Information] VocomNativeInterop_Patch: Searching for apcidb.dll in 9 locations
2025-07-22 00:35:00.927 [Information] VocomNativeInterop_Patch: Searching for WUDFPuma.dll...
2025-07-22 00:35:00.928 [Information] VocomNativeInterop_Patch: Searching for WUDFPuma.dll in 9 locations
2025-07-22 00:35:00.928 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-22 00:35:00.928 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-22 00:35:00.931 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-22 00:35:00.932 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-22 00:35:00.932 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-22 00:35:00.933 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-22 00:35:00.933 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-22 00:35:00.933 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-22 00:35:00.934 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-22 00:35:00.934 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-22 00:35:00.934 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-22 00:35:00.935 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-22 00:35:00.935 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-22 00:35:00.935 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-22 00:35:00.935 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-22 00:35:00.936 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-22 00:35:00.936 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-22 00:35:00.937 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-22 00:35:00.937 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-22 00:35:00.937 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-22 00:35:00.938 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-22 00:35:00.938 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-22 00:35:00.938 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-22 00:35:00.938 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-22 00:35:00.939 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-22 00:35:00.939 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-22 00:35:00.940 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-22 00:35:00.940 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-22 00:35:00.941 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-22 00:35:00.943 [Information] VocomNativeInterop_Patch: Loading function pointers from Vocom driver DLL
2025-07-22 00:35:00.944 [Error] VocomNativeInterop_Patch: Failed to find any initialize function in the DLL
2025-07-22 00:35:00.945 [Error] VocomNativeInterop_Patch: Failed to load function pointers from Vocom driver DLL
2025-07-22 00:35:00.946 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-22 00:35:00.947 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-22 00:35:00.949 [Information] VocomDriver: Initializing Vocom driver
2025-07-22 00:35:00.950 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-22 00:35:00.953 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-22 00:35:00.953 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-22 00:35:00.953 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-22 00:35:00.954 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-22 00:35:00.955 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-22 00:35:00.956 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr120.dll
2025-07-22 00:35:00.957 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp120.dll
2025-07-22 00:35:00.958 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-22 00:35:00.959 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-22 00:35:00.960 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-22 00:35:00.960 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-22 00:35:00.962 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-07-22 00:35:00.964 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-07-22 00:35:00.965 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-07-22 00:35:00.965 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-22 00:35:00.965 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-22 00:35:00.966 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-22 00:35:00.967 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-22 00:35:00.968 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-22 00:35:00.968 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-22 00:35:00.968 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-22 00:35:00.969 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-22 00:35:00.969 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-22 00:35:00.969 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-22 00:35:00.969 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-22 00:35:00.969 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-22 00:35:00.970 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-22 00:35:00.970 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-22 00:35:00.970 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-22 00:35:00.970 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-22 00:35:00.970 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-22 00:35:00.971 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-22 00:35:00.971 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-22 00:35:00.971 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-22 00:35:00.971 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-22 00:35:00.971 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-22 00:35:00.971 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-22 00:35:00.972 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-22 00:35:00.972 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-22 00:35:00.972 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-22 00:35:00.972 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-22 00:35:00.972 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-22 00:35:00.973 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-22 00:35:00.973 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-22 00:35:00.973 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-22 00:35:00.973 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-22 00:35:00.973 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-22 00:35:00.973 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-22 00:35:00.974 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-22 00:35:00.974 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-22 00:35:00.974 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-22 00:35:00.974 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-22 00:35:00.974 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-22 00:35:00.975 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-22 00:35:00.975 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-22 00:35:00.975 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-22 00:35:00.975 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-22 00:35:00.975 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-22 00:35:00.976 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-22 00:35:00.976 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-22 00:35:00.977 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-22 00:35:00.977 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-22 00:35:00.978 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-22 00:35:00.978 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-22 00:35:00.978 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-22 00:35:00.979 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-22 00:35:00.979 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-22 00:35:00.979 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-22 00:35:00.982 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-22 00:35:00.983 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-22 00:35:00.984 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-22 00:35:00.985 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-22 00:35:01.042 [Information] WiFiCommunicationService: WiFi is available
2025-07-22 00:35:01.043 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-22 00:35:01.044 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-22 00:35:01.045 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-22 00:35:01.046 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-22 00:35:01.048 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-22 00:35:01.049 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-22 00:35:01.050 [Information] VocomService: Initializing enhanced Vocom services
2025-07-22 00:35:01.051 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-22 00:35:01.053 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-22 00:35:01.055 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-22 00:35:01.055 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-22 00:35:01.055 [Information] VocomService: Native USB communication service initialized
2025-07-22 00:35:01.055 [Information] VocomService: Enhanced device detection service initialized
2025-07-22 00:35:01.056 [Information] VocomService: Connection recovery service initialized
2025-07-22 00:35:01.056 [Information] VocomService: Enhanced services initialization completed
2025-07-22 00:35:01.057 [Information] VocomService: Checking if PTT application is running
2025-07-22 00:35:01.069 [Information] VocomService: PTT application is not running
2025-07-22 00:35:01.070 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-22 00:35:01.071 [Debug] VocomService: Bluetooth is enabled
2025-07-22 00:35:01.072 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-22 00:35:01.072 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-22 00:35:01.072 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-07-22 00:35:01.076 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-22 00:35:01.077 [Information] VocomService: Using new enhanced device detection service
2025-07-22 00:35:01.078 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-22 00:35:01.080 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-22 00:35:01.353 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-22 00:35:01.354 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-22 00:35:01.355 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-22 00:35:01.356 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-22 00:35:01.357 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-22 00:35:01.358 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-22 00:35:01.359 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-22 00:35:01.361 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-22 00:35:01.551 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-22 00:35:01.553 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-22 00:35:01.555 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-22 00:35:01.555 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-22 00:35:01.556 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-22 00:35:01.557 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-22 00:35:01.557 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-22 00:35:01.557 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-22 00:35:01.557 [Debug] VocomService: Bluetooth is enabled
2025-07-22 00:35:01.560 [Debug] VocomService: Checking if WiFi is available
2025-07-22 00:35:01.561 [Debug] VocomService: WiFi is available
2025-07-22 00:35:01.561 [Information] VocomService: Found 3 Vocom devices
2025-07-22 00:35:01.561 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-07-22 00:35:01.563 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: 844b0d9e-df2d-449f-8aa2-126f3844d453, Type: USB)
2025-07-22 00:35:01.563 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: 391c596d-eb44-4f38-b2e7-611568ee4629, Type: Bluetooth)
2025-07-22 00:35:01.563 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: 645d06e0-fa89-4d6f-ae3c-4a53f873f395, Type: WiFi)
2025-07-22 00:35:01.564 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-07-22 00:35:01.564 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-07-22 00:35:01.564 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-07-22 00:35:01.565 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-07-22 00:35:01.565 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-07-22 00:35:01.565 [Information] App: Architecture-aware Vocom service created successfully
2025-07-22 00:35:01.565 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-22 00:35:01.566 [Information] VocomService: Initializing enhanced Vocom services
2025-07-22 00:35:01.566 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-22 00:35:01.566 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-22 00:35:01.567 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-22 00:35:01.567 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-22 00:35:01.567 [Information] VocomService: Native USB communication service initialized
2025-07-22 00:35:01.567 [Information] VocomService: Enhanced device detection service initialized
2025-07-22 00:35:01.568 [Information] VocomService: Connection recovery service initialized
2025-07-22 00:35:01.568 [Information] VocomService: Enhanced services initialization completed
2025-07-22 00:35:01.568 [Information] VocomService: Checking if PTT application is running
2025-07-22 00:35:01.579 [Information] VocomService: PTT application is not running
2025-07-22 00:35:01.579 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-22 00:35:01.579 [Debug] VocomService: Bluetooth is enabled
2025-07-22 00:35:01.580 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-22 00:35:01.580 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-22 00:35:01.580 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-22 00:35:01.580 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-22 00:35:01.615 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-22 00:35:01.615 [Information] VocomService: Using new enhanced device detection service
2025-07-22 00:35:01.615 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-22 00:35:01.615 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-22 00:35:01.789 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-22 00:35:01.789 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-22 00:35:01.790 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-22 00:35:01.790 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-22 00:35:01.790 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-22 00:35:01.790 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-22 00:35:01.790 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-22 00:35:01.791 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-22 00:35:01.965 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-22 00:35:01.966 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-22 00:35:01.966 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-22 00:35:01.966 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-22 00:35:01.967 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-22 00:35:01.968 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-22 00:35:01.968 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-22 00:35:01.968 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-22 00:35:01.968 [Debug] VocomService: Bluetooth is enabled
2025-07-22 00:35:01.969 [Debug] VocomService: Checking if WiFi is available
2025-07-22 00:35:01.969 [Debug] VocomService: WiFi is available
2025-07-22 00:35:01.969 [Information] VocomService: Found 3 Vocom devices
2025-07-22 00:35:01.969 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-22 00:35:01.971 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:01.972 [Information] VocomService: Checking if PTT application is running
2025-07-22 00:35:01.984 [Information] VocomService: PTT application is not running
2025-07-22 00:35:01.987 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-22 00:35:01.988 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-22 00:35:01.988 [Information] VocomService: Checking if PTT application is running
2025-07-22 00:35:01.998 [Information] VocomService: PTT application is not running
2025-07-22 00:35:01.999 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-22 00:35:02.001 [Information] VocomService: Native USB connection attempt 1/3 to WUDFPuma Driver
2025-07-22 00:35:02.003 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-22 00:35:02.005 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-22 00:35:02.006 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-22 00:35:02.007 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-22 00:35:04.008 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-22 00:35:05.009 [Information] VocomService: Native USB connection attempt 2/3 to WUDFPuma Driver
2025-07-22 00:35:05.009 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-22 00:35:05.009 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-22 00:35:05.009 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-22 00:35:05.010 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-22 00:35:07.010 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-22 00:35:08.010 [Information] VocomService: Native USB connection attempt 3/3 to WUDFPuma Driver
2025-07-22 00:35:08.011 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-22 00:35:08.011 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-22 00:35:08.011 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-22 00:35:08.011 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-22 00:35:10.011 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-22 00:35:10.012 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-22 00:35:10.013 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-22 00:35:10.033 [Information] ModernUSBCommunicationService: USB availability check: False
2025-07-22 00:35:10.034 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-22 00:35:10.035 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-22 00:35:10.036 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-22 00:35:10.037 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-22 00:35:10.037 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-22 00:35:10.037 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-22 00:35:10.038 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-22 00:35:10.040 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-22 00:35:10.041 [Information] ModernUSBCommunicationService: Found 0 HID devices total
2025-07-22 00:35:10.042 [Information] ModernUSBCommunicationService: Trying alternative device enumeration methods
2025-07-22 00:35:10.044 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-22 00:35:10.044 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-22 00:35:10.045 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-22 00:35:10.045 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-22 00:35:10.046 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:10.046 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-22 00:35:10.049 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-22 00:35:10.052 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-22 00:35:10.052 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-22 00:35:10.056 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-22 00:35:10.058 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-22 00:35:10.058 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-22 00:35:10.059 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-22 00:35:10.059 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-07-22 00:35:10.060 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-22 00:35:10.060 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-22 00:35:10.060 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-22 00:35:10.062 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-22 00:35:10.062 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-22 00:35:10.062 [Information] VocomService: Using new enhanced device detection service
2025-07-22 00:35:10.063 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-22 00:35:10.063 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-22 00:35:10.236 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-22 00:35:10.236 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-22 00:35:10.236 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-22 00:35:10.237 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-22 00:35:10.237 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-22 00:35:10.237 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-22 00:35:10.237 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-22 00:35:10.238 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-22 00:35:10.405 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-22 00:35:10.406 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-22 00:35:10.406 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-22 00:35:10.407 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-22 00:35:10.408 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-22 00:35:10.408 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-22 00:35:10.408 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-22 00:35:10.409 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-22 00:35:10.409 [Debug] VocomService: Bluetooth is enabled
2025-07-22 00:35:10.409 [Debug] VocomService: Checking if WiFi is available
2025-07-22 00:35:10.409 [Debug] VocomService: WiFi is available
2025-07-22 00:35:10.410 [Information] VocomService: Found 3 Vocom devices
2025-07-22 00:35:10.410 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:10.411 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:10.411 [Information] VocomService: Checking if PTT application is running
2025-07-22 00:35:10.421 [Information] VocomService: PTT application is not running
2025-07-22 00:35:10.422 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-22 00:35:10.422 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-22 00:35:10.422 [Information] VocomService: Checking if PTT application is running
2025-07-22 00:35:10.433 [Information] VocomService: PTT application is not running
2025-07-22 00:35:10.433 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-22 00:35:10.434 [Information] VocomService: Native USB connection attempt 1/3 to WUDFPuma Driver
2025-07-22 00:35:10.434 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-22 00:35:10.434 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-22 00:35:10.434 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-22 00:35:10.434 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-22 00:35:12.435 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-22 00:35:13.435 [Information] VocomService: Native USB connection attempt 2/3 to WUDFPuma Driver
2025-07-22 00:35:13.435 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-22 00:35:13.435 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-22 00:35:13.436 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-22 00:35:13.436 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-22 00:35:15.436 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-22 00:35:16.437 [Information] VocomService: Native USB connection attempt 3/3 to WUDFPuma Driver
2025-07-22 00:35:16.438 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-22 00:35:16.438 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-22 00:35:16.438 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-22 00:35:16.438 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-22 00:35:18.438 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-22 00:35:18.439 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-22 00:35:18.439 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-22 00:35:18.439 [Information] ModernUSBCommunicationService: USB availability check: False
2025-07-22 00:35:18.440 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-22 00:35:18.440 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-22 00:35:18.440 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-22 00:35:18.440 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-22 00:35:18.440 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-22 00:35:18.441 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-22 00:35:18.441 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-22 00:35:18.441 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-22 00:35:18.441 [Information] ModernUSBCommunicationService: Found 0 HID devices total
2025-07-22 00:35:18.442 [Information] ModernUSBCommunicationService: Trying alternative device enumeration methods
2025-07-22 00:35:18.442 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-22 00:35:18.442 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-22 00:35:18.442 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-22 00:35:18.443 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-22 00:35:18.443 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-22 00:35:18.444 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:18.444 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:18.445 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:18.445 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-22 00:35:18.445 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-22 00:35:18.445 [Information] VocomService: Checking if PTT application is running
2025-07-22 00:35:18.455 [Information] VocomService: PTT application is not running
2025-07-22 00:35:18.457 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-22 00:35:18.458 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-22 00:35:18.458 [Debug] VocomService: Bluetooth is enabled
2025-07-22 00:35:18.459 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-22 00:35:19.262 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-22 00:35:19.263 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-22 00:35:19.263 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-22 00:35:19.264 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-22 00:35:19.266 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-22 00:35:19.268 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-22 00:35:19.270 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-22 00:35:19.272 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-22 00:35:19.274 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-22 00:35:19.280 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-22 00:35:19.283 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-22 00:35:19.294 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-22 00:35:19.295 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-22 00:35:19.295 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-22 00:35:19.296 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-22 00:35:19.296 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-22 00:35:19.296 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-22 00:35:19.296 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-22 00:35:19.297 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-22 00:35:19.297 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-22 00:35:19.297 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-22 00:35:19.298 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-22 00:35:19.298 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-22 00:35:19.298 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-22 00:35:19.298 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-22 00:35:19.299 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-22 00:35:19.299 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-22 00:35:19.299 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-22 00:35:19.302 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-22 00:35:19.308 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-22 00:35:19.309 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-22 00:35:19.312 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-22 00:35:19.313 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-22 00:35:19.319 [Information] CANRegisterAccess: Read value 0x4D from register 0x0141 (simulated)
2025-07-22 00:35:19.320 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-22 00:35:19.321 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-22 00:35:19.321 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-22 00:35:19.327 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-22 00:35:19.327 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-22 00:35:19.333 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-22 00:35:19.333 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-22 00:35:19.333 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-22 00:35:19.339 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-22 00:35:19.339 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-22 00:35:19.339 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-22 00:35:19.345 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-22 00:35:19.345 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-22 00:35:19.351 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-22 00:35:19.351 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-22 00:35:19.357 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-22 00:35:19.357 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-22 00:35:19.363 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-22 00:35:19.363 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-22 00:35:19.369 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-22 00:35:19.369 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-22 00:35:19.375 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-22 00:35:19.375 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-22 00:35:19.381 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-22 00:35:19.381 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-22 00:35:19.386 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-22 00:35:19.386 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-22 00:35:19.391 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-22 00:35:19.392 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-22 00:35:19.397 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-22 00:35:19.398 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-22 00:35:19.403 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-22 00:35:19.404 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-22 00:35:19.409 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-22 00:35:19.410 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-22 00:35:19.415 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-22 00:35:19.416 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-22 00:35:19.421 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-22 00:35:19.422 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-22 00:35:19.426 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-22 00:35:19.427 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-22 00:35:19.432 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-22 00:35:19.433 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-22 00:35:19.438 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-22 00:35:19.439 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-22 00:35:19.439 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-22 00:35:19.444 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-22 00:35:19.444 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-22 00:35:19.445 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-22 00:35:19.445 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-22 00:35:19.451 [Information] CANRegisterAccess: Read value 0xAD from register 0x0141 (simulated)
2025-07-22 00:35:19.457 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-22 00:35:19.463 [Information] CANRegisterAccess: Read value 0x5E from register 0x0141 (simulated)
2025-07-22 00:35:19.464 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-22 00:35:19.464 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-22 00:35:19.464 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-22 00:35:19.464 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-22 00:35:19.470 [Information] CANRegisterAccess: Read value 0xB2 from register 0x0140 (simulated)
2025-07-22 00:35:19.471 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-22 00:35:19.471 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-22 00:35:19.473 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-22 00:35:19.473 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-22 00:35:19.484 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-22 00:35:19.485 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-22 00:35:19.486 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-22 00:35:19.489 [Information] VocomService: Sending data and waiting for response
2025-07-22 00:35:19.490 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-22 00:35:19.540 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-22 00:35:19.541 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-22 00:35:19.542 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-22 00:35:19.543 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-22 00:35:19.544 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-22 00:35:19.555 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-22 00:35:19.556 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-22 00:35:19.556 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-22 00:35:19.567 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-22 00:35:19.578 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-22 00:35:19.589 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-22 00:35:19.600 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-22 00:35:19.611 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-22 00:35:19.612 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-22 00:35:19.613 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-22 00:35:19.624 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-22 00:35:19.625 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-22 00:35:19.625 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-22 00:35:19.636 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-22 00:35:19.647 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-22 00:35:19.658 [Information] IICProtocolHandler: Enabling IIC module
2025-07-22 00:35:19.668 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-22 00:35:19.679 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-22 00:35:19.690 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-22 00:35:19.692 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-22 00:35:19.692 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-22 00:35:19.703 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-22 00:35:19.705 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-22 00:35:19.705 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-22 00:35:19.705 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-22 00:35:19.705 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-22 00:35:19.705 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-22 00:35:19.706 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-22 00:35:19.706 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-22 00:35:19.706 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-22 00:35:19.706 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-22 00:35:19.706 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-22 00:35:19.706 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-22 00:35:19.707 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-22 00:35:19.707 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-22 00:35:19.707 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-22 00:35:19.707 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-22 00:35:19.708 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-22 00:35:19.808 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-22 00:35:19.809 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-22 00:35:19.811 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-22 00:35:19.812 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-22 00:35:19.813 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-22 00:35:19.813 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-22 00:35:19.813 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-22 00:35:19.814 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-22 00:35:19.814 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-22 00:35:19.814 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-22 00:35:19.815 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-22 00:35:19.815 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-22 00:35:19.815 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-22 00:35:19.815 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-22 00:35:19.816 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-22 00:35:19.817 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-22 00:35:19.818 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-22 00:35:19.818 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-22 00:35:19.822 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-22 00:35:19.824 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-22 00:35:19.827 [Information] BackupService: Initializing backup service
2025-07-22 00:35:19.827 [Information] BackupService: Backup service initialized successfully
2025-07-22 00:35:19.828 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-22 00:35:19.828 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-22 00:35:19.830 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-22 00:35:19.868 [Information] BackupService: Compressing backup data
2025-07-22 00:35:19.875 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-07-22 00:35:19.876 [Information] BackupServiceFactory: Created template for category: Production
2025-07-22 00:35:19.876 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-22 00:35:19.876 [Information] BackupService: Compressing backup data
2025-07-22 00:35:19.877 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-07-22 00:35:19.878 [Information] BackupServiceFactory: Created template for category: Development
2025-07-22 00:35:19.878 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-22 00:35:19.878 [Information] BackupService: Compressing backup data
2025-07-22 00:35:19.879 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-07-22 00:35:19.880 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-22 00:35:19.880 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-22 00:35:19.880 [Information] BackupService: Compressing backup data
2025-07-22 00:35:19.881 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-22 00:35:19.881 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-22 00:35:19.881 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-22 00:35:19.882 [Information] BackupService: Compressing backup data
2025-07-22 00:35:19.883 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (450 bytes)
2025-07-22 00:35:19.883 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-22 00:35:19.883 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-22 00:35:19.884 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-22 00:35:19.884 [Information] BackupService: Compressing backup data
2025-07-22 00:35:19.886 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (515 bytes)
2025-07-22 00:35:19.886 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-22 00:35:19.886 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-22 00:35:19.888 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-22 00:35:19.891 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-22 00:35:19.894 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-22 00:35:19.946 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-22 00:35:19.947 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-22 00:35:19.948 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-22 00:35:19.949 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-22 00:35:19.949 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-22 00:35:19.950 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-22 00:35:19.951 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-22 00:35:19.954 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-22 00:35:19.955 [Information] App: Flash operation monitor service initialized successfully
2025-07-22 00:35:19.963 [Information] LicensingService: Initializing licensing service
2025-07-22 00:35:20.002 [Information] LicensingService: License information loaded successfully
2025-07-22 00:35:20.004 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-22 00:35:20.005 [Information] App: Licensing service initialized successfully
2025-07-22 00:35:20.005 [Information] App: License status: Trial
2025-07-22 00:35:20.005 [Information] App: Trial period: 22 days remaining
2025-07-22 00:35:20.006 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-22 00:35:20.029 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-22 00:35:20.177 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-22 00:35:20.178 [Information] VocomService: Initializing enhanced Vocom services
2025-07-22 00:35:20.178 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-22 00:35:20.178 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-22 00:35:20.179 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-22 00:35:20.179 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-22 00:35:20.179 [Information] VocomService: Native USB communication service initialized
2025-07-22 00:35:20.179 [Information] VocomService: Enhanced device detection service initialized
2025-07-22 00:35:20.179 [Information] VocomService: Connection recovery service initialized
2025-07-22 00:35:20.180 [Information] VocomService: Enhanced services initialization completed
2025-07-22 00:35:20.180 [Information] VocomService: Checking if PTT application is running
2025-07-22 00:35:20.190 [Information] VocomService: PTT application is not running
2025-07-22 00:35:20.191 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-22 00:35:20.192 [Debug] VocomService: Bluetooth is enabled
2025-07-22 00:35:20.192 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-22 00:35:20.242 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-22 00:35:20.243 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-22 00:35:20.243 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-22 00:35:20.243 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-22 00:35:20.244 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-22 00:35:20.244 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: f04b8291-b3dc-442c-a84c-60597d37afba
2025-07-22 00:35:20.246 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-22 00:35:20.246 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-22 00:35:20.247 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-22 00:35:20.247 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-22 00:35:20.248 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-22 00:35:20.248 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-22 00:35:20.249 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-22 00:35:20.250 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-22 00:35:20.250 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-22 00:35:20.262 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-22 00:35:20.262 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-22 00:35:20.262 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-22 00:35:20.262 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-22 00:35:20.263 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-22 00:35:20.263 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-22 00:35:20.263 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-22 00:35:20.263 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-22 00:35:20.263 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-22 00:35:20.264 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-22 00:35:20.264 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-22 00:35:20.264 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-22 00:35:20.264 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-22 00:35:20.265 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-22 00:35:20.265 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-22 00:35:20.265 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-22 00:35:20.265 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-22 00:35:20.266 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-22 00:35:20.271 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-22 00:35:20.272 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-22 00:35:20.272 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-22 00:35:20.272 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-22 00:35:20.278 [Information] CANRegisterAccess: Read value 0x2F from register 0x0141 (simulated)
2025-07-22 00:35:20.279 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-22 00:35:20.279 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-22 00:35:20.279 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-22 00:35:20.286 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-22 00:35:20.286 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-22 00:35:20.292 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-22 00:35:20.293 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-22 00:35:20.293 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-22 00:35:20.299 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-22 00:35:20.300 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-22 00:35:20.300 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-22 00:35:20.306 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-22 00:35:20.307 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-22 00:35:20.312 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-22 00:35:20.313 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-22 00:35:20.318 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-22 00:35:20.319 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-22 00:35:20.325 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-22 00:35:20.326 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-22 00:35:20.331 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-22 00:35:20.332 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-22 00:35:20.337 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-22 00:35:20.338 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-22 00:35:20.343 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-22 00:35:20.344 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-22 00:35:20.349 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-22 00:35:20.350 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-22 00:35:20.355 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-22 00:35:20.356 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-22 00:35:20.361 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-22 00:35:20.361 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-22 00:35:20.367 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-22 00:35:20.367 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-22 00:35:20.373 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-22 00:35:20.373 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-22 00:35:20.380 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-22 00:35:20.380 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-22 00:35:20.386 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-22 00:35:20.387 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-22 00:35:20.393 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-22 00:35:20.393 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-22 00:35:20.399 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-22 00:35:20.399 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-22 00:35:20.405 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-22 00:35:20.405 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-22 00:35:20.405 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-22 00:35:20.412 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-22 00:35:20.412 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-22 00:35:20.412 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-22 00:35:20.413 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-22 00:35:20.419 [Information] CANRegisterAccess: Read value 0xAA from register 0x0141 (simulated)
2025-07-22 00:35:20.419 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-22 00:35:20.419 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-22 00:35:20.420 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-22 00:35:20.420 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-22 00:35:20.426 [Information] CANRegisterAccess: Read value 0x85 from register 0x0140 (simulated)
2025-07-22 00:35:20.432 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-22 00:35:20.438 [Information] CANRegisterAccess: Read value 0x91 from register 0x0140 (simulated)
2025-07-22 00:35:20.438 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-22 00:35:20.438 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-22 00:35:20.439 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-22 00:35:20.439 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-22 00:35:20.450 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-22 00:35:20.450 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-22 00:35:20.450 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-22 00:35:20.450 [Information] VocomService: Sending data and waiting for response
2025-07-22 00:35:20.451 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-22 00:35:20.501 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-22 00:35:20.501 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-22 00:35:20.502 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-22 00:35:20.502 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-22 00:35:20.502 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-22 00:35:20.513 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-22 00:35:20.513 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-22 00:35:20.513 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-22 00:35:20.524 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-22 00:35:20.535 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-22 00:35:20.546 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-22 00:35:20.557 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-22 00:35:20.568 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-22 00:35:20.568 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-22 00:35:20.568 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-22 00:35:20.580 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-22 00:35:20.580 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-22 00:35:20.580 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-22 00:35:20.592 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-22 00:35:20.603 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-22 00:35:20.614 [Information] IICProtocolHandler: Enabling IIC module
2025-07-22 00:35:20.649 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-22 00:35:20.660 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-22 00:35:20.671 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-22 00:35:20.680 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-22 00:35:20.681 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-22 00:35:20.692 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-22 00:35:20.692 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-22 00:35:20.692 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-22 00:35:20.692 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-22 00:35:20.693 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-22 00:35:20.693 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-22 00:35:20.693 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-22 00:35:20.693 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-22 00:35:20.693 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-22 00:35:20.694 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-22 00:35:20.694 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-22 00:35:20.694 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-22 00:35:20.694 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-22 00:35:20.694 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-22 00:35:20.695 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-22 00:35:20.695 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-22 00:35:20.695 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-22 00:35:20.795 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-22 00:35:20.795 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-22 00:35:20.796 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-22 00:35:20.796 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-22 00:35:20.796 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-22 00:35:20.797 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-22 00:35:20.797 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-22 00:35:20.797 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-22 00:35:20.797 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-22 00:35:20.798 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-22 00:35:20.798 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-22 00:35:20.798 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-22 00:35:20.798 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-22 00:35:20.798 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-22 00:35:20.799 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-22 00:35:20.850 [Information] BackupService: Initializing backup service
2025-07-22 00:35:20.850 [Information] BackupService: Backup service initialized successfully
2025-07-22 00:35:20.902 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-22 00:35:20.902 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-22 00:35:20.903 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-22 00:35:20.904 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-22 00:35:20.954 [Information] BackupService: Getting predefined backup categories
2025-07-22 00:35:21.005 [Information] MainViewModel: Services initialized successfully
2025-07-22 00:35:21.007 [Information] MainViewModel: Scanning for Vocom devices
2025-07-22 00:35:21.009 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-22 00:35:21.009 [Information] VocomService: Using new enhanced device detection service
2025-07-22 00:35:21.009 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-22 00:35:21.009 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-22 00:35:21.199 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-22 00:35:21.200 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-22 00:35:21.200 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-22 00:35:21.200 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-22 00:35:21.200 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-22 00:35:21.209 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-22 00:35:21.209 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-22 00:35:21.209 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-22 00:35:21.432 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-22 00:35:21.432 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-22 00:35:21.433 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-22 00:35:21.433 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-22 00:35:21.434 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-22 00:35:21.434 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-22 00:35:21.434 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-22 00:35:21.434 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-22 00:35:21.436 [Debug] VocomService: Bluetooth is enabled
2025-07-22 00:35:21.436 [Debug] VocomService: Checking if WiFi is available
2025-07-22 00:35:21.437 [Debug] VocomService: WiFi is available
2025-07-22 00:35:21.437 [Information] VocomService: Found 3 Vocom devices
2025-07-22 00:35:21.438 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-22 00:35:30.441 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-07-22 00:35:30.442 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:30.444 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-22 00:35:30.444 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-22 00:35:30.854 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-22 00:35:30.855 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-22 00:35:30.856 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-22 00:35:30.858 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-22 00:35:30.858 [Information] ECUCommunicationService: No ECUs are connected
2025-07-22 00:35:30.859 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-22 00:35:30.859 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-22 00:35:30.859 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-22 00:35:30.860 [Information] ECUCommunicationService: No ECUs are connected
2025-07-22 00:35:30.860 [Information] VocomService: Checking if PTT application is running
2025-07-22 00:35:30.871 [Information] VocomService: PTT application is not running
2025-07-22 00:35:30.872 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-22 00:35:30.872 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-22 00:35:30.872 [Information] VocomService: Checking if PTT application is running
2025-07-22 00:35:30.883 [Information] VocomService: PTT application is not running
2025-07-22 00:35:30.883 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-22 00:35:30.883 [Information] VocomService: Native USB connection attempt 1/3 to WUDFPuma Driver
2025-07-22 00:35:30.884 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-22 00:35:30.884 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-22 00:35:30.884 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-22 00:35:30.884 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-22 00:35:32.891 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-22 00:35:33.896 [Information] VocomService: Native USB connection attempt 2/3 to WUDFPuma Driver
2025-07-22 00:35:33.897 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-22 00:35:33.897 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-22 00:35:33.897 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-22 00:35:33.898 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-22 00:35:35.912 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-22 00:35:36.920 [Information] VocomService: Native USB connection attempt 3/3 to WUDFPuma Driver
2025-07-22 00:35:36.920 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-22 00:35:36.920 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-22 00:35:36.920 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-22 00:35:36.921 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-22 00:35:38.920 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-22 00:35:38.921 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-22 00:35:38.921 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-22 00:35:38.921 [Information] ModernUSBCommunicationService: USB availability check: False
2025-07-22 00:35:38.922 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-22 00:35:38.922 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-22 00:35:38.922 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-22 00:35:38.922 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-22 00:35:38.923 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-22 00:35:38.923 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-22 00:35:38.923 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-22 00:35:38.923 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-22 00:35:38.924 [Information] ModernUSBCommunicationService: Found 0 HID devices total
2025-07-22 00:35:38.924 [Information] ModernUSBCommunicationService: Trying alternative device enumeration methods
2025-07-22 00:35:38.924 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-22 00:35:38.924 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-22 00:35:38.924 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-22 00:35:38.924 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-22 00:35:38.925 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-22 00:35:38.925 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-22 00:35:38.925 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-22 00:35:38.926 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-22 00:35:38.926 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-22 00:35:38.926 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:38.926 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:38.926 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:38.927 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:38.927 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:38.927 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-22 00:35:38.928 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
2025-07-22 00:36:05.233 [Information] MainViewModel: Connecting to Vocom device 88890300-BT
2025-07-22 00:36:05.234 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-22 00:36:05.234 [Information] VocomService: Checking if PTT application is running
2025-07-22 00:36:05.245 [Information] VocomService: PTT application is not running
2025-07-22 00:36:05.245 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-22 00:36:05.245 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-22 00:36:05.246 [Debug] VocomService: Bluetooth is enabled
2025-07-22 00:36:05.246 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-22 00:36:06.050 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-22 00:36:06.050 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-22 00:36:06.051 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-22 00:36:06.051 [Information] MainViewModel: Vocom device 88890300-BT connected
2025-07-22 00:36:06.051 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-22 00:36:06.052 [Information] MainViewModel: Connected to Vocom device 88890300-BT
2025-07-22 00:36:06.053 [Information] MainViewModel: Scanning for ECUs
2025-07-22 00:36:06.058 [Information] ECUCommunicationService: Scanning for ECUs
2025-07-22 00:36:06.058 [Information] ECUCommunicationService: Scanning for ECUs on the CAN bus...
2025-07-22 00:36:06.564 [Information] ECUCommunicationService: Scanning for ECUs using SPI protocol...
2025-07-22 00:36:06.874 [Information] ECUCommunicationService: Scanning for ECUs using SCI protocol...
2025-07-22 00:36:07.184 [Information] ECUCommunicationService: Scanning for ECUs using IIC protocol...
2025-07-22 00:36:07.496 [Information] ECUCommunicationService: Found 10 ECUs
2025-07-22 00:36:07.497 [Information] MainViewModel: Found 10 ECU(s)
2025-07-22 00:36:20.794 [Information] MainViewModel: Connecting to ECU DCM
2025-07-22 00:36:20.800 [Information] ECUCommunicationService: Connecting to ECU DCM
2025-07-22 00:36:20.801 [Information] ECUCommunicationService: Using high-speed communication for ECU DCM
2025-07-22 00:36:20.802 [Information] ECUCommunicationService: Setting operating mode to Bench for ECU DCM
2025-07-22 00:36:20.802 [Information] ECUCommunicationService: Attempting to connect to ECU DCM using SPI protocol with high-speed communication
2025-07-22 00:36:20.803 [Information] SPIProtocolHandler: Connecting to ECU DCM via SPI
2025-07-22 00:36:21.006 [Information] SPIProtocolHandler: Connected to ECU DCM via SPI
2025-07-22 00:36:21.007 [Information] ECUCommunicationService: Configuring MC9S12XEP100 specific settings for ECU DCM
2025-07-22 00:36:21.009 [Information] ECUCommunicationService: Configuring MC9S12XEP100 specific settings for ECU DCM
2025-07-22 00:36:21.009 [Information] ECUCommunicationService: Using MC9S12XEP100Integration for configuring ECU DCM
2025-07-22 00:36:21.009 [Information] MC9S12XEP100Helper: Configured ECU DCM with MC9S12XEP100-specific settings
2025-07-22 00:36:21.011 [Information] ECUCommunicationService: Configuring MC9S12XEP100 SPI settings for ECU DCM
2025-07-22 00:36:21.115 [Information] ECUCommunicationService: MC9S12XEP100 SPI settings configured for ECU DCM
2025-07-22 00:36:21.120 [Information] ECUCommunicationService: Getting MC9S12XEP100 flash memory configuration for ECU DCM
2025-07-22 00:36:21.121 [Information] VocomService: Reading MC9S12XEP100 specific registers from ECU 7cba41ee-898c-4d29-abbd-db76e7fd2157
2025-07-22 00:36:21.334 [Information] VocomService: Read 20 MC9S12XEP100 specific registers from ECU 7cba41ee-898c-4d29-abbd-db76e7fd2157
2025-07-22 00:36:21.336 [Information] ECUCommunicationService: Got MC9S12XEP100 flash memory configuration with 35 parameters
2025-07-22 00:36:21.339 [Information] SPIProtocolHandler: Setting operating mode to Bench via SPI
2025-07-22 00:36:21.339 [Information] SPIProtocolHandler: Configuring SPI controller for Bench mode
2025-07-22 00:36:21.340 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-22 00:36:21.340 [Information] SPIProtocolHandler: Writing 0x43 to SPI Baud Rate Register (0x00FA)
2025-07-22 00:36:21.397 [Information] SPIProtocolHandler: Operating mode set to Bench via SPI
2025-07-22 00:36:21.398 [Information] ECUCommunicationService: Reading initial parameters and faults from ECU DCM
2025-07-22 00:36:21.399 [Information] SPIProtocolHandler: Reading parameters from ECU DCM via SPI
2025-07-22 00:36:21.910 [Information] SPIProtocolHandler: Read 3 parameters from ECU DCM via SPI
2025-07-22 00:36:21.912 [Information] SPIProtocolHandler: Reading active faults from ECU DCM via SPI
2025-07-22 00:36:22.221 [Information] SPIProtocolHandler: Read 0 active faults from ECU DCM via SPI
2025-07-22 00:36:22.223 [Information] SPIProtocolHandler: Reading inactive faults from ECU DCM via SPI
2025-07-22 00:36:22.531 [Information] SPIProtocolHandler: Read 0 inactive faults from ECU DCM via SPI
2025-07-22 00:36:22.532 [Information] MainViewModel: ECU DCM connected
2025-07-22 00:36:22.535 [Information] ECUCommunicationService: Connected to ECU DCM
2025-07-22 00:36:22.536 [Information] MainViewModel: Connected to ECU DCM
