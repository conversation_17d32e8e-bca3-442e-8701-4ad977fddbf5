using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Backup;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Backup
{
    [TestFixture]
    public class BackupVersioningServiceTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IBackupService> _mockBackupService;
        private BackupVersioningService _versioningService;
        private List<BackupData> _testBackups;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _mockBackupService = new Mock<IBackupService>();
            _versioningService = new BackupVersioningService(_mockLogger.Object, _mockBackupService.Object);

            // Create test backup data
            _testBackups = CreateTestBackupVersions();
        }

        [Test]
        public async Task GetVersionTreeAsync_WithValidBackupId_ReturnsVersionTree()
        {
            // Arrange
            string backupId = "backup1";
            _mockBackupService.Setup(s => s.GetBackupVersionsAsync(backupId))
                .ReturnsAsync(_testBackups);

            // Act
            var result = await _versioningService.GetVersionTreeAsync(backupId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.VersionCount, Is.EqualTo(3));
            Assert.That(result.RootBackup, Is.EqualTo(_testBackups[0]));
            Assert.That(result.LatestVersion, Is.EqualTo(_testBackups[2]));
        }

        [Test]
        public async Task GetVersionTreeAsync_WithInvalidBackupId_ReturnsNull()
        {
            // Arrange
            string backupId = "nonexistent";
            _mockBackupService.Setup(s => s.GetBackupVersionsAsync(backupId))
                .ReturnsAsync(new List<BackupData>());

            // Act
            var result = await _versioningService.GetVersionTreeAsync(backupId);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task CompareVersionsAsync_WithValidVersionIds_ReturnsDifferences()
        {
            // Arrange
            string version1Id = "backup1";
            string version2Id = "backup2";
            _mockBackupService.Setup(s => s.GetAllBackupsAsync())
                .ReturnsAsync(_testBackups);

            // Act
            var result = await _versioningService.CompareVersionsAsync(version1Id, version2Id);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.ContainsKey("Description"), Is.True);
            Assert.That(result["Description"], Is.EqualTo(("Version 1", "Version 2")));
        }

        [Test]
        public async Task CompareVersionsAsync_WithInvalidVersionIds_ReturnsNull()
        {
            // Arrange
            string version1Id = "backup1";
            string version2Id = "nonexistent";
            _mockBackupService.Setup(s => s.GetAllBackupsAsync())
                .ReturnsAsync(_testBackups);

            // Act
            var result = await _versioningService.CompareVersionsAsync(version1Id, version2Id);

            // Assert
            Assert.That(result, Is.Null);
        }

        [Test]
        public async Task MergeVersionsAsync_WithValidVersionIds_ReturnsMergedBackup()
        {
            // Arrange
            string sourceVersionId = "backup2";
            string targetVersionId = "backup3";
            var mergeOptions = new BackupMergeOptions();
            var mergedBackup = new BackupData { Id = "merged", Description = "Merged Backup" };

            _mockBackupService.Setup(s => s.GetAllBackupsAsync())
                .ReturnsAsync(_testBackups);
            _mockBackupService.Setup(s => s.MergeBackupVersionsAsync(sourceVersionId, targetVersionId, mergeOptions))
                .ReturnsAsync(mergedBackup);

            bool eventRaised = false;
            _versioningService.VersionsMerged += (sender, backup) => eventRaised = true;

            // Act
            var result = await _versioningService.MergeVersionsAsync(sourceVersionId, targetVersionId, mergeOptions);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo("merged"));
            Assert.That(result.Description, Is.EqualTo("Merged Backup"));
            Assert.That(eventRaised, Is.True);
        }

        [Test]
        public async Task MergeVersionsAsync_WithInvalidVersionIds_ReturnsNull()
        {
            // Arrange
            string sourceVersionId = "backup1";
            string targetVersionId = "nonexistent";
            var mergeOptions = new BackupMergeOptions();

            _mockBackupService.Setup(s => s.GetAllBackupsAsync())
                .ReturnsAsync(_testBackups);

            bool errorEventRaised = false;
            _versioningService.VersioningError += (sender, message) => errorEventRaised = true;

            // Act
            var result = await _versioningService.MergeVersionsAsync(sourceVersionId, targetVersionId, mergeOptions);

            // Assert
            Assert.That(result, Is.Null);
            Assert.That(errorEventRaised, Is.True);
        }

        private List<BackupData> CreateTestBackupVersions()
        {
            // Create a chain of backup versions
            var backup1 = new BackupData
            {
                Id = "backup1",
                Version = 1,
                Description = "Version 1",
                CreationTime = DateTime.Now.AddDays(-3),
                Parameters = new Dictionary<string, object>
                {
                    { "Param1", 100 },
                    { "Param2", "Value1" }
                },
                EEPROMData = new byte[] { 1, 2, 3, 4 },
                MicrocontrollerCode = new byte[] { 5, 6, 7, 8 },
                Tags = new List<string> { "Tag1", "Tag2" }
            };

            var backup2 = new BackupData
            {
                Id = "backup2",
                Version = 2,
                Description = "Version 2",
                CreationTime = DateTime.Now.AddDays(-2),
                ParentBackupId = "backup1",
                RootBackupId = "backup1",
                Parameters = new Dictionary<string, object>
                {
                    { "Param1", 150 },
                    { "Param2", "Value2" }
                },
                EEPROMData = new byte[] { 1, 2, 3, 5 },
                MicrocontrollerCode = new byte[] { 5, 6, 7, 8 },
                Tags = new List<string> { "Tag1", "Tag3" }
            };

            var backup3 = new BackupData
            {
                Id = "backup3",
                Version = 3,
                Description = "Version 3",
                CreationTime = DateTime.Now.AddDays(-1),
                ParentBackupId = "backup2",
                RootBackupId = "backup1",
                Parameters = new Dictionary<string, object>
                {
                    { "Param1", 200 },
                    { "Param2", "Value3" },
                    { "Param3", true }
                },
                EEPROMData = new byte[] { 1, 2, 3, 6 },
                MicrocontrollerCode = new byte[] { 5, 6, 7, 9 },
                Tags = new List<string> { "Tag1", "Tag3", "Tag4" }
            };

            // Set up parent-child relationships
            backup1.ChildBackupIds.Add("backup2");
            backup2.ChildBackupIds.Add("backup3");

            return new List<BackupData> { backup1, backup2, backup3 };
        }
    }
}

