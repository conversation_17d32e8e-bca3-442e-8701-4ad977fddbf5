using System;
using System.Collections.Generic;
using System.Linq;
using System.Management;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Win32;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Enhanced Vocom device detection with comprehensive hardware identification
    /// Supports USB, WiFi, and Bluetooth connections with real hardware validation
    /// </summary>
    public class EnhancedVocomDeviceDetection
    {
        private readonly ILoggingService _logger;

        // Vocom device identifiers
        private const ushort VOCOM_VENDOR_ID = 0x178E;
        private const ushort VOCOM_PRODUCT_ID = 0x0024;
        private const string VOCOM_DEVICE_NAME = "88890300";
        private const string VOCOM_DRIVER_NAME = "WUDFPuma";

        // Windows API imports for device enumeration
        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern IntPtr SetupDiGetClassDevs(
            ref Guid classGuid,
            string? enumerator,
            IntPtr hwndParent,
            uint flags);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiEnumDeviceInfo(
            IntPtr deviceInfoSet,
            uint memberIndex,
            ref SP_DEVINFO_DATA deviceInfoData);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiGetDeviceRegistryProperty(
            IntPtr deviceInfoSet,
            ref SP_DEVINFO_DATA deviceInfoData,
            uint property,
            out uint propertyRegDataType,
            byte[] propertyBuffer,
            uint propertyBufferSize,
            out uint requiredSize);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiDestroyDeviceInfoList(IntPtr deviceInfoSet);

        [StructLayout(LayoutKind.Sequential)]
        private struct SP_DEVINFO_DATA
        {
            public uint cbSize;
            public Guid classGuid;
            public uint devInst;
            public IntPtr reserved;
        }

        // Device property constants
        private const uint SPDRP_DEVICEDESC = 0x00000000;
        private const uint SPDRP_HARDWAREID = 0x00000001;
        private const uint SPDRP_COMPATIBLEIDS = 0x00000002;
        private const uint SPDRP_SERVICE = 0x00000004;
        private const uint SPDRP_CLASS = 0x00000007;
        private const uint SPDRP_CLASSGUID = 0x00000008;
        private const uint SPDRP_DRIVER = 0x00000009;
        private const uint SPDRP_FRIENDLYNAME = 0x0000000C;
        private const uint SPDRP_LOCATION_INFORMATION = 0x0000000D;
        private const uint SPDRP_PHYSICAL_DEVICE_OBJECT_NAME = 0x0000000E;

        // Device class GUIDs
        private static readonly Guid USB_DEVICE_CLASS_GUID = new Guid("A5DCBF10-6530-11D2-901F-00C04FB951ED");
        private static readonly Guid HID_CLASS_GUID = new Guid("4d1e55b2-f16f-11cf-88cb-001111000030");
        private static readonly Guid PORTS_CLASS_GUID = new Guid("4D36E978-E325-11CE-BFC1-08002BE10318");

        public EnhancedVocomDeviceDetection(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Detects all available Vocom devices with comprehensive hardware validation
        /// </summary>
        public async Task<List<VocomDevice>> DetectVocomDevicesAsync()
        {
            var devices = new List<VocomDevice>();

            try
            {
                _logger.LogInformation("Starting comprehensive Vocom device detection", "EnhancedVocomDeviceDetection");

                // Detect USB devices
                var usbDevices = await DetectUSBVocomDevicesAsync();
                devices.AddRange(usbDevices);

                // Detect WiFi devices
                var wifiDevices = await DetectWiFiVocomDevicesAsync();
                devices.AddRange(wifiDevices);

                // Detect Bluetooth devices
                var bluetoothDevices = await DetectBluetoothVocomDevicesAsync();
                devices.AddRange(bluetoothDevices);

                // Validate detected devices
                var validatedDevices = await ValidateDetectedDevicesAsync(devices);

                _logger.LogInformation($"Detected {validatedDevices.Count} valid Vocom devices", "EnhancedVocomDeviceDetection");
                return validatedDevices;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error during Vocom device detection", "EnhancedVocomDeviceDetection", ex);
                return devices;
            }
        }

        /// <summary>
        /// Detects USB Vocom devices using Windows device enumeration
        /// </summary>
        private async Task<List<VocomDevice>> DetectUSBVocomDevicesAsync()
        {
            var devices = new List<VocomDevice>();

            try
            {
                _logger.LogInformation("Detecting USB Vocom devices", "EnhancedVocomDeviceDetection");

                // Search in USB device class
                devices.AddRange(await SearchDeviceClass(USB_DEVICE_CLASS_GUID, "USB"));

                // Search in HID device class
                devices.AddRange(await SearchDeviceClass(HID_CLASS_GUID, "HID"));

                // Search in Ports device class
                devices.AddRange(await SearchDeviceClass(PORTS_CLASS_GUID, "Ports"));

                // Also check WMI for USB devices
                var wmiDevices = await DetectUSBDevicesViaWMIAsync();
                devices.AddRange(wmiDevices);

                _logger.LogInformation($"Found {devices.Count} potential USB Vocom devices", "EnhancedVocomDeviceDetection");
            }
            catch (Exception ex)
            {
                _logger.LogError("Error detecting USB Vocom devices", "EnhancedVocomDeviceDetection", ex);
            }

            await Task.CompletedTask;
            return devices;
        }

        /// <summary>
        /// Searches for Vocom devices in a specific device class
        /// </summary>
        private async Task<List<VocomDevice>> SearchDeviceClass(Guid classGuid, string className)
        {
            var devices = new List<VocomDevice>();

            try
            {
                IntPtr deviceInfoSet = SetupDiGetClassDevs(
                    ref classGuid,
                    null,
                    IntPtr.Zero,
                    0x00000010 | 0x00000002); // DIGCF_PRESENT | DIGCF_DEVICEINTERFACE

                if (deviceInfoSet == IntPtr.Zero || deviceInfoSet == new IntPtr(-1))
                {
                    return devices;
                }

                try
                {
                    uint deviceIndex = 0;
                    var deviceInfoData = new SP_DEVINFO_DATA();
                    deviceInfoData.cbSize = (uint)Marshal.SizeOf(deviceInfoData);

                    while (SetupDiEnumDeviceInfo(deviceInfoSet, deviceIndex, ref deviceInfoData))
                    {
                        var device = await CreateVocomDeviceFromDeviceInfo(deviceInfoSet, deviceInfoData, className);
                        if (device != null)
                        {
                            devices.Add(device);
                        }

                        deviceIndex++;
                    }
                }
                finally
                {
                    SetupDiDestroyDeviceInfoList(deviceInfoSet);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error searching {className} device class", "EnhancedVocomDeviceDetection", ex);
            }

            await Task.CompletedTask;
            return devices;
        }

        /// <summary>
        /// Creates a VocomDevice from Windows device information
        /// </summary>
        private async Task<VocomDevice?> CreateVocomDeviceFromDeviceInfo(IntPtr deviceInfoSet, SP_DEVINFO_DATA deviceInfoData, string deviceClass)
        {
            try
            {
                // Get hardware ID
                string? hardwareId = GetDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_HARDWAREID);
                if (string.IsNullOrEmpty(hardwareId))
                {
                    return null;
                }

                // Check if this is a Vocom device
                if (!IsVocomDevice(hardwareId))
                {
                    return null;
                }

                // Get additional device properties
                string? friendlyName = GetDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_FRIENDLYNAME);
                string? deviceDesc = GetDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_DEVICEDESC);
                string? service = GetDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_SERVICE);
                string? location = GetDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_LOCATION_INFORMATION);

                var device = new VocomDevice
                {
                    Name = friendlyName ?? deviceDesc ?? "Vocom Adapter",
                    Id = hardwareId,
                    SerialNumber = ExtractSerialNumber(friendlyName, deviceDesc, hardwareId),
                    ConnectionType = VocomConnectionType.USB,
                    ConnectionStatus = VocomConnectionStatus.Connected,
                    USBPortInfo = ExtractPortName(hardwareId, location)
                    // Note: DeviceClass, Service, Location, Properties are not part of VocomDevice model
                    // Additional properties can be stored in a separate dictionary if needed
                };

                _logger.LogInformation($"Found Vocom device: {device.Name} ({device.Id})", "EnhancedVocomDeviceDetection");
                return device;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error creating VocomDevice from device info", "EnhancedVocomDeviceDetection", ex);
                return null;
            }
            finally
            {
                await Task.CompletedTask;
            }
        }

        /// <summary>
        /// Gets a device property as string
        /// </summary>
        private string? GetDeviceProperty(IntPtr deviceInfoSet, SP_DEVINFO_DATA deviceInfoData, uint property)
        {
            try
            {
                byte[] buffer = new byte[1024];
                bool success = SetupDiGetDeviceRegistryProperty(
                    deviceInfoSet,
                    ref deviceInfoData,
                    property,
                    out _,
                    buffer,
                    (uint)buffer.Length,
                    out _);

                if (success)
                {
                    return Encoding.Unicode.GetString(buffer).TrimEnd('\0');
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug($"Error getting device property {property}: {ex.Message}", "EnhancedVocomDeviceDetection");
            }

            return null;
        }

        /// <summary>
        /// Checks if a hardware ID represents a Vocom device
        /// </summary>
        private bool IsVocomDevice(string hardwareId)
        {
            if (string.IsNullOrEmpty(hardwareId))
                return false;

            // Check for Vocom vendor and product IDs
            bool hasVendorId = hardwareId.Contains($"VID_{VOCOM_VENDOR_ID:X4}", StringComparison.OrdinalIgnoreCase);
            bool hasProductId = hardwareId.Contains($"PID_{VOCOM_PRODUCT_ID:X4}", StringComparison.OrdinalIgnoreCase);
            bool hasDeviceName = hardwareId.Contains(VOCOM_DEVICE_NAME, StringComparison.OrdinalIgnoreCase);

            return hasVendorId && hasProductId || hasDeviceName;
        }

        /// <summary>
        /// Extracts serial number from device information
        /// </summary>
        private string ExtractSerialNumber(string? friendlyName, string? deviceDesc, string hardwareId)
        {
            // Try to extract serial number from friendly name first
            if (!string.IsNullOrEmpty(friendlyName))
            {
                // Look for patterns like "88890300", "Vocom - 88890300", etc.
                var match = System.Text.RegularExpressions.Regex.Match(friendlyName, @"88890\d{3}");
                if (match.Success)
                {
                    return match.Value;
                }
            }

            // Try device description
            if (!string.IsNullOrEmpty(deviceDesc))
            {
                var match = System.Text.RegularExpressions.Regex.Match(deviceDesc, @"88890\d{3}");
                if (match.Success)
                {
                    return match.Value;
                }
            }

            // Try to extract from hardware ID
            if (!string.IsNullOrEmpty(hardwareId))
            {
                var parts = hardwareId.Split('&', '\\');
                foreach (var part in parts)
                {
                    if (part.StartsWith("88890", StringComparison.OrdinalIgnoreCase))
                    {
                        return part;
                    }
                }
            }

            // Default fallback
            return "88890300";
        }

        /// <summary>
        /// Extracts port name from hardware ID and location information
        /// </summary>
        private string ExtractPortName(string hardwareId, string? location)
        {
            // Try to extract COM port from location
            if (!string.IsNullOrEmpty(location) && location.Contains("COM"))
            {
                var comIndex = location.IndexOf("COM", StringComparison.OrdinalIgnoreCase);
                if (comIndex >= 0)
                {
                    var endIndex = location.IndexOf(')', comIndex);
                    if (endIndex > comIndex)
                    {
                        return location.Substring(comIndex, endIndex - comIndex);
                    }
                }
            }

            // For USB devices, return the hardware ID as the device path
            // This will be something like "USB\VID_178E&PID_0024\0000007658"
            if (!string.IsNullOrEmpty(hardwareId) && hardwareId.StartsWith("USB\\"))
            {
                _logger.LogInformation($"Using USB hardware ID as port info: {hardwareId}", "EnhancedVocomDeviceDetection");
                return hardwareId;
            }

            // Fallback to hardware ID for other device types
            return hardwareId ?? "Unknown";
        }

        /// <summary>
        /// Detects USB devices via WMI as additional validation
        /// </summary>
        private async Task<List<VocomDevice>> DetectUSBDevicesViaWMIAsync()
        {
            var devices = new List<VocomDevice>();

            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PnPEntity WHERE DeviceID LIKE '%USB%'"))
                {
                    foreach (ManagementObject device in searcher.Get())
                    {
                        string? deviceId = device["DeviceID"]?.ToString();
                        string? name = device["Name"]?.ToString();

                        if (!string.IsNullOrEmpty(deviceId) && IsVocomDevice(deviceId))
                        {
                            var vocomDevice = new VocomDevice
                            {
                                Name = name ?? "Vocom Adapter (WMI)",
                                Id = deviceId,
                                SerialNumber = ExtractSerialNumber(name, null, deviceId),
                                ConnectionType = VocomConnectionType.USB,
                                ConnectionStatus = VocomConnectionStatus.Connected,
                                USBPortInfo = deviceId
                                // Note: Properties removed as not part of VocomDevice model
                            };

                            devices.Add(vocomDevice);
                            _logger.LogInformation($"Found Vocom device via WMI: {name} ({deviceId})", "EnhancedVocomDeviceDetection");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error detecting USB devices via WMI", "EnhancedVocomDeviceDetection", ex);
            }

            await Task.CompletedTask;
            return devices;
        }

        /// <summary>
        /// Detects WiFi Vocom devices (placeholder for future implementation)
        /// </summary>
        private async Task<List<VocomDevice>> DetectWiFiVocomDevicesAsync()
        {
            var devices = new List<VocomDevice>();

            try
            {
                _logger.LogInformation("WiFi Vocom device detection not yet implemented", "EnhancedVocomDeviceDetection");
                // TODO: Implement WiFi device detection
            }
            catch (Exception ex)
            {
                _logger.LogError("Error detecting WiFi Vocom devices", "EnhancedVocomDeviceDetection", ex);
            }

            await Task.CompletedTask;
            return devices;
        }

        /// <summary>
        /// Detects Bluetooth Vocom devices (placeholder for future implementation)
        /// </summary>
        private async Task<List<VocomDevice>> DetectBluetoothVocomDevicesAsync()
        {
            var devices = new List<VocomDevice>();

            try
            {
                _logger.LogInformation("Bluetooth Vocom device detection not yet implemented", "EnhancedVocomDeviceDetection");
                // TODO: Implement Bluetooth device detection
            }
            catch (Exception ex)
            {
                _logger.LogError("Error detecting Bluetooth Vocom devices", "EnhancedVocomDeviceDetection", ex);
            }

            await Task.CompletedTask;
            return devices;
        }

        /// <summary>
        /// Validates detected devices by attempting basic communication
        /// </summary>
        private async Task<List<VocomDevice>> ValidateDetectedDevicesAsync(List<VocomDevice> devices)
        {
            var validatedDevices = new List<VocomDevice>();

            foreach (var device in devices)
            {
                try
                {
                    _logger.LogInformation($"Validating device: {device.Name}", "EnhancedVocomDeviceDetection");

                    // Basic validation - check if device is accessible
                    bool isValid = await ValidateDeviceAsync(device);
                    if (isValid)
                    {
                        validatedDevices.Add(device);
                        _logger.LogInformation($"Device validated: {device.Name}", "EnhancedVocomDeviceDetection");
                    }
                    else
                    {
                        _logger.LogWarning($"Device validation failed: {device.Name}", "EnhancedVocomDeviceDetection");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error validating device {device.Name}", "EnhancedVocomDeviceDetection", ex);
                }
            }

            return validatedDevices;
        }

        /// <summary>
        /// Validates a single device
        /// </summary>
        private async Task<bool> ValidateDeviceAsync(VocomDevice device)
        {
            try
            {
                // For now, just check if the device ID is valid
                // In a real implementation, this would attempt basic communication
                bool isValid = !string.IsNullOrEmpty(device.Id) &&
                              !string.IsNullOrEmpty(device.Name) &&
                              IsVocomDevice(device.Id);

                await Task.Delay(100); // Simulate validation delay
                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during device validation for {device.Name}", "EnhancedVocomDeviceDetection", ex);
                return false;
            }
        }
    }
}
