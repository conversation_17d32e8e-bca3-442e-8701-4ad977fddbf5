Log started at 7/27/2025 11:44:09 AM
2025-07-27 11:44:09.786 [Information] LoggingService: Logging service initialized
2025-07-27 11:44:09.801 [Information] App: Starting integrated application initialization
2025-07-27 11:44:09.802 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-27 11:44:09.806 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-27 11:44:09.809 [Information] IntegratedStartupService: Setting up application environment
2025-07-27 11:44:09.810 [Information] IntegratedStartupService: Application environment setup completed
2025-07-27 11:44:09.812 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-27 11:44:09.814 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-27 11:44:09.817 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-27 11:44:09.821 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-27 11:44:09.828 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 11:44:09.831 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.834 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.835 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-27 11:44:09.839 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 11:44:09.842 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.845 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.846 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:44:09.850 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 11:44:09.853 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.856 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.857 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 11:44:09.861 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 11:44:09.863 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.866 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.867 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 11:44:09.871 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 11:44:09.873 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.877 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.877 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 11:44:09.881 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 11:44:09.884 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.887 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.888 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 11:44:09.892 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 11:44:09.895 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.898 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 11:44:09.898 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 11:44:09.901 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-27 11:44:09.904 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-27 11:44:09.904 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-27 11:44:09.907 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-27 11:44:09.908 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-27 11:44:09.909 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-27 11:44:09.909 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-27 11:44:09.909 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-27 11:44:09.912 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-27 11:44:09.912 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-27 11:44:09.914 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-27 11:44:09.914 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:44:09.915 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 11:44:09.915 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 11:44:09.916 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 11:44:09.916 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 11:44:09.916 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 11:44:09.925 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-27 11:44:09.926 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-27 11:44:09.926 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-27 11:44:09.934 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-27 11:44:09.935 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-27 11:44:09.937 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-27 11:44:09.941 [Information] LibraryExtractor: Starting library extraction process
2025-07-27 11:44:09.944 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-27 11:44:09.948 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-27 11:44:09.950 [Information] LibraryExtractor: Copying system libraries
2025-07-27 11:44:09.960 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-27 11:44:09.968 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-27 11:44:39.986 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 11:44:40.988 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-27 11:45:10.990 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 11:45:10.991 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:45:40.992 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 11:45:41.993 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-27 11:46:11.998 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 11:46:11.999 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 11:46:42.002 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 11:46:43.004 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-27 11:47:13.007 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 11:47:13.008 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-27 11:47:13.009 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-27 11:47:13.012 [Information] LibraryExtractor: Verifying library extraction
2025-07-27 11:47:13.013 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-27 11:47:13.013 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-27 11:47:13.014 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-27 11:47:13.014 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-27 11:47:13.015 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-27 11:47:13.018 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-27 11:47:13.020 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-27 11:47:13.022 [Information] DependencyManager: Initializing dependency manager
2025-07-27 11:47:13.023 [Information] DependencyManager: Setting up library search paths
2025-07-27 11:47:13.025 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 11:47:13.025 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 11:47:13.026 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 11:47:13.026 [Information] DependencyManager: Updated PATH environment variable
2025-07-27 11:47:13.028 [Information] DependencyManager: Verifying required directories
2025-07-27 11:47:13.028 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 11:47:13.029 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 11:47:13.029 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-27 11:47:13.029 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 11:47:13.031 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-27 11:47:13.037 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 11:47:13.039 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 11:47:13.039 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 11:47:13.041 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 11:47:13.043 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 11:47:13.044 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 11:47:13.044 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 11:47:13.047 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll from C:\Windows\system32\msvcp140.dll: Error 193
2025-07-27 11:47:13.047 [Warning] DependencyManager: Architecture mismatch detected for msvcp140.dll. Expected: x64
2025-07-27 11:47:13.048 [Debug] DependencyManager: Architecture mismatch: Library msvcp140.dll is x86, process is x64
2025-07-27 11:47:13.048 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp140.dll
2025-07-27 11:47:13.049 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll: Error 193
2025-07-27 11:47:13.049 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp140.dll
2025-07-27 11:47:13.050 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 11:47:13.051 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 11:47:13.052 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:47:13.052 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:47:13.053 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 11:47:13.054 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 11:47:13.055 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 11:47:13.055 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 11:47:13.056 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 11:47:13.056 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 11:47:13.058 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 11:47:13.058 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 11:47:13.059 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 11:47:13.060 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 11:47:13.060 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 11:47:13.061 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 11:47:13.062 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 11:47:13.062 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 11:47:13.063 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 11:47:13.063 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 11:47:13.065 [Information] DependencyManager: VC++ Redistributable library loading: 3/14 (21.4%) libraries loaded
2025-07-27 11:47:13.065 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-27 11:47:13.067 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-27 11:47:13.068 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-27 11:47:13.068 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 11:47:13.069 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-27 11:47:13.069 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 11:47:13.070 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 11:47:13.071 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-27 11:47:13.071 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-27 11:47:13.072 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-27 11:47:13.072 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-27 11:47:13.073 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-07-27 11:47:13.074 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-27 11:47:13.075 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-27 11:47:13.075 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-27 11:47:13.076 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-27 11:47:13.076 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-07-27 11:47:13.077 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-27 11:47:13.078 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-27 11:47:13.078 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-27 11:47:13.079 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-27 11:47:13.079 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-07-27 11:47:13.080 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-27 11:47:13.081 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-27 11:47:13.081 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 11:47:13.082 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 11:47:13.083 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 11:47:13.084 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 11:47:13.084 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 11:47:13.085 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 11:47:13.086 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-27 11:47:13.087 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 11:47:13.088 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-27 11:47:13.089 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 11:47:13.090 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 11:47:13.091 [Information] DependencyManager: Setting up environment variables
2025-07-27 11:47:13.091 [Information] DependencyManager: Environment variables configured
2025-07-27 11:47:13.093 [Information] DependencyManager: Verifying library loading status
2025-07-27 11:47:14.301 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-27 11:47:14.302 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-27 11:47:14.303 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-27 11:47:14.306 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-27 11:47:14.308 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-27 11:47:14.311 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-27 11:47:14.313 [Information] IntegratedStartupService: Verifying system readiness
2025-07-27 11:47:14.314 [Information] IntegratedStartupService: System readiness verification passed
2025-07-27 11:47:14.314 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-27 11:47:14.316 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-27 11:47:14.317 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 11:47:14.317 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 11:47:14.317 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 11:47:14.317 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-27 11:47:14.318 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-27 11:47:14.318 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-27 11:47:14.318 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 11:47:14.319 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-27 11:47:14.319 [Information] App: Integrated startup completed successfully
2025-07-27 11:47:14.322 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-27 11:47:14.341 [Information] App: Initializing application services
2025-07-27 11:47:14.344 [Information] AppConfigurationService: Initializing configuration service
2025-07-27 11:47:14.345 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 11:47:14.396 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 11:47:14.397 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-27 11:47:14.398 [Information] App: Configuration service initialized successfully
2025-07-27 11:47:14.400 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-27 11:47:14.401 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-27 11:47:14.406 [Information] App: Environment variable exists: True, not 'false': False
2025-07-27 11:47:14.406 [Information] App: Final useDummyImplementations value: False
2025-07-27 11:47:14.407 [Information] App: Updating config to NOT use dummy implementations
2025-07-27 11:47:14.408 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-27 11:47:14.422 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 11:47:14.422 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-27 11:47:14.422 [Information] App: usePatchedImplementation flag is: True
2025-07-27 11:47:14.423 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-27 11:47:14.424 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-27 11:47:14.425 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-27 11:47:14.425 [Information] App: verboseLogging flag is: True
2025-07-27 11:47:14.427 [Information] App: Verifying real hardware requirements...
2025-07-27 11:47:14.428 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-27 11:47:14.428 [Information] App: ✓ Found critical library: apci.dll
2025-07-27 11:47:14.428 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-27 11:47:14.429 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-27 11:47:14.429 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 11:47:14.430 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-27 11:47:14.430 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-27 11:47:14.431 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-27 11:47:14.443 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-27 11:47:14.446 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-27 11:47:14.446 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-27 11:47:14.448 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-27 11:47:14.454 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-07-27 11:48:08.783 [Warning] RuntimeDependencyResolver: Failed to download msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-27 11:48:08.785 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-27 11:48:08.785 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-27 11:48:08.786 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-27 11:48:08.787 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:48:08.787 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 11:48:08.787 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 11:48:08.788 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 11:48:08.788 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 11:48:08.788 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 11:48:08.789 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-27 11:48:08.790 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-27 11:48:08.790 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-27 11:48:08.791 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 11:48:08.791 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-27 11:48:08.791 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-27 11:48:08.791 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-27 11:48:08.792 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-27 11:48:08.792 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-27 11:48:08.794 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-27 11:48:08.794 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-27 11:48:08.796 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-27 11:48:08.797 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-27 11:48:08.797 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-27 11:48:08.797 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-27 11:48:08.799 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-27 11:48:08.800 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-07-27 11:48:08.802 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-07-27 11:48:08.803 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-27 11:48:08.805 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-07-27 11:48:08.805 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-07-27 11:48:08.838 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-27 11:48:08.839 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-07-27 11:48:08.843 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-27 11:48:08.844 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-27 11:48:08.845 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-27 11:48:08.845 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-27 11:48:08.848 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-27 11:48:08.848 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-27 11:48:08.849 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-27 11:48:08.849 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:48:08.850 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-27 11:48:08.850 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-27 11:48:08.851 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-27 11:48:08.854 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-27 11:48:08.857 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-27 11:48:08.861 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-27 11:48:08.862 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 11:48:08.862 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-27 11:48:08.877 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 11:48:08.896 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-27 11:48:08.897 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-27 11:48:08.898 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-27 11:48:08.899 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 11:48:08.899 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-07-27 11:48:08.900 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 11:48:08.900 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-07-27 11:48:08.901 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 11:48:08.901 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-27 11:48:08.902 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-27 11:48:08.902 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-27 11:48:08.904 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-27 11:48:08.906 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-27 11:48:08.906 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-27 11:48:08.907 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-27 11:48:08.908 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-27 11:48:08.910 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-27 11:48:08.910 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 11:48:08.911 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-27 11:48:08.911 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-27 11:48:08.913 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-27 11:48:08.915 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-27 11:48:08.915 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-27 11:48:08.915 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-27 11:48:08.916 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-27 11:48:08.916 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-27 11:48:08.916 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-27 11:48:08.917 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-27 11:48:08.918 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-27 11:48:08.918 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-27 11:48:08.920 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-27 11:48:08.920 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-27 11:48:08.920 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-27 11:48:08.921 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:48:08.921 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-27 11:48:08.924 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-27 11:48:08.925 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 11:48:08.925 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-27 11:48:08.925 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-27 11:48:08.927 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-27 11:48:08.927 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-27 11:48:08.927 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-27 11:48:08.930 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-27 11:48:08.932 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-27 11:48:08.933 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-27 11:48:08.937 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-27 11:48:08.938 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-27 11:48:08.938 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 11:48:08.938 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 11:48:08.940 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-27 11:48:08.942 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:48:08.945 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:48:08.945 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-27 11:48:08.946 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:48:08.946 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:48:08.946 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-27 11:48:08.947 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:48:08.947 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-27 11:48:08.948 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:48:08.948 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-27 11:48:08.949 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:48:08.949 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:48:08.949 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-27 11:48:08.950 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:48:08.950 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:48:08.951 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-27 11:48:08.951 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 11:48:08.952 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 11:48:08.953 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:48:08.953 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:48:08.954 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-27 11:48:08.954 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 11:48:08.955 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:48:08.957 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-27 11:48:08.957 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:48:08.958 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:48:08.959 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-27 11:48:08.959 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:48:08.960 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:48:08.960 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-27 11:48:08.961 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 11:48:08.961 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 11:48:08.962 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 11:48:08.966 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-27 11:48:08.966 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 11:48:08.966 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-27 11:48:08.967 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-27 11:48:08.968 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-27 11:48:08.969 [Information] VocomDriver: Initializing Vocom driver
2025-07-27 11:48:08.971 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-27 11:48:08.974 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-27 11:48:08.975 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 11:48:08.975 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 11:48:08.977 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 11:48:08.977 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 11:48:08.979 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-27 11:48:08.980 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-27 11:48:08.982 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-27 11:48:08.983 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-27 11:48:08.984 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-27 11:48:08.985 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:48:08.987 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-07-27 11:48:08.988 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-07-27 11:48:08.989 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-07-27 11:48:08.990 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-27 11:48:08.991 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-27 11:48:08.991 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-27 11:48:08.993 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-27 11:48:08.994 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-27 11:48:08.994 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-27 11:48:08.995 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-27 11:48:08.995 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-27 11:48:08.995 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-27 11:48:08.995 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-27 11:48:08.996 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-27 11:48:08.996 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-27 11:48:08.996 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-27 11:48:08.997 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-27 11:48:08.997 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-27 11:48:08.997 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-27 11:48:08.998 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-27 11:48:08.998 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-27 11:48:08.998 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-27 11:48:08.998 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-27 11:48:08.999 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-27 11:48:08.999 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-27 11:48:08.999 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-27 11:48:08.999 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-27 11:48:09.000 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-27 11:48:09.000 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-27 11:48:09.000 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-27 11:48:09.000 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-27 11:48:09.001 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-27 11:48:09.001 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-27 11:48:09.001 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-27 11:48:09.001 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-27 11:48:09.001 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-27 11:48:09.002 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-27 11:48:09.002 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-27 11:48:09.002 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-27 11:48:09.003 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-27 11:48:09.003 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-27 11:48:09.004 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-27 11:48:09.004 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-27 11:48:09.004 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-27 11:48:09.005 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-27 11:48:09.005 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-27 11:48:09.005 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-27 11:48:09.005 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-27 11:48:09.006 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-27 11:48:09.007 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-27 11:48:09.008 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-27 11:48:09.009 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-27 11:48:09.009 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-27 11:48:09.009 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-27 11:48:09.010 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-27 11:48:09.010 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-27 11:48:09.010 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-27 11:48:09.015 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-27 11:48:09.016 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-27 11:48:09.018 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-27 11:48:09.020 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-27 11:48:09.099 [Information] WiFiCommunicationService: WiFi is available
2025-07-27 11:48:09.101 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-27 11:48:09.103 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-27 11:48:09.105 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-27 11:48:09.107 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-27 11:48:09.107 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-27 11:48:09.109 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 11:48:09.110 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 11:48:09.111 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 11:48:09.114 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 11:48:09.119 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 11:48:09.120 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 11:48:09.120 [Information] VocomService: Native USB communication service initialized
2025-07-27 11:48:09.121 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 11:48:09.121 [Information] VocomService: Connection recovery service initialized
2025-07-27 11:48:09.122 [Information] VocomService: Enhanced services initialization completed
2025-07-27 11:48:09.125 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:48:09.142 [Information] VocomService: PTT application is not running
2025-07-27 11:48:09.147 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 11:48:09.147 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-27 11:48:09.148 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-07-27 11:48:09.151 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 11:48:09.151 [Information] VocomService: Using new enhanced device detection service
2025-07-27 11:48:09.153 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 11:48:09.155 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 11:48:09.546 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 11:48:09.547 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 11:48:09.549 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 11:48:09.551 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 11:48:09.551 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 11:48:09.553 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 11:48:09.556 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 11:48:09.559 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 11:48:09.827 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 11:48:09.830 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 11:48:09.833 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 11:48:09.835 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 11:48:09.837 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 11:48:09.847 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 11:48:09.849 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 11:48:09.849 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 11:48:09.850 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 11:48:09.855 [Information] VocomService: Found 3 Vocom devices
2025-07-27 11:48:09.856 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-07-27 11:48:09.858 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: 2c135e16-4abc-4466-a13a-2d4bbfd62ce4, Type: USB)
2025-07-27 11:48:09.858 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: 35086166-8285-422f-814c-6d0a20f9c65e, Type: Bluetooth)
2025-07-27 11:48:09.858 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: 88de7044-ab45-4171-a7ce-d7b611c6d192, Type: WiFi)
2025-07-27 11:48:09.859 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-07-27 11:48:09.860 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-07-27 11:48:09.860 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-07-27 11:48:09.860 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-07-27 11:48:09.860 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-07-27 11:48:09.861 [Information] App: Architecture-aware Vocom service created successfully
2025-07-27 11:48:09.861 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 11:48:09.861 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 11:48:09.861 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 11:48:09.862 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 11:48:09.863 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 11:48:09.863 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 11:48:09.864 [Information] VocomService: Native USB communication service initialized
2025-07-27 11:48:09.865 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 11:48:09.865 [Information] VocomService: Connection recovery service initialized
2025-07-27 11:48:09.865 [Information] VocomService: Enhanced services initialization completed
2025-07-27 11:48:09.865 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:48:09.879 [Information] VocomService: PTT application is not running
2025-07-27 11:48:09.880 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 11:48:09.881 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-27 11:48:09.881 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-27 11:48:09.881 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-27 11:48:09.920 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 11:48:09.921 [Information] VocomService: Using new enhanced device detection service
2025-07-27 11:48:09.921 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 11:48:09.921 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 11:48:10.191 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 11:48:10.192 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 11:48:10.193 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 11:48:10.194 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 11:48:10.194 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 11:48:10.195 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 11:48:10.195 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 11:48:10.196 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 11:48:10.460 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 11:48:10.460 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 11:48:10.461 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 11:48:10.461 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 11:48:10.462 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 11:48:10.469 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 11:48:10.471 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 11:48:10.471 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 11:48:10.472 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 11:48:10.474 [Information] VocomService: Found 3 Vocom devices
2025-07-27 11:48:10.474 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-27 11:48:10.477 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 11:48:10.477 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:48:10.492 [Information] VocomService: PTT application is not running
2025-07-27 11:48:10.497 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 11:48:10.497 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-27 11:48:10.498 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:48:10.512 [Information] VocomService: PTT application is not running
2025-07-27 11:48:10.513 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-27 11:48:10.516 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-27 11:48:10.518 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 11:48:10.520 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 11:48:10.524 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 11:48:10.525 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:48:10.525 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 11:48:10.526 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:48:10.526 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 11:48:10.526 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 11:48:10.527 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 11:48:10.527 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 11:48:11.528 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-27 11:48:11.528 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 11:48:11.528 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 11:48:11.529 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 11:48:11.530 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:48:11.530 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 11:48:11.531 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:48:11.532 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 11:48:11.532 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 11:48:11.533 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 11:48:11.533 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 11:48:12.533 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-27 11:48:12.534 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 11:48:12.534 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 11:48:12.535 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 11:48:12.536 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:48:12.536 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 11:48:12.536 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:48:12.537 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 11:48:12.537 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 11:48:12.537 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 11:48:12.538 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 11:48:12.539 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 11:48:12.540 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-27 11:48:12.574 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 11:48:12.575 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-27 11:48:12.576 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 11:48:12.577 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 11:48:12.578 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 11:48:12.578 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 11:48:12.578 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 11:48:12.581 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 11:48:12.582 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 11:48:12.587 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 11:48:12.588 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 11:48:12.588 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 11:48:12.588 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 11:48:12.590 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 11:48:12.590 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-27 11:48:12.594 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-27 11:48:12.598 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:48:12.598 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-27 11:48:12.603 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 11:48:12.605 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 11:48:12.606 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 11:48:12.607 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 11:48:12.607 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-07-27 11:48:12.607 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 11:48:12.608 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-27 11:48:12.608 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-27 11:48:12.611 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-27 11:48:12.611 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 11:48:12.611 [Information] VocomService: Using new enhanced device detection service
2025-07-27 11:48:12.612 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 11:48:12.612 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 11:48:12.886 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 11:48:12.886 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 11:48:12.887 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 11:48:12.887 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 11:48:12.887 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 11:48:12.888 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 11:48:12.888 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 11:48:12.888 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 11:48:13.159 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 11:48:13.160 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 11:48:13.160 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 11:48:13.161 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 11:48:13.162 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 11:48:13.168 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 11:48:13.169 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 11:48:13.169 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 11:48:13.169 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 11:48:13.170 [Information] VocomService: Found 3 Vocom devices
2025-07-27 11:48:13.171 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 11:48:13.174 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 11:48:13.174 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:48:13.190 [Information] VocomService: PTT application is not running
2025-07-27 11:48:13.190 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 11:48:13.191 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-27 11:48:13.191 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:48:13.208 [Information] VocomService: PTT application is not running
2025-07-27 11:48:13.209 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-27 11:48:13.209 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-27 11:48:13.209 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 11:48:13.210 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 11:48:13.211 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 11:48:13.211 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:48:13.211 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 11:48:13.212 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:48:13.212 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 11:48:13.213 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 11:48:13.213 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 11:48:13.214 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 11:48:14.214 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-27 11:48:14.215 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 11:48:14.215 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 11:48:14.216 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 11:48:14.217 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:48:14.217 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 11:48:14.217 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:48:14.218 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 11:48:14.218 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 11:48:14.218 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 11:48:14.219 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 11:48:15.219 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-27 11:48:15.220 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 11:48:15.220 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 11:48:15.221 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 11:48:15.221 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:48:15.222 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 11:48:15.222 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:48:15.224 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 11:48:15.224 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 11:48:15.225 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 11:48:15.225 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 11:48:15.226 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 11:48:15.226 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-27 11:48:15.226 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 11:48:15.227 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-27 11:48:15.227 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 11:48:15.227 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 11:48:15.227 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 11:48:15.228 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 11:48:15.228 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 11:48:15.228 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 11:48:15.229 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 11:48:15.229 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 11:48:15.229 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 11:48:15.230 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 11:48:15.230 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 11:48:15.230 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 11:48:15.231 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 11:48:15.231 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 11:48:15.232 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 11:48:15.232 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 11:48:15.232 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 11:48:15.233 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:48:15.247 [Information] VocomService: PTT application is not running
2025-07-27 11:48:15.249 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 11:48:15.251 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-27 11:48:16.056 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 11:48:16.057 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 11:48:16.057 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-27 11:48:16.058 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 11:48:16.061 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 11:48:16.063 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 11:48:16.066 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 11:48:16.068 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 11:48:16.071 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 11:48:16.081 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 11:48:16.085 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 11:48:16.097 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 11:48:16.098 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 11:48:16.099 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 11:48:16.099 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 11:48:16.099 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 11:48:16.100 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 11:48:16.100 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 11:48:16.100 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 11:48:16.100 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 11:48:16.101 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 11:48:16.101 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 11:48:16.101 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 11:48:16.102 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 11:48:16.102 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 11:48:16.102 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 11:48:16.104 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 11:48:16.104 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 11:48:16.108 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 11:48:16.115 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 11:48:16.116 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 11:48:16.119 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 11:48:16.121 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:48:16.126 [Information] CANRegisterAccess: Read value 0x49 from register 0x0141 (simulated)
2025-07-27 11:48:16.127 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 11:48:16.128 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 11:48:16.128 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 11:48:16.134 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 11:48:16.135 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 11:48:16.140 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 11:48:16.141 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 11:48:16.141 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 11:48:16.147 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 11:48:16.148 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 11:48:16.148 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 11:48:16.154 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 11:48:16.155 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 11:48:16.161 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 11:48:16.161 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 11:48:16.167 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 11:48:16.167 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 11:48:16.173 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 11:48:16.174 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 11:48:16.179 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 11:48:16.180 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 11:48:16.185 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 11:48:16.186 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 11:48:16.191 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 11:48:16.192 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 11:48:16.197 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 11:48:16.198 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 11:48:16.203 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 11:48:16.204 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 11:48:16.210 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 11:48:16.211 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 11:48:16.216 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 11:48:16.217 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 11:48:16.222 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 11:48:16.223 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 11:48:16.229 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 11:48:16.230 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 11:48:16.235 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 11:48:16.236 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 11:48:16.241 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 11:48:16.242 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 11:48:16.247 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 11:48:16.248 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 11:48:16.253 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 11:48:16.254 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 11:48:16.254 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 11:48:16.259 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 11:48:16.260 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 11:48:16.260 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 11:48:16.260 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:48:16.266 [Information] CANRegisterAccess: Read value 0x41 from register 0x0141 (simulated)
2025-07-27 11:48:16.272 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:48:16.277 [Information] CANRegisterAccess: Read value 0xA2 from register 0x0141 (simulated)
2025-07-27 11:48:16.277 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 11:48:16.278 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 11:48:16.278 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 11:48:16.278 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:48:16.284 [Information] CANRegisterAccess: Read value 0x28 from register 0x0140 (simulated)
2025-07-27 11:48:16.290 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:48:16.296 [Information] CANRegisterAccess: Read value 0x2A from register 0x0140 (simulated)
2025-07-27 11:48:16.302 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:48:16.309 [Information] CANRegisterAccess: Read value 0xFC from register 0x0140 (simulated)
2025-07-27 11:48:16.309 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 11:48:16.310 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 11:48:16.314 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 11:48:16.314 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 11:48:16.325 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 11:48:16.326 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 11:48:16.327 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 11:48:16.332 [Information] VocomService: Sending data and waiting for response
2025-07-27 11:48:16.333 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 11:48:16.384 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 11:48:16.385 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 11:48:16.386 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 11:48:16.387 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 11:48:16.388 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 11:48:16.399 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 11:48:16.400 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 11:48:16.401 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 11:48:16.411 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 11:48:16.422 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 11:48:16.434 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 11:48:16.445 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 11:48:16.456 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 11:48:16.458 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 11:48:16.459 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 11:48:16.470 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 11:48:16.471 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 11:48:16.471 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 11:48:16.482 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 11:48:16.494 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 11:48:16.505 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 11:48:16.516 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 11:48:16.526 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 11:48:16.537 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 11:48:16.539 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 11:48:16.540 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 11:48:16.551 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 11:48:16.553 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 11:48:16.553 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 11:48:16.554 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 11:48:16.554 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 11:48:16.554 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 11:48:16.555 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 11:48:16.555 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 11:48:16.555 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 11:48:16.555 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 11:48:16.556 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 11:48:16.556 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 11:48:16.556 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 11:48:16.557 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 11:48:16.557 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 11:48:16.557 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 11:48:16.558 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 11:48:16.657 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 11:48:16.658 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 11:48:16.661 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 11:48:16.663 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:48:16.663 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 11:48:16.664 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 11:48:16.664 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:48:16.665 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 11:48:16.665 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 11:48:16.666 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:48:16.666 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 11:48:16.666 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 11:48:16.667 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:48:16.667 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 11:48:16.668 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 11:48:16.669 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-27 11:48:16.672 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-27 11:48:16.674 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-27 11:48:16.677 [Information] BackupService: Initializing backup service
2025-07-27 11:48:16.677 [Information] BackupService: Backup service initialized successfully
2025-07-27 11:48:16.678 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-27 11:48:16.678 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-27 11:48:16.680 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-27 11:48:16.728 [Information] BackupService: Compressing backup data
2025-07-27 11:48:16.738 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (452 bytes)
2025-07-27 11:48:16.740 [Information] BackupServiceFactory: Created template for category: Production
2025-07-27 11:48:16.740 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-27 11:48:16.741 [Information] BackupService: Compressing backup data
2025-07-27 11:48:16.744 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (454 bytes)
2025-07-27 11:48:16.745 [Information] BackupServiceFactory: Created template for category: Development
2025-07-27 11:48:16.745 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-27 11:48:16.746 [Information] BackupService: Compressing backup data
2025-07-27 11:48:16.747 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (443 bytes)
2025-07-27 11:48:16.748 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-27 11:48:16.748 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-27 11:48:16.749 [Information] BackupService: Compressing backup data
2025-07-27 11:48:16.750 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (448 bytes)
2025-07-27 11:48:16.750 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-27 11:48:16.750 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-27 11:48:16.751 [Information] BackupService: Compressing backup data
2025-07-27 11:48:16.752 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (450 bytes)
2025-07-27 11:48:16.752 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-27 11:48:16.755 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-27 11:48:16.755 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-27 11:48:16.756 [Information] BackupService: Compressing backup data
2025-07-27 11:48:16.757 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-07-27 11:48:16.757 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-27 11:48:16.758 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-27 11:48:16.759 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-27 11:48:16.763 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 11:48:16.766 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 11:48:16.845 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 11:48:16.846 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 11:48:16.848 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-27 11:48:16.848 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-27 11:48:16.849 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-27 11:48:16.850 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-27 11:48:16.851 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-27 11:48:16.855 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-27 11:48:16.856 [Information] App: Flash operation monitor service initialized successfully
2025-07-27 11:48:16.869 [Information] LicensingService: Initializing licensing service
2025-07-27 11:48:16.920 [Information] LicensingService: License information loaded successfully
2025-07-27 11:48:16.924 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-27 11:48:16.924 [Information] App: Licensing service initialized successfully
2025-07-27 11:48:16.925 [Information] App: License status: Trial
2025-07-27 11:48:16.925 [Information] App: Trial period: 30 days remaining
2025-07-27 11:48:16.926 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-27 11:48:17.116 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 11:48:17.117 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 11:48:17.117 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 11:48:17.117 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 11:48:17.118 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 11:48:17.118 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 11:48:17.119 [Information] VocomService: Native USB communication service initialized
2025-07-27 11:48:17.119 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 11:48:17.119 [Information] VocomService: Connection recovery service initialized
2025-07-27 11:48:17.119 [Information] VocomService: Enhanced services initialization completed
2025-07-27 11:48:17.120 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:48:17.134 [Information] VocomService: PTT application is not running
2025-07-27 11:48:17.135 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 11:48:17.185 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 11:48:17.186 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 11:48:17.186 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 11:48:17.186 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 11:48:17.186 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-27 11:48:17.187 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: 8ef03380-8a2c-47e3-a851-c51b8d647b91
2025-07-27 11:48:17.189 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-27 11:48:17.189 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 11:48:17.189 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 11:48:17.190 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 11:48:17.191 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 11:48:17.192 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 11:48:17.194 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 11:48:17.194 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 11:48:17.195 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 11:48:17.206 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 11:48:17.207 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 11:48:17.207 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 11:48:17.207 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 11:48:17.208 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 11:48:17.208 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 11:48:17.208 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 11:48:17.208 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 11:48:17.209 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 11:48:17.209 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 11:48:17.209 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 11:48:17.209 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 11:48:17.210 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 11:48:17.210 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 11:48:17.210 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 11:48:17.210 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 11:48:17.211 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 11:48:17.211 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 11:48:17.217 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 11:48:17.218 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 11:48:17.218 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 11:48:17.218 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:48:17.224 [Information] CANRegisterAccess: Read value 0xFA from register 0x0141 (simulated)
2025-07-27 11:48:17.230 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:48:17.236 [Information] CANRegisterAccess: Read value 0x77 from register 0x0141 (simulated)
2025-07-27 11:48:17.237 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 11:48:17.237 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 11:48:17.237 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 11:48:17.243 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 11:48:17.244 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 11:48:17.250 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 11:48:17.251 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 11:48:17.251 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 11:48:17.257 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 11:48:17.258 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 11:48:17.258 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 11:48:17.264 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 11:48:17.265 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 11:48:17.271 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 11:48:17.271 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 11:48:17.276 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 11:48:17.277 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 11:48:17.282 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 11:48:17.283 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 11:48:17.289 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 11:48:17.290 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 11:48:17.296 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 11:48:17.296 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 11:48:17.302 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 11:48:17.303 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 11:48:17.309 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 11:48:17.310 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 11:48:17.316 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 11:48:17.317 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 11:48:17.322 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 11:48:17.323 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 11:48:17.329 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 11:48:17.330 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 11:48:17.335 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 11:48:17.336 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 11:48:17.341 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 11:48:17.342 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 11:48:17.348 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 11:48:17.349 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 11:48:17.355 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 11:48:17.355 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 11:48:17.361 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 11:48:17.362 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 11:48:17.368 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 11:48:17.368 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 11:48:17.369 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 11:48:17.375 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 11:48:17.375 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 11:48:17.376 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 11:48:17.376 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:48:17.386 [Information] CANRegisterAccess: Read value 0xAD from register 0x0141 (simulated)
2025-07-27 11:48:17.392 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:48:17.398 [Information] CANRegisterAccess: Read value 0x52 from register 0x0141 (simulated)
2025-07-27 11:48:17.399 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 11:48:17.399 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 11:48:17.399 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 11:48:17.400 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:48:17.406 [Information] CANRegisterAccess: Read value 0x01 from register 0x0140 (simulated)
2025-07-27 11:48:17.412 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:48:17.418 [Information] CANRegisterAccess: Read value 0x07 from register 0x0140 (simulated)
2025-07-27 11:48:17.424 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:48:17.430 [Information] CANRegisterAccess: Read value 0x4F from register 0x0140 (simulated)
2025-07-27 11:48:17.436 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:48:17.442 [Information] CANRegisterAccess: Read value 0x45 from register 0x0140 (simulated)
2025-07-27 11:48:17.449 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:48:17.455 [Information] CANRegisterAccess: Read value 0x72 from register 0x0140 (simulated)
2025-07-27 11:48:17.455 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 11:48:17.456 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 11:48:17.456 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 11:48:17.456 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 11:48:17.467 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 11:48:17.467 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 11:48:17.468 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 11:48:17.468 [Information] VocomService: Sending data and waiting for response
2025-07-27 11:48:17.468 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 11:48:17.518 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 11:48:17.518 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 11:48:17.519 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 11:48:17.519 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 11:48:17.520 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 11:48:17.531 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 11:48:17.531 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 11:48:17.532 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 11:48:17.543 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 11:48:17.555 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 11:48:17.566 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 11:48:17.577 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 11:48:17.588 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 11:48:17.588 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 11:48:17.589 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 11:48:17.600 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 11:48:17.600 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 11:48:17.601 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 11:48:17.612 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 11:48:17.623 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 11:48:17.635 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 11:48:17.646 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 11:48:17.657 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 11:48:17.668 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 11:48:17.668 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 11:48:17.669 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 11:48:17.680 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 11:48:17.680 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 11:48:17.681 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 11:48:17.681 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 11:48:17.681 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 11:48:17.681 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 11:48:17.682 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 11:48:17.682 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 11:48:17.682 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 11:48:17.683 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 11:48:17.683 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 11:48:17.683 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 11:48:17.684 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 11:48:17.684 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 11:48:17.684 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 11:48:17.684 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 11:48:17.685 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 11:48:17.786 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 11:48:17.786 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 11:48:17.787 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 11:48:17.787 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:48:17.787 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 11:48:17.788 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 11:48:17.788 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:48:17.788 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 11:48:17.788 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 11:48:17.789 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:48:17.789 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 11:48:17.789 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 11:48:17.790 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:48:17.790 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 11:48:17.790 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 11:48:17.842 [Information] BackupService: Initializing backup service
2025-07-27 11:48:17.842 [Information] BackupService: Backup service initialized successfully
2025-07-27 11:48:17.893 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 11:48:17.893 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 11:48:17.895 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 11:48:17.895 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 11:48:17.947 [Information] BackupService: Getting predefined backup categories
2025-07-27 11:48:17.999 [Information] MainViewModel: Services initialized successfully
2025-07-27 11:48:18.001 [Information] MainViewModel: Scanning for Vocom devices
2025-07-27 11:48:18.003 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 11:48:18.004 [Information] VocomService: Using new enhanced device detection service
2025-07-27 11:48:18.004 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 11:48:18.004 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 11:48:18.280 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 11:48:18.280 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 11:48:18.280 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 11:48:18.281 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 11:48:18.281 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 11:48:18.281 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 11:48:18.282 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 11:48:18.283 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 11:48:18.635 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 11:48:18.635 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 11:48:18.636 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 11:48:18.636 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 11:48:18.637 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 11:48:18.646 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 11:48:18.647 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 11:48:18.647 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 11:48:18.647 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 11:48:18.650 [Information] VocomService: Found 3 Vocom devices
2025-07-27 11:48:18.653 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-27 11:52:14.456 [Warning] RuntimeDependencyResolver: Failed to download msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-27 11:52:14.458 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-27 11:52:14.459 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-27 11:52:14.459 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-27 11:52:14.459 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:52:14.460 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 11:52:14.462 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 11:52:14.462 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 11:52:14.463 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 11:52:14.463 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 11:52:14.464 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-27 11:52:14.465 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-27 11:52:14.466 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-27 11:52:14.466 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 11:52:14.466 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-27 11:52:14.467 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-27 11:52:14.467 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-27 11:52:14.467 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-27 11:52:14.467 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-27 11:52:14.469 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-27 11:52:14.470 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-27 11:52:14.472 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-27 11:52:14.473 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-27 11:52:14.473 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-27 11:52:14.474 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-27 11:52:14.475 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-27 11:52:14.476 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-07-27 11:52:14.478 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-07-27 11:52:14.479 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-27 11:52:14.481 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-07-27 11:52:14.481 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-07-27 11:52:14.520 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-27 11:52:14.523 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-07-27 11:52:14.527 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-27 11:52:14.528 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-27 11:52:14.529 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-27 11:52:14.530 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-27 11:52:14.533 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-27 11:52:14.534 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-27 11:52:14.534 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-27 11:52:14.534 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:52:14.535 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-27 11:52:14.535 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-27 11:52:14.536 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-27 11:52:14.538 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-27 11:52:14.542 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-27 11:52:14.547 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-27 11:52:14.548 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 11:52:14.549 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-27 11:52:14.566 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 11:52:14.590 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-27 11:52:14.592 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-27 11:52:14.593 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-27 11:52:14.594 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 11:52:14.594 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-07-27 11:52:14.595 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 11:52:14.595 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-07-27 11:52:14.596 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 11:52:14.596 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-27 11:52:14.597 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-27 11:52:14.597 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-27 11:52:14.598 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-27 11:52:14.599 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-27 11:52:14.600 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-27 11:52:14.600 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-27 11:52:14.604 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-27 11:52:14.606 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-27 11:52:14.606 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 11:52:14.607 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-27 11:52:14.607 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-27 11:52:14.608 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-27 11:52:14.609 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-27 11:52:14.610 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-27 11:52:14.610 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-27 11:52:14.610 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-27 11:52:14.611 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-27 11:52:14.611 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-27 11:52:14.613 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-27 11:52:14.613 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-27 11:52:14.613 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-27 11:52:14.615 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-27 11:52:14.616 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-27 11:52:14.616 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-27 11:52:14.616 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:52:14.617 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-27 11:52:14.620 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-27 11:52:14.620 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 11:52:14.621 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-27 11:52:14.622 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-27 11:52:14.623 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-27 11:52:14.623 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-27 11:52:14.624 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-27 11:52:14.626 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-27 11:52:14.628 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-27 11:52:14.628 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-27 11:52:14.633 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-27 11:52:14.633 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-27 11:52:14.634 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 11:52:14.634 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 11:52:14.637 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-27 11:52:14.638 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:52:14.639 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:52:14.640 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-27 11:52:14.640 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:52:14.641 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:52:14.642 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-27 11:52:14.643 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:52:14.643 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-27 11:52:14.644 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:52:14.644 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-27 11:52:14.645 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:52:14.645 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:52:14.646 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-27 11:52:14.646 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:52:14.647 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:52:14.647 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-27 11:52:14.648 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 11:52:14.648 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 11:52:14.649 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:52:14.649 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:52:14.650 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-27 11:52:14.650 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 11:52:14.651 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:52:14.651 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-27 11:52:14.652 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:52:14.653 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:52:14.653 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-27 11:52:14.654 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 11:52:14.654 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 11:52:14.655 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-27 11:52:14.655 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 11:52:14.656 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 11:52:14.656 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 11:52:14.660 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-27 11:52:14.660 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 11:52:14.660 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-27 11:52:14.662 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-27 11:52:14.662 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-27 11:52:14.664 [Information] VocomDriver: Initializing Vocom driver
2025-07-27 11:52:14.665 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-27 11:52:14.669 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-27 11:52:14.669 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 11:52:14.670 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 11:52:14.672 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 11:52:14.673 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 11:52:14.675 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-27 11:52:14.675 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-27 11:52:14.678 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-27 11:52:14.678 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-27 11:52:14.679 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-27 11:52:14.680 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 11:52:14.682 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-07-27 11:52:14.683 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-07-27 11:52:14.685 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-07-27 11:52:14.686 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-27 11:52:14.686 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-27 11:52:14.687 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-27 11:52:14.688 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-27 11:52:14.689 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-27 11:52:14.689 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-27 11:52:14.690 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-27 11:52:14.690 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-27 11:52:14.690 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-27 11:52:14.690 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-27 11:52:14.691 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-27 11:52:14.691 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-27 11:52:14.692 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-27 11:52:14.692 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-27 11:52:14.692 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-27 11:52:14.693 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-27 11:52:14.693 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-27 11:52:14.693 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-27 11:52:14.694 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-27 11:52:14.694 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-27 11:52:14.694 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-27 11:52:14.695 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-27 11:52:14.695 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-27 11:52:14.695 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-27 11:52:14.695 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-27 11:52:14.696 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-27 11:52:14.696 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-27 11:52:14.696 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-27 11:52:14.696 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-27 11:52:14.697 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-27 11:52:14.697 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-27 11:52:14.697 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-27 11:52:14.697 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-27 11:52:14.698 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-27 11:52:14.698 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-27 11:52:14.698 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-27 11:52:14.699 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-27 11:52:14.699 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-27 11:52:14.699 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-27 11:52:14.699 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-27 11:52:14.700 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-27 11:52:14.700 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-27 11:52:14.701 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-27 11:52:14.702 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-27 11:52:14.702 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-27 11:52:14.702 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-27 11:52:14.704 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-27 11:52:14.705 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-27 11:52:14.706 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-27 11:52:14.707 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-27 11:52:14.707 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-27 11:52:14.707 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-27 11:52:14.707 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-27 11:52:14.708 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-27 11:52:14.712 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-27 11:52:14.713 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-27 11:52:14.715 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-27 11:52:14.716 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-27 11:52:14.791 [Information] WiFiCommunicationService: WiFi is available
2025-07-27 11:52:14.792 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-27 11:52:14.794 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-27 11:52:14.795 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-27 11:52:14.797 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-27 11:52:14.798 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-27 11:52:14.800 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 11:52:14.803 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 11:52:14.804 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 11:52:14.806 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 11:52:14.810 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 11:52:14.810 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 11:52:14.812 [Information] VocomService: Native USB communication service initialized
2025-07-27 11:52:14.813 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 11:52:14.813 [Information] VocomService: Connection recovery service initialized
2025-07-27 11:52:14.814 [Information] VocomService: Enhanced services initialization completed
2025-07-27 11:52:14.816 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:52:14.832 [Information] VocomService: PTT application is not running
2025-07-27 11:52:14.834 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 11:52:14.836 [Debug] VocomService: Bluetooth is enabled
2025-07-27 11:52:14.837 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 11:52:14.838 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-27 11:52:14.839 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-07-27 11:52:14.842 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 11:52:14.843 [Information] VocomService: Using new enhanced device detection service
2025-07-27 11:52:14.844 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 11:52:14.846 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 11:52:15.163 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 11:52:15.165 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 11:52:15.166 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 11:52:15.167 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 11:52:15.168 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 11:52:15.169 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 11:52:15.172 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 11:52:15.176 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 11:52:15.484 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 11:52:15.488 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 11:52:15.489 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 11:52:15.490 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 11:52:15.493 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 11:52:15.505 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 11:52:15.506 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 11:52:15.506 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 11:52:15.507 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 11:52:15.507 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 11:52:15.508 [Debug] VocomService: Bluetooth is enabled
2025-07-27 11:52:15.512 [Debug] VocomService: Checking if WiFi is available
2025-07-27 11:52:15.514 [Debug] VocomService: WiFi is available
2025-07-27 11:52:15.514 [Information] VocomService: Found 3 Vocom devices
2025-07-27 11:52:15.515 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-07-27 11:52:15.517 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: 788d9658-3335-46bd-8263-db238b15c586, Type: USB)
2025-07-27 11:52:15.518 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: d3b182f1-d8ae-48b4-8a70-4258730db7b2, Type: Bluetooth)
2025-07-27 11:52:15.518 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: 175b06c2-551b-4f3d-8a36-3ee5d7673773, Type: WiFi)
2025-07-27 11:52:15.519 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-07-27 11:52:15.519 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-07-27 11:52:15.520 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-07-27 11:52:15.520 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-07-27 11:52:15.521 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-07-27 11:52:15.521 [Information] App: Architecture-aware Vocom service created successfully
2025-07-27 11:52:15.521 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 11:52:15.522 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 11:52:15.522 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 11:52:15.523 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 11:52:15.523 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 11:52:15.524 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 11:52:15.524 [Information] VocomService: Native USB communication service initialized
2025-07-27 11:52:15.524 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 11:52:15.525 [Information] VocomService: Connection recovery service initialized
2025-07-27 11:52:15.525 [Information] VocomService: Enhanced services initialization completed
2025-07-27 11:52:15.525 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:52:15.543 [Information] VocomService: PTT application is not running
2025-07-27 11:52:15.543 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 11:52:15.544 [Debug] VocomService: Bluetooth is enabled
2025-07-27 11:52:15.544 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 11:52:15.545 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-27 11:52:15.545 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-27 11:52:15.545 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-27 11:52:15.586 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 11:52:15.586 [Information] VocomService: Using new enhanced device detection service
2025-07-27 11:52:15.587 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 11:52:15.587 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 11:52:15.945 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 11:52:15.946 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 11:52:15.947 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 11:52:15.947 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 11:52:15.948 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 11:52:15.948 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 11:52:15.949 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 11:52:15.950 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 11:52:16.294 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 11:52:16.295 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 11:52:16.295 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 11:52:16.296 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 11:52:16.297 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 11:52:16.306 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 11:52:16.306 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 11:52:16.306 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 11:52:16.307 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 11:52:16.307 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 11:52:16.308 [Debug] VocomService: Bluetooth is enabled
2025-07-27 11:52:16.308 [Debug] VocomService: Checking if WiFi is available
2025-07-27 11:52:16.308 [Debug] VocomService: WiFi is available
2025-07-27 11:52:16.309 [Information] VocomService: Found 3 Vocom devices
2025-07-27 11:52:16.309 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-27 11:52:16.313 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 11:52:16.313 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:52:16.329 [Information] VocomService: PTT application is not running
2025-07-27 11:52:16.333 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 11:52:16.334 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-27 11:52:16.334 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:52:16.351 [Information] VocomService: PTT application is not running
2025-07-27 11:52:16.352 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-27 11:52:16.355 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-27 11:52:16.357 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 11:52:16.360 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 11:52:16.363 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:16.364 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:16.365 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:16.365 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:16.366 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:16.366 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:16.366 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:16.367 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:16.367 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:16.367 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 11:52:16.368 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:16.368 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:16.368 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:16.368 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:16.369 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:16.369 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 11:52:16.373 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:52:16.373 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 11:52:16.374 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:52:16.374 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 11:52:16.374 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 11:52:16.375 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 11:52:16.375 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 11:52:17.375 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-27 11:52:17.376 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 11:52:17.376 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 11:52:17.377 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:17.377 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:17.377 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:17.378 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:17.378 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:17.378 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:17.379 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:17.379 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:17.379 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:17.380 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 11:52:17.380 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:17.380 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:17.383 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:17.383 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:17.383 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:17.384 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 11:52:17.384 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:52:17.385 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 11:52:17.385 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:52:17.385 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 11:52:17.386 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 11:52:17.386 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 11:52:17.386 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 11:52:18.386 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-27 11:52:18.386 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 11:52:18.387 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 11:52:18.387 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:18.388 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:18.389 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:18.389 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:18.389 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:18.391 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:18.391 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:18.392 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:18.392 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:18.392 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 11:52:18.393 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:18.393 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:18.394 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:18.394 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:18.394 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:18.395 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 11:52:18.395 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:52:18.396 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 11:52:18.397 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:52:18.397 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 11:52:18.398 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 11:52:18.398 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 11:52:18.398 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 11:52:18.399 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 11:52:18.400 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-27 11:52:18.439 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 11:52:18.441 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-27 11:52:18.442 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 11:52:18.444 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 11:52:18.445 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 11:52:18.446 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 11:52:18.446 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 11:52:18.449 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 11:52:18.450 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 11:52:18.454 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 11:52:18.454 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 11:52:18.455 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 11:52:18.455 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 11:52:18.456 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 11:52:18.457 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-27 11:52:18.462 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-27 11:52:18.466 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:52:18.467 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-27 11:52:18.473 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 11:52:18.475 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 11:52:18.476 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 11:52:18.477 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 11:52:18.478 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-07-27 11:52:18.478 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 11:52:18.479 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-27 11:52:18.479 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-27 11:52:18.483 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-27 11:52:18.483 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 11:52:18.483 [Information] VocomService: Using new enhanced device detection service
2025-07-27 11:52:18.484 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 11:52:18.484 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 11:52:18.773 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 11:52:18.774 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 11:52:18.774 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 11:52:18.774 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 11:52:18.775 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 11:52:18.775 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 11:52:18.775 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 11:52:18.776 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 11:52:19.023 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 11:52:19.023 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 11:52:19.024 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 11:52:19.024 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 11:52:19.025 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 11:52:19.031 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 11:52:19.031 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 11:52:19.032 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 11:52:19.032 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 11:52:19.032 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 11:52:19.034 [Debug] VocomService: Bluetooth is enabled
2025-07-27 11:52:19.035 [Debug] VocomService: Checking if WiFi is available
2025-07-27 11:52:19.035 [Debug] VocomService: WiFi is available
2025-07-27 11:52:19.035 [Information] VocomService: Found 3 Vocom devices
2025-07-27 11:52:19.036 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 11:52:19.036 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 11:52:19.037 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:52:19.050 [Information] VocomService: PTT application is not running
2025-07-27 11:52:19.050 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 11:52:19.051 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-27 11:52:19.051 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:52:19.064 [Information] VocomService: PTT application is not running
2025-07-27 11:52:19.064 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-27 11:52:19.065 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-27 11:52:19.065 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 11:52:19.065 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 11:52:19.066 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:19.066 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:19.066 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:19.067 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:19.067 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:19.067 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:19.068 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:19.068 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:19.068 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:19.068 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 11:52:19.069 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:19.069 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:19.069 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:19.070 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:19.070 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:19.071 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 11:52:19.071 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:52:19.071 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 11:52:19.072 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:52:19.072 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 11:52:19.072 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 11:52:19.072 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 11:52:19.073 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 11:52:20.072 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-27 11:52:20.073 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 11:52:20.073 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 11:52:20.074 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:20.074 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:20.075 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:20.075 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:20.076 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:20.076 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:20.077 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:20.078 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:20.078 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:20.078 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 11:52:20.079 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:20.079 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:20.079 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:20.080 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:20.080 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:20.081 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 11:52:20.082 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:52:20.082 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 11:52:20.083 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:52:20.083 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 11:52:20.083 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 11:52:20.085 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 11:52:20.085 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 11:52:21.085 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-27 11:52:21.086 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 11:52:21.086 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 11:52:21.087 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:21.087 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:21.088 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:21.088 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:21.088 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:21.089 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:21.089 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:21.090 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:21.091 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:21.091 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 11:52:21.092 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:21.092 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:21.092 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 11:52:21.093 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 11:52:21.093 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 11:52:21.093 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 11:52:21.094 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:52:21.094 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 11:52:21.094 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 11:52:21.095 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 11:52:21.095 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 11:52:21.095 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 11:52:21.096 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 11:52:21.096 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 11:52:21.096 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-27 11:52:21.097 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 11:52:21.097 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-27 11:52:21.097 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 11:52:21.098 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 11:52:21.098 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 11:52:21.098 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 11:52:21.099 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 11:52:21.099 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 11:52:21.099 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 11:52:21.100 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 11:52:21.100 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 11:52:21.100 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 11:52:21.101 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 11:52:21.101 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 11:52:21.102 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 11:52:21.102 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 11:52:21.103 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 11:52:21.103 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 11:52:21.103 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 11:52:21.103 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:52:21.117 [Information] VocomService: PTT application is not running
2025-07-27 11:52:21.118 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 11:52:21.119 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 11:52:21.119 [Debug] VocomService: Bluetooth is enabled
2025-07-27 11:52:21.121 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-27 11:52:21.925 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 11:52:21.926 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 11:52:21.926 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-27 11:52:21.927 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 11:52:21.930 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 11:52:21.934 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 11:52:21.937 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 11:52:21.940 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 11:52:21.943 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 11:52:21.949 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 11:52:21.954 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 11:52:21.966 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 11:52:21.967 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 11:52:21.968 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 11:52:21.968 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 11:52:21.969 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 11:52:21.969 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 11:52:21.969 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 11:52:21.970 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 11:52:21.970 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 11:52:21.970 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 11:52:21.972 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 11:52:21.973 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 11:52:21.973 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 11:52:21.973 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 11:52:21.974 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 11:52:21.974 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 11:52:21.974 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 11:52:21.977 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 11:52:21.984 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 11:52:21.985 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 11:52:21.990 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 11:52:21.992 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:52:21.999 [Information] CANRegisterAccess: Read value 0xBC from register 0x0141 (simulated)
2025-07-27 11:52:22.005 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:52:22.012 [Information] CANRegisterAccess: Read value 0x0E from register 0x0141 (simulated)
2025-07-27 11:52:22.018 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:52:22.024 [Information] CANRegisterAccess: Read value 0x27 from register 0x0141 (simulated)
2025-07-27 11:52:22.025 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 11:52:22.026 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 11:52:22.026 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 11:52:22.031 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 11:52:22.032 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 11:52:22.037 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 11:52:22.038 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 11:52:22.038 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 11:52:22.045 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 11:52:22.045 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 11:52:22.046 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 11:52:22.051 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 11:52:22.052 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 11:52:22.058 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 11:52:22.059 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 11:52:22.064 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 11:52:22.065 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 11:52:22.070 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 11:52:22.071 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 11:52:22.077 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 11:52:22.078 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 11:52:22.084 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 11:52:22.085 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 11:52:22.091 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 11:52:22.092 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 11:52:22.098 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 11:52:22.099 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 11:52:22.104 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 11:52:22.105 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 11:52:22.110 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 11:52:22.111 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 11:52:22.117 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 11:52:22.118 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 11:52:22.123 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 11:52:22.124 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 11:52:22.129 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 11:52:22.130 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 11:52:22.135 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 11:52:22.136 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 11:52:22.141 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 11:52:22.142 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 11:52:22.148 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 11:52:22.149 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 11:52:22.154 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 11:52:22.155 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 11:52:22.155 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 11:52:22.161 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 11:52:22.162 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 11:52:22.162 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 11:52:22.163 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:52:22.168 [Information] CANRegisterAccess: Read value 0x85 from register 0x0141 (simulated)
2025-07-27 11:52:22.174 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:52:22.180 [Information] CANRegisterAccess: Read value 0x24 from register 0x0141 (simulated)
2025-07-27 11:52:22.182 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 11:52:22.183 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 11:52:22.183 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 11:52:22.183 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:52:22.189 [Information] CANRegisterAccess: Read value 0xE3 from register 0x0140 (simulated)
2025-07-27 11:52:22.195 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:52:22.201 [Information] CANRegisterAccess: Read value 0x0A from register 0x0140 (simulated)
2025-07-27 11:52:22.208 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:52:22.214 [Information] CANRegisterAccess: Read value 0xBD from register 0x0140 (simulated)
2025-07-27 11:52:22.215 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 11:52:22.215 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 11:52:22.217 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 11:52:22.218 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 11:52:22.229 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 11:52:22.230 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 11:52:22.231 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 11:52:22.236 [Information] VocomService: Sending data and waiting for response
2025-07-27 11:52:22.236 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 11:52:22.287 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 11:52:22.288 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 11:52:22.289 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 11:52:22.291 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 11:52:22.292 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 11:52:22.303 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 11:52:22.304 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 11:52:22.305 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 11:52:22.315 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 11:52:22.326 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 11:52:22.337 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 11:52:22.348 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 11:52:22.359 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 11:52:22.362 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 11:52:22.362 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 11:52:22.373 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 11:52:22.374 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 11:52:22.375 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 11:52:22.385 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 11:52:22.397 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 11:52:22.408 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 11:52:22.419 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 11:52:22.430 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 11:52:22.441 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 11:52:22.443 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 11:52:22.444 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 11:52:22.454 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 11:52:22.456 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 11:52:22.456 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 11:52:22.456 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 11:52:22.457 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 11:52:22.457 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 11:52:22.457 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 11:52:22.457 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 11:52:22.458 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 11:52:22.458 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 11:52:22.458 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 11:52:22.459 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 11:52:22.459 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 11:52:22.459 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 11:52:22.459 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 11:52:22.460 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 11:52:22.460 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 11:52:22.561 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 11:52:22.561 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 11:52:22.564 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 11:52:22.565 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:52:22.566 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 11:52:22.566 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 11:52:22.567 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:52:22.567 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 11:52:22.567 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 11:52:22.568 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:52:22.568 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 11:52:22.568 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 11:52:22.569 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:52:22.569 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 11:52:22.569 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 11:52:22.571 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-27 11:52:22.572 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-27 11:52:22.573 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-27 11:52:22.576 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-27 11:52:22.577 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-27 11:52:22.580 [Information] BackupService: Initializing backup service
2025-07-27 11:52:22.581 [Information] BackupService: Backup service initialized successfully
2025-07-27 11:52:22.582 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-27 11:52:22.582 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-27 11:52:22.585 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-27 11:52:22.626 [Information] BackupService: Compressing backup data
2025-07-27 11:52:22.635 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-07-27 11:52:22.636 [Information] BackupServiceFactory: Created template for category: Production
2025-07-27 11:52:22.637 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-27 11:52:22.637 [Information] BackupService: Compressing backup data
2025-07-27 11:52:22.639 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (448 bytes)
2025-07-27 11:52:22.639 [Information] BackupServiceFactory: Created template for category: Development
2025-07-27 11:52:22.639 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-27 11:52:22.640 [Information] BackupService: Compressing backup data
2025-07-27 11:52:22.642 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (449 bytes)
2025-07-27 11:52:22.643 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-27 11:52:22.643 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-27 11:52:22.644 [Information] BackupService: Compressing backup data
2025-07-27 11:52:22.645 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (451 bytes)
2025-07-27 11:52:22.645 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-27 11:52:22.645 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-27 11:52:22.646 [Information] BackupService: Compressing backup data
2025-07-27 11:52:22.646 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (450 bytes)
2025-07-27 11:52:22.647 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-27 11:52:22.647 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-27 11:52:22.648 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-27 11:52:22.648 [Information] BackupService: Compressing backup data
2025-07-27 11:52:22.649 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-07-27 11:52:22.649 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-27 11:52:22.650 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-27 11:52:22.652 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-27 11:52:22.655 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 11:52:22.657 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 11:52:22.739 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 11:52:22.741 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 11:52:22.743 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-27 11:52:22.743 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-27 11:52:22.744 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-27 11:52:22.745 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-27 11:52:22.746 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-27 11:52:22.751 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-27 11:52:22.751 [Information] App: Flash operation monitor service initialized successfully
2025-07-27 11:52:22.764 [Information] LicensingService: Initializing licensing service
2025-07-27 11:52:22.824 [Information] LicensingService: License information loaded successfully
2025-07-27 11:52:22.828 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-27 11:52:22.828 [Information] App: Licensing service initialized successfully
2025-07-27 11:52:22.829 [Information] App: License status: Trial
2025-07-27 11:52:22.830 [Information] App: Trial period: 30 days remaining
2025-07-27 11:52:22.831 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-27 11:52:22.859 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-27 11:52:23.022 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 11:52:23.023 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 11:52:23.023 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 11:52:23.023 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 11:52:23.024 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 11:52:23.024 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 11:52:23.024 [Information] VocomService: Native USB communication service initialized
2025-07-27 11:52:23.025 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 11:52:23.025 [Information] VocomService: Connection recovery service initialized
2025-07-27 11:52:23.025 [Information] VocomService: Enhanced services initialization completed
2025-07-27 11:52:23.026 [Information] VocomService: Checking if PTT application is running
2025-07-27 11:52:23.042 [Information] VocomService: PTT application is not running
2025-07-27 11:52:23.043 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 11:52:23.044 [Debug] VocomService: Bluetooth is enabled
2025-07-27 11:52:23.044 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 11:52:23.094 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 11:52:23.095 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 11:52:23.095 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 11:52:23.095 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 11:52:23.095 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-27 11:52:23.096 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: 1e88e588-47b4-4723-83ba-bc61dc8ff95c
2025-07-27 11:52:23.098 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-27 11:52:23.098 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 11:52:23.098 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 11:52:23.099 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 11:52:23.100 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 11:52:23.100 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 11:52:23.103 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 11:52:23.103 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 11:52:23.104 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 11:52:23.115 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 11:52:23.115 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 11:52:23.116 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 11:52:23.116 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 11:52:23.116 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 11:52:23.117 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 11:52:23.117 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 11:52:23.117 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 11:52:23.117 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 11:52:23.118 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 11:52:23.118 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 11:52:23.118 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 11:52:23.118 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 11:52:23.119 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 11:52:23.119 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 11:52:23.119 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 11:52:23.120 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 11:52:23.120 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 11:52:23.126 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 11:52:23.127 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 11:52:23.127 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 11:52:23.127 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:52:23.133 [Information] CANRegisterAccess: Read value 0x4C from register 0x0141 (simulated)
2025-07-27 11:52:23.139 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:52:23.145 [Information] CANRegisterAccess: Read value 0x13 from register 0x0141 (simulated)
2025-07-27 11:52:23.145 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 11:52:23.146 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 11:52:23.146 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 11:52:23.152 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 11:52:23.152 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 11:52:23.158 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 11:52:23.159 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 11:52:23.159 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 11:52:23.165 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 11:52:23.166 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 11:52:23.166 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 11:52:23.172 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 11:52:23.173 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 11:52:23.179 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 11:52:23.179 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 11:52:23.185 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 11:52:23.186 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 11:52:23.192 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 11:52:23.193 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 11:52:23.199 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 11:52:23.200 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 11:52:23.206 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 11:52:23.206 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 11:52:23.212 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 11:52:23.212 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 11:52:23.218 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 11:52:23.219 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 11:52:23.225 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 11:52:23.225 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 11:52:23.231 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 11:52:23.231 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 11:52:23.238 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 11:52:23.239 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 11:52:23.245 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 11:52:23.246 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 11:52:23.252 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 11:52:23.253 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 11:52:23.259 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 11:52:23.260 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 11:52:23.266 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 11:52:23.267 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 11:52:23.272 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 11:52:23.273 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 11:52:23.279 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 11:52:23.279 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 11:52:23.280 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 11:52:23.286 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 11:52:23.286 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 11:52:23.287 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 11:52:23.287 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 11:52:23.292 [Information] CANRegisterAccess: Read value 0xA2 from register 0x0141 (simulated)
2025-07-27 11:52:23.292 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 11:52:23.293 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 11:52:23.293 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 11:52:23.293 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:52:23.299 [Information] CANRegisterAccess: Read value 0x02 from register 0x0140 (simulated)
2025-07-27 11:52:23.305 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:52:23.311 [Information] CANRegisterAccess: Read value 0x69 from register 0x0140 (simulated)
2025-07-27 11:52:23.317 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:52:23.323 [Information] CANRegisterAccess: Read value 0x05 from register 0x0140 (simulated)
2025-07-27 11:52:23.329 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 11:52:23.335 [Information] CANRegisterAccess: Read value 0x95 from register 0x0140 (simulated)
2025-07-27 11:52:23.335 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 11:52:23.336 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 11:52:23.336 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 11:52:23.336 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 11:52:23.347 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 11:52:23.347 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 11:52:23.348 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 11:52:23.348 [Information] VocomService: Sending data and waiting for response
2025-07-27 11:52:23.348 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 11:52:23.400 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 11:52:23.400 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 11:52:23.401 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 11:52:23.401 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 11:52:23.402 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 11:52:23.413 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 11:52:23.413 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 11:52:23.414 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 11:52:23.425 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 11:52:23.436 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 11:52:23.447 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 11:52:23.458 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 11:52:23.469 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 11:52:23.469 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 11:52:23.470 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 11:52:23.481 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 11:52:23.481 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 11:52:23.482 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 11:52:23.493 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 11:52:23.504 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 11:52:23.515 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 11:52:23.526 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 11:52:23.537 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 11:52:23.548 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 11:52:23.548 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 11:52:23.549 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 11:52:23.560 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 11:52:23.560 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 11:52:23.561 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 11:52:23.561 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 11:52:23.562 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 11:52:23.562 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 11:52:23.562 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 11:52:23.563 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 11:52:23.563 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 11:52:23.563 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 11:52:23.563 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 11:52:23.564 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 11:52:23.564 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 11:52:23.564 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 11:52:23.564 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 11:52:23.565 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 11:52:23.565 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 11:52:23.665 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 11:52:23.665 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 11:52:23.666 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 11:52:23.666 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:52:23.667 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 11:52:23.667 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 11:52:23.667 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:52:23.667 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 11:52:23.668 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 11:52:23.668 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:52:23.668 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 11:52:23.669 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 11:52:23.669 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 11:52:23.669 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 11:52:23.670 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 11:52:23.721 [Information] BackupService: Initializing backup service
2025-07-27 11:52:23.721 [Information] BackupService: Backup service initialized successfully
2025-07-27 11:52:23.773 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 11:52:23.773 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 11:52:23.775 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 11:52:23.775 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 11:52:23.827 [Information] BackupService: Getting predefined backup categories
2025-07-27 11:52:23.878 [Information] MainViewModel: Services initialized successfully
2025-07-27 11:52:23.882 [Information] MainViewModel: Scanning for Vocom devices
2025-07-27 11:52:23.883 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 11:52:23.884 [Information] VocomService: Using new enhanced device detection service
2025-07-27 11:52:23.884 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 11:52:23.884 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 11:52:24.160 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 11:52:24.161 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 11:52:24.161 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 11:52:24.161 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 11:52:24.162 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 11:52:24.162 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 11:52:24.162 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 11:52:24.163 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 11:52:24.508 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 11:52:24.508 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 11:52:24.509 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 11:52:24.509 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 11:52:24.510 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 11:52:24.521 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 11:52:24.522 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 11:52:24.523 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 11:52:24.523 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 11:52:24.523 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 11:52:24.525 [Debug] VocomService: Bluetooth is enabled
2025-07-27 11:52:24.526 [Debug] VocomService: Checking if WiFi is available
2025-07-27 11:52:24.526 [Debug] VocomService: WiFi is available
2025-07-27 11:52:24.527 [Information] VocomService: Found 3 Vocom devices
2025-07-27 11:52:24.528 [Information] MainViewModel: Found 3 Vocom device(s)
