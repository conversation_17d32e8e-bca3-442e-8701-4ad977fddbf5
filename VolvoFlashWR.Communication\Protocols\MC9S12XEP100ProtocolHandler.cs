using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Communication.Interfaces;
using VolvoFlashWR.Communication.Protocols;

namespace VolvoFlashWR.Communication.Protocols
{
    /// <summary>
    /// Enhanced protocol handler specifically designed for MC9S12XEP100 microcontroller
    /// Implements real hardware communication based on NXP specifications
    /// </summary>
    public class MC9S12XEP100ProtocolHandler : BaseECUProtocolHandler
    {
        private new readonly ILoggingService _logger;
        private new readonly IVocomService _vocomService;
        private readonly ECUProtocolType _protocolType;

        // MC9S12XEP100 specific constants based on datasheet
        private const uint FLASH_BASE_ADDRESS = 0x400000;
        private const uint EEPROM_BASE_ADDRESS = 0x100000;
        private const uint RAM_BASE_ADDRESS = 0x002000;

        // Flash memory organization (768KB total)
        private new const uint FLASH_SIZE = 0xC0000; // 768KB
        private const uint FLASH_SECTOR_SIZE = 0x800; // 2KB sectors
        private const uint FLASH_PHRASE_SIZE = 8; // 8-byte phrases for programming

        // EEPROM organization (4KB total)
        private new const uint EEPROM_SIZE = 0x1000; // 4KB
        private const uint EEPROM_SECTOR_SIZE = 4; // 4-byte sectors
        
        // Communication protocol constants
        private const byte CMD_READ_MEMORY = 0x11;
        private const byte CMD_WRITE_MEMORY = 0x12;
        private const byte CMD_ERASE_SECTOR = 0x13;
        private const byte CMD_SECURITY_ACCESS = 0x27;
        private const byte CMD_ROUTINE_CONTROL = 0x31;
        private const byte CMD_REQUEST_DOWNLOAD = 0x34;
        private const byte CMD_TRANSFER_DATA = 0x36;
        private const byte CMD_REQUEST_TRANSFER_EXIT = 0x37;

        // High/Low speed communication modes
        public enum CommunicationSpeed
        {
            LowSpeed = 0,   // Standard speed for diagnostics
            HighSpeed = 1   // High speed for flash programming
        }

        private CommunicationSpeed _currentSpeed = CommunicationSpeed.LowSpeed;

        public MC9S12XEP100ProtocolHandler(ILoggingService logger, IVocomService vocomService, ECUProtocolType protocolType)
            : base(logger, vocomService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _vocomService = vocomService ?? throw new ArgumentNullException(nameof(vocomService));
            _protocolType = protocolType;
        }

        // Override the ProtocolType property
        public override ECUProtocolType ProtocolType => _protocolType;

        /// <summary>
        /// Initializes the MC9S12XEP100 protocol handler
        /// </summary>
        public override async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing MC9S12XEP100 protocol handler", "MC9S12XEP100ProtocolHandler");

                // Initialize base protocol handler
                bool baseInitialized = await base.InitializeAsync();
                if (!baseInitialized)
                {
                    _logger.LogError("Failed to initialize base protocol handler", "MC9S12XEP100ProtocolHandler");
                    return false;
                }

                // Initialize MC9S12XEP100 specific settings
                await InitializeMC9S12XEP100SpecificSettingsAsync();

                _logger.LogInformation("MC9S12XEP100 protocol handler initialized successfully", "MC9S12XEP100ProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error initializing MC9S12XEP100 protocol handler", "MC9S12XEP100ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Initializes MC9S12XEP100 specific settings
        /// </summary>
        private async Task InitializeMC9S12XEP100SpecificSettingsAsync()
        {
            try
            {
                _logger.LogInformation("Initializing MC9S12XEP100 specific settings", "MC9S12XEP100ProtocolHandler");

                // Configure communication parameters based on protocol type
                switch (ProtocolType)
                {
                    case ECUProtocolType.CAN:
                        await InitializeCANSpecificSettingsAsync();
                        break;
                    case ECUProtocolType.SPI:
                        await InitializeSPISpecificSettingsAsync();
                        break;
                    case ECUProtocolType.SCI:
                        await InitializeSCISpecificSettingsAsync();
                        break;
                    case ECUProtocolType.IIC:
                        await InitializeIICSpecificSettingsAsync();
                        break;
                }

                _logger.LogInformation("MC9S12XEP100 specific settings initialized", "MC9S12XEP100ProtocolHandler");
            }
            catch (Exception ex)
            {
                _logger.LogError("Error initializing MC9S12XEP100 specific settings", "MC9S12XEP100ProtocolHandler", ex);
                throw;
            }
        }

        /// <summary>
        /// Initializes CAN specific settings for MC9S12XEP100
        /// </summary>
        private async Task InitializeCANSpecificSettingsAsync()
        {
            _logger.LogInformation("Initializing CAN settings for MC9S12XEP100", "MC9S12XEP100ProtocolHandler");
            
            // Configure CAN bit timing for MC9S12XEP100
            // Based on S12MSCANV3 specifications
            // Standard: 500 kbps, High-speed: 1 Mbps
            
            await Task.CompletedTask;
        }

        /// <summary>
        /// Initializes SPI specific settings for MC9S12XEP100
        /// </summary>
        private async Task InitializeSPISpecificSettingsAsync()
        {
            _logger.LogInformation("Initializing SPI settings for MC9S12XEP100", "MC9S12XEP100ProtocolHandler");
            
            // Configure SPI settings based on S12SPIV5 specifications
            // Support for 8-bit and 16-bit transfers
            // Configurable clock polarity and phase
            
            await Task.CompletedTask;
        }

        /// <summary>
        /// Initializes SCI specific settings for MC9S12XEP100
        /// </summary>
        private async Task InitializeSCISpecificSettingsAsync()
        {
            _logger.LogInformation("Initializing SCI settings for MC9S12XEP100", "MC9S12XEP100ProtocolHandler");
            
            // Configure SCI settings based on S12SCIV5 specifications
            // Standard baud rates: 9600, 19200, 38400, 115200
            // High-speed: up to 1 Mbps
            
            await Task.CompletedTask;
        }

        /// <summary>
        /// Initializes IIC specific settings for MC9S12XEP100
        /// </summary>
        private async Task InitializeIICSpecificSettingsAsync()
        {
            _logger.LogInformation("Initializing IIC settings for MC9S12XEP100", "MC9S12XEP100ProtocolHandler");
            
            // Configure IIC settings based on IICV3 specifications
            // Standard: 100 kbps, Fast: 400 kbps
            // Support for 7-bit and 10-bit addressing
            
            await Task.CompletedTask;
        }

        /// <summary>
        /// Sets the communication speed mode
        /// </summary>
        public async Task<bool> SetCommunicationSpeedAsync(CommunicationSpeed speed)
        {
            try
            {
                _logger.LogInformation($"Setting communication speed to {speed}", "MC9S12XEP100ProtocolHandler");

                if (_currentSpeed == speed)
                {
                    _logger.LogInformation($"Communication speed already set to {speed}", "MC9S12XEP100ProtocolHandler");
                    return true;
                }

                // Send speed change command to ECU
                byte[] speedCommand = CreateSpeedChangeCommand(speed);
                bool success = await SendCommandAsync(speedCommand);

                if (success)
                {
                    _currentSpeed = speed;
                    _logger.LogInformation($"Communication speed changed to {speed}", "MC9S12XEP100ProtocolHandler");
                }
                else
                {
                    _logger.LogError($"Failed to change communication speed to {speed}", "MC9S12XEP100ProtocolHandler");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error setting communication speed to {speed}", "MC9S12XEP100ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Creates a speed change command
        /// </summary>
        private byte[] CreateSpeedChangeCommand(CommunicationSpeed speed)
        {
            // Create protocol-specific speed change command
            switch (ProtocolType)
            {
                case ECUProtocolType.CAN:
                    return new byte[] { 0x10, (byte)speed }; // Diagnostic session control
                case ECUProtocolType.SCI:
                    return new byte[] { 0x86, (byte)speed }; // Change baud rate
                case ECUProtocolType.SPI:
                    return new byte[] { 0x87, (byte)speed }; // Change SPI speed
                case ECUProtocolType.IIC:
                    return new byte[] { 0x88, (byte)speed }; // Change IIC speed
                default:
                    return new byte[] { 0x10, (byte)speed };
            }
        }

        /// <summary>
        /// Sends a command to the ECU
        /// </summary>
        private async Task<bool> SendCommandAsync(byte[] command)
        {
            try
            {
                if (_vocomService == null || _vocomService.CurrentDevice?.ConnectionStatus != VocomConnectionStatus.Connected)
                {
                    _logger.LogError("Vocom service not connected", "MC9S12XEP100ProtocolHandler");
                    return false;
                }

                // Send command and wait for response
                byte[] response = await _vocomService.SendAndReceiveDataAsync(command, 5000);
                
                if (response != null && response.Length > 0)
                {
                    // Check for positive response
                    if (response[0] == (command[0] + 0x40)) // Positive response
                    {
                        return true;
                    }
                    else if (response[0] == 0x7F) // Negative response
                    {
                        _logger.LogWarning($"Negative response received: {BitConverter.ToString(response)}", "MC9S12XEP100ProtocolHandler");
                        return false;
                    }
                }

                _logger.LogWarning("No valid response received", "MC9S12XEP100ProtocolHandler");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error sending command", "MC9S12XEP100ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Reads EEPROM data using MC9S12XEP100 specific protocol
        /// </summary>
        public override async Task<byte[]> ReadEEPROMAsync(ECUDevice ecu)
        {
            try
            {
                _logger.LogInformation($"Reading EEPROM from MC9S12XEP100 ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return null;
                }

                // Switch to high-speed mode for faster data transfer
                await SetCommunicationSpeedAsync(CommunicationSpeed.HighSpeed);

                // Read EEPROM in chunks to handle large data efficiently
                byte[] eepromData = new byte[EEPROM_SIZE];
                uint chunkSize = 256; // Read in 256-byte chunks
                
                for (uint offset = 0; offset < EEPROM_SIZE; offset += chunkSize)
                {
                    uint currentChunkSize = Math.Min(chunkSize, EEPROM_SIZE - offset);
                    byte[] chunk = await ReadMemoryChunkAsync(EEPROM_BASE_ADDRESS + offset, currentChunkSize);
                    
                    if (chunk == null)
                    {
                        _logger.LogError($"Failed to read EEPROM chunk at offset 0x{offset:X}", "MC9S12XEP100ProtocolHandler");
                        return null;
                    }
                    
                    Array.Copy(chunk, 0, eepromData, offset, chunk.Length);
                }

                // Switch back to low-speed mode
                await SetCommunicationSpeedAsync(CommunicationSpeed.LowSpeed);

                _logger.LogInformation($"Successfully read {eepromData.Length} bytes of EEPROM data", "MC9S12XEP100ProtocolHandler");
                return eepromData;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error reading EEPROM from ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Reads a chunk of memory from the specified address
        /// </summary>
        private async Task<byte[]> ReadMemoryChunkAsync(uint address, uint size)
        {
            try
            {
                // Create read memory command
                byte[] command = new byte[7];
                command[0] = CMD_READ_MEMORY;
                command[1] = (byte)((address >> 24) & 0xFF);
                command[2] = (byte)((address >> 16) & 0xFF);
                command[3] = (byte)((address >> 8) & 0xFF);
                command[4] = (byte)(address & 0xFF);
                command[5] = (byte)((size >> 8) & 0xFF);
                command[6] = (byte)(size & 0xFF);

                // Send command and receive response
                byte[] response = await _vocomService.SendAndReceiveDataAsync(command, 5000);

                if (response != null && response.Length >= size + 3)
                {
                    // Extract data from response (skip header bytes)
                    byte[] data = new byte[size];
                    Array.Copy(response, 3, data, 0, size);
                    return data;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error reading memory chunk at address 0x{address:X}", "MC9S12XEP100ProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Reads microcontroller code using MC9S12XEP100 specific protocol
        /// </summary>
        public override async Task<byte[]> ReadMicrocontrollerCodeAsync(ECUDevice ecu)
        {
            try
            {
                _logger.LogInformation($"Reading microcontroller code from MC9S12XEP100 ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return null;
                }

                // Establish security access if required
                bool securityAccess = await EstablishSecurityAccessAsync();
                if (!securityAccess)
                {
                    _logger.LogError("Failed to establish security access", "MC9S12XEP100ProtocolHandler");
                    return null;
                }

                // Switch to high-speed mode for faster data transfer
                await SetCommunicationSpeedAsync(CommunicationSpeed.HighSpeed);

                // Read flash memory in optimized chunks
                byte[] flashData = new byte[FLASH_SIZE];
                uint chunkSize = 1024; // Read in 1KB chunks for efficiency

                _logger.LogInformation($"Reading {FLASH_SIZE} bytes of flash memory in {chunkSize}-byte chunks", "MC9S12XEP100ProtocolHandler");

                for (uint offset = 0; offset < FLASH_SIZE; offset += chunkSize)
                {
                    uint currentChunkSize = Math.Min(chunkSize, FLASH_SIZE - offset);
                    byte[] chunk = await ReadMemoryChunkAsync(FLASH_BASE_ADDRESS + offset, currentChunkSize);

                    if (chunk == null)
                    {
                        _logger.LogError($"Failed to read flash chunk at offset 0x{offset:X}", "MC9S12XEP100ProtocolHandler");
                        return null;
                    }

                    Array.Copy(chunk, 0, flashData, offset, chunk.Length);

                    // Log progress every 64KB
                    if ((offset + chunkSize) % 65536 == 0)
                    {
                        double progress = ((double)(offset + chunkSize) / FLASH_SIZE) * 100;
                        _logger.LogInformation($"Flash read progress: {progress:F1}%", "MC9S12XEP100ProtocolHandler");
                    }
                }

                // Switch back to low-speed mode
                await SetCommunicationSpeedAsync(CommunicationSpeed.LowSpeed);

                _logger.LogInformation($"Successfully read {flashData.Length} bytes of microcontroller code", "MC9S12XEP100ProtocolHandler");
                return flashData;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error reading microcontroller code from ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler", ex);
                return null;
            }
        }

        /// <summary>
        /// Establishes security access to the ECU
        /// </summary>
        private async Task<bool> EstablishSecurityAccessAsync()
        {
            try
            {
                _logger.LogInformation("Establishing security access", "MC9S12XEP100ProtocolHandler");

                // Request seed
                byte[] seedRequest = new byte[] { CMD_SECURITY_ACCESS, 0x01 };
                byte[] seedResponse = await _vocomService.SendAndReceiveDataAsync(seedRequest, 5000);

                if (seedResponse == null || seedResponse.Length < 6)
                {
                    _logger.LogError("Failed to receive security seed", "MC9S12XEP100ProtocolHandler");
                    return false;
                }

                // Extract seed (4 bytes)
                byte[] seed = new byte[4];
                Array.Copy(seedResponse, 2, seed, 0, 4);

                // Calculate key using proprietary algorithm
                byte[] key = CalculateSecurityKey(seed);

                // Send key
                byte[] keyRequest = new byte[6];
                keyRequest[0] = CMD_SECURITY_ACCESS;
                keyRequest[1] = 0x02;
                Array.Copy(key, 0, keyRequest, 2, 4);

                byte[] keyResponse = await _vocomService.SendAndReceiveDataAsync(keyRequest, 5000);

                if (keyResponse != null && keyResponse.Length >= 2 && keyResponse[0] == (CMD_SECURITY_ACCESS + 0x40))
                {
                    _logger.LogInformation("Security access established successfully", "MC9S12XEP100ProtocolHandler");
                    return true;
                }

                _logger.LogError("Security access denied", "MC9S12XEP100ProtocolHandler");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error establishing security access", "MC9S12XEP100ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Calculates security key from seed (proprietary algorithm)
        /// </summary>
        private byte[] CalculateSecurityKey(byte[] seed)
        {
            // This is a simplified key calculation
            // In a real implementation, this would use the actual Volvo security algorithm
            byte[] key = new byte[4];
            for (int i = 0; i < 4; i++)
            {
                key[i] = (byte)(seed[i] ^ 0xAA);
            }
            return key;
        }

        /// <summary>
        /// Writes EEPROM data using MC9S12XEP100 specific protocol
        /// </summary>
        public override async Task<bool> WriteEEPROMAsync(ECUDevice ecu, byte[] data)
        {
            try
            {
                _logger.LogInformation($"Writing EEPROM to MC9S12XEP100 ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu) || data == null)
                {
                    return false;
                }

                if (data.Length > EEPROM_SIZE)
                {
                    _logger.LogError($"Data size ({data.Length}) exceeds EEPROM size ({EEPROM_SIZE})", "MC9S12XEP100ProtocolHandler");
                    return false;
                }

                // Establish security access
                bool securityAccess = await EstablishSecurityAccessAsync();
                if (!securityAccess)
                {
                    return false;
                }

                // Switch to high-speed mode
                await SetCommunicationSpeedAsync(CommunicationSpeed.HighSpeed);

                // Write EEPROM in 4-byte sectors (MC9S12XEP100 EEPROM sector size)
                for (uint offset = 0; offset < data.Length; offset += EEPROM_SECTOR_SIZE)
                {
                    uint currentSectorSize = Math.Min(EEPROM_SECTOR_SIZE, (uint)(data.Length - offset));
                    byte[] sectorData = new byte[currentSectorSize];
                    Array.Copy(data, offset, sectorData, 0, currentSectorSize);

                    bool success = await WriteMemoryChunkAsync(EEPROM_BASE_ADDRESS + offset, sectorData);
                    if (!success)
                    {
                        _logger.LogError($"Failed to write EEPROM sector at offset 0x{offset:X}", "MC9S12XEP100ProtocolHandler");
                        return false;
                    }
                }

                // Switch back to low-speed mode
                await SetCommunicationSpeedAsync(CommunicationSpeed.LowSpeed);

                _logger.LogInformation($"Successfully wrote {data.Length} bytes to EEPROM", "MC9S12XEP100ProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error writing EEPROM to ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler", ex);
                return false;
            }
        }

        /// <summary>
        /// Writes a chunk of memory to the specified address
        /// </summary>
        private async Task<bool> WriteMemoryChunkAsync(uint address, byte[] data)
        {
            try
            {
                // Create write memory command
                byte[] command = new byte[7 + data.Length];
                command[0] = CMD_WRITE_MEMORY;
                command[1] = (byte)((address >> 24) & 0xFF);
                command[2] = (byte)((address >> 16) & 0xFF);
                command[3] = (byte)((address >> 8) & 0xFF);
                command[4] = (byte)(address & 0xFF);
                command[5] = (byte)((data.Length >> 8) & 0xFF);
                command[6] = (byte)(data.Length & 0xFF);
                Array.Copy(data, 0, command, 7, data.Length);

                // Send command and receive response
                byte[] response = await _vocomService.SendAndReceiveDataAsync(command, 5000);

                return response != null && response.Length >= 2 && response[0] == (CMD_WRITE_MEMORY + 0x40);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error writing memory chunk at address 0x{address:X}", "MC9S12XEP100ProtocolHandler", ex);
                return false;
            }
        }

        // Implementation of abstract methods from BaseECUProtocolHandler

        public override async Task<bool> ConnectAsync(ECUDevice ecu)
        {
            try
            {
                _logger.LogInformation($"Connecting to MC9S12XEP100 ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                // Establish communication with the ECU
                bool connected = await EstablishCommunicationAsync(ecu);
                if (connected)
                {
                    _logger.LogInformation($"Successfully connected to MC9S12XEP100 ECU {ecu.Name}", "MC9S12XEP100ProtocolHandler");
                    return true;
                }

                _logger.LogError($"Failed to connect to MC9S12XEP100 ECU {ecu.Name}", "MC9S12XEP100ProtocolHandler");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error connecting to ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler", ex);
                return false;
            }
        }

        public override async Task<bool> DisconnectAsync(ECUDevice ecu)
        {
            try
            {
                _logger.LogInformation($"Disconnecting from MC9S12XEP100 ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler");

                if (!ValidateECU(ecu))
                {
                    return false;
                }

                // Send disconnect command if needed
                await SetCommunicationSpeedAsync(CommunicationSpeed.LowSpeed);

                _logger.LogInformation($"Successfully disconnected from MC9S12XEP100 ECU {ecu.Name}", "MC9S12XEP100ProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error disconnecting from ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler", ex);
                return false;
            }
        }

        public override async Task<bool> WriteMicrocontrollerCodeAsync(ECUDevice ecu, byte[] code)
        {
            try
            {
                _logger.LogInformation($"Writing microcontroller code to MC9S12XEP100 ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu) || code == null)
                {
                    return false;
                }

                if (code.Length > FLASH_SIZE)
                {
                    _logger.LogError($"Code size ({code.Length}) exceeds flash size ({FLASH_SIZE})", "MC9S12XEP100ProtocolHandler");
                    return false;
                }

                // Establish security access
                bool securityAccess = await EstablishSecurityAccessAsync();
                if (!securityAccess)
                {
                    return false;
                }

                // Switch to high-speed mode for faster programming
                await SetCommunicationSpeedAsync(CommunicationSpeed.HighSpeed);

                // Write flash memory in optimized chunks
                uint chunkSize = 1024; // Write in 1KB chunks
                for (uint offset = 0; offset < code.Length; offset += chunkSize)
                {
                    uint currentChunkSize = Math.Min(chunkSize, (uint)(code.Length - offset));
                    byte[] chunkData = new byte[currentChunkSize];
                    Array.Copy(code, offset, chunkData, 0, currentChunkSize);

                    bool success = await WriteMemoryChunkAsync(FLASH_BASE_ADDRESS + offset, chunkData);
                    if (!success)
                    {
                        _logger.LogError($"Failed to write flash chunk at offset 0x{offset:X}", "MC9S12XEP100ProtocolHandler");
                        return false;
                    }

                    // Log progress every 64KB
                    if ((offset + chunkSize) % 65536 == 0)
                    {
                        double progress = ((double)(offset + chunkSize) / code.Length) * 100;
                        _logger.LogInformation($"Flash write progress: {progress:F1}%", "MC9S12XEP100ProtocolHandler");
                    }
                }

                // Switch back to low-speed mode
                await SetCommunicationSpeedAsync(CommunicationSpeed.LowSpeed);

                _logger.LogInformation($"Successfully wrote {code.Length} bytes of microcontroller code", "MC9S12XEP100ProtocolHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error writing microcontroller code to ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler", ex);
                return false;
            }
        }

        private async Task<bool> EstablishCommunicationAsync(ECUDevice ecu)
        {
            try
            {
                // Send a basic communication test command
                byte[] testCommand = new byte[] { 0x3E, 0x00 }; // Tester Present
                byte[] response = await _vocomService.SendAndReceiveDataAsync(testCommand, 5000);

                return response != null && response.Length >= 2 && response[0] == 0x7E;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error establishing communication", "MC9S12XEP100ProtocolHandler", ex);
                return false;
            }
        }

        // Fault-related methods
        public override async Task<List<ECUFault>> ReadActiveFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger.LogInformation($"Reading active faults from MC9S12XEP100 ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return new List<ECUFault>();
                }

                // Send read active faults command
                byte[] command = new byte[] { 0x19, 0x02 }; // Read DTC by status mask - active
                byte[] response = await _vocomService.SendAndReceiveDataAsync(command, 5000);

                if (response != null && response.Length > 3)
                {
                    return ParseFaultsFromResponse(response, true);
                }

                return new List<ECUFault>();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error reading active faults from ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler", ex);
                return new List<ECUFault>();
            }
        }

        public override async Task<List<ECUFault>> ReadInactiveFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger.LogInformation($"Reading inactive faults from MC9S12XEP100 ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return new List<ECUFault>();
                }

                // Send read inactive faults command
                byte[] command = new byte[] { 0x19, 0x08 }; // Read DTC by status mask - inactive
                byte[] response = await _vocomService.SendAndReceiveDataAsync(command, 5000);

                if (response != null && response.Length > 3)
                {
                    return ParseFaultsFromResponse(response, false);
                }

                return new List<ECUFault>();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error reading inactive faults from ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler", ex);
                return new List<ECUFault>();
            }
        }

        public override async Task<bool> ClearFaultsAsync(ECUDevice ecu)
        {
            try
            {
                _logger.LogInformation($"Clearing faults from MC9S12XEP100 ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return false;
                }

                // Send clear faults command
                byte[] command = new byte[] { 0x14, 0xFF, 0xFF, 0xFF }; // Clear all DTCs
                byte[] response = await _vocomService.SendAndReceiveDataAsync(command, 5000);

                bool success = response != null && response.Length >= 2 && response[0] == 0x54;
                if (success)
                {
                    _logger.LogInformation($"Successfully cleared faults from ECU {ecu.Name}", "MC9S12XEP100ProtocolHandler");
                }
                else
                {
                    _logger.LogError($"Failed to clear faults from ECU {ecu.Name}", "MC9S12XEP100ProtocolHandler");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error clearing faults from ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler", ex);
                return false;
            }
        }

        private List<ECUFault> ParseFaultsFromResponse(byte[] response, bool isActive)
        {
            var faults = new List<ECUFault>();

            try
            {
                // Skip header bytes and parse fault codes
                for (int i = 3; i < response.Length - 2; i += 3)
                {
                    if (i + 2 < response.Length)
                    {
                        ushort faultCode = (ushort)((response[i] << 8) | response[i + 1]);
                        byte status = response[i + 2];

                        var fault = new ECUFault
                        {
                            Code = faultCode.ToString("X4"),
                            Description = GetFaultDescription(faultCode),
                            IsActive = isActive,
                            Timestamp = DateTime.Now,
                            Severity = GetFaultSeverity(status) // Use status to determine severity
                        };

                        faults.Add(fault);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error parsing faults from response", "MC9S12XEP100ProtocolHandler", ex);
            }

            return faults;
        }

        /// <summary>
        /// Gets fault severity based on status byte
        /// </summary>
        private FaultSeverity GetFaultSeverity(byte status)
        {
            // Map status byte to severity levels
            // This is a simplified mapping - real implementation would depend on MC9S12XEP100 specifications
            if ((status & 0x80) != 0) return FaultSeverity.Critical;
            if ((status & 0x40) != 0) return FaultSeverity.High;
            if ((status & 0x20) != 0) return FaultSeverity.Medium;
            return FaultSeverity.Low;
        }

        private string GetFaultDescription(ushort faultCode)
        {
            // This would typically lookup fault descriptions from a database
            // For now, return a generic description
            return $"Fault Code 0x{faultCode:X4}";
        }

        // Parameter-related methods
        public override async Task<Dictionary<string, object>> ReadParametersAsync(ECUDevice ecu)
        {
            try
            {
                _logger.LogInformation($"Reading parameters from MC9S12XEP100 ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return new Dictionary<string, object>();
                }

                var parameters = new Dictionary<string, object>();

                // Read basic ECU parameters
                parameters["ECU_ID"] = await ReadParameterAsync(ecu, "ECU_ID");
                parameters["SOFTWARE_VERSION"] = await ReadParameterAsync(ecu, "SOFTWARE_VERSION");
                parameters["HARDWARE_VERSION"] = await ReadParameterAsync(ecu, "HARDWARE_VERSION");
                parameters["CALIBRATION_ID"] = await ReadParameterAsync(ecu, "CALIBRATION_ID");

                return parameters;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error reading parameters from ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler", ex);
                return new Dictionary<string, object>();
            }
        }

        public override async Task<bool> WriteParametersAsync(ECUDevice ecu, Dictionary<string, object> parameters)
        {
            try
            {
                _logger.LogInformation($"Writing parameters to MC9S12XEP100 ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu) || parameters == null)
                {
                    return false;
                }

                bool allSuccess = true;
                foreach (var parameter in parameters)
                {
                    bool success = await WriteParameterInternalAsync(ecu, parameter.Key, parameter.Value);
                    if (!success)
                    {
                        allSuccess = false;
                        _logger.LogError($"Failed to write parameter {parameter.Key}", "MC9S12XEP100ProtocolHandler");
                    }
                }

                return allSuccess;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error writing parameters to ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler", ex);
                return false;
            }
        }

        private async Task<object> ReadParameterAsync(ECUDevice ecu, string parameterName)
        {
            try
            {
                // This would implement parameter-specific reading logic
                // For now, return a placeholder value
                await Task.Delay(100); // Simulate parameter read
                return $"Value_{parameterName}";
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error reading parameter {parameterName}", "MC9S12XEP100ProtocolHandler", ex);
                return null;
            }
        }

        private async Task<bool> WriteParameterInternalAsync(ECUDevice ecu, string parameterName, object value)
        {
            try
            {
                // This would implement parameter-specific writing logic
                // For now, simulate a successful write
                await Task.Delay(100); // Simulate parameter write
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error writing parameter {parameterName}", "MC9S12XEP100ProtocolHandler", ex);
                return false;
            }
        }

        // Missing abstract method implementation
        public override async Task<DiagnosticData> PerformDiagnosticSessionAsync(ECUDevice ecu)
        {
            var startTime = DateTime.Now;

            try
            {
                _logger.LogInformation($"Performing diagnostic session for MC9S12XEP100 ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler");

                if (!ValidateInitialization() || !ValidateECU(ecu))
                {
                    return new DiagnosticData
                    {
                        ECUId = ecu?.Id,
                        ECUName = ecu?.Name,
                        Timestamp = startTime,
                        IsSuccessful = false,
                        ErrorMessage = "Validation failed",
                        Status = DiagnosticStatus.Failed
                    };
                }

                // Send diagnostic session control command
                byte[] command = new byte[] { 0x10, 0x01 }; // Default session
                byte[] response = await _vocomService.SendAndReceiveDataAsync(command, 5000);

                bool sessionEstablished = response != null && response.Length >= 2 && response[0] == 0x50;

                if (sessionEstablished)
                {
                    _logger.LogInformation($"Diagnostic session established for ECU {ecu.Name}", "MC9S12XEP100ProtocolHandler");

                    // Collect comprehensive diagnostic data
                    var activeFaults = await ReadActiveFaultsAsync(ecu);
                    var inactiveFaults = await ReadInactiveFaultsAsync(ecu);
                    var parameters = await ReadParametersAsync(ecu);

                    var endTime = DateTime.Now;
                    var sessionDuration = (long)(endTime - startTime).TotalMilliseconds;

                    return new DiagnosticData
                    {
                        ECUId = ecu.Id,
                        ECUName = ecu.Name,
                        Timestamp = startTime,
                        ActiveFaults = activeFaults,
                        InactiveFaults = inactiveFaults,
                        Parameters = parameters,
                        OperatingMode = _currentOperatingMode,
                        ConnectionType = _vocomService?.CurrentDevice?.ConnectionType ?? VocomConnectionType.USB,
                        IsSuccessful = true,
                        SessionDurationMs = sessionDuration,
                        Status = DiagnosticStatus.Success
                    };
                }
                else
                {
                    _logger.LogError($"Failed to establish diagnostic session for ECU {ecu.Name}", "MC9S12XEP100ProtocolHandler");

                    return new DiagnosticData
                    {
                        ECUId = ecu.Id,
                        ECUName = ecu.Name,
                        Timestamp = startTime,
                        IsSuccessful = false,
                        ErrorMessage = "Failed to establish diagnostic session",
                        Status = DiagnosticStatus.Failed,
                        SessionDurationMs = (long)(DateTime.Now - startTime).TotalMilliseconds
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error performing diagnostic session for ECU {ecu?.Name}", "MC9S12XEP100ProtocolHandler", ex);

                return new DiagnosticData
                {
                    ECUId = ecu?.Id,
                    ECUName = ecu?.Name,
                    Timestamp = startTime,
                    IsSuccessful = false,
                    ErrorMessage = ex.Message,
                    Status = DiagnosticStatus.Failed,
                    SessionDurationMs = (long)(DateTime.Now - startTime).TotalMilliseconds
                };
            }
        }
    }
}
