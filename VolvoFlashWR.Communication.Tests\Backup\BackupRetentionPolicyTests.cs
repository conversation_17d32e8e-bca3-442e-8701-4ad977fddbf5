using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Backup;
using VolvoFlashWR.Core.Enums;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Backup
{
    [TestFixture]
    public class BackupRetentionPolicyTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IBackupService> _mockBackupService;
        private Mock<IECUCommunicationService> _mockEcuService;
        private BackupSchedulerService _schedulerService;
        private string _testSchedulesDirectory;
        private List<BackupData> _testBackups;
        private ECUDevice _testEcu;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _mockBackupService = new Mock<IBackupService>();
            _mockEcuService = new Mock<IECUCommunicationService>();

            // Create a test directory for schedules
            _testSchedulesDirectory = Path.Combine(Path.GetTempPath(), "VolvoFlashWR_Tests_Schedules");
            if (Directory.Exists(_testSchedulesDirectory))
            {
                Directory.Delete(_testSchedulesDirectory, true);
            }
            Directory.CreateDirectory(_testSchedulesDirectory);

            // Create the scheduler service with the test directory
            _schedulerService = new BackupSchedulerService(_mockLogger.Object);

            // Use reflection to set the schedules directory to our test directory
            var directoryField = typeof(BackupSchedulerService).GetField("_schedulesDirectoryPath", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            directoryField.SetValue(_schedulerService, _testSchedulesDirectory);

            var filePathField = typeof(BackupSchedulerService).GetField("_schedulesFilePath", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            filePathField.SetValue(_schedulerService, Path.Combine(_testSchedulesDirectory, "backup_schedules.json"));

            // Create a test ECU
            _testEcu = new ECUDevice
            {
                Id = "ecu1",
                Name = "Test ECU 1",
                SerialNumber = "SN12345",
                HardwareVersion = "HW1.0",
                SoftwareVersion = "SW1.0"
            };

            // Set up the ECU service to return our test ECU
            _mockEcuService.Setup(s => s.ScanForECUsAsync()).ReturnsAsync(new List<ECUDevice> { _testEcu });
            _mockEcuService.Setup(s => s.ConnectToECUAsync(It.IsAny<ECUDevice>())).ReturnsAsync(true);
            _mockEcuService.Setup(s => s.ConnectedECUs).Returns(new List<ECUDevice> { _testEcu });

            // Create test backups
            _testBackups = CreateTestBackups();

            // Set up the backup service to return our test backups
            _mockBackupService.Setup(s => s.GetAllBackupsAsync()).ReturnsAsync(_testBackups);
            _mockBackupService.Setup(s => s.CreateBackupAsync(
                It.IsAny<ECUDevice>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<List<string>>(),
                It.IsAny<bool>(),
                It.IsAny<bool>(),
                It.IsAny<bool>()
            )).ReturnsAsync((ECUDevice ecu, string desc, string cat, List<string> tags, bool includeEEPROM, bool includeMCU, bool includeParams) =>
            {
                // Create a new backup with a unique ID
                return new BackupData
                {
                    Id = Guid.NewGuid().ToString(),
                    ECUId = ecu.Id,
                    ECUName = ecu.Name,
                    ECUSerialNumber = ecu.SerialNumber,
                    ECUHardwareVersion = ecu.HardwareVersion,
                    ECUSoftwareVersion = ecu.SoftwareVersion,
                    Description = desc,
                    Category = cat,
                    Tags = tags ?? new List<string>(),
                    CreationTime = DateTime.Now,
                    LastModifiedTime = DateTime.Now,
                    CreatedBy = Environment.UserName,
                    LastModifiedBy = Environment.UserName,
                    Version = 1,
                    IsComplete = true
                };
            });

            // Initialize the scheduler service
            _schedulerService.InitializeAsync(_mockBackupService.Object, _mockEcuService.Object).Wait();
        }

        [TearDown]
        public void TearDown()
        {
            // Stop the scheduler if it's running
            if (_schedulerService.IsRunning)
            {
                _schedulerService.StopAsync().Wait();
            }

            // Clean up the test directory
            if (Directory.Exists(_testSchedulesDirectory))
            {
                Directory.Delete(_testSchedulesDirectory, true);
            }
        }

        [Test]
        public async Task DailyRetentionPolicy_KeepsOneBackupPerDay()
        {
            // Arrange
            var schedule = new BackupSchedule
            {
                Name = "Daily Backup",
                Description = "Test Daily Backup",
                ECUId = "ecu1",
                ECUName = "Test ECU 1",
                ScheduleType = "Standard",
                FrequencyType = BackupFrequencyType.Daily,
                Interval = 1,
                TimeOfDay = new TimeSpan(3, 0, 0), // 3:00 AM
                MaxBackupsToKeep = 5,
                IsEnabled = true,
                IncludeEEPROM = true,
                IncludeMicrocontrollerCode = true,
                IncludeParameters = true
            };

            // Add all test backup IDs to the schedule
            schedule.CreatedBackupIds.AddRange(_testBackups.Select(b => b.Id));

            // Set up the backup service to delete backups
            var deletedBackups = new List<BackupData>();
            _mockBackupService.Setup(s => s.DeleteBackupAsync(It.IsAny<BackupData>()))
                .Callback<BackupData>(b => deletedBackups.Add(b))
                .ReturnsAsync(true);

            // Act
            var createdSchedule = await _schedulerService.CreateScheduleAsync(schedule);
            var backup = await _schedulerService.ExecuteScheduleNowAsync(createdSchedule.Id);

            // Assert
            Assert.That(backup, Is.Not.Null);

            // Verify the backup service was called to get all backups
            _mockBackupService.Verify(s => s.GetAllBackupsAsync(), Times.AtLeastOnce);

            // Verify the backup service was called to delete backups
            // We expect it to keep 5 backups (one per day for the last 5 days)
            // Our test data has 15 backups, so we expect 10 to be deleted
            Assert.That(deletedBackups.Count, Is.GreaterThan(0));

            // Verify that the deleted backups are the oldest ones
            var oldestBackups = _testBackups.OrderBy(b => b.CreationTime).Take(deletedBackups.Count).ToList();
            foreach (var oldBackup in oldestBackups)
            {
                Assert.That(deletedBackups.Any(b => b.Id == oldBackup.Id), Is.True);
            }
        }

        [Test]
        public async Task WeeklyRetentionPolicy_KeepsOneBackupPerWeek()
        {
            // Arrange
            var schedule = new BackupSchedule
            {
                Name = "Weekly Backup",
                Description = "Test Weekly Backup",
                ECUId = "ecu1",
                ECUName = "Test ECU 1",
                ScheduleType = "Standard",
                FrequencyType = BackupFrequencyType.Weekly,
                Interval = 1,
                StartDayOfWeek = DayOfWeek.Monday,
                TimeOfDay = new TimeSpan(3, 0, 0), // 3:00 AM
                MaxBackupsToKeep = 4,
                IsEnabled = true,
                IncludeEEPROM = true,
                IncludeMicrocontrollerCode = true,
                IncludeParameters = true
            };

            // Add all test backup IDs to the schedule
            schedule.CreatedBackupIds.AddRange(_testBackups.Select(b => b.Id));

            // Set up the backup service to delete backups
            var deletedBackups = new List<BackupData>();
            _mockBackupService.Setup(s => s.DeleteBackupAsync(It.IsAny<BackupData>()))
                .Callback<BackupData>(b => deletedBackups.Add(b))
                .ReturnsAsync(true);

            // Act
            var createdSchedule = await _schedulerService.CreateScheduleAsync(schedule);
            var backup = await _schedulerService.ExecuteScheduleNowAsync(createdSchedule.Id);

            // Assert
            Assert.That(backup, Is.Not.Null);

            // Verify the backup service was called to delete backups
            Assert.That(deletedBackups.Count, Is.GreaterThan(0));
        }

        [Test]
        public async Task MonthlyRetentionPolicy_KeepsOneBackupPerMonth()
        {
            // Arrange
            var schedule = new BackupSchedule
            {
                Name = "Monthly Backup",
                Description = "Test Monthly Backup",
                ECUId = "ecu1",
                ECUName = "Test ECU 1",
                ScheduleType = "Standard",
                FrequencyType = BackupFrequencyType.Monthly,
                Interval = 1,
                DayOfMonth = 1,
                TimeOfDay = new TimeSpan(3, 0, 0), // 3:00 AM
                MaxBackupsToKeep = 3,
                IsEnabled = true,
                IncludeEEPROM = true,
                IncludeMicrocontrollerCode = true,
                IncludeParameters = true
            };

            // Add all test backup IDs to the schedule
            schedule.CreatedBackupIds.AddRange(_testBackups.Select(b => b.Id));

            // Set up the backup service to delete backups
            var deletedBackups = new List<BackupData>();
            _mockBackupService.Setup(s => s.DeleteBackupAsync(It.IsAny<BackupData>()))
                .Callback<BackupData>(b => deletedBackups.Add(b))
                .ReturnsAsync(true);

            // Act
            var createdSchedule = await _schedulerService.CreateScheduleAsync(schedule);
            var backup = await _schedulerService.ExecuteScheduleNowAsync(createdSchedule.Id);

            // Assert
            Assert.That(backup, Is.Not.Null);

            // Verify the backup service was called to delete backups
            Assert.That(deletedBackups.Count, Is.GreaterThan(0));
        }

        private List<BackupData> CreateTestBackups()
        {
            var backups = new List<BackupData>();

            // Create backups for the last 30 days (one per day)
            for (int i = 0; i < 30; i++)
            {
                var creationTime = DateTime.Now.AddDays(-i);
                backups.Add(new BackupData
                {
                    Id = Guid.NewGuid().ToString(),
                    ECUId = "ecu1",
                    ECUName = "Test ECU 1",
                    ECUSerialNumber = "SN12345",
                    ECUHardwareVersion = "HW1.0",
                    ECUSoftwareVersion = "SW1.0",
                    Description = $"Backup {i + 1}",
                    Category = "Test",
                    Tags = new List<string> { "Test" },
                    CreationTime = creationTime,
                    LastModifiedTime = creationTime,
                    CreatedBy = "Test User",
                    LastModifiedBy = "Test User",
                    Version = 1,
                    IsComplete = true
                });
            }

            return backups;
        }
    }
}

