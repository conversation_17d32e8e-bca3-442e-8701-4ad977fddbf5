using System;
using System.Collections.Generic;
using System.Text.Json;

namespace JsonTest
{
    public class BridgeVocomDevice
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string ConnectionType { get; set; } = string.Empty;
        public string ConnectionStatus { get; set; } = string.Empty;
    }

    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing JSON serialization/deserialization...");

            // Create a test device (same as in bridge service)
            var devices = new List<BridgeVocomDevice>
            {
                new BridgeVocomDevice
                {
                    Id = "BRIDGE_VOCOM_001",
                    Name = "Bridge Vocom Device (Simulated)",
                    ConnectionType = "USB",
                    ConnectionStatus = "Disconnected"
                }
            };

            // Serialize to JSON
            var jsonData = JsonSerializer.Serialize(devices);
            Console.WriteLine($"Serialized JSON: {jsonData}");

            // Try to deserialize back
            try
            {
                var deserializedDevices = JsonSerializer.Deserialize<BridgeVocomDevice[]>(jsonData);
                Console.WriteLine($"Deserialization successful! Found {deserializedDevices?.Length} devices");
                
                if (deserializedDevices != null && deserializedDevices.Length > 0)
                {
                    var device = deserializedDevices[0];
                    Console.WriteLine($"Device: Id={device.Id}, Name={device.Name}, ConnectionType={device.ConnectionType}, ConnectionStatus={device.ConnectionStatus}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Deserialization failed: {ex.Message}");
            }

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
