using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Implementation of the Vocom communication service
    /// </summary>
    public class VocomService : IVocomService
    {
        private readonly ILoggingService? _logger;
        private VocomDevice? _currentDevice = null;
        private ConnectionSettings _connectionSettings = new ConnectionSettings();
        private List<VocomDevice> _connectedDevices = new List<VocomDevice>();
        private IVocomDeviceDriver? _vocomDriver;
        private IUSBCommunicationService? _usbService;
        private IWiFiCommunicationService? _wifiService;
        private IBluetoothCommunicationService? _bluetoothService;
        private readonly EnhancedVocomDeviceDetector _enhancedDetector;

        // Enhanced services for improved connection handling
        private NativeVocomUSBCommunication? _nativeUsbService;
        private EnhancedVocomDeviceDetection? _enhancedDeviceDetection;
        private VocomConnectionRecoveryService? _connectionRecoveryService;

        /// <summary>
        /// Event triggered when a Vocom device is connected
        /// </summary>
        public event EventHandler<VocomDevice>? VocomConnected;

        /// <summary>
        /// Event triggered when a Vocom device is disconnected
        /// </summary>
        public event EventHandler<VocomDevice>? VocomDisconnected;

        /// <summary>
        /// Event triggered when an error occurs during Vocom communication
        /// </summary>
        public event EventHandler<string>? VocomError;

        /// <summary>
        /// Gets the currently connected Vocom device
        /// </summary>
        public VocomDevice? CurrentDevice => _currentDevice;

        /// <summary>
        /// Gets the current connection settings
        /// </summary>
        public ConnectionSettings ConnectionSettings => _connectionSettings;

        /// <summary>
        /// Gets the list of connected Vocom devices
        /// </summary>
        public List<VocomDevice> ConnectedDevices => _connectedDevices;

        /// <summary>
        /// Initializes a new instance of the VocomService class
        /// </summary>
        /// <param name="logger">The logging service to use</param>
        public VocomService(ILoggingService? logger)
        {
            _logger = logger;
            _enhancedDetector = new EnhancedVocomDeviceDetector(_logger);

            // Initialize events to empty handlers to avoid null reference exceptions
            VocomConnected = (sender, device) => { };
            VocomDisconnected = (sender, device) => { };
            VocomError = (sender, message) => { };
        }

        /// <summary>
        /// Initializes the Vocom service
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger?.LogInformation("Initializing Vocom service with enhanced capabilities", "VocomService");

                // Initialize enhanced services
                await InitializeEnhancedServicesAsync();

                // Check if PTT application is running and disconnect it if necessary
                if (_connectionSettings.AutoDisconnectPTT)
                {
                    bool pttDisconnected = await DisconnectPTTAsync();
                    if (!pttDisconnected)
                    {
                        _logger?.LogWarning("Failed to disconnect PTT application", "VocomService");
                    }
                }

                // Check if Bluetooth is enabled if necessary
                if (_connectionSettings.AutoCheckBluetooth)
                {
                    bool bluetoothEnabled = await IsBluetoothEnabledAsync();
                    if (!bluetoothEnabled)
                    {
                        bool enabled = await EnableBluetoothAsync();
                        if (!enabled)
                        {
                            _logger?.LogWarning("Failed to enable Bluetooth", "VocomService");
                        }
                    }
                }

                _logger?.LogInformation("Vocom service initialized successfully with enhanced capabilities", "VocomService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to initialize Vocom service", "VocomService", ex);
                VocomError?.Invoke(this, $"Initialization error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Initializes enhanced services for improved connection handling
        /// </summary>
        private async Task InitializeEnhancedServicesAsync()
        {
            try
            {
                _logger?.LogInformation("Initializing enhanced Vocom services", "VocomService");

                // Initialize native USB communication service
                if (_logger != null)
                {
                    _nativeUsbService = new NativeVocomUSBCommunication(_logger);
                    bool nativeUsbInitialized = await _nativeUsbService.InitializeAsync();
                    if (nativeUsbInitialized)
                    {
                        _logger.LogInformation("Native USB communication service initialized", "VocomService");
                    }
                    else
                    {
                        _logger.LogWarning("Failed to initialize native USB communication service", "VocomService");
                    }

                    // Initialize enhanced device detection
                    _enhancedDeviceDetection = new EnhancedVocomDeviceDetection(_logger);
                    _logger.LogInformation("Enhanced device detection service initialized", "VocomService");

                    // Initialize connection recovery service
                    if (_enhancedDeviceDetection != null)
                    {
                        _connectionRecoveryService = new VocomConnectionRecoveryService(_logger, this, _enhancedDeviceDetection);
                        _logger.LogInformation("Connection recovery service initialized", "VocomService");
                    }
                }

                _logger?.LogInformation("Enhanced services initialization completed", "VocomService");
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error initializing enhanced services", "VocomService", ex);
            }
        }

        /// <summary>
        /// Initializes the Vocom service with dependencies
        /// </summary>
        /// <param name="vocomDriver">The Vocom device driver</param>
        /// <param name="usbService">The USB communication service</param>
        /// <param name="wifiService">The WiFi communication service</param>
        /// <param name="bluetoothService">The Bluetooth communication service</param>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync(IVocomDeviceDriver vocomDriver, IUSBCommunicationService usbService, IWiFiCommunicationService wifiService, IBluetoothCommunicationService bluetoothService)
        {
            try
            {
                _logger?.LogInformation("Initializing Vocom service with dependencies", "VocomService");

                _vocomDriver = vocomDriver ?? throw new ArgumentNullException(nameof(vocomDriver));
                _usbService = usbService ?? throw new ArgumentNullException(nameof(usbService));
                _wifiService = wifiService ?? throw new ArgumentNullException(nameof(wifiService));
                _bluetoothService = bluetoothService ?? throw new ArgumentNullException(nameof(bluetoothService));

                // Initialize all communication services
                bool usbInitialized = await _usbService.InitializeAsync();
                bool wifiInitialized = await _wifiService.InitializeAsync();
                bool bluetoothInitialized = await _bluetoothService.InitializeAsync();

                if (!usbInitialized && !wifiInitialized && !bluetoothInitialized)
                {
                    _logger?.LogError("Failed to initialize any communication service", "VocomService");
                    VocomError?.Invoke(this, "Failed to initialize any communication service");
                    return false;
                }

                return await InitializeAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to initialize Vocom service with dependencies", "VocomService", ex);
                VocomError?.Invoke(this, $"Initialization error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Scans for available Vocom devices
        /// </summary>
        /// <returns>List of available Vocom devices</returns>
        public async Task<List<VocomDevice>> ScanForDevicesAsync()
        {
            try
            {
                _logger?.LogInformation("Scanning for Vocom devices with enhanced detection capabilities", "VocomService");

                List<VocomDevice> devices = new List<VocomDevice>();

                // Primary: Use new enhanced device detection service
                if (_enhancedDeviceDetection != null)
                {
                    try
                    {
                        _logger?.LogInformation("Using new enhanced device detection service", "VocomService");
                        List<VocomDevice> enhancedDevices = await _enhancedDeviceDetection.DetectVocomDevicesAsync();
                        devices.AddRange(enhancedDevices);

                        if (enhancedDevices.Count > 0)
                        {
                            _logger?.LogInformation($"Enhanced device detection found {enhancedDevices.Count} Vocom devices", "VocomService");
                            return devices; // Return immediately if we found devices with the enhanced method
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError("Error using enhanced device detection service", "VocomService", ex);
                    }
                }

                // Fallback: Use original enhanced detector for real hardware detection
                try
                {
                    _logger?.LogInformation("Using original enhanced Vocom device detector for real hardware", "VocomService");
                    List<VocomDevice> realDevices = await _enhancedDetector.DetectRealVocomDevicesAsync();
                    devices.AddRange(realDevices);

                    if (realDevices.Count > 0)
                    {
                        _logger?.LogInformation($"Original enhanced detector found {realDevices.Count} real Vocom devices", "VocomService");
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError("Error using original enhanced Vocom detector", "VocomService", ex);
                }

                // Fallback: Scan for USB devices using the USB communication service
                if (_usbService != null && devices.Count == 0)
                {
                    try
                    {
                        List<string> usbPorts = await _usbService.DetectVocomDevicesAsync();
                        foreach (string port in usbPorts)
                        {
                            VocomDevice usbDevice = new VocomDevice
                            {
                                Id = Guid.NewGuid().ToString(),
                                Name = $"Vocom - 88890300 ({port})",
                                SerialNumber = $"88890300-{port}", // Generate a unique serial number
                                ConnectionType = VocomConnectionType.USB,
                                ConnectionStatus = VocomConnectionStatus.Disconnected,
                                USBPortInfo = port
                            };
                            devices.Add(usbDevice);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError("Error detecting USB devices", "VocomService", ex);
                    }
                }

                // Final fallback: Check using connection helper
                if (devices.Count == 0)
                {
                    if (ConnectionHelper.IsUSBDeviceConnected("Vocom - 88890300"))
                    {
                        VocomDevice usbDevice = new VocomDevice
                        {
                            Id = Guid.NewGuid().ToString(),
                            Name = "Vocom - 88890300 (USB)",
                            SerialNumber = "88890300", // Example serial number
                            ConnectionType = VocomConnectionType.USB,
                            ConnectionStatus = VocomConnectionStatus.Disconnected,
                            USBPortInfo = "USB" // This would be the actual USB port in a real implementation
                        };
                        devices.Add(usbDevice);
                    }
                }

                // Scan for Bluetooth devices (placeholder implementation)
                if (await IsBluetoothEnabledAsync())
                {
                    // In a real implementation, this would scan for Bluetooth devices
                    // For now, we'll just add a placeholder device
                    VocomDevice btDevice = new VocomDevice
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = "Vocom - 88890300 (Bluetooth)",
                        SerialNumber = "88890300-BT", // Example serial number
                        ConnectionType = VocomConnectionType.Bluetooth,
                        ConnectionStatus = VocomConnectionStatus.Disconnected,
                        BluetoothAddress = "00:11:22:33:44:55" // Example Bluetooth address
                    };
                    devices.Add(btDevice);
                }

                // Scan for WiFi devices (placeholder implementation)
                if (_connectionSettings.UseWiFiFallback && await IsWiFiAvailableAsync())
                {
                    // In a real implementation, this would scan for WiFi devices
                    // For now, we'll just add a placeholder device
                    VocomDevice wifiDevice = new VocomDevice
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = "Vocom - 88890300 (WiFi)",
                        SerialNumber = "88890300-WiFi", // Example serial number
                        ConnectionType = VocomConnectionType.WiFi,
                        ConnectionStatus = VocomConnectionStatus.Disconnected,
                        WiFiIPAddress = "*************" // Example WiFi IP address
                    };
                    devices.Add(wifiDevice);
                }

                _logger?.LogInformation($"Found {devices.Count} Vocom devices", "VocomService");
                return devices;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to scan for Vocom devices", "VocomService", ex);
                VocomError?.Invoke(this, $"Scan error: {ex.Message}");
                return new List<VocomDevice>();
            }
        }

        /// <summary>
        /// Connects to a Vocom device
        /// </summary>
        /// <param name="device">The device to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        public async Task<bool> ConnectAsync(VocomDevice device)
        {
            if (device == null)
            {
                _logger?.LogError("Cannot connect to null device", "VocomService");
                VocomError?.Invoke(this, "Cannot connect to null device");
                return false;
            }

            try
            {
                _logger?.LogInformation($"Connecting to Vocom device {device.SerialNumber} via {device.ConnectionType}", "VocomService");

                // Disconnect from current device if connected
                if (_currentDevice != null && _currentDevice.ConnectionStatus == VocomConnectionStatus.Connected)
                {
                    await DisconnectAsync();
                }

                // Check if PTT application is running and disconnect it if necessary
                if (_connectionSettings.AutoDisconnectPTT)
                {
                    bool pttDisconnected = await DisconnectPTTAsync();
                    if (!pttDisconnected)
                    {
                        _logger?.LogWarning("Failed to disconnect PTT application, connection may fail", "VocomService");
                        // Continue anyway, as the connection might still succeed
                    }
                }

                // Update device status
                device.ConnectionStatus = VocomConnectionStatus.Connecting;

                // Perform connection based on connection type
                bool connected = false;
                switch (device.ConnectionType)
                {
                    case VocomConnectionType.USB:
                        connected = await ConnectViaUSBAsync(device);
                        break;
                    case VocomConnectionType.Bluetooth:
                        connected = await ConnectViaBluetoothAsync(device);
                        break;
                    case VocomConnectionType.WiFi:
                        connected = await ConnectViaWiFiAsync(device);
                        break;
                    default:
                        _logger?.LogError($"Unsupported connection type: {device.ConnectionType}", "VocomService");
                        VocomError?.Invoke(this, $"Unsupported connection type: {device.ConnectionType}");
                        return false;
                }

                if (connected)
                {
                    // Update device status and set as current device
                    device.ConnectionStatus = VocomConnectionStatus.Connected;
                    device.LastConnectionTime = DateTime.Now;
                    _currentDevice = device;

                    _logger?.LogInformation($"Connected to Vocom device {device.SerialNumber} via {device.ConnectionType}", "VocomService");
                    VocomConnected?.Invoke(this, device);
                    return true;
                }
                else
                {
                    device.ConnectionStatus = VocomConnectionStatus.Error;
                    _logger?.LogError($"Failed to connect to Vocom device {device.SerialNumber} via {device.ConnectionType}", "VocomService");
                    VocomError?.Invoke(this, $"Failed to connect to Vocom device {device.SerialNumber} via {device.ConnectionType}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                device.ConnectionStatus = VocomConnectionStatus.Error;
                _logger?.LogError($"Error connecting to Vocom device {device.SerialNumber}", "VocomService", ex);
                VocomError?.Invoke(this, $"Connection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disconnects from the currently connected Vocom device
        /// </summary>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public async Task<bool> DisconnectAsync()
        {
            if (_currentDevice == null || _currentDevice.ConnectionStatus != VocomConnectionStatus.Connected)
            {
                _logger?.LogInformation("No device connected to disconnect", "VocomService");
                return true;
            }

            try
            {
                _logger?.LogInformation($"Disconnecting from Vocom device {_currentDevice.SerialNumber}", "VocomService");

                // Perform disconnection based on connection type
                bool disconnected = false;
                switch (_currentDevice.ConnectionType)
                {
                    case VocomConnectionType.USB:
                        disconnected = await DisconnectFromUSBAsync(_currentDevice);
                        break;
                    case VocomConnectionType.Bluetooth:
                        disconnected = await DisconnectFromBluetoothAsync(_currentDevice);
                        break;
                    case VocomConnectionType.WiFi:
                        disconnected = await DisconnectFromWiFiAsync(_currentDevice);
                        break;
                    default:
                        _logger?.LogError($"Unsupported connection type: {_currentDevice.ConnectionType}", "VocomService");
                        VocomError?.Invoke(this, $"Unsupported connection type: {_currentDevice.ConnectionType}");
                        return false;
                }

                if (disconnected)
                {
                    VocomDevice disconnectedDevice = _currentDevice;
                    disconnectedDevice.ConnectionStatus = VocomConnectionStatus.Disconnected;
                    _currentDevice = null;

                    _logger?.LogInformation($"Disconnected from Vocom device {disconnectedDevice.SerialNumber}", "VocomService");
                    VocomDisconnected?.Invoke(this, disconnectedDevice);
                    return true;
                }
                else
                {
                    _currentDevice.ConnectionStatus = VocomConnectionStatus.Error;
                    _logger?.LogError($"Failed to disconnect from Vocom device {_currentDevice.SerialNumber}", "VocomService");
                    VocomError?.Invoke(this, $"Failed to disconnect from Vocom device {_currentDevice.SerialNumber}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                if (_currentDevice != null)
                {
                    _currentDevice.ConnectionStatus = VocomConnectionStatus.Error;
                    _logger?.LogError($"Error disconnecting from Vocom device {_currentDevice.SerialNumber}", "VocomService", ex);
                }
                else
                {
                    _logger?.LogError("Error disconnecting from Vocom device (device is null)", "VocomService", ex);
                }
                VocomError?.Invoke(this, $"Disconnection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if PTT application is running and disconnects it if necessary
        /// </summary>
        /// <returns>True if PTT is successfully disconnected or not running, false otherwise</returns>
        public async Task<bool> DisconnectPTTAsync()
        {
            try
            {
                _logger?.LogInformation("Checking if PTT application is running", "VocomService");

                // Check if PTT process is running using the enhanced method
                bool isPTTRunning = ConnectionHelper.IsPTTApplicationRunning();
                if (!isPTTRunning)
                {
                    _logger?.LogInformation("PTT application is not running", "VocomService");
                    return true;
                }

                _logger?.LogInformation("PTT application is running, attempting to disconnect", "VocomService");

                // First try using the VocomNativeInterop if available
                if (_vocomDriver != null && _vocomDriver is VocomDriver vocomDriver)
                {
                    _logger?.LogInformation("Attempting to disconnect PTT using VocomNativeInterop", "VocomService");

                    // Get the native interop from the driver
                    var nativeInterop = vocomDriver.GetNativeInterop();
                    if (nativeInterop != null)
                    {
                        bool nativeDisconnected = await nativeInterop.DisconnectPTTAsync();
                        if (nativeDisconnected)
                        {
                            _logger?.LogInformation("PTT application successfully disconnected using VocomNativeInterop", "VocomService");

                            // Verify that PTT is actually disconnected
                            await Task.Delay(500); // Give it a moment to close
                            bool stillRunning = ConnectionHelper.IsPTTApplicationRunning();
                            if (!stillRunning)
                            {
                                return true;
                            }
                            else
                            {
                                _logger?.LogWarning("Native PTT disconnection reported success but PTT is still running, trying alternative methods", "VocomService");
                            }
                        }
                        else
                        {
                            _logger?.LogWarning("Failed to disconnect PTT using VocomNativeInterop, trying alternative methods", "VocomService");
                        }
                    }
                }

                // Use the enhanced PTT disconnection method
                bool disconnected = await ConnectionHelper.DisconnectPTTApplicationAsync();
                if (disconnected)
                {
                    _logger?.LogInformation("PTT application successfully disconnected using ConnectionHelper", "VocomService");
                    return true;
                }
                else
                {
                    _logger?.LogWarning("Failed to disconnect PTT application using standard methods", "VocomService");

                    // As a fallback, try using the Vocom driver to terminate PTT
                    if (_vocomDriver != null)
                    {
                        _logger?.LogInformation("Attempting to terminate PTT using Vocom driver", "VocomService");
                        bool driverDisconnected = await _vocomDriver.TerminatePTTAsync();

                        if (driverDisconnected)
                        {
                            _logger?.LogInformation("PTT application successfully terminated using Vocom driver", "VocomService");
                            return true;
                        }
                    }

                    // If all methods fail, try to forcefully terminate the process
                    _logger?.LogWarning("Standard methods failed, attempting to forcefully terminate PTT process", "VocomService");
                    bool forceTerminated = ConnectionHelper.ForceTerminatePTTProcess();
                    if (forceTerminated)
                    {
                        _logger?.LogInformation("PTT application forcefully terminated", "VocomService");
                        return true;
                    }

                    // If all methods fail, notify the user
                    _logger?.LogWarning("All PTT disconnection methods failed", "VocomService");
                    VocomError?.Invoke(this, "Failed to disconnect PTT application. Please close it manually before proceeding.");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error disconnecting PTT application", "VocomService", ex);
                VocomError?.Invoke(this, $"PTT disconnection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Attempts to find and save information about the PTT application
        /// </summary>
        /// <returns>True if information was found and saved, false otherwise</returns>
        private async Task<bool> FindAndSavePTTInfoAsync()
        {
            try
            {
                _logger?.LogInformation("Attempting to find PTT application information", "VocomService");

                // Find the PTT installation path
                string pttPath = ConnectionHelper.FindPTTInstallationPath();
                if (!string.IsNullOrEmpty(pttPath))
                {
                    _logger?.LogInformation($"Found PTT installation path: {pttPath}", "VocomService");

                    // Save the path for future use
                    string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
                    if (!Directory.Exists(configPath))
                    {
                        Directory.CreateDirectory(configPath);
                    }

                    string pttInfoPath = Path.Combine(configPath, "ptt_info.txt");
                    await File.WriteAllTextAsync(pttInfoPath, pttPath);

                    _logger?.LogInformation("Saved PTT information for future use", "VocomService");
                    return true;
                }
                else
                {
                    _logger?.LogWarning("Could not find PTT installation path", "VocomService");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error finding and saving PTT information", "VocomService", ex);
                return false;
            }
        }

        /// <summary>
        /// Checks if Bluetooth is enabled
        /// </summary>
        /// <returns>True if Bluetooth is enabled, false otherwise</returns>
        public async Task<bool> IsBluetoothEnabledAsync()
        {
            try
            {
                _logger?.LogDebug("Checking if Bluetooth is enabled", "VocomService");

                // Run the Bluetooth check on a background thread to avoid blocking
                bool isEnabled = await Task.Run(() => ConnectionHelper.IsBluetoothEnabled());

                _logger?.LogDebug($"Bluetooth is {(isEnabled ? "enabled" : "disabled")}", "VocomService");
                return isEnabled;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error checking Bluetooth status", "VocomService", ex);
                VocomError?.Invoke(this, $"Bluetooth check error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Enables Bluetooth if it is disabled
        /// </summary>
        /// <returns>True if Bluetooth is successfully enabled, false otherwise</returns>
        public async Task<bool> EnableBluetoothAsync()
        {
            try
            {
                _logger?.LogInformation("Attempting to enable Bluetooth", "VocomService");
                bool enabled = ConnectionHelper.EnableBluetooth();
                if (enabled)
                {
                    _logger?.LogInformation("Bluetooth successfully enabled", "VocomService");
                    return true;
                }
                else
                {
                    _logger?.LogWarning("Failed to enable Bluetooth", "VocomService");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error enabling Bluetooth", "VocomService", ex);
                VocomError?.Invoke(this, $"Bluetooth enable error: {ex.Message}");
                return false;
            }
        }



        /// <summary>
        /// Checks if the connected ECU is a MC9S12XEP100 microcontroller
        /// </summary>
        /// <param name="ecuId">The ID of the ECU to check</param>
        /// <returns>True if the ECU is a MC9S12XEP100, false otherwise</returns>
        public async Task<bool> IsMC9S12XEP100ECUAsync(string ecuId)
        {
            try
            {
                _logger?.LogInformation($"Checking if ECU {ecuId} is a MC9S12XEP100 microcontroller", "VocomService");

                if (_currentDevice == null || _currentDevice.ConnectionStatus != VocomConnectionStatus.Connected)
                {
                    _logger?.LogWarning("No Vocom device connected", "VocomService");
                    return false;
                }

                // In a real implementation, this would query the ECU for its microcontroller type
                // For now, we'll just simulate this by checking if the ECU ID contains "MC9S12XEP100"
                bool isMC9S12XEP100 = ecuId.Contains("MC9S12XEP100", StringComparison.OrdinalIgnoreCase);

                // If not found by ID, we could also check by reading specific registers that are unique to MC9S12XEP100
                if (!isMC9S12XEP100)
                {
                    // Simulate reading the PARTID register (0x001A) which contains the part identification number
                    // For MC9S12XEP100, this would be 0x12 (family) and 0xEP (part)
                    await Task.Delay(100);

                    // For simulation purposes, we'll assume any ECU with "EMS" in the ID is a MC9S12XEP100
                    isMC9S12XEP100 = ecuId.Contains("EMS", StringComparison.OrdinalIgnoreCase);

                    // If still not identified, check for other common ECUs that typically use MC9S12XEP100
                    if (!isMC9S12XEP100)
                    {
                        // Check for TCM (Transmission Control Module) which often uses MC9S12XEP100
                        if (ecuId.Contains("TCM", StringComparison.OrdinalIgnoreCase))
                        {
                            isMC9S12XEP100 = true;
                        }
                        // Check for BCM (Body Control Module) which often uses MC9S12XEP100
                        else if (ecuId.Contains("BCM", StringComparison.OrdinalIgnoreCase))
                        {
                            isMC9S12XEP100 = true;
                        }
                        // Check for ABS (Anti-lock Braking System) which often uses MC9S12XEP100
                        else if (ecuId.Contains("ABS", StringComparison.OrdinalIgnoreCase))
                        {
                            isMC9S12XEP100 = true;
                        }
                    }
                }

                _logger?.LogInformation($"ECU {ecuId} is {(isMC9S12XEP100 ? "a" : "not a")} MC9S12XEP100 microcontroller", "VocomService");
                return isMC9S12XEP100;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error checking if ECU {ecuId} is a MC9S12XEP100 microcontroller", "VocomService", ex);
                VocomError?.Invoke(this, $"ECU check error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reads the MC9S12XEP100 specific registers from an ECU
        /// </summary>
        /// <param name="ecuId">The ID of the ECU to read from</param>
        /// <returns>Dictionary of register names and values</returns>
        public async Task<Dictionary<string, object>> ReadMC9S12XEP100RegistersAsync(string ecuId)
        {
            try
            {
                _logger?.LogInformation($"Reading MC9S12XEP100 specific registers from ECU {ecuId}", "VocomService");

                if (_currentDevice == null || _currentDevice.ConnectionStatus != VocomConnectionStatus.Connected)
                {
                    _logger?.LogWarning("No Vocom device connected", "VocomService");
                    return new Dictionary<string, object>();
                }

                // In a real implementation, this would read specific registers from the MC9S12XEP100 microcontroller
                // For now, we'll just simulate this by returning some example register values
                Dictionary<string, object> registers = new Dictionary<string, object>();

                // Simulate reading registers with a delay
                await Task.Delay(200);

                // Core registers
                registers["PARTID"] = 0x12E0; // Part identification register
                registers["CPUID"] = 0x0100; // CPU identification register
                registers["MEMSIZ0"] = 0x3000; // Memory size register 0 (Flash = 768KB)
                registers["MEMSIZ1"] = 0x0C00; // Memory size register 1 (RAM = 48KB)

                // Flash memory registers
                registers["FLASH_PROT"] = 0x0000; // Flash protection register (0 = unprotected)
                registers["FLASH_ECC_CTRL"] = 0x0001; // Flash ECC control register (1 = enabled)
                registers["FLASH_ECC_ERR"] = 0x0000; // Flash ECC error register (0 = no errors)

                // CAN registers
                registers["CAN0_CTL0"] = 0x0000; // CAN0 control register 0
                registers["CAN0_CTL1"] = 0x0080; // CAN0 control register 1 (CAN enabled)
                registers["CAN0_BTR0"] = 0x0001; // CAN0 bus timing register 0 (500 kbps)
                registers["CAN0_BTR1"] = 0x001C; // CAN0 bus timing register 1

                // SPI registers
                registers["SPI0_CR1"] = 0x0050; // SPI0 control register 1 (SPI enabled, master mode)
                registers["SPI0_CR2"] = 0x0000; // SPI0 control register 2
                registers["SPI0_BR"] = 0x0002; // SPI0 baud rate register (1 MHz)

                // SCI registers
                registers["SCI0_CR1"] = 0x0000; // SCI0 control register 1
                registers["SCI0_CR2"] = 0x000C; // SCI0 control register 2 (receiver and transmitter enabled)
                registers["SCI0_BDH"] = 0x0000; // SCI0 baud rate register high (115.2 kbps)
                registers["SCI0_BDL"] = 0x001A; // SCI0 baud rate register low

                // IIC registers
                registers["IIC0_IBCR"] = 0x0080; // IIC0 control register (IIC enabled)
                registers["IIC0_IBFD"] = 0x0001; // IIC0 frequency divider register (400 kHz)

                _logger?.LogInformation($"Read {registers.Count} MC9S12XEP100 specific registers from ECU {ecuId}", "VocomService");
                return registers;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error reading MC9S12XEP100 specific registers from ECU {ecuId}", "VocomService", ex);
                VocomError?.Invoke(this, $"Register read error: {ex.Message}");
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// Sends a CAN frame to an ECU
        /// </summary>
        /// <param name="device">The Vocom device to use</param>
        /// <param name="canId">The CAN ID to use</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseTimeout">The timeout for waiting for a response in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendCANFrameAsync(VocomDevice device, uint canId, byte[] data, int responseTimeout)
        {
            try
            {
                _logger?.LogInformation($"Sending CAN frame with ID 0x{canId:X} and {data?.Length ?? 0} bytes of data", "VocomService");

                if (device == null || device.ConnectionStatus != VocomConnectionStatus.Connected)
                {
                    _logger?.LogWarning("No Vocom device connected", "VocomService");
                    return Array.Empty<byte>();
                }

                if (data == null || data.Length == 0)
                {
                    _logger?.LogError("CAN frame data is null or empty", "VocomService");
                    VocomError?.Invoke(this, "CAN frame data is null or empty");
                    return Array.Empty<byte>();
                }

                if (data.Length > 8)
                {
                    _logger?.LogError($"CAN frame data length ({data.Length} bytes) exceeds maximum length (8 bytes)", "VocomService");
                    VocomError?.Invoke(this, $"CAN frame data length ({data.Length} bytes) exceeds maximum length (8 bytes)");
                    return Array.Empty<byte>();
                }

                // In a real implementation, this would send a CAN frame and wait for a response
                // For now, we'll just simulate this by returning a simulated response

                // Simulate sending the CAN frame with a delay
                await Task.Delay(50);

                // Generate a simulated response based on the command in the data
                byte[] response;
                if (data.Length > 0)
                {
                    byte command = data[0];
                    switch (command)
                    {
                        case 0x22: // Read data by identifier
                            response = new byte[] { 0x00, 0x62, 0x01, 0x02, 0x03, 0x04 }; // Positive response to 0x22
                            break;
                        case 0x23: // Read memory by address
                            response = new byte[] { 0x00, 0x63, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 }; // Positive response to 0x23
                            break;
                        case 0x3C: // Write memory by address (flash)
                            response = new byte[] { 0x00, 0x7C }; // Positive response to 0x3C
                            break;
                        case 0x3D: // Write memory by address (EEPROM)
                            response = new byte[] { 0x00, 0x7D }; // Positive response to 0x3D
                            break;
                        case 0x10: // Diagnostic session control
                            response = new byte[] { 0x00, 0x50, 0x01 }; // Positive response to 0x10
                            break;
                        case 0x11: // ECU reset
                            response = new byte[] { 0x00, 0x51, 0x01 }; // Positive response to 0x11
                            break;
                        case 0x14: // Clear diagnostic information
                            response = new byte[] { 0x00, 0x54 }; // Positive response to 0x14
                            break;
                        case 0x19: // Read DTC information
                            response = new byte[] { 0x00, 0x59, 0x01, 0x02, 0x03, 0x04 }; // Positive response to 0x19
                            break;
                        case 0x27: // Security access
                            response = new byte[] { 0x00, 0x67, 0x01, 0x02, 0x03, 0x04 }; // Positive response to 0x27
                            break;
                        case 0x2E: // Write data by identifier
                            response = new byte[] { 0x00, 0x6E, 0x01 }; // Positive response to 0x2E
                            break;
                        case 0x2F: // Input/output control by identifier
                            response = new byte[] { 0x00, 0x6F, 0x01 }; // Positive response to 0x2F
                            break;
                        case 0x31: // Routine control
                            response = new byte[] { 0x00, 0x71, 0x01 }; // Positive response to 0x31
                            break;
                        case 0x3E: // Tester present
                            response = new byte[] { 0x00, 0x7E }; // Positive response to 0x3E
                            break;
                        default:
                            response = new byte[] { 0x00, 0x7F, command, 0x11 }; // Negative response (service not supported)
                            break;
                    }
                }
                else
                {
                    response = new byte[] { 0x00, 0x7F, 0x00, 0x11 }; // Negative response (service not supported)
                }

                _logger?.LogInformation($"Received CAN frame response with {response.Length} bytes of data", "VocomService");
                return response;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error sending CAN frame", "VocomService", ex);
                VocomError?.Invoke(this, $"CAN frame error: {ex.Message}");
                return Array.Empty<byte>();
            }
        }

        /// <summary>
        /// Checks if WiFi is available for connection
        /// </summary>
        /// <returns>True if WiFi is available, false otherwise</returns>
        public async Task<bool> IsWiFiAvailableAsync()
        {
            try
            {
                _logger?.LogDebug("Checking if WiFi is available", "VocomService");

                // Run the WiFi check on a background thread to avoid blocking
                bool isAvailable = await Task.Run(() => ConnectionHelper.IsWiFiAvailable());

                _logger?.LogDebug($"WiFi is {(isAvailable ? "available" : "not available")}", "VocomService");
                return isAvailable;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error checking WiFi availability", "VocomService", ex);
                VocomError?.Invoke(this, $"WiFi check error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Detects available Vocom devices using the Vocom driver
        /// </summary>
        /// <returns>List of detected Vocom devices</returns>
        public async Task<List<VocomDevice>> DetectVocomDevicesAsync()
        {
            try
            {
                _logger?.LogInformation("Detecting Vocom devices using driver", "VocomService");

                if (_vocomDriver == null)
                {
                    _logger?.LogError("Vocom driver is not initialized", "VocomService");
                    VocomError?.Invoke(this, "Vocom driver is not initialized");
                    return new List<VocomDevice>();
                }

                List<VocomDevice> devices = await _vocomDriver.DetectDevicesAsync();

                _logger?.LogInformation($"Detected {devices.Count} Vocom devices", "VocomService");
                return devices;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error detecting Vocom devices", "VocomService", ex);
                VocomError?.Invoke(this, $"Detection error: {ex.Message}");
                return new List<VocomDevice>();
            }
        }

        /// <summary>
        /// Connects to a Vocom device using the Vocom driver
        /// </summary>
        /// <param name="device">The device to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        public async Task<bool> ConnectToVocomDeviceAsync(VocomDevice device)
        {
            try
            {
                _logger?.LogInformation($"Connecting to Vocom device {device?.Name} using driver", "VocomService");

                if (_vocomDriver == null)
                {
                    _logger?.LogError("Vocom driver is not initialized", "VocomService");
                    VocomError?.Invoke(this, "Vocom driver is not initialized");
                    return false;
                }

                if (device == null)
                {
                    _logger?.LogError("Cannot connect to null device", "VocomService");
                    VocomError?.Invoke(this, "Cannot connect to null device");
                    return false;
                }

                bool connected = await _vocomDriver.ConnectToDeviceAsync(device);

                if (connected)
                {
                    device.ConnectionStatus = VocomConnectionStatus.Connected;
                    device.LastConnectionTime = DateTime.Now;
                    _currentDevice = device;

                    if (!_connectedDevices.Contains(device))
                    {
                        _connectedDevices.Add(device);
                    }

                    _logger?.LogInformation($"Connected to Vocom device {device.Name}", "VocomService");
                    VocomConnected?.Invoke(this, device);
                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to connect to Vocom device {device.Name}", "VocomService");
                    VocomError?.Invoke(this, $"Failed to connect to Vocom device {device.Name}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error connecting to Vocom device {device?.Name}", "VocomService", ex);
                VocomError?.Invoke(this, $"Connection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disconnects from a Vocom device using the Vocom driver
        /// </summary>
        /// <param name="device">The device to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public async Task<bool> DisconnectFromVocomDeviceAsync(VocomDevice device)
        {
            try
            {
                _logger?.LogInformation($"Disconnecting from Vocom device {device?.Name} using driver", "VocomService");

                if (_vocomDriver == null)
                {
                    _logger?.LogError("Vocom driver is not initialized", "VocomService");
                    VocomError?.Invoke(this, "Vocom driver is not initialized");
                    return false;
                }

                if (device == null)
                {
                    _logger?.LogError("Cannot disconnect from null device", "VocomService");
                    VocomError?.Invoke(this, "Cannot disconnect from null device");
                    return false;
                }

                bool disconnected = await _vocomDriver.DisconnectFromDeviceAsync(device);

                if (disconnected)
                {
                    device.ConnectionStatus = VocomConnectionStatus.Disconnected;

                    if (_connectedDevices.Contains(device))
                    {
                        _connectedDevices.Remove(device);
                    }

                    if (_currentDevice == device)
                    {
                        _currentDevice = null;
                    }

                    _logger?.LogInformation($"Disconnected from Vocom device {device.Name}", "VocomService");
                    VocomDisconnected?.Invoke(this, device);
                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to disconnect from Vocom device {device.Name}", "VocomService");
                    VocomError?.Invoke(this, $"Failed to disconnect from Vocom device {device.Name}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error disconnecting from Vocom device {device?.Name}", "VocomService", ex);
                VocomError?.Invoke(this, $"Disconnection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Connects to a Vocom device with fallback to other connection types
        /// </summary>
        /// <param name="device">The device to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        public async Task<bool> ConnectToVocomDeviceWithFallbackAsync(VocomDevice device)
        {
            try
            {
                _logger?.LogInformation($"Connecting to Vocom device {device?.Name} with fallback", "VocomService");

                if (device == null)
                {
                    _logger?.LogError("Cannot connect to null device", "VocomService");
                    VocomError?.Invoke(this, "Cannot connect to null device");
                    return false;
                }

                // Try primary connection type first
                VocomConnectionType originalType = device.ConnectionType;
                bool connected = false;

                // Try USB first
                if (originalType == VocomConnectionType.USB)
                {
                    _logger?.LogInformation($"Attempting USB connection to {device.Name}", "VocomService");

                    if (_vocomDriver != null)
                    {
                        connected = await _vocomDriver.ConnectToDeviceAsync(device);
                    }
                    else
                    {
                        connected = await ConnectViaUSBAsync(device);
                    }

                    if (connected)
                    {
                        _logger?.LogInformation($"USB connection to {device.Name} successful", "VocomService");
                        return await FinalizeConnectionAsync(device);
                    }
                }

                // Try WiFi as fallback
                if (!connected && !string.IsNullOrEmpty(device.IPAddress))
                {
                    _logger?.LogInformation($"USB connection failed, attempting WiFi connection to {device.Name}", "VocomService");
                    device.ConnectionType = VocomConnectionType.WiFi;

                    if (_wifiService != null)
                    {
                        connected = await _wifiService.ConnectToDeviceAsync(device.IPAddress, _connectionSettings.ConnectionTimeoutMs);
                    }
                    else
                    {
                        connected = await ConnectViaWiFiAsync(device);
                    }

                    if (connected)
                    {
                        _logger?.LogInformation($"WiFi connection to {device.Name} successful", "VocomService");
                        return await FinalizeConnectionAsync(device);
                    }
                }

                // Try Bluetooth as final fallback
                if (!connected && !string.IsNullOrEmpty(device.BluetoothAddress))
                {
                    _logger?.LogInformation($"WiFi connection failed, attempting Bluetooth connection to {device.Name}", "VocomService");
                    device.ConnectionType = VocomConnectionType.Bluetooth;

                    if (_bluetoothService != null)
                    {
                        connected = await _bluetoothService.ConnectToDeviceAsync(device.BluetoothAddress);
                    }
                    else
                    {
                        connected = await ConnectViaBluetoothAsync(device);
                    }

                    if (connected)
                    {
                        _logger?.LogInformation($"Bluetooth connection to {device.Name} successful", "VocomService");
                        return await FinalizeConnectionAsync(device);
                    }
                }

                // If all connection attempts failed, restore original connection type
                if (!connected)
                {
                    device.ConnectionType = originalType;
                    _logger?.LogError($"All connection attempts to {device.Name} failed", "VocomService");
                    VocomError?.Invoke(this, $"All connection attempts to {device.Name} failed");
                    return false;
                }

                return connected;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error connecting to Vocom device {device?.Name} with fallback", "VocomService", ex);
                VocomError?.Invoke(this, $"Connection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Finalizes a successful connection
        /// </summary>
        /// <param name="device">The connected device</param>
        /// <returns>True if finalization is successful, false otherwise</returns>
        private async Task<bool> FinalizeConnectionAsync(VocomDevice device)
        {
            device.ConnectionStatus = VocomConnectionStatus.Connected;
            device.LastConnectionTime = DateTime.Now;
            _currentDevice = device;

            if (!_connectedDevices.Contains(device))
            {
                _connectedDevices.Add(device);
            }

            VocomConnected?.Invoke(this, device);
            return true;
        }

        /// <summary>
        /// Updates the firmware of a Vocom device
        /// </summary>
        /// <param name="device">The device to update</param>
        /// <param name="firmwareData">The firmware data</param>
        /// <returns>True if update is successful, false otherwise</returns>
        public async Task<bool> UpdateVocomFirmwareAsync(VocomDevice device, byte[] firmwareData)
        {
            try
            {
                _logger?.LogInformation($"Updating firmware for Vocom device {device?.Name}", "VocomService");

                if (_vocomDriver == null)
                {
                    _logger?.LogError("Vocom driver is not initialized", "VocomService");
                    VocomError?.Invoke(this, "Vocom driver is not initialized");
                    return false;
                }

                if (device == null)
                {
                    _logger?.LogError("Cannot update firmware for null device", "VocomService");
                    VocomError?.Invoke(this, "Cannot update firmware for null device");
                    return false;
                }

                if (firmwareData == null || firmwareData.Length == 0)
                {
                    _logger?.LogError("Firmware data is null or empty", "VocomService");
                    VocomError?.Invoke(this, "Firmware data is null or empty");
                    return false;
                }

                bool updated = await _vocomDriver.UpdateFirmwareAsync(device, firmwareData);

                if (updated)
                {
                    _logger?.LogInformation($"Firmware updated successfully for {device.Name}", "VocomService");
                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to update firmware for {device.Name}", "VocomService");
                    VocomError?.Invoke(this, $"Failed to update firmware for {device.Name}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error updating firmware for Vocom device {device?.Name}", "VocomService", ex);
                VocomError?.Invoke(this, $"Firmware update error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Updates the connection settings
        /// </summary>
        /// <param name="settings">The new connection settings</param>
        public void UpdateConnectionSettings(ConnectionSettings settings)
        {
            if (settings == null)
            {
                _logger?.LogError("Cannot update connection settings with null value", "VocomService");
                VocomError?.Invoke(this, "Cannot update connection settings with null value");
                return;
            }

            _connectionSettings = settings;
            _logger?.LogInformation("Connection settings updated", "VocomService");
        }

        /// <summary>
        /// Connects to the first available Vocom device
        /// </summary>
        /// <returns>True if connection is successful, false otherwise</returns>
        public async Task<bool> ConnectToFirstAvailableDeviceAsync()
        {
            try
            {
                _logger?.LogInformation("Attempting to connect to the first available Vocom device", "VocomService");

                // Scan for available devices
                List<VocomDevice> devices = await ScanForDevicesAsync();
                if (devices == null || devices.Count == 0)
                {
                    _logger?.LogWarning("No Vocom devices found", "VocomService");
                    VocomError?.Invoke(this, "No Vocom devices found");
                    return false;
                }

                // Try to connect to the first device
                VocomDevice device = devices[0];
                _logger?.LogInformation($"Attempting to connect to Vocom device {device.SerialNumber} via {device.ConnectionType}", "VocomService");
                bool connected = await ConnectAsync(device);
                if (connected)
                {
                    _logger?.LogInformation($"Successfully connected to Vocom device {device.SerialNumber} via {device.ConnectionType}", "VocomService");
                    return true;
                }
                else
                {
                    _logger?.LogWarning($"Failed to connect to Vocom device {device.SerialNumber} via {device.ConnectionType}", "VocomService");

                    // Try other devices if available
                    for (int i = 1; i < devices.Count; i++)
                    {
                        device = devices[i];
                        _logger?.LogInformation($"Attempting to connect to alternative Vocom device {device.SerialNumber} via {device.ConnectionType}", "VocomService");
                        connected = await ConnectAsync(device);
                        if (connected)
                        {
                            _logger?.LogInformation($"Successfully connected to alternative Vocom device {device.SerialNumber} via {device.ConnectionType}", "VocomService");
                            return true;
                        }
                        else
                        {
                            _logger?.LogWarning($"Failed to connect to alternative Vocom device {device.SerialNumber} via {device.ConnectionType}", "VocomService");
                        }
                    }

                    _logger?.LogError("Failed to connect to any Vocom device", "VocomService");
                    VocomError?.Invoke(this, "Failed to connect to any Vocom device");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error connecting to first available Vocom device", "VocomService", ex);
                VocomError?.Invoke(this, $"Connection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reconnects to the last connected Vocom device
        /// </summary>
        /// <returns>True if reconnection is successful, false otherwise</returns>
        public async Task<bool> ReconnectAsync()
        {
            try
            {
                _logger?.LogInformation("Attempting to reconnect to the last connected Vocom device", "VocomService");

                if (_currentDevice == null)
                {
                    _logger?.LogWarning("No previous device to reconnect to", "VocomService");
                    VocomError?.Invoke(this, "No previous device to reconnect to");
                    return false;
                }

                // Store the device reference before disconnecting
                var deviceToReconnect = _currentDevice;

                // Disconnect first if still connected
                if (_currentDevice.ConnectionStatus == VocomConnectionStatus.Connected)
                {
                    await DisconnectAsync();
                }

                // Reset connection status
                deviceToReconnect.ConnectionStatus = VocomConnectionStatus.Disconnected;

                // Try to reconnect
                _logger?.LogInformation($"Attempting to reconnect to Vocom device {deviceToReconnect.SerialNumber} via {deviceToReconnect.ConnectionType}", "VocomService");
                bool connected = await ConnectAsync(deviceToReconnect);
                if (connected)
                {
                    _logger?.LogInformation($"Successfully reconnected to Vocom device {deviceToReconnect.SerialNumber} via {deviceToReconnect.ConnectionType}", "VocomService");
                    return true;
                }
                else
                {
                    _logger?.LogWarning($"Failed to reconnect to Vocom device {_currentDevice.SerialNumber} via {_currentDevice.ConnectionType}", "VocomService");

                    // Try to find the device again in case it changed ports
                    List<VocomDevice> devices = await ScanForDevicesAsync();
                    VocomDevice matchingDevice = devices.FirstOrDefault(d => d.SerialNumber == _currentDevice.SerialNumber);
                    if (matchingDevice != null && matchingDevice.ConnectionType == _currentDevice.ConnectionType)
                    {
                        _logger?.LogInformation($"Found matching device, attempting to connect", "VocomService");
                        connected = await ConnectAsync(matchingDevice);
                        if (connected)
                        {
                            _logger?.LogInformation($"Successfully reconnected to Vocom device {matchingDevice.SerialNumber} via {matchingDevice.ConnectionType}", "VocomService");
                            return true;
                        }
                    }

                    _logger?.LogError($"Failed to reconnect to Vocom device {_currentDevice.SerialNumber}", "VocomService");
                    VocomError?.Invoke(this, $"Failed to reconnect to Vocom device {_currentDevice.SerialNumber}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error reconnecting to Vocom device", "VocomService", ex);
                VocomError?.Invoke(this, $"Reconnection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if PTT application is running
        /// </summary>
        /// <returns>True if PTT is running, false otherwise</returns>
        public async Task<bool> IsPTTRunningAsync()
        {
            try
            {
                _logger?.LogInformation("Checking if PTT application is running", "VocomService");

                // Run the process check on a background thread to avoid blocking
                bool isRunning = await Task.Run(() => ConnectionHelper.IsPTTApplicationRunning());

                _logger?.LogInformation($"PTT application is {(isRunning ? "running" : "not running")}", "VocomService");

                // If PTT is running and we haven't saved its info yet, try to do so
                if (isRunning)
                {
                    string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
                    string pttInfoPath = Path.Combine(configPath, "ptt_info.txt");

                    if (!File.Exists(pttInfoPath))
                    {
                        // Try to find and save PTT info in the background
                        _ = Task.Run(() => FindAndSavePTTInfoAsync());
                    }
                }

                return isRunning;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error checking if PTT application is running", "VocomService", ex);
                VocomError?.Invoke(this, $"PTT check error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Sets the connection settings for the Vocom device
        /// </summary>
        /// <param name="settings">The connection settings to apply</param>
        /// <returns>True if settings were applied successfully, false otherwise</returns>
        public async Task<bool> SetConnectionSettings(VocomConnectionSettings settings)
        {
            try
            {
                _logger?.LogInformation("Setting Vocom connection settings", "VocomService");

                if (settings == null)
                {
                    _logger?.LogError("Connection settings cannot be null", "VocomService");
                    VocomError?.Invoke(this, "Connection settings cannot be null");
                    return false;
                }

                // Convert VocomConnectionSettings to ConnectionSettings
                ConnectionSettings connectionSettings = new ConnectionSettings
                {
                    AutoCheckBluetooth = settings.AutoCheckBluetooth,
                    AutoDisconnectPTT = settings.AutoDisconnectPTT,
                    UseWiFiFallback = settings.UseWiFiFallback,
                    ConnectionTimeoutMs = settings.ConnectionTimeoutMs,
                    ConnectionRetryAttempts = settings.ConnectionRetryAttempts,
                    ConnectionRetryDelayMs = settings.ConnectionRetryDelayMs,
                    AutoReconnect = settings.AutoReconnect,
                    MaxAutoReconnectAttempts = settings.MaxAutoReconnectAttempts
                };

                // Update the connection settings
                UpdateConnectionSettings(connectionSettings);

                _logger?.LogInformation("Vocom connection settings applied successfully", "VocomService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error setting Vocom connection settings", "VocomService", ex);
                VocomError?.Invoke(this, $"Settings error: {ex.Message}");
                return false;
            }
        }

        #region Private Methods

        private async Task<bool> ConnectViaUSBAsync(VocomDevice device)
        {
            Exception? lastConnectionError = null;

            try
            {
                _logger?.LogInformation($"Connecting to Vocom device {device.SerialNumber} via USB with enhanced capabilities", "VocomService");

                // Get the USB port information
                string usbPort = device.USBPortInfo;
                if (string.IsNullOrEmpty(usbPort))
                {
                    _logger?.LogError("USB port information is missing", "VocomService");
                    VocomError?.Invoke(this, "USB port information is missing");
                    return false;
                }

                _logger?.LogInformation($"Using USB port: {usbPort}", "VocomService");

                // Check if PTT application is running and disconnect it if necessary
                if (_connectionSettings.AutoDisconnectPTT)
                {
                    bool pttDisconnected = await DisconnectPTTAsync();
                    if (!pttDisconnected)
                    {
                        _logger?.LogWarning("Failed to disconnect PTT application, USB connection may fail", "VocomService");
                        // Continue anyway, as the connection might still succeed
                    }
                }

                // Try native USB service first (for better hardware compatibility)
                if (_nativeUsbService != null)
                {
                    try
                    {
                        _logger?.LogInformation($"Attempting connection with native USB service to {usbPort}", "VocomService");

                        // Try multiple connection attempts with different approaches
                        bool nativeConnected = await TryNativeConnectionWithRetry(usbPort);

                        if (nativeConnected)
                        {
                            _logger?.LogInformation($"Successfully connected via native USB service to {device.SerialNumber}", "VocomService");
                            return true;
                        }
                        else
                        {
                            _logger?.LogWarning("Native USB connection failed, trying standard USB service", "VocomService");
                        }
                    }
                    catch (Exception ex)
                    {
                        lastConnectionError = ex;
                        _logger?.LogWarning($"Native USB connection error: {ex.Message}", "VocomService");
                    }
                }

                // Try standard USB communication service as fallback
                if (_usbService != null)
                {
                    try
                    {
                        _logger?.LogInformation($"Using standard USB communication service to connect to {usbPort}", "VocomService");

                        // Initialize the USB service if not already initialized
                        if (!await _usbService.IsUSBAvailableAsync())
                        {
                            bool initialized = await _usbService.InitializeAsync();
                            if (!initialized)
                            {
                                _logger?.LogError("Failed to initialize USB communication service", "VocomService");
                                VocomError?.Invoke(this, "Failed to initialize USB communication service");
                                return false;
                            }
                        }

                        // Connect to the device using the USB service
                        bool connected = await _usbService.ConnectToDeviceAsync(
                            usbPort,
                            _connectionSettings.BaudRate,
                            _connectionSettings.ConnectionTimeoutMs);

                        if (connected)
                        {
                            _logger?.LogInformation($"Successfully connected to Vocom device {device.SerialNumber} via standard USB service", "VocomService");

                            // Update device information
                            device.LastConnectionTime = DateTime.Now;
                            device.ConnectionStatus = VocomConnectionStatus.Connected;

                            return true;
                        }
                        else
                        {
                            _logger?.LogError($"Standard USB connection failed for device {device.SerialNumber}", "VocomService");
                        }
                    }
                    catch (Exception ex)
                    {
                        lastConnectionError = ex;
                        _logger?.LogError($"Standard USB connection error: {ex.Message}", "VocomService");
                    }
                }
                else
                {
                    _logger?.LogWarning("USB communication service is not available, falling back to Vocom driver", "VocomService");

                    // Verify that the USB device is still connected using the fallback method
                    if (!ConnectionHelper.IsUSBDeviceConnected(device.SerialNumber))
                    {
                        _logger?.LogError($"USB device {device.SerialNumber} is not connected", "VocomService");
                        VocomError?.Invoke(this, $"USB device {device.SerialNumber} is not connected");
                        return false;
                    }

                    // Use the Vocom driver if available
                    if (_vocomDriver != null)
                    {
                        _logger?.LogInformation($"Using Vocom driver to connect to {device.SerialNumber}", "VocomService");
                        return await _vocomDriver.ConnectToDeviceAsync(device);
                    }
                    else
                    {
                        // Fallback to the old implementation with retry logic
                        _logger?.LogWarning("Vocom driver is not available, using simulated connection", "VocomService");

                        bool connected = await ConnectionHelper.ExecuteWithRetryAsync(async () =>
                        {
                            // Simulate connection attempt
                            await Task.Delay(500);

                            // Simulate a successful connection
                            return true;
                        },
                        _connectionSettings.ConnectionRetryAttempts,
                        _connectionSettings.ConnectionRetryDelayMs,
                        _logger);

                        if (connected)
                        {
                            _logger?.LogInformation($"Successfully connected to Vocom device {device.SerialNumber} via USB (simulated)", "VocomService");

                            // Update device information
                            device.LastConnectionTime = DateTime.Now;
                            device.ConnectionStatus = VocomConnectionStatus.Connected;

                            return true;
                        }
                        else
                        {
                            _logger?.LogError($"Failed to connect to Vocom device {device.SerialNumber} via USB (simulated)", "VocomService");
                            VocomError?.Invoke(this, $"Failed to connect to Vocom device {device.SerialNumber} via USB (simulated)");
                            return false;
                        }
                    }
                }

                // If all connection attempts failed, try recovery
                if (_connectionRecoveryService != null && lastConnectionError != null)
                {
                    _logger?.LogInformation("All USB connection attempts failed, attempting recovery", "VocomService");
                    bool recovered = await _connectionRecoveryService.RecoverConnectionAsync(device, lastConnectionError);

                    if (recovered)
                    {
                        _logger?.LogInformation("Connection recovery successful", "VocomService");
                        device.LastConnectionTime = DateTime.Now;
                        device.ConnectionStatus = VocomConnectionStatus.Connected;
                        return true;
                    }
                    else
                    {
                        _logger?.LogError("Connection recovery failed", "VocomService");
                    }
                }

                _logger?.LogError($"All USB connection methods failed for device {device.SerialNumber}", "VocomService");
                VocomError?.Invoke(this, $"All USB connection methods failed for device {device.SerialNumber}");
                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error connecting to Vocom device {device.SerialNumber} via USB", "VocomService", ex);
                VocomError?.Invoke(this, $"USB connection error: {ex.Message}");

                // Try recovery as last resort
                if (_connectionRecoveryService != null)
                {
                    try
                    {
                        _logger?.LogInformation("Attempting recovery after connection exception", "VocomService");
                        bool recovered = await _connectionRecoveryService.RecoverConnectionAsync(device, ex);
                        if (recovered)
                        {
                            _logger?.LogInformation("Exception recovery successful", "VocomService");
                            return true;
                        }
                    }
                    catch (Exception recoveryEx)
                    {
                        _logger?.LogError("Recovery attempt also failed", "VocomService", recoveryEx);
                    }
                }

                return false;
            }
        }

        private async Task<bool> ConnectViaBluetoothAsync(VocomDevice device)
        {
            try
            {
                _logger?.LogInformation($"Connecting to Vocom device {device.SerialNumber} via Bluetooth", "VocomService");

                // Verify that Bluetooth is enabled
                bool bluetoothEnabled = await IsBluetoothEnabledAsync();
                if (!bluetoothEnabled)
                {
                    _logger?.LogWarning("Bluetooth is not enabled, attempting to enable it", "VocomService");

                    bool enabled = await EnableBluetoothAsync();
                    if (!enabled)
                    {
                        _logger?.LogError("Failed to enable Bluetooth", "VocomService");
                        VocomError?.Invoke(this, "Failed to enable Bluetooth");
                        return false;
                    }
                }

                // Get the Bluetooth address
                string bluetoothAddress = device.BluetoothAddress;
                if (string.IsNullOrEmpty(bluetoothAddress))
                {
                    _logger?.LogError("Bluetooth address is missing", "VocomService");
                    VocomError?.Invoke(this, "Bluetooth address is missing");
                    return false;
                }

                _logger?.LogInformation($"Using Bluetooth address: {bluetoothAddress}", "VocomService");

                // In a real implementation, this would use the Vocom driver to connect via Bluetooth
                // For example:
                // 1. Pair with the Bluetooth device if not already paired
                // 2. Establish a Bluetooth connection
                // 3. Configure the connection

                // Simulate connection with retry logic
                bool connected = await ConnectionHelper.ExecuteWithRetryAsync(async () =>
                {
                    // Simulate connection attempt
                    await Task.Delay(800);

                    // Simulate a successful connection
                    return true;
                },
                _connectionSettings.ConnectionRetryAttempts,
                _connectionSettings.ConnectionRetryDelayMs,
                _logger);

                if (connected)
                {
                    _logger?.LogInformation($"Successfully connected to Vocom device {device.SerialNumber} via Bluetooth", "VocomService");

                    // Update device information
                    device.LastConnectionTime = DateTime.Now;
                    device.ConnectionStatus = VocomConnectionStatus.Connected;

                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to connect to Vocom device {device.SerialNumber} via Bluetooth", "VocomService");
                    VocomError?.Invoke(this, $"Failed to connect to Vocom device {device.SerialNumber} via Bluetooth");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error connecting to Vocom device {device.SerialNumber} via Bluetooth", "VocomService", ex);
                VocomError?.Invoke(this, $"Bluetooth connection error: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> ConnectViaWiFiAsync(VocomDevice device)
        {
            try
            {
                _logger?.LogInformation($"Connecting to Vocom device {device.SerialNumber} via WiFi", "VocomService");

                // Verify that WiFi is available
                bool wifiAvailable = await IsWiFiAvailableAsync();
                if (!wifiAvailable)
                {
                    _logger?.LogError("WiFi is not available", "VocomService");
                    VocomError?.Invoke(this, "WiFi is not available");
                    return false;
                }

                // Get the WiFi IP address
                string ipAddress = device.WiFiIPAddress;
                if (string.IsNullOrEmpty(ipAddress))
                {
                    _logger?.LogError("WiFi IP address is missing", "VocomService");
                    VocomError?.Invoke(this, "WiFi IP address is missing");
                    return false;
                }

                _logger?.LogInformation($"Using WiFi IP address: {ipAddress}", "VocomService");

                // In a real implementation, this would use the Vocom driver to connect via WiFi
                // For example:
                // 1. Establish a TCP/IP connection to the device
                // 2. Authenticate with the device
                // 3. Configure the connection

                // Simulate connection with retry logic
                bool connected = await ConnectionHelper.ExecuteWithRetryAsync(async () =>
                {
                    // Simulate connection attempt
                    await Task.Delay(1000);

                    // Simulate a successful connection
                    return true;
                },
                _connectionSettings.ConnectionRetryAttempts,
                _connectionSettings.ConnectionRetryDelayMs,
                _logger);

                if (connected)
                {
                    _logger?.LogInformation($"Successfully connected to Vocom device {device.SerialNumber} via WiFi", "VocomService");

                    // Update device information
                    device.LastConnectionTime = DateTime.Now;
                    device.ConnectionStatus = VocomConnectionStatus.Connected;

                    return true;
                }
                else
                {
                    _logger?.LogError($"Failed to connect to Vocom device {device.SerialNumber} via WiFi", "VocomService");
                    VocomError?.Invoke(this, $"Failed to connect to Vocom device {device.SerialNumber} via WiFi");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error connecting to Vocom device {device.SerialNumber} via WiFi", "VocomService", ex);
                VocomError?.Invoke(this, $"WiFi connection error: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> DisconnectFromUSBAsync(VocomDevice device)
        {
            try
            {
                _logger?.LogInformation($"Disconnecting from Vocom device {device.SerialNumber} via USB", "VocomService");

                // Get the USB port information
                string usbPort = device.USBPortInfo;
                if (string.IsNullOrEmpty(usbPort))
                {
                    _logger?.LogError("USB port information is missing", "VocomService");
                    VocomError?.Invoke(this, "USB port information is missing");
                    return false;
                }

                // Use the USB communication service if available
                if (_usbService != null)
                {
                    _logger?.LogInformation($"Using USB communication service to disconnect from {usbPort}", "VocomService");

                    // Disconnect from the device using the USB service
                    bool disconnected = await _usbService.DisconnectFromDeviceAsync(usbPort);

                    if (disconnected)
                    {
                        _logger?.LogInformation($"Successfully disconnected from Vocom device {device.SerialNumber} via USB port {usbPort}", "VocomService");
                        return true;
                    }
                    else
                    {
                        _logger?.LogError($"Failed to disconnect from Vocom device {device.SerialNumber} via USB port {usbPort}", "VocomService");
                        VocomError?.Invoke(this, $"Failed to disconnect from Vocom device {device.SerialNumber} via USB port {usbPort}");
                        return false;
                    }
                }
                else
                {
                    _logger?.LogWarning("USB communication service is not available, falling back to Vocom driver", "VocomService");

                    // Use the Vocom driver if available
                    if (_vocomDriver != null)
                    {
                        _logger?.LogInformation($"Using Vocom driver to disconnect from {device.SerialNumber}", "VocomService");
                        return await _vocomDriver.DisconnectFromDeviceAsync(device);
                    }
                    else
                    {
                        // Fallback to the old implementation
                        _logger?.LogWarning("Vocom driver is not available, using simulated disconnection", "VocomService");

                        // Simulate disconnection
                        await Task.Delay(300);

                        _logger?.LogInformation($"Successfully disconnected from Vocom device {device.SerialNumber} via USB (simulated)", "VocomService");
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error disconnecting from Vocom device {device.SerialNumber} via USB", "VocomService", ex);
                VocomError?.Invoke(this, $"USB disconnection error: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> DisconnectFromBluetoothAsync(VocomDevice device)
        {
            try
            {
                _logger?.LogInformation($"Disconnecting from Vocom device {device.SerialNumber} via Bluetooth", "VocomService");

                // In a real implementation, this would use the Vocom driver to disconnect from Bluetooth
                // For example:
                // 1. Close any open Bluetooth connections
                // 2. Release resources

                // Simulate disconnection
                await Task.Delay(400);

                _logger?.LogInformation($"Successfully disconnected from Vocom device {device.SerialNumber} via Bluetooth", "VocomService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error disconnecting from Vocom device {device.SerialNumber} via Bluetooth", "VocomService", ex);
                VocomError?.Invoke(this, $"Bluetooth disconnection error: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> DisconnectFromWiFiAsync(VocomDevice device)
        {
            try
            {
                _logger?.LogInformation($"Disconnecting from Vocom device {device.SerialNumber} via WiFi", "VocomService");

                // In a real implementation, this would use the Vocom driver to disconnect from WiFi
                // For example:
                // 1. Close any open TCP/IP connections
                // 2. Release resources

                // Simulate disconnection
                await Task.Delay(300);

                _logger?.LogInformation($"Successfully disconnected from Vocom device {device.SerialNumber} via WiFi", "VocomService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error disconnecting from Vocom device {device.SerialNumber} via WiFi", "VocomService", ex);
                VocomError?.Invoke(this, $"WiFi disconnection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Sends data to the device and waits for a response
        /// </summary>
        /// <param name="data">The data to send</param>
        /// <param name="responseTimeout">The timeout for waiting for a response in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendAndReceiveDataAsync(byte[] data, int responseTimeout = 1000)
        {
            try
            {
                _logger?.LogInformation($"Sending data and waiting for response", "VocomService");

                if (_currentDevice == null || _currentDevice.ConnectionStatus != VocomConnectionStatus.Connected)
                {
                    _logger?.LogError("No device is currently connected", "VocomService");
                    VocomError?.Invoke(this, "No device is currently connected");
                    return Array.Empty<byte>();
                }

                if (data == null || data.Length == 0)
                {
                    _logger?.LogWarning("Data is null or empty", "VocomService");
                    VocomError?.Invoke(this, "Data is null or empty");
                    return Array.Empty<byte>();
                }

                // Check if we should use the real implementation or the dummy one
                bool useDummyImplementation = false;
                string dummyEnv = Environment.GetEnvironmentVariable("USE_DUMMY_IMPLEMENTATIONS");
                if (!string.IsNullOrEmpty(dummyEnv))
                {
                    useDummyImplementation = true;
                }

                if (useDummyImplementation)
                {
                    _logger?.LogInformation("Using dummy implementation for SendAndReceiveDataAsync", "VocomService");

                    // Simulate operation delay
                    await Task.Delay(Math.Min(responseTimeout, 50));

                    // Create a simulated response
                    byte[] response = new byte[data.Length + 2]; // Response is typically longer than request
                    new Random().NextBytes(response);

                    // First byte is often a status code in real implementations
                    response[0] = 0x10; // Positive response code

                    _logger?.LogInformation($"Sent {data.Length} bytes and received {response.Length} bytes response (simulated)", "VocomService");
                    return response;
                }
                else
                {
                    _logger?.LogInformation("Using real implementation for SendAndReceiveDataAsync", "VocomService");

                    // Use the Vocom driver if available
                    if (_vocomDriver != null)
                    {
                        _logger?.LogInformation($"Sending data to device {_currentDevice.SerialNumber} using Vocom driver", "VocomService");

                        // Determine the appropriate protocol based on the data
                        byte protocolByte = data.Length > 0 ? data[0] : (byte)0;

                        // Prepare the expected response length
                        int expectedResponseLength = 256; // Default buffer size

                        // Send the data using the appropriate protocol based on the first byte
                        byte[] response;

                        // Check the protocol identifier in the first byte
                        switch (protocolByte & 0xF0)
                        {
                            case 0x10: // CAN protocol
                                _logger?.LogInformation("Detected CAN protocol request", "VocomService");
                                response = await _vocomDriver.SendCANFrameAsync(_currentDevice, data, expectedResponseLength, responseTimeout);
                                break;

                            case 0x20: // SPI protocol
                                _logger?.LogInformation("Detected SPI protocol request", "VocomService");
                                byte spiCommand = data.Length > 1 ? data[1] : (byte)0;
                                byte[] spiData = data.Length > 2 ? data.Skip(2).ToArray() : Array.Empty<byte>();
                                response = await _vocomDriver.SendSPICommandAsync(_currentDevice, spiCommand, spiData, expectedResponseLength, responseTimeout);
                                break;

                            case 0x30: // SCI protocol
                                _logger?.LogInformation("Detected SCI protocol request", "VocomService");
                                byte sciCommand = data.Length > 1 ? data[1] : (byte)0;
                                byte[] sciData = data.Length > 2 ? data.Skip(2).ToArray() : Array.Empty<byte>();
                                response = await _vocomDriver.SendSCICommandAsync(_currentDevice, sciCommand, sciData, expectedResponseLength, responseTimeout);
                                break;

                            case 0x40: // IIC protocol
                                _logger?.LogInformation("Detected IIC protocol request", "VocomService");
                                byte iicAddress = data.Length > 1 ? data[1] : (byte)0;
                                byte[] iicData = data.Length > 2 ? data.Skip(2).ToArray() : Array.Empty<byte>();
                                response = await _vocomDriver.SendIICCommandAsync(_currentDevice, iicAddress, iicData, expectedResponseLength, responseTimeout);
                                break;

                            default: // Generic data transfer
                                _logger?.LogInformation("Using generic data transfer", "VocomService");

                                // For generic data transfer, use the raw data interface
                                // This is a direct passthrough to the device
                                response = await _vocomDriver.SendRawDataAsync(_currentDevice, data, expectedResponseLength, responseTimeout);
                                break;
                        }

                        if (response != null && response.Length > 0)
                        {
                            _logger?.LogInformation($"Sent {data.Length} bytes and received {response.Length} bytes response", "VocomService");
                            return response;
                        }
                        else
                        {
                            _logger?.LogWarning("No response received from device", "VocomService");
                            VocomError?.Invoke(this, "No response received from device");
                            return Array.Empty<byte>();
                        }
                    }
                    else
                    {
                        _logger?.LogWarning("Vocom driver not available, falling back to dummy implementation", "VocomService");

                        // Fallback to dummy implementation
                        await Task.Delay(Math.Min(responseTimeout, 50));

                        // Create a simulated response
                        byte[] response = new byte[data.Length + 2];
                        new Random().NextBytes(response);
                        response[0] = 0x10; // Positive response code

                        _logger?.LogInformation($"Sent {data.Length} bytes and received {response.Length} bytes response (fallback simulation)", "VocomService");
                        return response;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to send and receive data", "VocomService", ex);
                VocomError?.Invoke(this, $"Send/receive error: {ex.Message}");
                return Array.Empty<byte>();
            }
        }

        /// <summary>
        /// Gets the current device asynchronously
        /// </summary>
        /// <returns>The current Vocom device</returns>
        public async Task<VocomDevice?> GetCurrentDeviceAsync()
        {
            try
            {
                _logger?.LogDebug("Getting current device", "VocomService");
                return _currentDevice;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error getting current device", "VocomService", ex);
                VocomError?.Invoke(this, $"Error getting current device: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Sends SPI data to the device
        /// </summary>
        /// <param name="device">The Vocom device to use</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseTimeout">The timeout for waiting for a response in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendSPIDataAsync(VocomDevice device, byte[] data, int responseTimeout = 1000)
        {
            try
            {
                _logger?.LogInformation($"Sending SPI data to device {device?.SerialNumber}", "VocomService");

                if (_currentDevice == null || _currentDevice.ConnectionStatus != VocomConnectionStatus.Connected)
                {
                    _logger?.LogError("No device is currently connected", "VocomService");
                    VocomError?.Invoke(this, "No device is currently connected");
                    return Array.Empty<byte>();
                }

                if (data == null || data.Length == 0)
                {
                    _logger?.LogWarning("Data is null or empty", "VocomService");
                    VocomError?.Invoke(this, "Data is null or empty");
                    return Array.Empty<byte>();
                }

                // Check if we should use the real implementation or the dummy one
                bool useDummyImplementation = false;
                string dummyEnv = Environment.GetEnvironmentVariable("USE_DUMMY_IMPLEMENTATIONS");
                if (!string.IsNullOrEmpty(dummyEnv))
                {
                    useDummyImplementation = true;
                }

                if (useDummyImplementation)
                {
                    _logger?.LogInformation("Using dummy implementation for SendSPIDataAsync", "VocomService");

                    // Simulate operation delay
                    await Task.Delay(Math.Min(responseTimeout, 30));

                    // Create a simulated response
                    byte[] response = new byte[data.Length + 2]; // Response is typically longer than request
                    new Random().NextBytes(response);

                    // First byte is often a status code in real implementations
                    response[0] = 0x10; // Positive response code

                    _logger?.LogInformation($"Sent {data.Length} bytes of SPI data to device {device?.SerialNumber} and received {response.Length} bytes response (simulated)", "VocomService");
                    return response;
                }
                else
                {
                    _logger?.LogInformation("Using real implementation for SendSPIDataAsync", "VocomService");

                    // Use the Vocom driver if available
                    if (_vocomDriver != null)
                    {
                        _logger?.LogInformation($"Sending SPI data to device {device.SerialNumber} using Vocom driver", "VocomService");

                        // Extract SPI command from data if available
                        byte spiCommand = data.Length > 0 ? data[0] : (byte)0;
                        byte[] spiData = data.Length > 1 ? data.Skip(1).ToArray() : Array.Empty<byte>();

                        // Prepare the expected response length
                        int expectedResponseLength = 256; // Default buffer size

                        // Send the SPI command
                        byte[] response = await _vocomDriver.SendSPICommandAsync(device, spiCommand, spiData, expectedResponseLength, responseTimeout);

                        if (response != null && response.Length > 0)
                        {
                            _logger?.LogInformation($"Sent {data.Length} bytes of SPI data to device {device.SerialNumber} and received {response.Length} bytes response", "VocomService");

                            // Process the response according to MC9S12XEP100 SPI protocol
                            // The MC9S12XEP100 SPI response format is:
                            // Byte 0: Status (0x10 = success, other values indicate errors)
                            // Byte 1: Length of data
                            // Bytes 2+: Data

                            // Check if the response indicates success
                            if (response.Length > 0 && response[0] == 0x10)
                            {
                                _logger?.LogInformation("SPI command executed successfully", "VocomService");
                            }
                            else if (response.Length > 0)
                            {
                                _logger?.LogWarning($"SPI command returned error status: 0x{response[0]:X2}", "VocomService");
                            }

                            return response;
                        }
                        else
                        {
                            _logger?.LogWarning("No response received from device", "VocomService");
                            VocomError?.Invoke(this, "No response received from device");
                            return Array.Empty<byte>();
                        }
                    }
                    else
                    {
                        _logger?.LogWarning("Vocom driver not available, falling back to dummy implementation", "VocomService");

                        // Fallback to dummy implementation
                        await Task.Delay(Math.Min(responseTimeout, 30));

                        // Create a simulated response
                        byte[] response = new byte[data.Length + 2];
                        new Random().NextBytes(response);
                        response[0] = 0x10; // Positive response code

                        _logger?.LogInformation($"Sent {data.Length} bytes of SPI data to device {device?.SerialNumber} and received {response.Length} bytes response (fallback simulation)", "VocomService");
                        return response;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to send SPI data to device {device?.SerialNumber}", "VocomService", ex);
                VocomError?.Invoke(this, $"SPI send/receive error: {ex.Message}");
                return Array.Empty<byte>();
            }
        }

        /// <summary>
        /// Sends SCI data to the device
        /// </summary>
        /// <param name="device">The Vocom device to use</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseTimeout">The timeout for waiting for a response in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendSCIDataAsync(VocomDevice device, byte[] data, int responseTimeout = 1000)
        {
            try
            {
                _logger?.LogInformation($"Sending SCI data to device {device?.SerialNumber}", "VocomService");

                if (_currentDevice == null || _currentDevice.ConnectionStatus != VocomConnectionStatus.Connected)
                {
                    _logger?.LogError("No device is currently connected", "VocomService");
                    VocomError?.Invoke(this, "No device is currently connected");
                    return Array.Empty<byte>();
                }

                if (data == null || data.Length == 0)
                {
                    _logger?.LogWarning("Data is null or empty", "VocomService");
                    VocomError?.Invoke(this, "Data is null or empty");
                    return Array.Empty<byte>();
                }

                // In a real implementation, this would send SCI data to the device and wait for a response
                // For now, we'll just simulate sending the data and receiving a response

                // Simulate operation delay
                await Task.Delay(Math.Min(responseTimeout, 35));

                // Create a simulated response
                byte[] response = new byte[data.Length + 2]; // Response is typically longer than request
                new Random().NextBytes(response);

                // First byte is often a status code in real implementations
                response[0] = 0x10; // Positive response code

                _logger?.LogInformation($"Sent {data.Length} bytes of SCI data to device {device?.SerialNumber} and received {response.Length} bytes response", "VocomService");
                return response;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to send SCI data to device {device?.SerialNumber}", "VocomService", ex);
                VocomError?.Invoke(this, $"SCI send/receive error: {ex.Message}");
                return Array.Empty<byte>();
            }
        }

        /// <summary>
        /// Sends IIC data to the device
        /// </summary>
        /// <param name="device">The Vocom device to use</param>
        /// <param name="address">The IIC device address</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseTimeout">The timeout for waiting for a response in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendIICDataAsync(VocomDevice device, byte address, byte[] data, int responseTimeout = 1000)
        {
            try
            {
                _logger?.LogInformation($"Sending IIC data to device {device?.SerialNumber} at address 0x{address:X2}", "VocomService");

                if (_currentDevice == null || _currentDevice.ConnectionStatus != VocomConnectionStatus.Connected)
                {
                    _logger?.LogError("No device is currently connected", "VocomService");
                    VocomError?.Invoke(this, "No device is currently connected");
                    return Array.Empty<byte>();
                }

                if (data == null || data.Length == 0)
                {
                    _logger?.LogWarning("Data is null or empty", "VocomService");
                    VocomError?.Invoke(this, "Data is null or empty");
                    return Array.Empty<byte>();
                }

                // In a real implementation, this would send IIC data to the device and wait for a response
                // For now, we'll just simulate sending the data and receiving a response

                // Simulate operation delay
                await Task.Delay(Math.Min(responseTimeout, 40));

                // Create a simulated response
                byte[] response = new byte[data.Length + 2]; // Response is typically longer than request
                new Random().NextBytes(response);

                // First byte is often a status code in real implementations
                response[0] = 0x10; // Positive response code

                _logger?.LogInformation($"Sent {data.Length} bytes of IIC data to device {device?.SerialNumber} at address 0x{address:X2} and received {response.Length} bytes response", "VocomService");
                return response;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Failed to send IIC data to device {device?.SerialNumber} at address 0x{address:X2}", "VocomService", ex);
                VocomError?.Invoke(this, $"IIC send/receive error: {ex.Message}");
                return Array.Empty<byte>();
            }
        }

        #endregion

        #region Connection Retry Methods

        private async Task<bool> TryNativeConnectionWithRetry(string usbPort)
        {
            const int maxRetries = 3;
            const int retryDelayMs = 1000;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    _logger?.LogInformation($"Native USB connection attempt {attempt}/{maxRetries} to {usbPort}", "VocomService");

                    bool connected = await _nativeUsbService.ConnectToDeviceAsync(
                        usbPort,
                        _connectionSettings.BaudRate,
                        _connectionSettings.ConnectionTimeoutMs);

                    if (connected)
                    {
                        _logger?.LogInformation($"Native USB connection successful on attempt {attempt}", "VocomService");
                        return true;
                    }

                    if (attempt < maxRetries)
                    {
                        _logger?.LogInformation($"Native USB connection attempt {attempt} failed, retrying in {retryDelayMs}ms", "VocomService");
                        await Task.Delay(retryDelayMs);
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning($"Native USB connection attempt {attempt} failed: {ex.Message}", "VocomService");

                    if (attempt < maxRetries)
                    {
                        await Task.Delay(retryDelayMs);
                    }
                }
            }

            _logger?.LogWarning($"All {maxRetries} native USB connection attempts failed", "VocomService");
            return false;
        }

        private async Task<bool> TryStandardConnectionWithRetry(string usbPort)
        {
            const int maxRetries = 3;
            const int retryDelayMs = 1000;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    _logger?.LogInformation($"Standard USB connection attempt {attempt}/{maxRetries} to {usbPort}", "VocomService");

                    // Initialize the USB service if not already initialized
                    if (!await _usbService.IsUSBAvailableAsync())
                    {
                        bool initialized = await _usbService.InitializeAsync();
                        if (!initialized)
                        {
                            _logger?.LogError("Failed to initialize USB communication service", "VocomService");
                            continue;
                        }
                    }

                    bool connected = await _usbService.ConnectToDeviceAsync(
                        usbPort,
                        _connectionSettings.BaudRate,
                        _connectionSettings.ConnectionTimeoutMs);

                    if (connected)
                    {
                        _logger?.LogInformation($"Standard USB connection successful on attempt {attempt}", "VocomService");
                        return true;
                    }

                    if (attempt < maxRetries)
                    {
                        _logger?.LogInformation($"Standard USB connection attempt {attempt} failed, retrying in {retryDelayMs}ms", "VocomService");
                        await Task.Delay(retryDelayMs);
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning($"Standard USB connection attempt {attempt} failed: {ex.Message}", "VocomService");

                    if (attempt < maxRetries)
                    {
                        await Task.Delay(retryDelayMs);
                    }
                }
            }

            _logger?.LogWarning($"All {maxRetries} standard USB connection attempts failed", "VocomService");
            return false;
        }

        #endregion
    }
}
