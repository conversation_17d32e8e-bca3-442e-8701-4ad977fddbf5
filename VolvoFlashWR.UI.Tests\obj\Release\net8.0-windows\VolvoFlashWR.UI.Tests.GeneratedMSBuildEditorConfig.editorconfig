is_global = true
build_property.TargetFramework = net8.0-windows
build_property.TargetPlatformMinVersion = 7.0
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = VolvoFlashWR.UI.Tests
build_property.ProjectDir = D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI.Tests\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
