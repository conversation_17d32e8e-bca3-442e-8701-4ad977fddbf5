using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Enhanced runtime installer for Visual C++ redistributables and Universal CRT
    /// </summary>
    public class EnhancedRuntimeInstaller
    {
        private readonly ILoggingService _logger;
        private readonly string _applicationPath;
        private readonly string _librariesPath;

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        // Critical runtime libraries with their expected locations
        private static readonly Dictionary<string, RuntimeLibrary> CriticalRuntimeLibraries = new()
        {
            ["msvcr140.dll"] = new RuntimeLibrary
            {
                Name = "msvcr140.dll",
                Description = "Microsoft Visual C++ 2015-2022 Runtime",
                IsUniversalCRT = false,
                RequiredForVocom = true
            },
            ["msvcp140.dll"] = new RuntimeLibrary
            {
                Name = "msvcp140.dll", 
                Description = "Microsoft Visual C++ 2015-2022 C++ Runtime",
                IsUniversalCRT = false,
                RequiredForVocom = true
            },
            ["vcruntime140.dll"] = new RuntimeLibrary
            {
                Name = "vcruntime140.dll",
                Description = "Microsoft Visual C++ 2015-2022 Runtime",
                IsUniversalCRT = false,
                RequiredForVocom = true
            },
            ["api-ms-win-crt-runtime-l1-1-0.dll"] = new RuntimeLibrary
            {
                Name = "api-ms-win-crt-runtime-l1-1-0.dll",
                Description = "Universal CRT Runtime",
                IsUniversalCRT = true,
                RequiredForVocom = true
            },
            ["api-ms-win-crt-heap-l1-1-0.dll"] = new RuntimeLibrary
            {
                Name = "api-ms-win-crt-heap-l1-1-0.dll",
                Description = "Universal CRT Heap",
                IsUniversalCRT = true,
                RequiredForVocom = true
            },
            ["api-ms-win-crt-string-l1-1-0.dll"] = new RuntimeLibrary
            {
                Name = "api-ms-win-crt-string-l1-1-0.dll",
                Description = "Universal CRT String",
                IsUniversalCRT = true,
                RequiredForVocom = true
            },
            ["api-ms-win-crt-stdio-l1-1-0.dll"] = new RuntimeLibrary
            {
                Name = "api-ms-win-crt-stdio-l1-1-0.dll",
                Description = "Universal CRT Standard I/O",
                IsUniversalCRT = true,
                RequiredForVocom = false
            },
            ["api-ms-win-crt-math-l1-1-0.dll"] = new RuntimeLibrary
            {
                Name = "api-ms-win-crt-math-l1-1-0.dll",
                Description = "Universal CRT Math",
                IsUniversalCRT = true,
                RequiredForVocom = false
            },
            ["api-ms-win-crt-locale-l1-1-0.dll"] = new RuntimeLibrary
            {
                Name = "api-ms-win-crt-locale-l1-1-0.dll",
                Description = "Universal CRT Locale",
                IsUniversalCRT = true,
                RequiredForVocom = false
            }
        };

        public EnhancedRuntimeInstaller(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory;
            _librariesPath = Path.Combine(_applicationPath, "Libraries");
        }

        /// <summary>
        /// Installs missing runtime dependencies
        /// </summary>
        public async Task<bool> InstallMissingRuntimesAsync()
        {
            try
            {
                _logger.LogInformation("Starting enhanced runtime dependency installation", "EnhancedRuntimeInstaller");

                bool isX64Process = Environment.Is64BitProcess;
                _logger.LogInformation($"Process architecture: {(isX64Process ? "x64" : "x86")}", "EnhancedRuntimeInstaller");

                // Check current status
                var missingLibraries = await AnalyzeMissingLibrariesAsync();
                
                if (missingLibraries.Count == 0)
                {
                    _logger.LogInformation("All runtime dependencies are already available", "EnhancedRuntimeInstaller");
                    return true;
                }

                _logger.LogInformation($"Found {missingLibraries.Count} missing runtime libraries", "EnhancedRuntimeInstaller");

                // Try to install Visual C++ Redistributable
                bool vcRedistInstalled = await InstallVCRedistributableAsync(isX64Process);
                
                // Try to install Universal CRT if needed
                bool ucrtInstalled = await InstallUniversalCRTAsync(isX64Process);

                // Verify installation
                var stillMissing = await AnalyzeMissingLibrariesAsync();
                int resolvedCount = missingLibraries.Count - stillMissing.Count;

                _logger.LogInformation($"Runtime installation completed: {resolvedCount}/{missingLibraries.Count} libraries resolved", "EnhancedRuntimeInstaller");

                return resolvedCount >= (missingLibraries.Count * 0.8); // 80% success rate
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during runtime installation: {ex.Message}", "EnhancedRuntimeInstaller", ex);
                return false;
            }
        }

        /// <summary>
        /// Analyzes which runtime libraries are missing
        /// </summary>
        private async Task<List<RuntimeLibrary>> AnalyzeMissingLibrariesAsync()
        {
            await Task.CompletedTask;
            
            var missingLibraries = new List<RuntimeLibrary>();

            foreach (var kvp in CriticalRuntimeLibraries)
            {
                string libraryName = kvp.Key;
                RuntimeLibrary library = kvp.Value;

                if (!IsLibraryAvailable(libraryName))
                {
                    missingLibraries.Add(library);
                    _logger.LogWarning($"Missing runtime library: {library.Description} ({libraryName})", "EnhancedRuntimeInstaller");
                }
            }

            return missingLibraries;
        }

        /// <summary>
        /// Checks if a library is available in the system
        /// </summary>
        private bool IsLibraryAvailable(string libraryName)
        {
            try
            {
                // Check in application directory
                string appPath = Path.Combine(_applicationPath, libraryName);
                if (File.Exists(appPath))
                    return true;

                // Check in Libraries subdirectory
                string libPath = Path.Combine(_librariesPath, libraryName);
                if (File.Exists(libPath))
                    return true;

                // Try to load from system
                IntPtr handle = LoadLibrary(libraryName);
                if (handle != IntPtr.Zero)
                {
                    FreeLibrary(handle);
                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Installs Visual C++ Redistributable
        /// </summary>
        private async Task<bool> InstallVCRedistributableAsync(bool isX64Process)
        {
            try
            {
                string downloadUrl = isX64Process ? 
                    "https://aka.ms/vs/17/release/vc_redist.x64.exe" : 
                    "https://aka.ms/vs/17/release/vc_redist.x86.exe";

                _logger.LogInformation($"Downloading Visual C++ Redistributable ({(isX64Process ? "x64" : "x86")})", "EnhancedRuntimeInstaller");

                string tempPath = Path.Combine(Path.GetTempPath(), $"vc_redist_{Guid.NewGuid()}.exe");

                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromMinutes(10);

                var response = await httpClient.GetAsync(downloadUrl);
                response.EnsureSuccessStatusCode();

                using (var fileStream = File.Create(tempPath))
                {
                    await response.Content.CopyToAsync(fileStream);
                }

                _logger.LogInformation("Installing Visual C++ Redistributable (this may take a few minutes)", "EnhancedRuntimeInstaller");

                // Install silently
                var processInfo = new ProcessStartInfo
                {
                    FileName = tempPath,
                    Arguments = "/install /quiet /norestart",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(processInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    
                    if (process.ExitCode == 0)
                    {
                        _logger.LogInformation("Visual C++ Redistributable installed successfully", "EnhancedRuntimeInstaller");
                    }
                    else
                    {
                        _logger.LogWarning($"Visual C++ Redistributable installation completed with exit code: {process.ExitCode}", "EnhancedRuntimeInstaller");
                    }
                }

                // Cleanup
                if (File.Exists(tempPath))
                    File.Delete(tempPath);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to install Visual C++ Redistributable: {ex.Message}", "EnhancedRuntimeInstaller");
                return false;
            }
        }

        /// <summary>
        /// Installs Universal CRT if needed
        /// </summary>
        private async Task<bool> InstallUniversalCRTAsync(bool isX64Process)
        {
            try
            {
                // Universal CRT is typically installed with Windows 10+ or via Windows Update
                _logger.LogInformation("Checking Universal CRT availability", "EnhancedRuntimeInstaller");

                // For Windows 10+, Universal CRT should be available
                var osVersion = Environment.OSVersion;
                if (osVersion.Version.Major >= 10)
                {
                    _logger.LogInformation("Windows 10+ detected - Universal CRT should be available via Windows Update", "EnhancedRuntimeInstaller");
                    return true;
                }

                // For older Windows versions, suggest Windows Update
                _logger.LogWarning("Older Windows version detected - Universal CRT may need to be installed via Windows Update", "EnhancedRuntimeInstaller");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error checking Universal CRT: {ex.Message}", "EnhancedRuntimeInstaller");
                return false;
            }
        }

        /// <summary>
        /// Runtime library information
        /// </summary>
        private class RuntimeLibrary
        {
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public bool IsUniversalCRT { get; set; }
            public bool RequiredForVocom { get; set; }
        }
    }
}
