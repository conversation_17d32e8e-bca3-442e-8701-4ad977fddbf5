using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Backup
{
    /// <summary>
    /// Implementation of the backup scheduler service
    /// </summary>
    public class BackupSchedulerService : IBackupSchedulerService
    {
        #region Private Fields

        private readonly ILoggingService _logger;
        private IBackupService? _backupService;
        private IECUCommunicationService? _ecuService;
        private string _schedulesDirectoryPath;
        private string _schedulesFilePath;
        private bool _isInitialized;
        private bool _isRunning;
        private Timer _schedulerTimer;
        private readonly object _lockObject = new object();
        private readonly TimeSpan _timerInterval = TimeSpan.FromMinutes(1);
        private readonly List<BackupSchedule> _schedules = new List<BackupSchedule>();
        private readonly List<string> _runningSchedules = new List<string>();
        private readonly Dictionary<string, DateTime> _lastFailedSchedules = new Dictionary<string, DateTime>();
        private readonly int _maxRetryAttempts = 3;
        private readonly TimeSpan _retryDelay = TimeSpan.FromMinutes(5);

        #endregion

        #region Events

        /// <summary>
        /// Event triggered when a backup schedule is created
        /// </summary>
        public event EventHandler<BackupSchedule> ScheduleCreated;

        /// <summary>
        /// Event triggered when a backup schedule is updated
        /// </summary>
        public event EventHandler<BackupSchedule> ScheduleUpdated;

        /// <summary>
        /// Event triggered when a backup schedule is deleted
        /// </summary>
        public event EventHandler<BackupSchedule> ScheduleDeleted;

        /// <summary>
        /// Event triggered when a scheduled backup starts
        /// </summary>
        public event EventHandler<BackupSchedule> ScheduledBackupStarted;

        /// <summary>
        /// Event triggered when a scheduled backup completes
        /// </summary>
        public event EventHandler<(BackupSchedule Schedule, BackupData Backup)> ScheduledBackupCompleted;

        /// <summary>
        /// Event triggered when an error occurs during scheduled backup
        /// </summary>
        public event EventHandler<(BackupSchedule Schedule, string ErrorMessage)> ScheduledBackupError;

        #endregion

        #region Properties

        /// <summary>
        /// Gets whether the scheduler is running
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// Gets the schedules directory path
        /// </summary>
        public string SchedulesDirectoryPath => _schedulesDirectoryPath;

        /// <summary>
        /// Gets the list of backup schedules
        /// </summary>
        public List<BackupSchedule> Schedules => _schedules;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the BackupSchedulerService class
        /// </summary>
        /// <param name="logger">The logging service</param>
        public BackupSchedulerService(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _schedulesDirectoryPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Schedules");
            _schedulesFilePath = Path.Combine(_schedulesDirectoryPath, "backup_schedules.json");
            _isInitialized = false;
            _isRunning = false;

            // Initialize the timer
            _schedulerTimer = new Timer(CheckSchedules, null, Timeout.Infinite, Timeout.Infinite);

            // Initialize events to empty handlers to avoid null reference exceptions
            ScheduleCreated = (sender, schedule) => { };
            ScheduleUpdated = (sender, schedule) => { };
            ScheduleDeleted = (sender, schedule) => { };
            ScheduledBackupStarted = (sender, schedule) => { };
            ScheduledBackupCompleted = (sender, data) => { };
            ScheduledBackupError = (sender, data) => { };
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Initializes the backup scheduler service
        /// </summary>
        /// <param name="backupService">The backup service to use</param>
        /// <param name="ecuService">The ECU communication service to use</param>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync(IBackupService backupService, IECUCommunicationService ecuService)
        {
            try
            {
                _logger?.LogInformation("Initializing backup scheduler service", "BackupSchedulerService");

                if (backupService == null)
                {
                    _logger?.LogError("Backup service is null", "BackupSchedulerService");
                    return false;
                }

                if (ecuService == null)
                {
                    _logger?.LogError("ECU communication service is null", "BackupSchedulerService");
                    return false;
                }

                _backupService = backupService;
                _ecuService = ecuService;

                // Create schedules directory if it doesn't exist
                if (!Directory.Exists(_schedulesDirectoryPath))
                {
                    Directory.CreateDirectory(_schedulesDirectoryPath);
                    _logger?.LogInformation($"Created schedules directory: {_schedulesDirectoryPath}", "BackupSchedulerService");
                }

                // Load existing schedules
                await LoadSchedulesAsync();

                _isInitialized = true;
                _logger?.LogInformation("Backup scheduler service initialized successfully", "BackupSchedulerService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to initialize backup scheduler service", "BackupSchedulerService", ex);
                return false;
            }
        }

        /// <summary>
        /// Starts the scheduler
        /// </summary>
        /// <returns>True if the scheduler was started successfully, false otherwise</returns>
        public Task<bool> StartAsync()
        {
            try
            {
                _logger?.LogInformation("Starting backup scheduler", "BackupSchedulerService");

                if (!_isInitialized)
                {
                    _logger?.LogError("Backup scheduler service is not initialized", "BackupSchedulerService");
                    return Task.FromResult(false);
                }

                if (_isRunning)
                {
                    _logger?.LogInformation("Backup scheduler is already running", "BackupSchedulerService");
                    return Task.FromResult(true);
                }

                // Create a timer that checks for scheduled backups every minute
                _schedulerTimer = new Timer(CheckSchedules, null, TimeSpan.Zero, _timerInterval);
                _isRunning = true;

                _logger?.LogInformation("Backup scheduler started successfully", "BackupSchedulerService");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to start backup scheduler", "BackupSchedulerService", ex);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Stops the scheduler
        /// </summary>
        /// <returns>True if the scheduler was stopped successfully, false otherwise</returns>
        public Task<bool> StopAsync()
        {
            try
            {
                _logger?.LogInformation("Stopping backup scheduler", "BackupSchedulerService");

                if (!_isRunning)
                {
                    _logger?.LogInformation("Backup scheduler is not running", "BackupSchedulerService");
                    return Task.FromResult(true);
                }

                // Dispose the timer
                _schedulerTimer?.Dispose();
                _schedulerTimer = null;
                _isRunning = false;

                _logger?.LogInformation("Backup scheduler stopped successfully", "BackupSchedulerService");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger?.LogError("Failed to stop backup scheduler", "BackupSchedulerService", ex);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Creates a new backup schedule
        /// </summary>
        /// <param name="schedule">The schedule to create</param>
        /// <returns>The created schedule, or null if creation fails</returns>
        public async Task<BackupSchedule?> CreateScheduleAsync(BackupSchedule schedule)
        {
            try
            {
                _logger?.LogInformation($"Creating backup schedule for ECU {schedule?.ECUName}", "BackupSchedulerService");

                if (!_isInitialized)
                {
                    _logger?.LogError("Backup scheduler service is not initialized", "BackupSchedulerService");
                    return null;
                }

                if (schedule == null)
                {
                    _logger?.LogError("Schedule is null", "BackupSchedulerService");
                    return null;
                }

                // Generate a new ID if not provided
                if (string.IsNullOrEmpty(schedule.Id))
                {
                    schedule.Id = Guid.NewGuid().ToString();
                }

                // Calculate the next execution time
                schedule.NextExecutionTime = schedule.CalculateNextExecutionTime(DateTime.Now);

                // Add the schedule to the list
                lock (_lockObject)
                {
                    _schedules.Add(schedule);
                }

                // Save schedules to disk
                await SaveSchedulesAsync();

                _logger?.LogInformation($"Backup schedule created for ECU {schedule.ECUName}", "BackupSchedulerService");
                ScheduleCreated?.Invoke(this, schedule);
                return schedule;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error creating backup schedule for ECU {schedule?.ECUName}", "BackupSchedulerService", ex);
                return null;
            }
        }

        /// <summary>
        /// Updates an existing backup schedule
        /// </summary>
        /// <param name="schedule">The schedule to update</param>
        /// <returns>The updated schedule, or null if update fails</returns>
        public async Task<BackupSchedule?> UpdateScheduleAsync(BackupSchedule schedule)
        {
            try
            {
                _logger?.LogInformation($"Updating backup schedule {schedule?.Id}", "BackupSchedulerService");

                if (!_isInitialized)
                {
                    _logger?.LogError("Backup scheduler service is not initialized", "BackupSchedulerService");
                    return null;
                }

                if (schedule == null)
                {
                    _logger?.LogError("Schedule is null", "BackupSchedulerService");
                    return null;
                }

                // Find the existing schedule
                BackupSchedule? existingSchedule;
                lock (_lockObject)
                {
                    existingSchedule = _schedules.FirstOrDefault(s => s.Id == schedule.Id);
                }

                if (existingSchedule == null)
                {
                    _logger?.LogError($"Schedule with ID {schedule.Id} not found", "BackupSchedulerService");
                    return null;
                }

                // Update the schedule properties
                lock (_lockObject)
                {
                    int index = _schedules.IndexOf(existingSchedule);
                    _schedules[index] = schedule;
                }

                // Recalculate the next execution time
                schedule.NextExecutionTime = schedule.CalculateNextExecutionTime(DateTime.Now);

                // Save schedules to disk
                await SaveSchedulesAsync();

                _logger?.LogInformation($"Backup schedule {schedule.Id} updated", "BackupSchedulerService");
                ScheduleUpdated?.Invoke(this, schedule);
                return schedule;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error updating backup schedule {schedule?.Id}", "BackupSchedulerService", ex);
                return null;
            }
        }

        /// <summary>
        /// Deletes a backup schedule
        /// </summary>
        /// <param name="scheduleId">The ID of the schedule to delete</param>
        /// <returns>True if deletion is successful, false otherwise</returns>
        public async Task<bool> DeleteScheduleAsync(string scheduleId)
        {
            try
            {
                _logger?.LogInformation($"Deleting backup schedule {scheduleId}", "BackupSchedulerService");

                if (!_isInitialized)
                {
                    _logger?.LogError("Backup scheduler service is not initialized", "BackupSchedulerService");
                    return false;
                }

                if (string.IsNullOrEmpty(scheduleId))
                {
                    _logger?.LogError("Schedule ID is null or empty", "BackupSchedulerService");
                    return false;
                }

                // Find the schedule
                BackupSchedule schedule;
                lock (_lockObject)
                {
                    schedule = _schedules.FirstOrDefault(s => s.Id == scheduleId);
                }

                if (schedule == null)
                {
                    _logger?.LogError($"Schedule with ID {scheduleId} not found", "BackupSchedulerService");
                    return false;
                }

                // Remove the schedule
                lock (_lockObject)
                {
                    _schedules.Remove(schedule);
                }

                // Save schedules to disk
                await SaveSchedulesAsync();

                _logger?.LogInformation($"Backup schedule {scheduleId} deleted", "BackupSchedulerService");
                ScheduleDeleted?.Invoke(this, schedule);
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error deleting backup schedule {scheduleId}", "BackupSchedulerService", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets a backup schedule by ID
        /// </summary>
        /// <param name="scheduleId">The ID of the schedule to get</param>
        /// <returns>The backup schedule, or null if not found</returns>
        public Task<BackupSchedule?> GetScheduleAsync(string scheduleId)
        {
            try
            {
                _logger?.LogInformation($"Getting backup schedule {scheduleId}", "BackupSchedulerService");

                if (!_isInitialized)
                {
                    _logger?.LogError("Backup scheduler service is not initialized", "BackupSchedulerService");
                    return Task.FromResult<BackupSchedule?>(null);
                }

                if (string.IsNullOrEmpty(scheduleId))
                {
                    _logger?.LogError("Schedule ID is null or empty", "BackupSchedulerService");
                    return Task.FromResult<BackupSchedule?>(null);
                }

                // Find the schedule
                BackupSchedule? schedule;
                lock (_lockObject)
                {
                    schedule = _schedules.FirstOrDefault(s => s.Id == scheduleId);
                }

                if (schedule == null)
                {
                    _logger?.LogWarning($"Schedule with ID {scheduleId} not found", "BackupSchedulerService");
                }

                return Task.FromResult(schedule);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error getting backup schedule {scheduleId}", "BackupSchedulerService", ex);
                return Task.FromResult<BackupSchedule?>(null);
            }
        }

        /// <summary>
        /// Gets all backup schedules
        /// </summary>
        /// <returns>List of all backup schedules</returns>
        public Task<List<BackupSchedule>> GetAllSchedulesAsync()
        {
            try
            {
                _logger?.LogInformation("Getting all backup schedules", "BackupSchedulerService");

                if (!_isInitialized)
                {
                    _logger?.LogError("Backup scheduler service is not initialized", "BackupSchedulerService");
                    return Task.FromResult(new List<BackupSchedule>());
                }

                // Return a copy of the schedules list
                List<BackupSchedule> schedules;
                lock (_lockObject)
                {
                    schedules = _schedules.ToList();
                }

                return Task.FromResult(schedules);
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error getting all backup schedules", "BackupSchedulerService", ex);
                return Task.FromResult(new List<BackupSchedule>());
            }
        }

        /// <summary>
        /// Gets backup schedules for a specific ECU
        /// </summary>
        /// <param name="ecuId">The ID of the ECU</param>
        /// <returns>List of backup schedules for the specified ECU</returns>
        public Task<List<BackupSchedule>> GetSchedulesForECUAsync(string ecuId)
        {
            try
            {
                _logger?.LogInformation($"Getting backup schedules for ECU {ecuId}", "BackupSchedulerService");

                if (!_isInitialized)
                {
                    _logger?.LogError("Backup scheduler service is not initialized", "BackupSchedulerService");
                    return Task.FromResult(new List<BackupSchedule>());
                }

                if (string.IsNullOrEmpty(ecuId))
                {
                    _logger?.LogError("ECU ID is null or empty", "BackupSchedulerService");
                    return Task.FromResult(new List<BackupSchedule>());
                }

                // Filter schedules by ECU ID
                List<BackupSchedule> schedules;
                lock (_lockObject)
                {
                    schedules = _schedules.Where(s => s.ECUId == ecuId).ToList();
                }

                _logger?.LogInformation($"Found {schedules.Count} backup schedules for ECU {ecuId}", "BackupSchedulerService");
                return Task.FromResult(schedules);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error getting backup schedules for ECU {ecuId}", "BackupSchedulerService", ex);
                return Task.FromResult(new List<BackupSchedule>());
            }
        }

        /// <summary>
        /// Enables a backup schedule
        /// </summary>
        /// <param name="scheduleId">The ID of the schedule to enable</param>
        /// <returns>True if enabling is successful, false otherwise</returns>
        public async Task<bool> EnableScheduleAsync(string scheduleId)
        {
            try
            {
                _logger?.LogInformation($"Enabling backup schedule {scheduleId}", "BackupSchedulerService");

                if (!_isInitialized)
                {
                    _logger?.LogError("Backup scheduler service is not initialized", "BackupSchedulerService");
                    return false;
                }

                // Find the schedule
                BackupSchedule schedule;
                lock (_lockObject)
                {
                    schedule = _schedules.FirstOrDefault(s => s.Id == scheduleId);
                }

                if (schedule == null)
                {
                    _logger?.LogError($"Schedule with ID {scheduleId} not found", "BackupSchedulerService");
                    return false;
                }

                // Enable the schedule
                lock (_lockObject)
                {
                    schedule.IsEnabled = true;
                    schedule.NextExecutionTime = schedule.CalculateNextExecutionTime(DateTime.Now);
                }

                // Save schedules to disk
                await SaveSchedulesAsync();

                _logger?.LogInformation($"Backup schedule {scheduleId} enabled", "BackupSchedulerService");
                ScheduleUpdated?.Invoke(this, schedule);
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error enabling backup schedule {scheduleId}", "BackupSchedulerService", ex);
                return false;
            }
        }

        /// <summary>
        /// Disables a backup schedule
        /// </summary>
        /// <param name="scheduleId">The ID of the schedule to disable</param>
        /// <returns>True if disabling is successful, false otherwise</returns>
        public async Task<bool> DisableScheduleAsync(string scheduleId)
        {
            try
            {
                _logger?.LogInformation($"Disabling backup schedule {scheduleId}", "BackupSchedulerService");

                if (!_isInitialized)
                {
                    _logger?.LogError("Backup scheduler service is not initialized", "BackupSchedulerService");
                    return false;
                }

                // Find the schedule
                BackupSchedule schedule;
                lock (_lockObject)
                {
                    schedule = _schedules.FirstOrDefault(s => s.Id == scheduleId);
                }

                if (schedule == null)
                {
                    _logger?.LogError($"Schedule with ID {scheduleId} not found", "BackupSchedulerService");
                    return false;
                }

                // Disable the schedule
                lock (_lockObject)
                {
                    schedule.IsEnabled = false;
                }

                // Save schedules to disk
                await SaveSchedulesAsync();

                _logger?.LogInformation($"Backup schedule {scheduleId} disabled", "BackupSchedulerService");
                ScheduleUpdated?.Invoke(this, schedule);
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error disabling backup schedule {scheduleId}", "BackupSchedulerService", ex);
                return false;
            }
        }

        /// <summary>
        /// Executes a backup schedule immediately
        /// </summary>
        /// <param name="scheduleId">The ID of the schedule to execute</param>
        /// <returns>The created backup data, or null if execution fails</returns>
        public async Task<BackupData?> ExecuteScheduleNowAsync(string scheduleId)
        {
            try
            {
                _logger?.LogInformation($"Executing backup schedule {scheduleId} now", "BackupSchedulerService");

                if (!_isInitialized)
                {
                    _logger?.LogError("Backup scheduler service is not initialized", "BackupSchedulerService");
                    return null;
                }

                // Find the schedule
                BackupSchedule? schedule;
                lock (_lockObject)
                {
                    schedule = _schedules.FirstOrDefault(s => s.Id == scheduleId);
                }

                if (schedule == null)
                {
                    _logger?.LogError($"Schedule with ID {scheduleId} not found", "BackupSchedulerService");
                    return null;
                }

                // Execute the backup
                return await ExecuteBackupAsync(schedule);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error executing backup schedule {scheduleId}", "BackupSchedulerService", ex);
                return null;
            }
        }

        /// <summary>
        /// Saves all schedules to disk
        /// </summary>
        /// <returns>True if saving is successful, false otherwise</returns>
        public async Task<bool> SaveSchedulesAsync()
        {
            try
            {
                _logger?.LogInformation("Saving backup schedules to disk", "BackupSchedulerService");

                if (!_isInitialized)
                {
                    _logger?.LogError("Backup scheduler service is not initialized", "BackupSchedulerService");
                    return false;
                }

                // Ensure the directory exists
                if (!Directory.Exists(_schedulesDirectoryPath))
                {
                    Directory.CreateDirectory(_schedulesDirectoryPath);
                }

                // Serialize the schedules to JSON
                List<BackupSchedule> schedules;
                lock (_lockObject)
                {
                    schedules = _schedules.ToList();
                }

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };

                string json = JsonSerializer.Serialize(schedules, options);
                await File.WriteAllTextAsync(_schedulesFilePath, json);

                _logger?.LogInformation($"Saved {schedules.Count} backup schedules to {_schedulesFilePath}", "BackupSchedulerService");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error saving backup schedules to disk", "BackupSchedulerService", ex);
                return false;
            }
        }

        /// <summary>
        /// Adds a new backup schedule
        /// </summary>
        /// <param name="schedule">The schedule to add</param>
        /// <returns>True if the schedule was added successfully, false otherwise</returns>
        public async Task<bool> AddScheduleAsync(BackupSchedule schedule)
        {
            try
            {
                _logger?.LogInformation($"Adding backup schedule for ECU {schedule?.ECUName}", "BackupSchedulerService");

                if (!_isInitialized)
                {
                    _logger?.LogError("Backup scheduler service is not initialized", "BackupSchedulerService");
                    return false;
                }

                if (schedule == null)
                {
                    _logger?.LogError("Schedule is null", "BackupSchedulerService");
                    return false;
                }

                // Generate a new ID if not provided
                if (string.IsNullOrEmpty(schedule.Id))
                {
                    schedule.Id = Guid.NewGuid().ToString();
                }

                // Calculate the next execution time
                schedule.NextExecutionTime = schedule.CalculateNextExecutionTime(DateTime.Now);

                // Add the schedule to the list
                lock (_lockObject)
                {
                    _schedules.Add(schedule);
                }

                // Save schedules to disk
                await SaveSchedulesAsync();

                _logger?.LogInformation($"Backup schedule added for ECU {schedule.ECUName}", "BackupSchedulerService");
                ScheduleCreated?.Invoke(this, schedule);
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error adding backup schedule for ECU {schedule?.ECUName}", "BackupSchedulerService", ex);
                return false;
            }
        }

        /// <summary>
        /// Executes a backup schedule immediately
        /// </summary>
        /// <param name="schedule">The schedule to execute</param>
        /// <returns>The created backup data, or null if execution fails</returns>
        public async Task<BackupData?> ExecuteScheduleAsync(BackupSchedule schedule)
        {
            try
            {
                _logger?.LogInformation($"Executing backup schedule {schedule?.Id} ({schedule?.Name})", "BackupSchedulerService");

                if (!_isInitialized)
                {
                    _logger?.LogError("Backup scheduler service is not initialized", "BackupSchedulerService");
                    return null;
                }

                if (schedule == null)
                {
                    _logger?.LogError("Schedule is null", "BackupSchedulerService");
                    return null;
                }

                // Execute the backup
                return await ExecuteBackupAsync(schedule);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error executing backup schedule {schedule?.Id}", "BackupSchedulerService", ex);
                return null;
            }
        }

        /// <summary>
        /// Loads all schedules from disk
        /// </summary>
        /// <returns>True if loading is successful, false otherwise</returns>
        public async Task<bool> LoadSchedulesAsync()
        {
            try
            {
                _logger?.LogInformation("Loading backup schedules from disk", "BackupSchedulerService");

                if (!File.Exists(_schedulesFilePath))
                {
                    _logger?.LogInformation($"Schedules file not found: {_schedulesFilePath}", "BackupSchedulerService");
                    return true; // Not an error, just no schedules yet
                }

                // Read and deserialize the schedules
                string json = await File.ReadAllTextAsync(_schedulesFilePath);
                var loadedSchedules = JsonSerializer.Deserialize<List<BackupSchedule>>(json);

                if (loadedSchedules != null)
                {
                    // Update next execution times
                    foreach (var schedule in loadedSchedules)
                    {
                        schedule.NextExecutionTime = schedule.CalculateNextExecutionTime(DateTime.Now);
                    }

                    // Replace the current schedules
                    lock (_lockObject)
                    {
                        _schedules.Clear();
                        _schedules.AddRange(loadedSchedules);
                    }

                    _logger?.LogInformation($"Loaded {loadedSchedules.Count} backup schedules from {_schedulesFilePath}", "BackupSchedulerService");
                }
                else
                {
                    _logger?.LogWarning("Failed to deserialize backup schedules", "BackupSchedulerService");
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error loading backup schedules from disk", "BackupSchedulerService", ex);
                return false;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Timer callback to check for scheduled backups
        /// </summary>
        private void CheckSchedules(object? state)
        {
            try
            {
                if (!_isInitialized || !_isRunning)
                {
                    return;
                }

                DateTime now = DateTime.Now;
                List<BackupSchedule> schedulesToRun = new List<BackupSchedule>();

                // Find schedules that need to be executed
                lock (_lockObject)
                {
                    foreach (var schedule in _schedules)
                    {
                        // Skip disabled schedules
                        if (!schedule.IsEnabled)
                        {
                            continue;
                        }

                        // Skip schedules that are already running
                        if (_runningSchedules.Contains(schedule.Id))
                        {
                            continue;
                        }

                        // Check if it's time to run the schedule
                        if (schedule.NextExecutionTime <= now)
                        {
                            schedulesToRun.Add(schedule);
                            _runningSchedules.Add(schedule.Id);
                        }
                    }
                }

                // Execute the schedules
                foreach (var schedule in schedulesToRun)
                {
                    // Execute the backup asynchronously
                    Task.Run(async () =>
                    {
                        try
                        {
                            await ExecuteBackupAsync(schedule);
                        }
                        finally
                        {
                            // Remove the schedule from the running list
                            lock (_lockObject)
                            {
                                _runningSchedules.Remove(schedule.Id);
                            }
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error checking backup schedules", "BackupSchedulerService", ex);
            }
        }

        /// <summary>
        /// Executes a backup for a schedule
        /// </summary>
        /// <param name="schedule">The schedule to execute</param>
        /// <returns>The created backup data, or null if execution fails</returns>
        private async Task<BackupData?> ExecuteBackupAsync(BackupSchedule schedule)
        {
            try
            {
                _logger?.LogInformation($"Executing backup for schedule {schedule.Id} ({schedule.Name})", "BackupSchedulerService");

                // Notify that the backup has started
                ScheduledBackupStarted?.Invoke(this, schedule);

                if (_ecuService == null)
                {
                    string errorMessage = "ECU communication service is not initialized";
                    _logger?.LogError(errorMessage, "BackupSchedulerService");
                    ScheduledBackupError?.Invoke(this, (schedule, errorMessage));
                    return null;
                }

                if (_backupService == null)
                {
                    string errorMessage = "Backup service is not initialized";
                    _logger?.LogError(errorMessage, "BackupSchedulerService");
                    ScheduledBackupError?.Invoke(this, (schedule, errorMessage));
                    return null;
                }

                // Find the ECU
                var ecuDevices = await _ecuService.ScanForECUsAsync();
                var ecu = ecuDevices.FirstOrDefault(e => e.Id == schedule.ECUId);

                if (ecu == null)
                {
                    string errorMessage = $"ECU with ID {schedule.ECUId} not found";
                    _logger?.LogError(errorMessage, "BackupSchedulerService");
                    ScheduledBackupError?.Invoke(this, (schedule, errorMessage));

                    // Handle the failure with retry logic
                    await HandleBackupFailureAsync(schedule, errorMessage);
                    return null;
                }

                // Connect to the ECU if not already connected
                if (!_ecuService.ConnectedECUs.Any(e => e.Id == ecu.Id))
                {
                    bool connected = await _ecuService.ConnectToECUAsync(ecu);
                    if (!connected)
                    {
                        string errorMessage = $"Failed to connect to ECU {ecu.Name}";
                        _logger?.LogError(errorMessage, "BackupSchedulerService");
                        ScheduledBackupError?.Invoke(this, (schedule, errorMessage));

                        // Handle the failure with retry logic
                        await HandleBackupFailureAsync(schedule, errorMessage);
                        return null;
                    }
                }

                // Create the backup
                var backup = await _backupService.CreateBackupAsync(
                    ecu,
                    $"Scheduled backup: {schedule.Name}",
                    schedule.Category,
                    schedule.Tags,
                    schedule.IncludeEEPROM,
                    schedule.IncludeMicrocontrollerCode,
                    schedule.IncludeParameters);

                if (backup == null)
                {
                    string errorMessage = $"Failed to create backup for ECU {ecu.Name}";
                    _logger?.LogError(errorMessage, "BackupSchedulerService");
                    ScheduledBackupError?.Invoke(this, (schedule, errorMessage));

                    // Handle the failure with retry logic
                    await HandleBackupFailureAsync(schedule, errorMessage);
                    return null;
                }

                // Reset retry tracking on successful backup
                lock (_lockObject)
                {
                    if (_lastFailedSchedules.ContainsKey(schedule.Id))
                    {
                        _lastFailedSchedules.Remove(schedule.Id);
                    }
                }

                // Reset the retry count
                schedule.RetryCount = 0;

                // Update the schedule
                // Update last execution time
                schedule.LastExecutionTime = DateTime.Now;

                // Calculate next execution time
                schedule.NextExecutionTime = schedule.CalculateNextExecutionTime(DateTime.Now);

                // Add the backup ID to the list of created backups
                lock (_lockObject)
                {
                    schedule.CreatedBackupIds.Add(backup.Id);
                }

                // Apply backup rotation and cleanup policies
                await ApplyBackupRetentionPolicyAsync(schedule, backup);

                // Save the updated schedules
                await SaveSchedulesAsync();

                // Notify that the backup has completed
                ScheduledBackupCompleted?.Invoke(this, (schedule, backup));

                _logger?.LogInformation($"Backup for schedule {schedule.Id} ({schedule.Name}) completed successfully", "BackupSchedulerService");
                return backup;
            }
            catch (Exception ex)
            {
                string errorMessage = $"Error executing backup for schedule {schedule.Id} ({schedule.Name}): {ex.Message}";
                _logger?.LogError(errorMessage, "BackupSchedulerService", ex);
                ScheduledBackupError?.Invoke(this, (schedule, errorMessage));
                return null;
            }
        }

        /// <summary>
        /// Applies backup retention policy based on schedule settings
        /// </summary>
        /// <param name="schedule">The backup schedule</param>
        /// <param name="newBackup">The newly created backup</param>
        /// <returns>Task representing the asynchronous operation</returns>
        private async Task ApplyBackupRetentionPolicyAsync(BackupSchedule schedule, BackupData newBackup)
        {
            try
            {
                _logger?.LogInformation($"Applying backup retention policy for schedule {schedule.Id} ({schedule.Name})", "BackupSchedulerService");

                if (_backupService == null)
                {
                    _logger?.LogError("Backup service is not initialized", "BackupSchedulerService");
                    return;
                }

                // Get all backups created by this schedule
                var allBackups = await _backupService.GetAllBackupsAsync();
                var scheduleBackups = allBackups
                    .Where(b => schedule.CreatedBackupIds.Contains(b.Id))
                    .OrderByDescending(b => b.CreationTime)
                    .ToList();

                // If no maximum is set or we're under the limit, nothing to do
                if (schedule.MaxBackupsToKeep <= 0 || scheduleBackups.Count <= schedule.MaxBackupsToKeep)
                {
                    return;
                }

                _logger?.LogInformation($"Schedule has {scheduleBackups.Count} backups, maximum is {schedule.MaxBackupsToKeep}", "BackupSchedulerService");

                // Determine which backups to keep based on retention policy
                List<BackupData> backupsToKeep = new List<BackupData>();
                List<BackupData> backupsToDelete = new List<BackupData>();

                // Always keep the newest backup
                backupsToKeep.Add(newBackup);

                // Apply retention policy based on schedule type
                switch (schedule.FrequencyType)
                {
                    case BackupFrequencyType.Hourly:
                        // For hourly backups, keep one backup per hour for the last N hours
                        ApplyHourlyRetentionPolicy(scheduleBackups, backupsToKeep, schedule.MaxBackupsToKeep);
                        break;

                    case BackupFrequencyType.Daily:
                        // For daily backups, keep one backup per day for the last N days
                        ApplyDailyRetentionPolicy(scheduleBackups, backupsToKeep, schedule.MaxBackupsToKeep);
                        break;

                    case BackupFrequencyType.Weekly:
                        // For weekly backups, keep one backup per week for the last N weeks
                        ApplyWeeklyRetentionPolicy(scheduleBackups, backupsToKeep, schedule.MaxBackupsToKeep);
                        break;

                    case BackupFrequencyType.Monthly:
                        // For monthly backups, keep one backup per month for the last N months
                        ApplyMonthlyRetentionPolicy(scheduleBackups, backupsToKeep, schedule.MaxBackupsToKeep);
                        break;

                    default:
                        // For other schedule types, just keep the newest N backups
                        backupsToKeep.AddRange(scheduleBackups.Skip(1).Take(schedule.MaxBackupsToKeep - 1));
                        break;
                }

                // Determine which backups to delete
                backupsToDelete = scheduleBackups.Except(backupsToKeep).ToList();

                // Update the schedule's list of created backups
                foreach (var backup in backupsToDelete)
                {
                    schedule.CreatedBackupIds.Remove(backup.Id);
                }

                // Delete the backups
                foreach (var backup in backupsToDelete)
                {
                    try
                    {
                        await _backupService.DeleteBackupAsync(backup);
                        _logger?.LogInformation($"Deleted backup {backup.Id} ({backup.CreationTime}) to maintain retention policy", "BackupSchedulerService");
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError($"Error deleting backup {backup.Id}", "BackupSchedulerService", ex);
                    }
                }

                _logger?.LogInformation($"Backup retention policy applied: kept {backupsToKeep.Count} backups, deleted {backupsToDelete.Count} backups", "BackupSchedulerService");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error applying backup retention policy for schedule {schedule.Id}", "BackupSchedulerService", ex);
            }
        }

        /// <summary>
        /// Applies hourly retention policy to backups
        /// </summary>
        /// <param name="allBackups">All backups for the schedule</param>
        /// <param name="backupsToKeep">List of backups to keep</param>
        /// <param name="maxBackupsToKeep">Maximum number of backups to keep</param>
        private void ApplyHourlyRetentionPolicy(List<BackupData> allBackups, List<BackupData> backupsToKeep, int maxBackupsToKeep)
        {
            // Group backups by hour
            var backupsByHour = allBackups
                .GroupBy(b => new { b.CreationTime.Date, Hour = b.CreationTime.Hour })
                .OrderByDescending(g => g.Key.Date)
                .ThenByDescending(g => g.Key.Hour)
                .ToList();

            // Keep one backup per hour for the last N hours
            foreach (var hourGroup in backupsByHour)
            {
                if (backupsToKeep.Count >= maxBackupsToKeep)
                {
                    break;
                }

                // Get the newest backup for this hour that's not already in the keep list
                var backupForHour = hourGroup
                    .OrderByDescending(b => b.CreationTime)
                    .FirstOrDefault(b => !backupsToKeep.Contains(b));

                if (backupForHour != null)
                {
                    backupsToKeep.Add(backupForHour);
                }
            }
        }

        /// <summary>
        /// Applies daily retention policy to backups
        /// </summary>
        /// <param name="allBackups">All backups for the schedule</param>
        /// <param name="backupsToKeep">List of backups to keep</param>
        /// <param name="maxBackupsToKeep">Maximum number of backups to keep</param>
        private void ApplyDailyRetentionPolicy(List<BackupData> allBackups, List<BackupData> backupsToKeep, int maxBackupsToKeep)
        {
            // Group backups by day
            var backupsByDay = allBackups
                .GroupBy(b => b.CreationTime.Date)
                .OrderByDescending(g => g.Key)
                .ToList();

            // Keep one backup per day for the last N days
            foreach (var dayGroup in backupsByDay)
            {
                if (backupsToKeep.Count >= maxBackupsToKeep)
                {
                    break;
                }

                // Get the newest backup for this day that's not already in the keep list
                var backupForDay = dayGroup
                    .OrderByDescending(b => b.CreationTime)
                    .FirstOrDefault(b => !backupsToKeep.Contains(b));

                if (backupForDay != null)
                {
                    backupsToKeep.Add(backupForDay);
                }
            }
        }

        /// <summary>
        /// Applies weekly retention policy to backups
        /// </summary>
        /// <param name="allBackups">All backups for the schedule</param>
        /// <param name="backupsToKeep">List of backups to keep</param>
        /// <param name="maxBackupsToKeep">Maximum number of backups to keep</param>
        private void ApplyWeeklyRetentionPolicy(List<BackupData> allBackups, List<BackupData> backupsToKeep, int maxBackupsToKeep)
        {
            // Group backups by week
            var backupsByWeek = allBackups
                .GroupBy(b => GetWeekOfYear(b.CreationTime))
                .OrderByDescending(g => g.Key)
                .ToList();

            // Keep one backup per week for the last N weeks
            foreach (var weekGroup in backupsByWeek)
            {
                if (backupsToKeep.Count >= maxBackupsToKeep)
                {
                    break;
                }

                // Get the newest backup for this week that's not already in the keep list
                var backupForWeek = weekGroup
                    .OrderByDescending(b => b.CreationTime)
                    .FirstOrDefault(b => !backupsToKeep.Contains(b));

                if (backupForWeek != null)
                {
                    backupsToKeep.Add(backupForWeek);
                }
            }
        }

        /// <summary>
        /// Applies monthly retention policy to backups
        /// </summary>
        /// <param name="allBackups">All backups for the schedule</param>
        /// <param name="backupsToKeep">List of backups to keep</param>
        /// <param name="maxBackupsToKeep">Maximum number of backups to keep</param>
        private void ApplyMonthlyRetentionPolicy(List<BackupData> allBackups, List<BackupData> backupsToKeep, int maxBackupsToKeep)
        {
            // Group backups by month
            var backupsByMonth = allBackups
                .GroupBy(b => new { b.CreationTime.Year, b.CreationTime.Month })
                .OrderByDescending(g => g.Key.Year)
                .ThenByDescending(g => g.Key.Month)
                .ToList();

            // Keep one backup per month for the last N months
            foreach (var monthGroup in backupsByMonth)
            {
                if (backupsToKeep.Count >= maxBackupsToKeep)
                {
                    break;
                }

                // Get the newest backup for this month that's not already in the keep list
                var backupForMonth = monthGroup
                    .OrderByDescending(b => b.CreationTime)
                    .FirstOrDefault(b => !backupsToKeep.Contains(b));

                if (backupForMonth != null)
                {
                    backupsToKeep.Add(backupForMonth);
                }
            }
        }

        /// <summary>
        /// Gets the week number of the year for a given date
        /// </summary>
        /// <param name="date">The date</param>
        /// <returns>The week number</returns>
        private int GetWeekOfYear(DateTime date)
        {
            // Use ISO 8601 week definition
            var cal = System.Globalization.DateTimeFormatInfo.CurrentInfo.Calendar;
            return cal.GetWeekOfYear(date, System.Globalization.CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Monday);
        }

        /// <summary>
        /// Handles retry logic for failed backup schedules
        /// </summary>
        /// <param name="schedule">The schedule that failed</param>
        /// <param name="errorMessage">The error message</param>
        private async Task HandleBackupFailureAsync(BackupSchedule schedule, string errorMessage)
        {
            try
            {
                _logger?.LogWarning($"Handling backup failure for schedule {schedule.Id} ({schedule.Name}): {errorMessage}", "BackupSchedulerService");

                // Increment the retry count
                schedule.RetryCount++;

                // Update the last failure time in our tracking dictionary
                lock (_lockObject)
                {
                    _lastFailedSchedules[schedule.Id] = DateTime.Now;
                }

                // If we've exceeded the maximum retry attempts, disable the schedule
                if (schedule.RetryCount >= _maxRetryAttempts)
                {
                    _logger?.LogError($"Schedule {schedule.Id} ({schedule.Name}) has failed {schedule.RetryCount} times, disabling it", "BackupSchedulerService");

                    // Disable the schedule
                    schedule.IsEnabled = false;

                    // Save the updated schedule
                    await SaveSchedulesAsync();

                    // Notify that the schedule has been updated
                    ScheduleUpdated?.Invoke(this, schedule);
                    return;
                }

                // Schedule a retry after the delay
                DateTime retryTime = DateTime.Now.Add(_retryDelay);
                schedule.NextExecutionTime = retryTime;

                _logger?.LogInformation($"Scheduled retry {schedule.RetryCount}/{_maxRetryAttempts} for schedule {schedule.Id} at {retryTime}", "BackupSchedulerService");

                // Save the updated schedule
                await SaveSchedulesAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error handling backup failure for schedule {schedule.Id}", "BackupSchedulerService", ex);
            }
        }

        /// <summary>
        /// Applies a yearly retention policy to backups
        /// </summary>
        /// <param name="allBackups">All backups for the schedule</param>
        /// <param name="backupsToKeep">List of backups to keep</param>
        /// <param name="maxBackupsToKeep">Maximum number of backups to keep</param>
        private void ApplyYearlyRetentionPolicy(List<BackupData> allBackups, List<BackupData> backupsToKeep, int maxBackupsToKeep)
        {
            // Group backups by year
            var backupsByYear = allBackups
                .GroupBy(b => b.CreationTime.Year)
                .OrderByDescending(g => g.Key)
                .ToList();

            // Keep one backup per year for the last N years
            foreach (var yearGroup in backupsByYear)
            {
                if (backupsToKeep.Count >= maxBackupsToKeep)
                {
                    break;
                }

                // Get the newest backup for this year that's not already in the keep list
                var backupForYear = yearGroup
                    .OrderByDescending(b => b.CreationTime)
                    .FirstOrDefault(b => !backupsToKeep.Contains(b));

                if (backupForYear != null)
                {
                    backupsToKeep.Add(backupForYear);
                }
            }
        }

        #endregion
    }
}
