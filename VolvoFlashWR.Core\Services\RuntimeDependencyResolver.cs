using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Resolves missing runtime dependencies like Visual C++ redistributables
    /// </summary>
    public class RuntimeDependencyResolver
    {
        private readonly ILoggingService _logger;
        private readonly string _applicationPath;

        public RuntimeDependencyResolver(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = AppDomain.CurrentDomain.BaseDirectory;
        }

        /// <summary>
        /// Checks and resolves missing Visual C++ runtime dependencies with enhanced x64 support
        /// </summary>
        public async Task<bool> ResolveVCRuntimeDependenciesAsync()
        {
            try
            {
                _logger.LogInformation("Checking Visual C++ runtime dependencies", "RuntimeDependencyResolver");

                // Determine process architecture
                bool isX64Process = Environment.Is64BitProcess;
                _logger.LogInformation($"Process architecture: {(isX64Process ? "x64" : "x86")}", "RuntimeDependencyResolver");

                // Enhanced list of critical VC runtime libraries for x64 process
                string[] criticalLibraries = {
                    "msvcr140.dll",
                    "msvcp140.dll",
                    "vcruntime140.dll",
                    "api-ms-win-crt-runtime-l1-1-0.dll",
                    "api-ms-win-crt-heap-l1-1-0.dll",
                    "api-ms-win-crt-string-l1-1-0.dll",
                    "api-ms-win-crt-stdio-l1-1-0.dll",
                    "api-ms-win-crt-math-l1-1-0.dll",
                    "api-ms-win-crt-locale-l1-1-0.dll"
                };

                bool allResolved = true;
                int resolvedCount = 0;
                int totalCount = criticalLibraries.Length;

                foreach (string library in criticalLibraries)
                {
                    if (!IsVCRuntimeAvailable(library))
                    {
                        _logger.LogInformation($"Attempting to resolve missing library: {library}", "RuntimeDependencyResolver");

                        if (await TryResolveLibraryAsync(library, isX64Process))
                        {
                            resolvedCount++;
                            _logger.LogInformation($"✓ Successfully resolved: {library}", "RuntimeDependencyResolver");
                        }
                        else
                        {
                            _logger.LogWarning($"✗ Failed to resolve: {library}", "RuntimeDependencyResolver");
                            allResolved = false;
                        }
                    }
                    else
                    {
                        resolvedCount++;
                        _logger.LogInformation($"✓ Already available: {library}", "RuntimeDependencyResolver");
                    }
                }

                _logger.LogInformation($"Runtime dependency resolution: {resolvedCount}/{totalCount} libraries available", "RuntimeDependencyResolver");

                if (!allResolved)
                {
                    await LogInstallationGuidanceAsync(isX64Process);
                }

                return resolvedCount >= (totalCount * 0.7); // Allow 70% success rate
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during VC runtime dependency resolution: {ex.Message}", "RuntimeDependencyResolver");
                return false;
            }
        }

        /// <summary>
        /// Checks if a Visual C++ runtime library is available
        /// </summary>
        private bool IsVCRuntimeAvailable(string libraryName)
        {
            try
            {
                // Check in application directory
                string appPath = Path.Combine(_applicationPath, libraryName);
                if (File.Exists(appPath))
                {
                    return true;
                }

                // Check in Libraries subdirectory
                string libPath = Path.Combine(_applicationPath, "Libraries", libraryName);
                if (File.Exists(libPath))
                {
                    return true;
                }

                // Try to load from system
                IntPtr handle = LoadLibrary(libraryName);
                if (handle != IntPtr.Zero)
                {
                    FreeLibrary(handle);
                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Attempts to copy a library from system directories
        /// </summary>
        private async Task<bool> TryCopyFromSystemDirectoriesAsync(string libraryName)
        {
            try
            {
                string[] systemPaths = {
                    Environment.GetFolderPath(Environment.SpecialFolder.System),
                    Environment.GetFolderPath(Environment.SpecialFolder.SystemX86),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "System32"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "SysWOW64")
                };

                foreach (string systemPath in systemPaths)
                {
                    string sourcePath = Path.Combine(systemPath, libraryName);
                    if (File.Exists(sourcePath))
                    {
                        try
                        {
                            string destinationPath = Path.Combine(_applicationPath, "Libraries", libraryName);
                            
                            // Ensure Libraries directory exists
                            Directory.CreateDirectory(Path.GetDirectoryName(destinationPath));
                            
                            await Task.Run(() => File.Copy(sourcePath, destinationPath, true));
                            
                            _logger.LogInformation($"Successfully copied {libraryName} from {sourcePath}", "RuntimeDependencyResolver");
                            return true;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"Failed to copy {libraryName} from {sourcePath}: {ex.Message}", "RuntimeDependencyResolver");
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception copying {libraryName}: {ex.Message}", "RuntimeDependencyResolver");
                return false;
            }
        }

        /// <summary>
        /// Enhanced library resolution with architecture awareness
        /// </summary>
        private async Task<bool> TryResolveLibraryAsync(string libraryName, bool isX64Process)
        {
            // Try multiple resolution strategies
            if (await TryCopyFromSystemDirectoriesAsync(libraryName))
                return true;

            if (await TryDownloadFromMicrosoftAsync(libraryName, isX64Process))
                return true;

            return false;
        }

        /// <summary>
        /// Attempts to download library from Microsoft servers
        /// </summary>
        private async Task<bool> TryDownloadFromMicrosoftAsync(string libraryName, bool isX64Process)
        {
            try
            {
                string downloadUrl = isX64Process ?
                    "https://aka.ms/vs/17/release/vc_redist.x64.exe" :
                    "https://aka.ms/vs/17/release/vc_redist.x86.exe";

                _logger.LogInformation($"Attempting to download {libraryName} from Microsoft redistributable", "RuntimeDependencyResolver");

                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromMinutes(5);

                string tempPath = Path.Combine(Path.GetTempPath(), $"vcredist_{Guid.NewGuid()}.exe");

                var response = await httpClient.GetAsync(downloadUrl);
                response.EnsureSuccessStatusCode();

                using (var fileStream = File.Create(tempPath))
                {
                    await response.Content.CopyToAsync(fileStream);
                }

                // Try to extract the specific library from the redistributable
                bool extracted = await TryExtractLibraryFromRedistAsync(tempPath, libraryName);

                // Cleanup
                if (File.Exists(tempPath))
                    File.Delete(tempPath);

                return extracted;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to download {libraryName}: {ex.Message}", "RuntimeDependencyResolver");
                return false;
            }
        }

        /// <summary>
        /// Attempts to extract a specific library from a redistributable package
        /// </summary>
        private async Task<bool> TryExtractLibraryFromRedistAsync(string redistPath, string libraryName)
        {
            try
            {
                string extractPath = Path.Combine(Path.GetTempPath(), $"vcredist_extract_{Guid.NewGuid()}");
                Directory.CreateDirectory(extractPath);

                // Try to extract using different methods
                var processInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = redistPath,
                    Arguments = $"/extract:{extractPath} /quiet",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = System.Diagnostics.Process.Start(processInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();

                    // Look for the target library in extracted files
                    var extractedFiles = Directory.GetFiles(extractPath, "*.dll", SearchOption.AllDirectories);
                    foreach (string file in extractedFiles)
                    {
                        if (Path.GetFileName(file).Equals(libraryName, StringComparison.OrdinalIgnoreCase))
                        {
                            string targetPath = Path.Combine(_applicationPath, "Libraries", libraryName);
                            Directory.CreateDirectory(Path.GetDirectoryName(targetPath));
                            File.Copy(file, targetPath, true);
                            _logger.LogInformation($"Extracted and copied: {libraryName}", "RuntimeDependencyResolver");

                            // Cleanup
                            Directory.Delete(extractPath, true);
                            return true;
                        }
                    }
                }

                // Cleanup
                if (Directory.Exists(extractPath))
                    Directory.Delete(extractPath, true);

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to extract {libraryName}: {ex.Message}", "RuntimeDependencyResolver");
                return false;
            }
        }

        /// <summary>
        /// Logs enhanced installation guidance based on architecture
        /// </summary>
        private async Task LogInstallationGuidanceAsync(bool isX64Process)
        {
            await Task.CompletedTask;

            _logger.LogInformation("=== Visual C++ Runtime Installation Guidance ===", "RuntimeDependencyResolver");
            _logger.LogInformation("If you continue to experience issues with missing Visual C++ runtime libraries:", "RuntimeDependencyResolver");

            if (isX64Process)
            {
                _logger.LogInformation("1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)", "RuntimeDependencyResolver");
                _logger.LogInformation("2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe", "RuntimeDependencyResolver");
            }
            else
            {
                _logger.LogInformation("1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x86)", "RuntimeDependencyResolver");
                _logger.LogInformation("2. Download link: https://aka.ms/vs/17/release/vc_redist.x86.exe", "RuntimeDependencyResolver");
            }

            _logger.LogInformation("3. Restart the application after installation", "RuntimeDependencyResolver");
            _logger.LogInformation("4. Ensure Windows is up to date", "RuntimeDependencyResolver");
            _logger.LogInformation("=== End Installation Guidance ===", "RuntimeDependencyResolver");
        }

        /// <summary>
        /// Provides installation guidance for missing dependencies
        /// </summary>
        public void ProvideInstallationGuidance()
        {
            bool isX64Process = Environment.Is64BitProcess;
            _logger.LogInformation("=== Visual C++ Runtime Installation Guidance ===", "RuntimeDependencyResolver");
            _logger.LogInformation("If you continue to experience issues with missing Visual C++ runtime libraries:", "RuntimeDependencyResolver");

            if (isX64Process)
            {
                _logger.LogInformation("1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)", "RuntimeDependencyResolver");
                _logger.LogInformation("2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe", "RuntimeDependencyResolver");
            }
            else
            {
                _logger.LogInformation("1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x86)", "RuntimeDependencyResolver");
                _logger.LogInformation("2. Download link: https://aka.ms/vs/17/release/vc_redist.x86.exe", "RuntimeDependencyResolver");
            }

            _logger.LogInformation("3. Restart the application after installation", "RuntimeDependencyResolver");
            _logger.LogInformation("=== End Installation Guidance ===", "RuntimeDependencyResolver");
        }

        #region P/Invoke Declarations

        [System.Runtime.InteropServices.DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [System.Runtime.InteropServices.DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        #endregion
    }
}
