using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Enhanced library resolver specifically designed for x64 architecture compatibility
    /// Handles the architecture mismatch between x64 application and x86 APCI libraries
    /// </summary>
    public class X64LibraryResolver
    {
        private readonly ILoggingService _logger;
        private readonly string _applicationPath;
        private readonly string _librariesPath;
        private readonly bool _is64BitProcess;

        // Windows API imports for library loading
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern int GetLastError();

        // Critical Visual C++ runtime libraries for x64
        private static readonly Dictionary<string, LibraryInfo> RequiredX64Libraries = new()
        {
            ["msvcr140.dll"] = new LibraryInfo
            {
                Name = "msvcr140.dll",
                Description = "Microsoft Visual C++ 2015-2022 Runtime (x64)",
                DownloadUrl = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
                Architecture = "x64",
                IsRequired = true
            },
            ["msvcp140.dll"] = new LibraryInfo
            {
                Name = "msvcp140.dll", 
                Description = "Microsoft Visual C++ 2015-2022 C++ Runtime (x64)",
                DownloadUrl = "https://aka.ms/vs/17/release/vc_redist.x64.exe",
                Architecture = "x64",
                IsRequired = true
            },
            ["vcruntime140.dll"] = new LibraryInfo
            {
                Name = "vcruntime140.dll",
                Description = "Microsoft Visual C++ 2015-2022 Runtime (x64)",
                DownloadUrl = "https://aka.ms/vs/17/release/vc_redist.x64.exe", 
                Architecture = "x64",
                IsRequired = true
            }
        };

        // APCI libraries that are x86 and need special handling
        private static readonly Dictionary<string, LibraryInfo> ApciLibraries = new()
        {
            ["apci.dll"] = new LibraryInfo
            {
                Name = "apci.dll",
                Description = "APCI Communication Library (x86)",
                Architecture = "x86",
                RequiresBridge = true
            },
            ["apcidb.dll"] = new LibraryInfo
            {
                Name = "apcidb.dll", 
                Description = "APCI Database Library (x86)",
                Architecture = "x86",
                RequiresBridge = true
            },
            ["Volvo.ApciPlus.dll"] = new LibraryInfo
            {
                Name = "Volvo.ApciPlus.dll",
                Description = "Volvo APCI Plus Library (x86)",
                Architecture = "x86", 
                RequiresBridge = true
            },
            ["Volvo.ApciPlusData.dll"] = new LibraryInfo
            {
                Name = "Volvo.ApciPlusData.dll",
                Description = "Volvo APCI Plus Data Library (x86)",
                Architecture = "x86",
                RequiresBridge = true
            }
        };

        public X64LibraryResolver(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = AppDomain.CurrentDomain.BaseDirectory;
            _librariesPath = Path.Combine(_applicationPath, "Libraries");
            _is64BitProcess = Environment.Is64BitProcess;

            _logger.LogInformation($"X64LibraryResolver initialized for {(_is64BitProcess ? "x64" : "x86")} process", "X64LibraryResolver");
        }

        /// <summary>
        /// Resolves all library dependencies for x64 architecture
        /// </summary>
        public async Task<LibraryResolutionResult> ResolveLibrariesAsync()
        {
            var result = new LibraryResolutionResult();

            try
            {
                _logger.LogInformation("Starting x64 library resolution", "X64LibraryResolver");

                // Ensure libraries directory exists
                if (!Directory.Exists(_librariesPath))
                {
                    Directory.CreateDirectory(_librariesPath);
                    _logger.LogInformation($"Created libraries directory: {_librariesPath}", "X64LibraryResolver");
                }

                // Step 1: Resolve Visual C++ runtime libraries
                await ResolveVCRuntimeLibrariesAsync(result);

                // Step 2: Analyze APCI library compatibility
                await AnalyzeApciLibrariesAsync(result);

                // Step 3: Set up architecture bridge if needed
                await SetupArchitectureBridgeAsync(result);

                // Step 4: Configure environment for x64 compatibility
                ConfigureX64Environment(result);

                _logger.LogInformation($"Library resolution completed. Success: {result.IsSuccessful}", "X64LibraryResolver");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during library resolution: {ex.Message}", "X64LibraryResolver");
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        private async Task ResolveVCRuntimeLibrariesAsync(LibraryResolutionResult result)
        {
            _logger.LogInformation("Resolving Visual C++ runtime libraries", "X64LibraryResolver");

            foreach (var library in RequiredX64Libraries.Values)
            {
                string libraryPath = Path.Combine(_librariesPath, library.Name);
                
                if (File.Exists(libraryPath))
                {
                    // Verify architecture compatibility
                    if (IsLibraryCompatible(libraryPath))
                    {
                        result.ResolvedLibraries.Add(library.Name);
                        _logger.LogInformation($"✓ Found compatible library: {library.Name}", "X64LibraryResolver");
                        continue;
                    }
                    else
                    {
                        _logger.LogWarning($"⚠ Library architecture mismatch: {library.Name}", "X64LibraryResolver");
                    }
                }

                // Try to resolve from system
                if (await TryResolveFromSystemAsync(library))
                {
                    result.ResolvedLibraries.Add(library.Name);
                    continue;
                }

                // Try to download and install
                if (await TryDownloadAndInstallAsync(library))
                {
                    result.ResolvedLibraries.Add(library.Name);
                    continue;
                }

                // Failed to resolve
                result.MissingLibraries.Add(library.Name);
                _logger.LogWarning($"✗ Failed to resolve: {library.Name}", "X64LibraryResolver");
            }
        }

        private async Task AnalyzeApciLibrariesAsync(LibraryResolutionResult result)
        {
            _logger.LogInformation("Analyzing APCI library compatibility", "X64LibraryResolver");

            foreach (var library in ApciLibraries.Values)
            {
                var paths = new[]
                {
                    Path.Combine(_applicationPath, library.Name),
                    Path.Combine(_librariesPath, library.Name)
                };

                bool found = false;
                foreach (string path in paths)
                {
                    if (File.Exists(path))
                    {
                        string arch = GetLibraryArchitecture(path);
                        _logger.LogInformation($"Found APCI library: {library.Name} ({arch})", "X64LibraryResolver");
                        
                        if (arch == "x86" && _is64BitProcess)
                        {
                            result.IncompatibleLibraries.Add(library.Name);
                            result.RequiresArchitectureBridge = true;
                            _logger.LogWarning($"⚠ Architecture mismatch: {library.Name} is x86, process is x64", "X64LibraryResolver");
                        }
                        else if (arch == "x64")
                        {
                            result.CompatibleLibraries.Add(library.Name);
                            _logger.LogInformation($"✓ Compatible: {library.Name} is x64", "X64LibraryResolver");
                        }
                        
                        found = true;
                        break;
                    }
                }

                if (!found)
                {
                    result.MissingLibraries.Add(library.Name);
                    _logger.LogWarning($"✗ Missing: {library.Name}", "X64LibraryResolver");
                }
            }
        }

        private async Task SetupArchitectureBridgeAsync(LibraryResolutionResult result)
        {
            if (!result.RequiresArchitectureBridge)
            {
                _logger.LogInformation("Architecture bridge not required", "X64LibraryResolver");
                return;
            }

            _logger.LogInformation("Setting up architecture bridge for x86 library compatibility", "X64LibraryResolver");

            string bridgePath = Path.Combine(_applicationPath, "Bridge", "VolvoFlashWR.VocomBridge.exe");
            
            if (File.Exists(bridgePath))
            {
                string bridgeArch = GetLibraryArchitecture(bridgePath);
                _logger.LogInformation($"Found architecture bridge: {bridgePath} ({bridgeArch})", "X64LibraryResolver");
                
                if (bridgeArch == "x86")
                {
                    result.ArchitectureBridgeAvailable = true;
                    result.BridgePath = bridgePath;
                    _logger.LogInformation("✓ Architecture bridge is properly configured", "X64LibraryResolver");
                }
                else
                {
                    _logger.LogWarning($"⚠ Bridge architecture mismatch: expected x86, found {bridgeArch}", "X64LibraryResolver");
                }
            }
            else
            {
                _logger.LogWarning($"⚠ Architecture bridge not found: {bridgePath}", "X64LibraryResolver");
                result.Recommendations.Add("Consider building the VocomBridge project as x86 for APCI library compatibility");
            }

            await Task.CompletedTask;
        }

        private void ConfigureX64Environment(LibraryResolutionResult result)
        {
            _logger.LogInformation("Configuring environment for x64 compatibility", "X64LibraryResolver");

            // Set environment variables for proper library loading
            var envVars = new Dictionary<string, string>
            {
                ["USE_PATCHED_IMPLEMENTATION"] = "true",
                ["PHOENIX_VOCOM_ENABLED"] = "true",
                ["VERBOSE_LOGGING"] = "true", 
                ["APCI_LIBRARY_PATH"] = _librariesPath,
                ["FORCE_ARCHITECTURE_BRIDGE"] = result.RequiresArchitectureBridge.ToString().ToLower()
            };

            foreach (var envVar in envVars)
            {
                Environment.SetEnvironmentVariable(envVar.Key, envVar.Value, EnvironmentVariableTarget.Process);
                result.EnvironmentVariables[envVar.Key] = envVar.Value;
                _logger.LogInformation($"Set environment variable: {envVar.Key} = {envVar.Value}", "X64LibraryResolver");
            }

            // Add libraries path to PATH environment variable
            string currentPath = Environment.GetEnvironmentVariable("PATH", EnvironmentVariableTarget.Process) ?? "";
            if (!currentPath.Contains(_librariesPath))
            {
                string newPath = $"{_librariesPath};{currentPath}";
                Environment.SetEnvironmentVariable("PATH", newPath, EnvironmentVariableTarget.Process);
                _logger.LogInformation($"Added libraries path to PATH: {_librariesPath}", "X64LibraryResolver");
            }
        }
