using System;
using System.IO;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Communication.Backup
{
    /// <summary>
    /// Factory for creating and initializing backup scheduler services
    /// </summary>
    public class BackupSchedulerServiceFactory
    {
        private readonly ILoggingService _logger;
        private readonly IBackupService _backupService;
        private readonly IECUCommunicationService _ecuService;

        /// <summary>
        /// Initializes a new instance of the BackupSchedulerServiceFactory class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="backupService">The backup service</param>
        /// <param name="ecuService">The ECU communication service</param>
        public BackupSchedulerServiceFactory(ILoggingService logger, IBackupService backupService, IECUCommunicationService ecuService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));
            _ecuService = ecuService ?? throw new ArgumentNullException(nameof(ecuService));
        }

        /// <summary>
        /// Creates and initializes a new backup scheduler service with default settings
        /// </summary>
        /// <returns>The initialized backup scheduler service</returns>
        public async Task<IBackupSchedulerService> CreateServiceAsync()
        {
            try
            {
                _logger.LogInformation("Creating backup scheduler service with default settings", "BackupSchedulerServiceFactory");

                // Create the service
                var service = new BackupSchedulerService(_logger);

                // Initialize the service
                bool initialized = await service.InitializeAsync(_backupService, _ecuService);
                if (!initialized)
                {
                    _logger.LogError("Failed to initialize backup scheduler service", "BackupSchedulerServiceFactory");
                    return null;
                }

                // Start the scheduler
                bool started = await service.StartAsync();
                if (!started)
                {
                    _logger.LogError("Failed to start backup scheduler service", "BackupSchedulerServiceFactory");
                    return null;
                }

                _logger.LogInformation("Backup scheduler service created, initialized, and started successfully", "BackupSchedulerServiceFactory");
                return service;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error creating backup scheduler service", "BackupSchedulerServiceFactory", ex);
                return null;
            }
        }

        /// <summary>
        /// Creates and initializes a new backup scheduler service with custom settings
        /// </summary>
        /// <param name="schedulesDirectoryPath">Custom schedules directory path (null for default)</param>
        /// <returns>The initialized backup scheduler service</returns>
        public async Task<IBackupSchedulerService> CreateServiceAsync(string schedulesDirectoryPath)
        {
            try
            {
                _logger.LogInformation("Creating backup scheduler service with custom settings", "BackupSchedulerServiceFactory");

                // Create the service
                var service = new BackupSchedulerService(_logger);

                // Set the schedules directory path using reflection
                if (!string.IsNullOrEmpty(schedulesDirectoryPath))
                {
                    var field = typeof(BackupSchedulerService).GetField("_schedulesDirectoryPath", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    field.SetValue(service, schedulesDirectoryPath);

                    // Set the schedules file path
                    field = typeof(BackupSchedulerService).GetField("_schedulesFilePath", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    field.SetValue(service, Path.Combine(schedulesDirectoryPath, "backup_schedules.json"));

                    _logger.LogInformation($"Set custom schedules directory path: {schedulesDirectoryPath}", "BackupSchedulerServiceFactory");
                }

                // Initialize the service
                bool initialized = await service.InitializeAsync(_backupService, _ecuService);
                if (!initialized)
                {
                    _logger.LogError("Failed to initialize backup scheduler service", "BackupSchedulerServiceFactory");
                    return null;
                }

                // Start the scheduler
                bool started = await service.StartAsync();
                if (!started)
                {
                    _logger.LogError("Failed to start backup scheduler service", "BackupSchedulerServiceFactory");
                    return null;
                }

                _logger.LogInformation("Backup scheduler service created with custom settings, initialized, and started successfully", "BackupSchedulerServiceFactory");
                return service;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error creating backup scheduler service with custom settings", "BackupSchedulerServiceFactory", ex);
                return null;
            }
        }
    }
}
