using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;
using VolvoFlashWR.Core.Utilities;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Implementation of Vocom adapter using Phoenix Diag Flash Editor Plus libraries
    /// </summary>
    public class PhoenixVocomAdapter : IVocomDeviceDriver
    {
        private readonly ILoggingService _logger;
        private bool _isInitialized = false;
        private bool _isConnected = false;
        private VocomDevice? _currentDevice = null;

        // DLL imports from apci.dll with error handling
        [DllImport("apci.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "APCI_Initialize")]
        private static extern int APCI_Initialize_Native();

        [DllImport("apci.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "APCI_Shutdown")]
        private static extern int APCI_Shutdown_Native();

        [DllImport("apci.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int APCI_DetectDevices(IntPtr deviceBuffer, ref int deviceCount);

        [DllImport("apci.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int APCI_ConnectDevice(string serialNumber, int connectionType);

        [DllImport("apci.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int APCI_DisconnectDevice(string serialNumber);

        [DllImport("apci.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int APCI_SendCANFrame(string serialNumber, byte[] data, int dataLength, byte[] response, ref int responseLength, int timeout);

        [DllImport("apci.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int APCI_CheckPTTRunning();

        [DllImport("apci.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int APCI_DisconnectPTT();

        [DllImport("apci.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int APCI_SendData(string serialNumber, byte[] data, int dataLength, byte[] response, ref int responseLength, int timeout);

        // DLL imports from Volvo.ApciPlus.dll
        [DllImport("Volvo.ApciPlus.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int ApciPlus_Initialize();

        [DllImport("Volvo.ApciPlus.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int ApciPlus_Shutdown();

        [DllImport("Volvo.ApciPlus.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int ApciPlus_DetectDevices(IntPtr deviceBuffer, ref int deviceCount);

        [DllImport("Volvo.ApciPlus.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int ApciPlus_ConnectDevice(string serialNumber, int connectionType);

        [DllImport("Volvo.ApciPlus.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int ApciPlus_DisconnectDevice(string serialNumber);

        [DllImport("Volvo.ApciPlus.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int ApciPlus_SendCANFrame(string serialNumber, byte[] data, int dataLength, byte[] response, ref int responseLength, int timeout);

        [DllImport("Volvo.ApciPlus.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int ApciPlus_SendData(string serialNumber, byte[] data, int dataLength, byte[] response, ref int responseLength, int timeout);

        [DllImport("Volvo.ApciPlus.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int ApciPlus_CheckPTTRunning();

        [DllImport("Volvo.ApciPlus.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int ApciPlus_DisconnectPTT();

        /// <summary>
        /// Initializes a new instance of the <see cref="PhoenixVocomAdapter"/> class
        /// </summary>
        /// <param name="logger">The logging service</param>
        // Dynamic function loading for compatibility
        private delegate int APCI_Initialize_Delegate();
        private delegate int APCI_Shutdown_Delegate();
        private APCI_Initialize_Delegate? _apciInitialize;
        private APCI_Shutdown_Delegate? _apciShutdown;
        private IntPtr _apciDllHandle = IntPtr.Zero;

        public PhoenixVocomAdapter(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets a value indicating whether the driver is initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Gets the current device
        /// </summary>
        public VocomDevice? CurrentDevice => _currentDevice;

        /// <summary>
        /// Initializes the Vocom driver
        /// </summary>
        /// <returns>True if initialization is successful, false otherwise</returns>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing Phoenix Vocom adapter", "PhoenixVocomAdapter");

                // Ensure we're not already initialized
                if (_isInitialized)
                {
                    _logger.LogWarning("Phoenix Vocom adapter already initialized", "PhoenixVocomAdapter");
                    return true;
                }

                // Check if required libraries are available
                bool librariesAvailable = await CheckRequiredLibrariesAsync();
                if (!librariesAvailable)
                {
                    _logger.LogError("Required Phoenix libraries are not available", "PhoenixVocomAdapter");
                    return false;
                }

                // Copy the required DLLs to the application directory if they don't exist
                await CopyRequiredDllsAsync();

                // Load APCI library dynamically to handle missing entry points
                if (!LoadApciLibraryDynamically())
                {
                    _logger.LogError("Failed to load APCI library dynamically", "PhoenixVocomAdapter");

                    // Run diagnostics to help identify the issue
                    var diagnosticTool = new VocomDiagnosticTool(_logger);
                    diagnosticTool.RunDiagnostics();

                    return false;
                }

                // Initialize the Phoenix APCI library
                int result = await Task.Run(() => SafeAPCI_Initialize());
                if (result != 0)
                {
                    _logger.LogWarning($"Failed to initialize Phoenix APCI library. Error code: {result}. Trying ApciPlus...", "PhoenixVocomAdapter");

                    // Try ApciPlus as fallback
                    result = await Task.Run(() => ApciPlus_Initialize());
                    if (result != 0)
                    {
                        _logger.LogError($"Failed to initialize Phoenix ApciPlus library. Error code: {result}", "PhoenixVocomAdapter");
                        return false;
                    }
                }

                _isInitialized = true;
                _logger.LogInformation("Phoenix Vocom adapter initialized successfully", "PhoenixVocomAdapter");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error initializing Phoenix Vocom adapter", "PhoenixVocomAdapter", ex);
                return false;
            }
        }

        /// <summary>
        /// Shuts down the Vocom driver
        /// </summary>
        /// <returns>True if shutdown is successful, false otherwise</returns>
        public async Task<bool> ShutdownAsync()
        {
            try
            {
                _logger.LogInformation("Shutting down Phoenix Vocom adapter", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogWarning("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return true;
                }

                // Disconnect any connected device
                if (_currentDevice != null)
                {
                    await DisconnectDeviceAsync(_currentDevice);
                }

                // Shutdown the Phoenix APCI library
                int result = await Task.Run(() => SafeAPCI_Shutdown());
                if (result != 0)
                {
                    _logger.LogError($"Failed to shutdown Phoenix APCI library. Error code: {result}", "PhoenixVocomAdapter");

                    // Try ApciPlus as fallback
                    result = await Task.Run(() => ApciPlus_Shutdown());
                    if (result != 0)
                    {
                        _logger.LogError($"Failed to shutdown Phoenix ApciPlus library. Error code: {result}", "PhoenixVocomAdapter");
                        return false;
                    }
                }

                // Clean up dynamic library loading
                if (_apciDllHandle != IntPtr.Zero)
                {
                    FreeLibrary(_apciDllHandle);
                    _apciDllHandle = IntPtr.Zero;
                }

                _isInitialized = false;
                _logger.LogInformation("Phoenix Vocom adapter shut down successfully", "PhoenixVocomAdapter");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error shutting down Phoenix Vocom adapter", "PhoenixVocomAdapter", ex);
                return false;
            }
        }

        /// <summary>
        /// Detects available Vocom devices
        /// </summary>
        /// <returns>List of available Vocom devices</returns>
        public async Task<List<VocomDevice>> DetectDevicesAsync()
        {
            try
            {
                _logger.LogInformation("Detecting Vocom devices using Phoenix libraries", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogError("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return new List<VocomDevice>();
                }

                return await Task.Run(() =>
                {
                    // Allocate buffer for devices (max 10 devices)
                    const int maxDevices = 10;
                    IntPtr deviceBuffer = Marshal.AllocHGlobal(maxDevices * 256); // Assuming each device info is at most 256 bytes
                    int deviceCount = maxDevices;

                    try
                    {
                        // Call APCI_DetectDevices
                        int result = APCI_DetectDevices(deviceBuffer, ref deviceCount);
                        if (result != 0)
                        {
                            _logger.LogError($"Failed to detect Vocom devices using APCI. Error code: {result}", "PhoenixVocomAdapter");

                            // Try ApciPlus as fallback
                            result = ApciPlus_DetectDevices(deviceBuffer, ref deviceCount);
                            if (result != 0)
                            {
                                _logger.LogError($"Failed to detect Vocom devices using ApciPlus. Error code: {result}", "PhoenixVocomAdapter");
                                return new List<VocomDevice>();
                            }
                        }

                        // Convert to managed devices
                        List<VocomDevice> devices = new List<VocomDevice>();
                        for (int i = 0; i < deviceCount; i++)
                        {
                            // Extract device info from the buffer
                            // This is a simplified approach - in a real implementation, you would need to know the exact structure
                            IntPtr deviceInfoPtr = IntPtr.Add(deviceBuffer, i * 256);
                            string serialNumber = Marshal.PtrToStringAnsi(deviceInfoPtr) ?? $"VOCOM-{i}";

                            VocomDevice device = new VocomDevice
                            {
                                SerialNumber = serialNumber,
                                Name = $"Vocom {serialNumber}",
                                ConnectionType = VocomConnectionType.USB,
                                ConnectionStatus = VocomConnectionStatus.Disconnected,
                                LastConnectionTime = DateTime.MinValue
                            };

                            devices.Add(device);
                        }

                        _logger.LogInformation($"Detected {deviceCount} Vocom devices", "PhoenixVocomAdapter");
                        return devices;
                    }
                    finally
                    {
                        // Free the allocated memory
                        Marshal.FreeHGlobal(deviceBuffer);
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError("Error detecting Vocom devices", "PhoenixVocomAdapter", ex);
                return new List<VocomDevice>();
            }
        }

        /// <summary>
        /// Connects to a Vocom device
        /// </summary>
        /// <param name="device">The device to connect to</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        public async Task<bool> ConnectToDeviceAsync(VocomDevice device)
        {
            try
            {
                _logger.LogInformation($"Connecting to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogError("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return false;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot connect to null device", "PhoenixVocomAdapter");
                    return false;
                }

                // Check if PTT is running and disconnect it
                await CheckAndDisconnectPTTAsync();

                // Connect to the device
                int result = await Task.Run(() => APCI_ConnectDevice(device.SerialNumber, (int)device.ConnectionType));
                if (result != 0)
                {
                    _logger.LogError($"Failed to connect to Vocom device {device.SerialNumber}. Error code: {result}", "PhoenixVocomAdapter");

                    // Try ApciPlus as fallback
                    result = await Task.Run(() => ApciPlus_ConnectDevice(device.SerialNumber, (int)device.ConnectionType));
                    if (result != 0)
                    {
                        _logger.LogError($"Failed to connect to Vocom device {device.SerialNumber} using ApciPlus. Error code: {result}", "PhoenixVocomAdapter");
                        return false;
                    }
                }

                _isConnected = true;
                _currentDevice = device;
                device.ConnectionStatus = VocomConnectionStatus.Connected;
                device.LastConnectionTime = DateTime.Now;

                _logger.LogInformation($"Connected to Vocom device {device.SerialNumber}", "PhoenixVocomAdapter");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error connecting to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter", ex);
                return false;
            }
        }

        /// <summary>
        /// Disconnects from a Vocom device
        /// </summary>
        /// <param name="device">The device to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public async Task<bool> DisconnectDeviceAsync(VocomDevice device)
        {
            return await DisconnectFromDeviceAsync(device);
        }

        /// <summary>
        /// Disconnects from a Vocom device
        /// </summary>
        /// <param name="device">The device to disconnect from</param>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public async Task<bool> DisconnectFromDeviceAsync(VocomDevice device)
        {
            try
            {
                _logger.LogInformation($"Disconnecting from Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogError("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return false;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot disconnect from null device", "PhoenixVocomAdapter");
                    return false;
                }

                if (!_isConnected || _currentDevice == null || _currentDevice.SerialNumber != device.SerialNumber)
                {
                    _logger.LogWarning($"Device {device.SerialNumber} is not connected", "PhoenixVocomAdapter");
                    return true;
                }

                // Disconnect from the device
                int result = await Task.Run(() => APCI_DisconnectDevice(device.SerialNumber));
                if (result != 0)
                {
                    _logger.LogWarning($"Failed to disconnect from Vocom device using APCI. Error code: {result}. Trying ApciPlus...", "PhoenixVocomAdapter");

                    // Try ApciPlus as fallback
                    result = await Task.Run(() => ApciPlus_DisconnectDevice(device.SerialNumber));
                    if (result != 0)
                    {
                        _logger.LogError($"Failed to disconnect from Vocom device {device.SerialNumber}. Error code: {result}", "PhoenixVocomAdapter");
                        return false;
                    }
                }

                _isConnected = false;
                _currentDevice = null;
                device.ConnectionStatus = VocomConnectionStatus.Disconnected;

                _logger.LogInformation($"Disconnected from Vocom device {device.SerialNumber}", "PhoenixVocomAdapter");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error disconnecting from Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter", ex);
                return false;
            }
        }

        /// <summary>
        /// Sends a CAN frame to a device
        /// </summary>
        /// <param name="device">The device to send to</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendCANFrameAsync(VocomDevice device, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending CAN frame to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogError("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send CAN frame to null device", "PhoenixVocomAdapter");
                    return null;
                }

                if (!_isConnected || _currentDevice == null || _currentDevice.SerialNumber != device.SerialNumber)
                {
                    _logger.LogError($"Device {device.SerialNumber} is not connected", "PhoenixVocomAdapter");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("CAN frame data is null or empty", "PhoenixVocomAdapter");
                    return null;
                }

                return await Task.Run(() =>
                {
                    byte[] response = new byte[responseLength];
                    int actualResponseLength = responseLength;

                    int result = APCI_SendCANFrame(device.SerialNumber, data, data.Length, response, ref actualResponseLength, timeout);
                    if (result != 0)
                    {
                        _logger.LogWarning($"Failed to send CAN frame using APCI. Error code: {result}. Trying ApciPlus...", "PhoenixVocomAdapter");

                        // Try ApciPlus as fallback
                        result = ApciPlus_SendCANFrame(device.SerialNumber, data, data.Length, response, ref actualResponseLength, timeout);
                        if (result != 0)
                        {
                            _logger.LogError($"Failed to send CAN frame to Vocom device {device.SerialNumber}. Error code: {result}", "PhoenixVocomAdapter");
                            return null;
                        }
                    }

                    // Resize response if needed
                    if (actualResponseLength != responseLength)
                    {
                        Array.Resize(ref response, actualResponseLength);
                    }

                    _logger.LogInformation($"Sent CAN frame to Vocom device {device.SerialNumber}", "PhoenixVocomAdapter");
                    return response;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending CAN frame to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends data to a device
        /// </summary>
        /// <param name="device">The device to send to</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendDataAsync(VocomDevice device, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending data to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogError("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send data to null device", "PhoenixVocomAdapter");
                    return null;
                }

                if (!_isConnected || _currentDevice == null || _currentDevice.SerialNumber != device.SerialNumber)
                {
                    _logger.LogError($"Device {device.SerialNumber} is not connected", "PhoenixVocomAdapter");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("Data is null or empty", "PhoenixVocomAdapter");
                    return null;
                }

                return await Task.Run(() =>
                {
                    byte[] response = new byte[responseLength];
                    int actualResponseLength = responseLength;

                    int result = APCI_SendData(device.SerialNumber, data, data.Length, response, ref actualResponseLength, timeout);
                    if (result != 0)
                    {
                        _logger.LogWarning($"Failed to send data using APCI. Error code: {result}. Trying ApciPlus...", "PhoenixVocomAdapter");

                        // Try ApciPlus as fallback
                        result = ApciPlus_SendData(device.SerialNumber, data, data.Length, response, ref actualResponseLength, timeout);
                        if (result != 0)
                        {
                            _logger.LogError($"Failed to send data to Vocom device {device.SerialNumber}. Error code: {result}", "PhoenixVocomAdapter");
                            return null;
                        }
                    }

                    // Resize response if needed
                    if (actualResponseLength != responseLength)
                    {
                        Array.Resize(ref response, actualResponseLength);
                    }

                    _logger.LogInformation($"Sent data to Vocom device {device.SerialNumber}", "PhoenixVocomAdapter");
                    return response;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending data to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter", ex);
                return null;
            }
        }

        /// <summary>
        /// Checks if PTT application is running
        /// </summary>
        /// <returns>True if PTT is running, false otherwise</returns>
        public async Task<bool> IsPTTRunningAsync()
        {
            try
            {
                _logger.LogInformation("Checking if PTT application is running", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogError("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return false;
                }

                int result = await Task.Run(() => APCI_CheckPTTRunning());

                // If APCI fails, try ApciPlus
                if (result < 0)
                {
                    _logger.LogWarning($"Failed to check PTT status using APCI. Error code: {result}. Trying ApciPlus...", "PhoenixVocomAdapter");
                    result = await Task.Run(() => ApciPlus_CheckPTTRunning());
                }

                bool isRunning = result == 1;

                _logger.LogInformation($"PTT application is {(isRunning ? "running" : "not running")}", "PhoenixVocomAdapter");
                return isRunning;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error checking if PTT application is running", "PhoenixVocomAdapter", ex);
                return false;
            }
        }

        /// <summary>
        /// Disconnects the PTT application
        /// </summary>
        /// <returns>True if disconnection is successful, false otherwise</returns>
        public async Task<bool> DisconnectPTTAsync()
        {
            try
            {
                _logger.LogInformation("Disconnecting PTT application", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogError("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return false;
                }

                int result = await Task.Run(() => APCI_DisconnectPTT());

                // If APCI fails, try ApciPlus
                if (result != 0)
                {
                    _logger.LogWarning($"Failed to disconnect PTT using APCI. Error code: {result}. Trying ApciPlus...", "PhoenixVocomAdapter");
                    result = await Task.Run(() => ApciPlus_DisconnectPTT());
                }

                bool disconnected = result == 0;

                if (disconnected)
                {
                    _logger.LogInformation("PTT application disconnected successfully", "PhoenixVocomAdapter");
                }
                else
                {
                    _logger.LogError("Failed to disconnect PTT application", "PhoenixVocomAdapter");
                }

                return disconnected;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error disconnecting PTT application", "PhoenixVocomAdapter", ex);
                return false;
            }
        }

        /// <summary>
        /// Updates the firmware of a Vocom device
        /// </summary>
        /// <param name="device">The device to update</param>
        /// <param name="firmwareData">The firmware data</param>
        /// <returns>True if update is successful, false otherwise</returns>
        public async Task<bool> UpdateFirmwareAsync(VocomDevice device, byte[] firmwareData)
        {
            try
            {
                _logger.LogInformation($"Updating firmware for Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogError("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return false;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot update firmware for null device", "PhoenixVocomAdapter");
                    return false;
                }

                if (firmwareData == null || firmwareData.Length == 0)
                {
                    _logger.LogError("Firmware data is null or empty", "PhoenixVocomAdapter");
                    return false;
                }

                if (!_isConnected || _currentDevice == null || _currentDevice.SerialNumber != device.SerialNumber)
                {
                    _logger.LogError($"Device {device.SerialNumber} is not connected", "PhoenixVocomAdapter");
                    return false;
                }

                // Firmware update is not directly supported by the Phoenix libraries
                // This would require a custom implementation using the available commands
                _logger.LogWarning("Firmware update is not directly supported by the Phoenix libraries", "PhoenixVocomAdapter");

                // Add an await to make the method truly asynchronous
                await Task.Delay(1); // Minimal delay to satisfy the async requirement
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error updating firmware for Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter", ex);
                return false;
            }
        }

        /// <summary>
        /// Sends an SPI command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="command">The command byte</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendSPICommandAsync(VocomDevice device, byte command, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending SPI command to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogError("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send SPI command to null device", "PhoenixVocomAdapter");
                    return null;
                }

                if (!_isConnected || _currentDevice == null || _currentDevice.SerialNumber != device.SerialNumber)
                {
                    _logger.LogError($"Device {device.SerialNumber} is not connected", "PhoenixVocomAdapter");
                    return null;
                }

                // Prepare the data with SPI protocol identifier (0x20) and command
                byte[] spiData = new byte[data.Length + 2];
                spiData[0] = 0x20; // SPI protocol identifier
                spiData[1] = command;
                Array.Copy(data, 0, spiData, 2, data.Length);

                // Send the data using the SendDataAsync method
                return await SendDataAsync(device, spiData, responseLength, timeout);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending SPI command to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends an SCI command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="command">The command byte</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendSCICommandAsync(VocomDevice device, byte command, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending SCI command to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogError("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send SCI command to null device", "PhoenixVocomAdapter");
                    return null;
                }

                if (!_isConnected || _currentDevice == null || _currentDevice.SerialNumber != device.SerialNumber)
                {
                    _logger.LogError($"Device {device.SerialNumber} is not connected", "PhoenixVocomAdapter");
                    return null;
                }

                // Prepare the data with SCI protocol identifier (0x30) and command
                byte[] sciData = new byte[data.Length + 2];
                sciData[0] = 0x30; // SCI protocol identifier
                sciData[1] = command;
                Array.Copy(data, 0, sciData, 2, data.Length);

                // Send the data using the SendDataAsync method
                return await SendDataAsync(device, sciData, responseLength, timeout);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending SCI command to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends an IIC command to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="address">The device address</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendIICCommandAsync(VocomDevice device, byte address, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending IIC command to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogError("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send IIC command to null device", "PhoenixVocomAdapter");
                    return null;
                }

                if (!_isConnected || _currentDevice == null || _currentDevice.SerialNumber != device.SerialNumber)
                {
                    _logger.LogError($"Device {device.SerialNumber} is not connected", "PhoenixVocomAdapter");
                    return null;
                }

                // Prepare the data with IIC protocol identifier (0x40) and address
                byte[] iicData = new byte[data.Length + 2];
                iicData[0] = 0x40; // IIC protocol identifier
                iicData[1] = address;
                Array.Copy(data, 0, iicData, 2, data.Length);

                // Send the data using the SendDataAsync method
                return await SendDataAsync(device, iicData, responseLength, timeout);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending IIC command to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter", ex);
                return null;
            }
        }

        /// <summary>
        /// Sends raw data to a device
        /// </summary>
        /// <param name="device">The Vocom device</param>
        /// <param name="data">The data to send</param>
        /// <param name="responseLength">Expected response length</param>
        /// <param name="timeout">Timeout in milliseconds</param>
        /// <returns>The response data</returns>
        public async Task<byte[]> SendRawDataAsync(VocomDevice device, byte[] data, int responseLength, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Sending raw data to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogError("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return null;
                }

                if (device == null)
                {
                    _logger.LogError("Cannot send raw data to null device", "PhoenixVocomAdapter");
                    return null;
                }

                if (!_isConnected || _currentDevice == null || _currentDevice.SerialNumber != device.SerialNumber)
                {
                    _logger.LogError($"Device {device.SerialNumber} is not connected", "PhoenixVocomAdapter");
                    return null;
                }

                if (data == null || data.Length == 0)
                {
                    _logger.LogError("Raw data is null or empty", "PhoenixVocomAdapter");
                    return null;
                }

                // Check the protocol identifier in the first byte if available
                if (data.Length > 0)
                {
                    byte protocolByte = data[0];
                    switch (protocolByte & 0xF0)
                    {
                        case 0x10: // CAN protocol
                            _logger.LogInformation("Detected CAN protocol in raw data", "PhoenixVocomAdapter");
                            return await SendCANFrameAsync(device, data, responseLength, timeout);

                        case 0x20: // SPI protocol
                            _logger.LogInformation("Detected SPI protocol in raw data", "PhoenixVocomAdapter");
                            byte spiCommand = data.Length > 1 ? data[1] : (byte)0;
                            byte[] spiData = data.Length > 2 ? data.Skip(2).ToArray() : Array.Empty<byte>();
                            return await SendSPICommandAsync(device, spiCommand, spiData, responseLength, timeout);

                        case 0x30: // SCI protocol
                            _logger.LogInformation("Detected SCI protocol in raw data", "PhoenixVocomAdapter");
                            byte sciCommand = data.Length > 1 ? data[1] : (byte)0;
                            byte[] sciData = data.Length > 2 ? data.Skip(2).ToArray() : Array.Empty<byte>();
                            return await SendSCICommandAsync(device, sciCommand, sciData, responseLength, timeout);

                        case 0x40: // IIC protocol
                            _logger.LogInformation("Detected IIC protocol in raw data", "PhoenixVocomAdapter");
                            byte iicAddress = data.Length > 1 ? data[1] : (byte)0;
                            byte[] iicData = data.Length > 2 ? data.Skip(2).ToArray() : Array.Empty<byte>();
                            return await SendIICCommandAsync(device, iicAddress, iicData, responseLength, timeout);

                        default: // Unknown protocol, use CAN as default
                            _logger.LogWarning($"Unknown protocol identifier in raw data: 0x{protocolByte:X2}, using CAN as default", "PhoenixVocomAdapter");
                            return await SendCANFrameAsync(device, data, responseLength, timeout);
                    }
                }

                // If no protocol identifier, use SendDataAsync directly
                return await SendDataAsync(device, data, responseLength, timeout);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending raw data to Vocom device {device?.SerialNumber}", "PhoenixVocomAdapter", ex);
                return null;
            }
        }

        /// <summary>
        /// Terminates the PTT application if it is running
        /// </summary>
        /// <returns>True if termination is successful, false otherwise</returns>
        public async Task<bool> TerminatePTTAsync()
        {
            try
            {
                _logger.LogInformation("Terminating PTT application", "PhoenixVocomAdapter");

                if (!_isInitialized)
                {
                    _logger.LogError("Phoenix Vocom adapter not initialized", "PhoenixVocomAdapter");
                    return false;
                }

                // Check if PTT is running
                bool isRunning = await IsPTTRunningAsync();
                if (!isRunning)
                {
                    _logger.LogInformation("PTT application is not running", "PhoenixVocomAdapter");
                    return true;
                }

                // Try to disconnect PTT
                bool disconnected = await DisconnectPTTAsync();
                if (disconnected)
                {
                    _logger.LogInformation("PTT application disconnected successfully", "PhoenixVocomAdapter");
                    return true;
                }

                // If disconnection fails, try to forcefully terminate the process
                _logger.LogWarning("Failed to disconnect PTT, attempting to forcefully terminate", "PhoenixVocomAdapter");
                bool terminated = Core.Utilities.ConnectionHelper.ForceTerminatePTTProcess();
                if (terminated)
                {
                    _logger.LogInformation("PTT application forcefully terminated", "PhoenixVocomAdapter");
                    return true;
                }

                _logger.LogError("Failed to terminate PTT application", "PhoenixVocomAdapter");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error terminating PTT application", "PhoenixVocomAdapter", ex);
                return false;
            }
        }

        /// <summary>
        /// Checks if PTT application is running and disconnects it if necessary
        /// </summary>
        /// <returns>True if PTT is successfully disconnected or not running, false otherwise</returns>
        public async Task<bool> CheckAndDisconnectPTTAsync()
        {
            try
            {
                _logger.LogInformation("Checking if PTT application is running", "PhoenixVocomAdapter");

                // Check if PTT is running
                bool isRunning = await IsPTTRunningAsync();
                if (!isRunning)
                {
                    _logger.LogInformation("PTT application is not running", "PhoenixVocomAdapter");
                    return true;
                }

                _logger.LogInformation("PTT application is running, attempting to disconnect", "PhoenixVocomAdapter");
                bool disconnected = await DisconnectPTTAsync();

                if (disconnected)
                {
                    _logger.LogInformation("PTT application disconnected successfully", "PhoenixVocomAdapter");
                    return true;
                }
                else
                {
                    _logger.LogWarning("Failed to disconnect PTT application", "PhoenixVocomAdapter");

                    // Try using ConnectionHelper as a fallback
                    bool helperDisconnected = await Core.Utilities.ConnectionHelper.DisconnectPTTApplicationAsync();
                    if (helperDisconnected)
                    {
                        _logger.LogInformation("PTT application disconnected successfully using ConnectionHelper", "PhoenixVocomAdapter");
                        return true;
                    }

                    // If all methods fail, try to forcefully terminate the process
                    _logger.LogWarning("Standard methods failed, attempting to forcefully terminate PTT process", "PhoenixVocomAdapter");
                    bool forceTerminated = Core.Utilities.ConnectionHelper.ForceTerminatePTTProcess();
                    if (forceTerminated)
                    {
                        _logger.LogInformation("PTT application forcefully terminated", "PhoenixVocomAdapter");
                        return true;
                    }

                    _logger.LogError("All PTT disconnection methods failed", "PhoenixVocomAdapter");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error checking and disconnecting PTT application", "PhoenixVocomAdapter", ex);
                return false;
            }
        }

        /// <summary>
        /// Checks if the required libraries are available
        /// </summary>
        /// <returns>True if all required libraries are available, false otherwise</returns>
        private async Task<bool> CheckRequiredLibrariesAsync()
        {
            try
            {
                _logger.LogInformation("Checking for required Phoenix libraries", "PhoenixVocomAdapter");

                // Get Phoenix Diag path from environment variable or use default
                string phoenixDiagPath = Environment.GetEnvironmentVariable("PHOENIX_DIAG_PATH") ??
                                        @"C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021";

                // Check if Phoenix Diag path exists
                bool pathExists = await Task.Run(() => Directory.Exists(phoenixDiagPath));
                if (!pathExists)
                {
                    _logger.LogError($"Phoenix Diag path not found at {phoenixDiagPath}", "PhoenixVocomAdapter");
                    return false;
                }

                // List of critical libraries that must be present
                string[] criticalLibraries = new[]
                {
                    "apci.dll",
                    "Volvo.ApciPlus.dll",
                    "Ionic.Zip.Reduced.dll",
                    "SharpCompress.dll"
                };

                int missingCount = 0;
                foreach (string library in criticalLibraries)
                {
                    string path = Path.Combine(phoenixDiagPath, library);
                    bool fileExists = await Task.Run(() => File.Exists(path));
                    if (!fileExists)
                    {
                        _logger.LogError($"Critical library {library} not found at {path}", "PhoenixVocomAdapter");
                        missingCount++;
                    }
                }

                if (missingCount > 0)
                {
                    _logger.LogError($"{missingCount} critical libraries are missing", "PhoenixVocomAdapter");
                    return false;
                }

                // Check for Vodia libraries
                string[] vodiaFiles = await Task.Run(() => Directory.GetFiles(phoenixDiagPath, "Vodia*.dll"));
                if (vodiaFiles.Length == 0)
                {
                    _logger.LogWarning("No Vodia libraries found", "PhoenixVocomAdapter");
                }
                else
                {
                    _logger.LogInformation($"Found {vodiaFiles.Length} Vodia libraries", "PhoenixVocomAdapter");
                }

                // Check for System libraries
                string[] systemFiles = await Task.Run(() => Directory.GetFiles(phoenixDiagPath, "System*.dll"));
                if (systemFiles.Length == 0)
                {
                    _logger.LogWarning("No System libraries found", "PhoenixVocomAdapter");
                }
                else
                {
                    _logger.LogInformation($"Found {systemFiles.Length} System libraries", "PhoenixVocomAdapter");
                }

                _logger.LogInformation($"Required libraries check completed. Found {criticalLibraries.Length - missingCount}/{criticalLibraries.Length} critical libraries, {vodiaFiles.Length} Vodia libraries, {systemFiles.Length} System libraries", "PhoenixVocomAdapter");

                // We can proceed if all critical libraries are present
                return missingCount == 0;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error checking for required libraries", "PhoenixVocomAdapter", ex);
                return false;
            }
        }

        /// <summary>
        /// Copies the required DLLs to the application directory
        /// </summary>
        /// <returns>True if copying is successful, false otherwise</returns>
        private async Task<bool> CopyRequiredDllsAsync()
        {
            try
            {
                // Get Phoenix Diag path from environment variable or use default
                string phoenixDiagPath = Environment.GetEnvironmentVariable("PHOENIX_DIAG_PATH") ??
                                        @"C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021";
                string appPath = AppDomain.CurrentDomain.BaseDirectory;
                string driversPath = Path.Combine(appPath, "Drivers", "Phoenix");

                // Create the drivers directory if it doesn't exist
                if (!Directory.Exists(driversPath))
                {
                    Directory.CreateDirectory(driversPath);
                }

                // List of DLLs to copy
                string[] dllsToCopy = new[]
                {
                    // Core APCI libraries
                    "apci.dll",
                    "apcidb.dll",

                    // Volvo APCI libraries
                    "Volvo.ApciPlus.dll",
                    "Volvo.ApciPlusData.dll",
                    "Volvo.ApciPlusTea2Data.dll",

                    // Volvo NAMS libraries
                    "Volvo.NAMS.AC.Services.Interface.dll",
                    "Volvo.NAMS.AC.Services.Interfaces.dll",

                    // Volvo NVS libraries
                    "Volvo.NVS.Core.dll",
                    "Volvo.NVS.Logging.dll",
                    "Volvo.NVS.Persistence.dll",
                    "Volvo.NVS.Persistence.NHibernate.dll",

                    // Volvo IT libraries
                    "VolvoIt.Baf.Utility.dll",
                    "VolvoIt.Fido.Agent.Gateway.Contract.dll",
                    "VolvoIt.Waf.ServiceContract.dll",
                    "VolvoIt.Waf.Utility.dll",

                    // Configuration files
                    "Volvo.ApciPlus.dll.config",
                    "Volvo.ApciPlusData.dll.config",

                    // NHibernate dependencies
                    "NHibernate.dll",
                    "NHibernate.Caches.SysCache2.dll",
                    "Iesi.Collections.dll",

                    // Compression libraries
                    "Ionic.Zip.Reduced.dll",
                    "SharpCompress.dll",
                    "DotNetZip.dll",
                    "ICSharpCode.SharpZipLib.dll",

                    // Vodia libraries
                    "Vodia.CommonDomain.Model.dll",
                    "Vodia.Contracts.Common.dll",
                    "Vodia.UtilityComponent.dll",

                    // Other potential dependencies
                    "log4net.dll",
                    "Newtonsoft.Json.dll",
                    "AutoMapper.dll"
                };

                // Create subdirectories
                string configPath = Path.Combine(driversPath, "Config");
                string dependenciesPath = Path.Combine(driversPath, "Dependencies");
                string systemPath = Path.Combine(driversPath, "System");

                if (!Directory.Exists(configPath))
                {
                    Directory.CreateDirectory(configPath);
                }

                if (!Directory.Exists(dependenciesPath))
                {
                    Directory.CreateDirectory(dependenciesPath);
                }

                if (!Directory.Exists(systemPath))
                {
                    Directory.CreateDirectory(systemPath);
                }

                // Copy all files
                int copiedFiles = 0;
                int missingFiles = 0;

                // First, copy the explicitly listed files
                foreach (string file in dllsToCopy)
                {
                    string sourcePath = Path.Combine(phoenixDiagPath, file);
                    string targetPath;
                    string appTargetPath;

                    // Determine target path based on file extension
                    if (file.EndsWith(".config", StringComparison.OrdinalIgnoreCase))
                    {
                        targetPath = Path.Combine(configPath, file);
                        appTargetPath = Path.Combine(appPath, file);
                    }
                    else if (file.StartsWith("Volvo.", StringComparison.OrdinalIgnoreCase) ||
                             file.StartsWith("VolvoIt.", StringComparison.OrdinalIgnoreCase) ||
                             file.Equals("apci.dll", StringComparison.OrdinalIgnoreCase) ||
                             file.Equals("apcidb.dll", StringComparison.OrdinalIgnoreCase))
                    {
                        // Core Volvo and APCI files go directly to drivers folder and app folder
                        targetPath = Path.Combine(driversPath, file);
                        appTargetPath = Path.Combine(appPath, file);
                    }
                    else if (file.StartsWith("Vodia.", StringComparison.OrdinalIgnoreCase))
                    {
                        // Vodia files go directly to drivers folder and app folder
                        targetPath = Path.Combine(driversPath, file);
                        appTargetPath = Path.Combine(appPath, file);
                    }
                    else
                    {
                        // Dependencies go to dependencies folder
                        targetPath = Path.Combine(dependenciesPath, file);
                        appTargetPath = Path.Combine(appPath, file);
                    }

                    if (File.Exists(sourcePath))
                    {
                        try
                        {
                            // Copy to drivers folder
                            if (!File.Exists(targetPath) || new FileInfo(sourcePath).LastWriteTime > new FileInfo(targetPath).LastWriteTime)
                            {
                                await Task.Run(() => File.Copy(sourcePath, targetPath, true));
                                _logger.LogInformation($"Copied {file} to {targetPath}", "PhoenixVocomAdapter");
                            }

                            // Copy to application folder for direct loading
                            if (!File.Exists(appTargetPath) || new FileInfo(sourcePath).LastWriteTime > new FileInfo(appTargetPath).LastWriteTime)
                            {
                                await Task.Run(() => File.Copy(sourcePath, appTargetPath, true));
                                _logger.LogInformation($"Copied {file} to {appTargetPath}", "PhoenixVocomAdapter");
                            }

                            copiedFiles++;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError($"Error copying {file}: {ex.Message}", "PhoenixVocomAdapter");
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"Could not find {file} at {sourcePath}", "PhoenixVocomAdapter");
                        missingFiles++;
                    }
                }

                // Now, find and copy all System libraries
                try
                {
                    string[] systemFiles = Directory.GetFiles(phoenixDiagPath, "System*.dll");
                    _logger.LogInformation($"Found {systemFiles.Length} System libraries", "PhoenixVocomAdapter");

                    foreach (string sourcePath in systemFiles)
                    {
                        string fileName = Path.GetFileName(sourcePath);
                        string targetPath = Path.Combine(systemPath, fileName);
                        string appTargetPath = Path.Combine(appPath, fileName);

                        try
                        {
                            // Copy to system folder
                            if (!File.Exists(targetPath) || new FileInfo(sourcePath).LastWriteTime > new FileInfo(targetPath).LastWriteTime)
                            {
                                await Task.Run(() => File.Copy(sourcePath, targetPath, true));
                                _logger.LogInformation($"Copied {fileName} to {targetPath}", "PhoenixVocomAdapter");
                            }

                            // Copy to application folder for direct loading
                            if (!File.Exists(appTargetPath) || new FileInfo(sourcePath).LastWriteTime > new FileInfo(appTargetPath).LastWriteTime)
                            {
                                await Task.Run(() => File.Copy(sourcePath, appTargetPath, true));
                                _logger.LogInformation($"Copied {fileName} to {appTargetPath}", "PhoenixVocomAdapter");
                            }

                            copiedFiles++;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError($"Error copying {fileName}: {ex.Message}", "PhoenixVocomAdapter");
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error finding System libraries: {ex.Message}", "PhoenixVocomAdapter");
                }

                _logger.LogInformation($"Copied {copiedFiles} files, {missingFiles} files were missing", "PhoenixVocomAdapter");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error copying required DLLs", "PhoenixVocomAdapter", ex);
                return false;
            }
        }

        #region Dynamic Library Loading

        // Windows API imports for dynamic loading
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        /// <summary>
        /// Loads APCI library dynamically with architecture awareness
        /// </summary>
        private bool LoadApciLibraryDynamically()
        {
            try
            {
                _logger.LogInformation("Loading APCI library dynamically with architecture awareness", "PhoenixVocomAdapter");

                // Detect current process architecture
                bool is64BitProcess = Environment.Is64BitProcess;
                string architecture = is64BitProcess ? "x64" : "x86";
                _logger.LogInformation($"Current process architecture: {architecture}", "PhoenixVocomAdapter");

                // Try multiple paths for APCI library
                string[] apciPaths = new[]
                {
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "apci.dll"),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Libraries", "apci.dll"),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Libraries", architecture, "apci.dll"),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Drivers", "Phoenix", "apci.dll")
                };

                string? validApciPath = null;
                foreach (string path in apciPaths)
                {
                    if (File.Exists(path))
                    {
                        // Check architecture compatibility
                        if (IsLibraryArchitectureCompatible(path, is64BitProcess))
                        {
                            validApciPath = path;
                            _logger.LogInformation($"Found compatible APCI library at: {path}", "PhoenixVocomAdapter");
                            break;
                        }
                        else
                        {
                            _logger.LogWarning($"APCI library at {path} has incompatible architecture", "PhoenixVocomAdapter");
                        }
                    }
                }

                if (validApciPath == null)
                {
                    _logger.LogError("No compatible APCI library found", "PhoenixVocomAdapter");
                    return false;
                }

                _apciDllHandle = LoadLibrary(validApciPath);
                if (_apciDllHandle == IntPtr.Zero)
                {
                    int error = Marshal.GetLastWin32Error();
                    _logger.LogError($"Failed to load APCI library from {validApciPath}. Error code: {error}", "PhoenixVocomAdapter");

                    // Provide specific error analysis
                    AnalyzeLibraryLoadError(error, validApciPath);
                    return false;
                }

                _logger.LogInformation($"Successfully loaded APCI library from: {validApciPath}", "PhoenixVocomAdapter");

                // Try to get function pointers
                return LoadApciFunction();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception loading APCI library dynamically: {ex.Message}", "PhoenixVocomAdapter");
                return false;
            }
        }

        /// <summary>
        /// Checks if a library is compatible with the current process architecture
        /// </summary>
        private bool IsLibraryArchitectureCompatible(string libraryPath, bool is64BitProcess)
        {
            try
            {
                using (var fileStream = new FileStream(libraryPath, FileMode.Open, FileAccess.Read))
                using (var reader = new BinaryReader(fileStream))
                {
                    // Check DOS header
                    if (reader.ReadUInt16() != 0x5A4D) // "MZ"
                        return false;

                    // Jump to PE header
                    fileStream.Seek(60, SeekOrigin.Begin);
                    int peHeaderOffset = reader.ReadInt32();
                    fileStream.Seek(peHeaderOffset, SeekOrigin.Begin);

                    // Check PE signature
                    if (reader.ReadUInt32() != 0x00004550) // "PE\0\0"
                        return false;

                    // Read machine type
                    ushort machineType = reader.ReadUInt16();

                    // Determine compatibility
                    bool is64BitLibrary = (machineType == 0x8664); // IMAGE_FILE_MACHINE_AMD64
                    bool is32BitLibrary = (machineType == 0x014c); // IMAGE_FILE_MACHINE_I386

                    bool compatible = (is64BitProcess && is64BitLibrary) || (!is64BitProcess && is32BitLibrary);

                    if (!compatible)
                    {
                        string libArch = is64BitLibrary ? "x64" : (is32BitLibrary ? "x86" : "unknown");
                        string processArch = is64BitProcess ? "x64" : "x86";
                        _logger.LogWarning($"Architecture mismatch: Library {Path.GetFileName(libraryPath)} is {libArch}, process is {processArch}", "PhoenixVocomAdapter");
                    }

                    return compatible;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error checking architecture compatibility for {libraryPath}: {ex.Message}", "PhoenixVocomAdapter");
                // If we can't determine, assume it's compatible and let LoadLibrary handle it
                return true;
            }
        }

        /// <summary>
        /// Analyzes library loading errors and provides specific recommendations
        /// </summary>
        private void AnalyzeLibraryLoadError(int errorCode, string libraryPath)
        {
            switch (errorCode)
            {
                case 126: // ERROR_MOD_NOT_FOUND
                    _logger.LogError("Error 126: The specified module could not be found", "PhoenixVocomAdapter");
                    _logger.LogError("This usually means missing dependencies. Check Visual C++ Redistributables.", "PhoenixVocomAdapter");
                    break;

                case 193: // ERROR_BAD_EXE_FORMAT
                    _logger.LogError("Error 193: The application or DLL is not a valid Windows image", "PhoenixVocomAdapter");
                    _logger.LogError("This usually means architecture mismatch (32-bit vs 64-bit).", "PhoenixVocomAdapter");
                    _logger.LogError($"Try using the correct architecture version of {Path.GetFileName(libraryPath)}", "PhoenixVocomAdapter");
                    break;

                case 127: // ERROR_PROC_NOT_FOUND
                    _logger.LogError("Error 127: The specified procedure could not be found", "PhoenixVocomAdapter");
                    _logger.LogError("This usually means the DLL is missing required functions.", "PhoenixVocomAdapter");
                    break;

                case 2: // ERROR_FILE_NOT_FOUND
                    _logger.LogError("Error 2: The system cannot find the file specified", "PhoenixVocomAdapter");
                    _logger.LogError($"Library not found at: {libraryPath}", "PhoenixVocomAdapter");
                    break;

                default:
                    _logger.LogError($"Unknown error {errorCode} loading library {libraryPath}", "PhoenixVocomAdapter");
                    break;
            }
        }

        /// <summary>
        /// Loads APCI function pointers
        /// </summary>
        private bool LoadApciFunction()
        {
            try
            {
                // Try different possible function names for initialization
                string[] initFunctionNames = {
                    "APCI_Initialize",
                    "ApciInitialize",
                    "APCI_Init",
                    "ApciInit",
                    "Initialize",
                    "Init"
                };

                foreach (string funcName in initFunctionNames)
                {
                    IntPtr procAddress = GetProcAddress(_apciDllHandle, funcName);
                    if (procAddress != IntPtr.Zero)
                    {
                        _apciInitialize = Marshal.GetDelegateForFunctionPointer<APCI_Initialize_Delegate>(procAddress);
                        _logger.LogInformation($"Found APCI initialize function: {funcName}", "PhoenixVocomAdapter");
                        break;
                    }
                }

                // Try different possible function names for shutdown
                string[] shutdownFunctionNames = {
                    "APCI_Shutdown",
                    "ApciShutdown",
                    "APCI_Close",
                    "ApciClose",
                    "Shutdown",
                    "Close"
                };

                foreach (string funcName in shutdownFunctionNames)
                {
                    IntPtr procAddress = GetProcAddress(_apciDllHandle, funcName);
                    if (procAddress != IntPtr.Zero)
                    {
                        _apciShutdown = Marshal.GetDelegateForFunctionPointer<APCI_Shutdown_Delegate>(procAddress);
                        _logger.LogInformation($"Found APCI shutdown function: {funcName}", "PhoenixVocomAdapter");
                        break;
                    }
                }

                if (_apciInitialize == null)
                {
                    _logger.LogWarning("No APCI initialize function found in library", "PhoenixVocomAdapter");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception loading APCI functions: {ex.Message}", "PhoenixVocomAdapter");
                return false;
            }
        }

        /// <summary>
        /// Safe wrapper for APCI_Initialize that handles missing functions
        /// </summary>
        private int SafeAPCI_Initialize()
        {
            try
            {
                if (_apciInitialize != null)
                {
                    return _apciInitialize();
                }
                else
                {
                    _logger.LogWarning("APCI_Initialize function not available, returning success", "PhoenixVocomAdapter");
                    return 0; // Return success if function not available
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception calling APCI_Initialize: {ex.Message}", "PhoenixVocomAdapter");
                return -1;
            }
        }

        /// <summary>
        /// Safe wrapper for APCI_Shutdown that handles missing functions
        /// </summary>
        private int SafeAPCI_Shutdown()
        {
            try
            {
                if (_apciShutdown != null)
                {
                    return _apciShutdown();
                }
                else
                {
                    _logger.LogWarning("APCI_Shutdown function not available, returning success", "PhoenixVocomAdapter");
                    return 0; // Return success if function not available
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception calling APCI_Shutdown: {ex.Message}", "PhoenixVocomAdapter");
                return -1;
            }
        }

        #endregion
    }
}
