Log started at 7/21/2025 11:20:49 PM
2025-07-21 23:20:49.759 [Information] LoggingService: Logging service initialized
2025-07-21 23:20:49.774 [Information] App: Starting integrated application initialization
2025-07-21 23:20:49.775 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-21 23:20:49.777 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-21 23:20:49.779 [Information] IntegratedStartupService: Setting up application environment
2025-07-21 23:20:49.779 [Information] IntegratedStartupService: Application environment setup completed
2025-07-21 23:20:49.781 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-21 23:20:49.783 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-21 23:20:49.785 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-21 23:20:49.789 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-21 23:20:49.796 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-21 23:20:49.798 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.800 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.800 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-21 23:20:49.803 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-21 23:20:49.805 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.806 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.807 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-21 23:20:49.809 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-21 23:20:49.811 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.812 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.812 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-21 23:20:49.814 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-21 23:20:49.815 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.816 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.816 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-21 23:20:49.818 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-21 23:20:49.819 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.821 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.822 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-21 23:20:49.825 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-21 23:20:49.826 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.828 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.828 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-21 23:20:49.830 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-21 23:20:49.831 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.832 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-21 23:20:49.833 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-21 23:20:49.834 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-21 23:20:49.837 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-21 23:20:49.838 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-21 23:20:49.856 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-21 23:20:49.857 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-21 23:20:49.857 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-21 23:20:49.858 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-21 23:20:49.858 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-21 23:20:49.926 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-21 23:20:49.926 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-21 23:20:49.929 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-21 23:20:49.929 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-21 23:20:49.929 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-21 23:20:49.930 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-21 23:20:49.930 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-21 23:20:49.930 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-21 23:20:49.931 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-21 23:20:49.940 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-21 23:20:49.941 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-21 23:20:49.941 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-21 23:20:49.946 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-21 23:20:49.946 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-21 23:20:49.948 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-21 23:20:49.949 [Information] LibraryExtractor: Starting library extraction process
2025-07-21 23:20:49.951 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-21 23:20:49.954 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-21 23:20:49.956 [Information] LibraryExtractor: Copying system libraries
2025-07-21 23:20:49.962 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-21 23:20:49.968 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-21 23:21:19.980 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-21 23:21:20.982 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-21 23:21:50.985 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-21 23:21:50.986 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-21 23:22:20.987 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-21 23:22:21.988 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-21 23:22:52.040 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-21 23:22:52.041 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-21 23:23:22.227 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-21 23:23:23.228 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-21 23:23:53.231 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-21 23:23:53.231 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-21 23:23:53.232 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-21 23:23:53.234 [Information] LibraryExtractor: Verifying library extraction
2025-07-21 23:23:53.235 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-21 23:23:53.235 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-21 23:23:53.235 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-21 23:23:53.236 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-21 23:23:53.236 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-21 23:23:53.240 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-21 23:23:53.242 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-21 23:23:53.243 [Information] DependencyManager: Initializing dependency manager
2025-07-21 23:23:53.244 [Information] DependencyManager: Setting up library search paths
2025-07-21 23:23:53.245 [Information] DependencyManager: Added library path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-21 23:23:53.245 [Information] DependencyManager: Added driver path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-21 23:23:53.245 [Information] DependencyManager: Added application path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-21 23:23:53.245 [Information] DependencyManager: Updated PATH environment variable
2025-07-21 23:23:53.246 [Information] DependencyManager: Verifying required directories
2025-07-21 23:23:53.247 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-21 23:23:53.247 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-21 23:23:53.247 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-21 23:23:53.248 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-21 23:23:53.249 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-21 23:23:53.268 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-21 23:23:53.269 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-21 23:23:53.270 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-21 23:23:53.272 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcr120.dll: Error 193
2025-07-21 23:23:53.273 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr120.dll
2025-07-21 23:23:53.273 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-21 23:23:53.274 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-21 23:23:53.275 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-21 23:23:53.276 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp120.dll: Error 193
2025-07-21 23:23:53.276 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp120.dll
2025-07-21 23:23:53.276 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-21 23:23:53.277 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-21 23:23:53.290 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-21 23:23:53.297 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll from C:\Windows\system32\msvcp140.dll: Error 193
2025-07-21 23:23:53.297 [Warning] DependencyManager: Architecture mismatch detected for msvcp140.dll. Expected: x64
2025-07-21 23:23:53.298 [Debug] DependencyManager: Architecture mismatch: Library msvcp140.dll is x86, process is x64
2025-07-21 23:23:53.298 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp140.dll
2025-07-21 23:23:53.299 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll: Error 193
2025-07-21 23:23:53.299 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp140.dll
2025-07-21 23:23:53.300 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-21 23:23:53.302 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-21 23:23:53.302 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-21 23:23:53.303 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-21 23:23:53.303 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-21 23:23:53.304 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-21 23:23:53.304 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-21 23:23:53.304 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-21 23:23:53.305 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-21 23:23:53.305 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-21 23:23:53.306 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-21 23:23:53.306 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-21 23:23:53.307 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-21 23:23:53.307 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-21 23:23:53.307 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-21 23:23:53.308 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-21 23:23:53.308 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-21 23:23:53.308 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-21 23:23:53.309 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-21 23:23:53.309 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-21 23:23:53.309 [Information] DependencyManager: VC++ Redistributable library loading: 1/14 (7.1%) libraries loaded
2025-07-21 23:23:53.309 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-21 23:23:53.310 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-21 23:23:53.312 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-21 23:23:53.427 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-21 23:23:53.427 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-21 23:23:53.430 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-21 23:23:53.430 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-21 23:23:53.431 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-21 23:23:53.432 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-21 23:23:53.692 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-21 23:23:53.693 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-21 23:23:53.694 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-21 23:23:53.795 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-21 23:23:53.796 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-21 23:23:53.796 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-21 23:23:53.797 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-21 23:23:53.798 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-21 23:23:53.799 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-21 23:23:53.800 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-21 23:23:53.800 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-21 23:23:53.800 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-21 23:23:53.801 [Warning] DependencyManager: Failed to load Critical library msvcr120.dll: Error 193
2025-07-21 23:23:53.801 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-21 23:23:53.802 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-21 23:23:53.802 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-21 23:23:53.802 [Warning] DependencyManager: Failed to load Critical library msvcp120.dll: Error 193
2025-07-21 23:23:53.803 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-21 23:23:53.803 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-21 23:23:53.807 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-21 23:23:53.807 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-21 23:23:53.808 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-21 23:23:53.808 [Information] DependencyManager: Setting up environment variables
2025-07-21 23:23:53.808 [Information] DependencyManager: Environment variables configured
2025-07-21 23:23:53.810 [Information] DependencyManager: Verifying library loading status
2025-07-21 23:23:54.116 [Information] DependencyManager: Library loading verification: 4/11 (36.4%) critical libraries loaded
2025-07-21 23:23:54.116 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-21 23:23:54.116 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-21 23:23:54.118 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-21 23:23:54.119 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-21 23:23:54.122 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-21 23:23:54.123 [Information] IntegratedStartupService: Verifying system readiness
2025-07-21 23:23:54.124 [Information] IntegratedStartupService: System readiness verification passed
2025-07-21 23:23:54.124 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-21 23:23:54.125 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-21 23:23:54.126 [Information] IntegratedStartupService: Application Path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-21 23:23:54.126 [Information] IntegratedStartupService: Libraries Path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-21 23:23:54.126 [Information] IntegratedStartupService: Drivers Path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-21 23:23:54.127 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-21 23:23:54.127 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-21 23:23:54.127 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-21 23:23:54.127 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-21 23:23:54.128 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-21 23:23:54.128 [Information] App: Integrated startup completed successfully
2025-07-21 23:23:54.130 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-21 23:23:54.340 [Information] App: Initializing application services
2025-07-21 23:23:54.342 [Information] AppConfigurationService: Initializing configuration service
2025-07-21 23:23:54.342 [Information] AppConfigurationService: Created configuration directory: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-21 23:23:54.397 [Information] AppConfigurationService: Configuration loaded from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-21 23:23:54.398 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-21 23:23:54.398 [Information] App: Configuration service initialized successfully
2025-07-21 23:23:54.400 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-21 23:23:54.401 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-21 23:23:54.405 [Information] App: Environment variable exists: True, not 'false': False
2025-07-21 23:23:54.405 [Information] App: Final useDummyImplementations value: False
2025-07-21 23:23:54.406 [Information] App: Updating config to NOT use dummy implementations
2025-07-21 23:23:54.407 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-21 23:23:54.422 [Information] AppConfigurationService: Configuration saved to D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-21 23:23:54.423 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-21 23:23:54.423 [Information] App: usePatchedImplementation flag is: True
2025-07-21 23:23:54.423 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-21 23:23:54.424 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-21 23:23:54.424 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-21 23:23:54.424 [Information] App: verboseLogging flag is: True
2025-07-21 23:23:54.426 [Information] App: Verifying real hardware requirements...
2025-07-21 23:23:54.427 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-21 23:23:54.427 [Information] App: ✓ Found critical library: apci.dll
2025-07-21 23:23:54.427 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-21 23:23:54.428 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-21 23:23:54.429 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-21 23:23:54.430 [Information] App: Phoenix Diag not found at: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021 (optional)
2025-07-21 23:23:54.430 [Information] App: ✓ Found Vocom driver config: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-21 23:23:54.430 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-21 23:23:54.442 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-21 23:23:54.444 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-21 23:23:54.444 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-21 23:23:54.445 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-21 23:23:54.449 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-07-21 23:26:35.450 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-21 23:26:35.450 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-21 23:26:35.451 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-21 23:26:35.451 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-21 23:26:35.451 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-21 23:26:35.452 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-21 23:26:35.452 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-21 23:26:35.452 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-21 23:26:35.452 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-21 23:26:35.453 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-21 23:26:35.454 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-21 23:26:35.454 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-21 23:26:35.454 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-21 23:26:35.454 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-21 23:26:35.455 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-21 23:26:35.455 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-21 23:26:35.455 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-21 23:26:35.455 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-21 23:26:35.457 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-21 23:26:35.457 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-21 23:26:35.459 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-21 23:26:35.460 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-21 23:26:35.460 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-21 23:26:35.460 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-21 23:26:35.462 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-21 23:26:35.462 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-07-21 23:26:35.463 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-07-21 23:26:35.464 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-21 23:26:35.465 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-07-21 23:26:35.465 [Information] PatchedVocomServiceFactory: Assembly location: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-07-21 23:26:35.491 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-21 23:26:35.492 [Information] PatchedVocomServiceFactory: Created marker file at D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-07-21 23:26:35.496 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-21 23:26:35.496 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-21 23:26:35.497 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-21 23:26:35.498 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-21 23:26:35.499 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-21 23:26:35.500 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-21 23:26:35.500 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-21 23:26:35.500 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-21 23:26:35.500 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-21 23:26:35.500 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-21 23:26:35.501 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-21 23:26:35.503 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-21 23:26:35.506 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-21 23:26:35.508 [Error] PhoenixVocomAdapter: Phoenix Diag path not found at C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-21 23:26:35.509 [Error] PhoenixVocomAdapter: Required Phoenix libraries are not available
2025-07-21 23:26:35.510 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-21 23:26:35.510 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-21 23:26:35.510 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-21 23:26:35.512 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-21 23:26:35.513 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-21 23:26:35.514 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-21 23:26:35.521 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-21 23:26:35.522 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-21 23:26:35.522 [Information] VocomNativeInterop_Patch: Searching for apcidb.dll...
2025-07-21 23:26:35.523 [Information] VocomNativeInterop_Patch: Searching for apcidb.dll in 9 locations
2025-07-21 23:26:35.524 [Information] VocomNativeInterop_Patch: Searching for WUDFPuma.dll...
2025-07-21 23:26:35.524 [Information] VocomNativeInterop_Patch: Searching for WUDFPuma.dll in 9 locations
2025-07-21 23:26:35.524 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-21 23:26:35.525 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-21 23:26:35.527 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-21 23:26:35.528 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-21 23:26:35.528 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-21 23:26:35.528 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-21 23:26:35.529 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-21 23:26:35.530 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-21 23:26:35.530 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-21 23:26:35.531 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-21 23:26:35.532 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-21 23:26:35.532 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-21 23:26:35.532 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-21 23:26:35.534 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-21 23:26:35.534 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-21 23:26:35.535 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-21 23:26:35.536 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-21 23:26:35.538 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-21 23:26:35.538 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-21 23:26:35.538 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-21 23:26:35.540 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-21 23:26:35.541 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-21 23:26:35.542 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-21 23:26:35.542 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-21 23:26:35.543 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-21 23:26:35.543 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-21 23:26:35.545 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-21 23:26:35.738 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-21 23:26:35.739 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-21 23:26:35.743 [Information] VocomNativeInterop_Patch: Loading function pointers from Vocom driver DLL
2025-07-21 23:26:35.744 [Error] VocomNativeInterop_Patch: Failed to find any initialize function in the DLL
2025-07-21 23:26:35.745 [Error] VocomNativeInterop_Patch: Failed to load function pointers from Vocom driver DLL
2025-07-21 23:26:35.746 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-21 23:26:35.746 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-21 23:26:35.748 [Information] VocomDriver: Initializing Vocom driver
2025-07-21 23:26:35.750 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-21 23:26:35.754 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-21 23:26:35.754 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-21 23:26:35.754 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-21 23:26:35.756 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-21 23:26:35.757 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-21 23:26:35.759 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr120.dll
2025-07-21 23:26:35.760 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp120.dll
2025-07-21 23:26:35.762 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-21 23:26:35.763 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-21 23:26:35.763 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-21 23:26:35.763 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-21 23:26:35.830 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-07-21 23:26:36.001 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-07-21 23:26:36.181 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-07-21 23:26:36.182 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-21 23:26:36.182 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-21 23:26:36.183 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-21 23:26:36.185 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-21 23:26:36.186 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-21 23:26:36.186 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-21 23:26:36.187 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-21 23:26:36.187 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-21 23:26:36.187 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-21 23:26:36.188 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-21 23:26:36.188 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-21 23:26:36.188 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-21 23:26:36.189 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-21 23:26:36.189 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-21 23:26:36.189 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-21 23:26:36.189 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-21 23:26:36.190 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-21 23:26:36.190 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-21 23:26:36.190 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-21 23:26:36.190 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-21 23:26:36.190 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-21 23:26:36.191 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-21 23:26:36.191 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-21 23:26:36.191 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-21 23:26:36.191 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-21 23:26:36.191 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-21 23:26:36.191 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-21 23:26:36.192 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-21 23:26:36.192 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-21 23:26:36.192 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-21 23:26:36.192 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-21 23:26:36.192 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-21 23:26:36.193 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-21 23:26:36.193 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-21 23:26:36.194 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-21 23:26:36.194 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-21 23:26:36.194 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-21 23:26:36.194 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-21 23:26:36.195 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-21 23:26:36.195 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-21 23:26:36.195 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-21 23:26:36.195 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-21 23:26:36.195 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-21 23:26:36.196 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-21 23:26:36.196 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-21 23:26:36.196 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-21 23:26:36.198 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-21 23:26:36.198 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-21 23:26:36.199 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-21 23:26:36.200 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-21 23:26:36.200 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-21 23:26:36.200 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-21 23:26:36.201 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-21 23:26:36.201 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-21 23:26:36.206 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-21 23:26:36.207 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-21 23:26:36.209 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-21 23:26:36.211 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-21 23:26:36.271 [Information] WiFiCommunicationService: WiFi is available
2025-07-21 23:26:36.272 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-21 23:26:36.273 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-21 23:26:36.274 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-21 23:26:36.276 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-21 23:26:36.276 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-21 23:26:36.278 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-21 23:26:36.280 [Information] VocomService: Initializing enhanced Vocom services
2025-07-21 23:26:36.281 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-21 23:26:36.283 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-21 23:26:36.287 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-21 23:26:36.288 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-21 23:26:36.288 [Information] VocomService: Native USB communication service initialized
2025-07-21 23:26:36.288 [Information] VocomService: Enhanced device detection service initialized
2025-07-21 23:26:36.289 [Information] VocomService: Connection recovery service initialized
2025-07-21 23:26:36.289 [Information] VocomService: Enhanced services initialization completed
2025-07-21 23:26:36.291 [Information] VocomService: Checking if PTT application is running
2025-07-21 23:26:36.302 [Information] VocomService: PTT application is not running
2025-07-21 23:26:36.304 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-21 23:26:36.305 [Debug] VocomService: Bluetooth is enabled
2025-07-21 23:26:36.306 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-21 23:26:36.306 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-21 23:26:36.306 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-07-21 23:26:36.309 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-21 23:26:36.309 [Information] VocomService: Using new enhanced device detection service
2025-07-21 23:26:36.311 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-21 23:26:36.313 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-21 23:26:36.622 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-21 23:26:36.624 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-21 23:26:36.625 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-21 23:26:36.627 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-21 23:26:36.628 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-21 23:26:36.630 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-21 23:26:36.633 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-21 23:26:36.638 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-21 23:26:36.833 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-21 23:26:36.835 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-21 23:26:36.836 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-21 23:26:36.837 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-21 23:26:36.839 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-21 23:26:36.839 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-21 23:26:36.839 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-21 23:26:36.839 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-21 23:26:36.840 [Debug] VocomService: Bluetooth is enabled
2025-07-21 23:26:36.842 [Debug] VocomService: Checking if WiFi is available
2025-07-21 23:26:36.843 [Debug] VocomService: WiFi is available
2025-07-21 23:26:36.844 [Information] VocomService: Found 3 Vocom devices
2025-07-21 23:26:36.844 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-07-21 23:26:36.845 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: 8e2f7992-db6f-45e5-88e1-2981a1d145dd, Type: USB)
2025-07-21 23:26:36.846 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: 3ac0aac2-c3c0-437b-8628-1b35223ad50d, Type: Bluetooth)
2025-07-21 23:26:36.846 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: 68d5e017-f893-45f3-a93b-acf99becd2e9, Type: WiFi)
2025-07-21 23:26:36.847 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-07-21 23:26:36.848 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-07-21 23:26:36.848 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-07-21 23:26:36.848 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-07-21 23:26:36.848 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-07-21 23:26:36.849 [Information] App: Architecture-aware Vocom service created successfully
2025-07-21 23:26:36.849 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-21 23:26:36.849 [Information] VocomService: Initializing enhanced Vocom services
2025-07-21 23:26:36.849 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-21 23:26:36.850 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-21 23:26:36.850 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-21 23:26:36.850 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-21 23:26:36.850 [Information] VocomService: Native USB communication service initialized
2025-07-21 23:26:36.851 [Information] VocomService: Enhanced device detection service initialized
2025-07-21 23:26:36.851 [Information] VocomService: Connection recovery service initialized
2025-07-21 23:26:36.851 [Information] VocomService: Enhanced services initialization completed
2025-07-21 23:26:36.851 [Information] VocomService: Checking if PTT application is running
2025-07-21 23:26:36.862 [Information] VocomService: PTT application is not running
2025-07-21 23:26:36.862 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-21 23:26:36.862 [Debug] VocomService: Bluetooth is enabled
2025-07-21 23:26:36.863 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-21 23:26:36.863 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-21 23:26:36.863 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-21 23:26:36.864 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-21 23:26:36.896 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-21 23:26:36.896 [Information] VocomService: Using new enhanced device detection service
2025-07-21 23:26:36.896 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-21 23:26:36.897 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-21 23:26:37.068 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-21 23:26:37.068 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-21 23:26:37.069 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-21 23:26:37.069 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-21 23:26:37.069 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-21 23:26:37.069 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-21 23:26:37.069 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-21 23:26:37.070 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-21 23:26:37.239 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-21 23:26:37.239 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-21 23:26:37.239 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-21 23:26:37.240 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-21 23:26:37.240 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-21 23:26:37.241 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-21 23:26:37.241 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-21 23:26:37.241 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-21 23:26:37.241 [Debug] VocomService: Bluetooth is enabled
2025-07-21 23:26:37.242 [Debug] VocomService: Checking if WiFi is available
2025-07-21 23:26:37.242 [Debug] VocomService: WiFi is available
2025-07-21 23:26:37.242 [Information] VocomService: Found 3 Vocom devices
2025-07-21 23:26:37.242 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-21 23:26:37.245 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-21 23:26:37.245 [Information] VocomService: Checking if PTT application is running
2025-07-21 23:26:37.257 [Information] VocomService: PTT application is not running
2025-07-21 23:26:37.260 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-21 23:26:37.261 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-21 23:26:37.261 [Information] VocomService: Checking if PTT application is running
2025-07-21 23:26:37.272 [Information] VocomService: PTT application is not running
2025-07-21 23:26:37.273 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-21 23:26:37.274 [Information] VocomService: Native USB connection attempt 1/3 to WUDFPuma Driver
2025-07-21 23:26:37.276 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-21 23:26:37.278 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-21 23:26:37.279 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-21 23:26:37.280 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-21 23:26:39.282 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-21 23:26:40.282 [Information] VocomService: Native USB connection attempt 2/3 to WUDFPuma Driver
2025-07-21 23:26:40.282 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-21 23:26:40.283 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-21 23:26:40.283 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-21 23:26:40.283 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-21 23:26:42.284 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-21 23:26:43.285 [Information] VocomService: Native USB connection attempt 3/3 to WUDFPuma Driver
2025-07-21 23:26:43.285 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-21 23:26:43.286 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-21 23:26:43.286 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-21 23:26:43.286 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-21 23:26:45.286 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-21 23:26:45.287 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-21 23:26:45.287 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-21 23:26:45.320 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-21 23:26:45.321 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-21 23:26:45.322 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-21 23:26:45.323 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-21 23:26:45.323 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-21 23:26:45.323 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-21 23:26:45.323 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-21 23:26:45.325 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-21 23:26:45.326 [Information] ModernUSBCommunicationService: Found 1 HID devices total
2025-07-21 23:26:45.331 [Debug] ModernUSBCommunicationService: Checking HID device: VID=10C4, PID=8108, Name=
2025-07-21 23:26:45.333 [Information] ModernUSBCommunicationService: Trying alternative device enumeration methods
2025-07-21 23:26:45.334 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-21 23:26:45.335 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-21 23:26:45.335 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-21 23:26:45.335 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-21 23:26:45.336 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-21 23:26:45.337 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-21 23:26:45.339 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-21 23:26:45.342 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-21 23:26:45.342 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-21 23:26:45.346 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-21 23:26:45.347 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-21 23:26:45.348 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-21 23:26:45.348 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-21 23:26:45.348 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-07-21 23:26:45.348 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-21 23:26:45.349 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-21 23:26:45.349 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-21 23:26:45.351 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-21 23:26:45.351 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-21 23:26:45.351 [Information] VocomService: Using new enhanced device detection service
2025-07-21 23:26:45.352 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-21 23:26:45.352 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-21 23:26:45.521 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-21 23:26:45.521 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-21 23:26:45.521 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-21 23:26:45.522 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-21 23:26:45.522 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-21 23:26:45.522 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-21 23:26:45.522 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-21 23:26:45.523 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-21 23:26:45.689 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-21 23:26:45.689 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-21 23:26:45.689 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-21 23:26:45.690 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-21 23:26:45.690 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-21 23:26:45.691 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-21 23:26:45.691 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-21 23:26:45.691 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-21 23:26:45.692 [Debug] VocomService: Bluetooth is enabled
2025-07-21 23:26:45.692 [Debug] VocomService: Checking if WiFi is available
2025-07-21 23:26:45.692 [Debug] VocomService: WiFi is available
2025-07-21 23:26:45.692 [Information] VocomService: Found 3 Vocom devices
2025-07-21 23:26:45.693 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-21 23:26:45.693 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-21 23:26:45.694 [Information] VocomService: Checking if PTT application is running
2025-07-21 23:26:45.705 [Information] VocomService: PTT application is not running
2025-07-21 23:26:45.705 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-21 23:26:45.706 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-21 23:26:45.706 [Information] VocomService: Checking if PTT application is running
2025-07-21 23:26:45.716 [Information] VocomService: PTT application is not running
2025-07-21 23:26:45.716 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-21 23:26:45.717 [Information] VocomService: Native USB connection attempt 1/3 to WUDFPuma Driver
2025-07-21 23:26:45.717 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-21 23:26:45.717 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-21 23:26:45.717 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-21 23:26:45.717 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-21 23:26:47.717 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-21 23:26:48.718 [Information] VocomService: Native USB connection attempt 2/3 to WUDFPuma Driver
2025-07-21 23:26:48.718 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-21 23:26:48.718 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-21 23:26:48.719 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-21 23:26:48.719 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-21 23:26:50.720 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-21 23:26:51.720 [Information] VocomService: Native USB connection attempt 3/3 to WUDFPuma Driver
2025-07-21 23:26:51.721 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-21 23:26:51.721 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-21 23:26:51.721 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-21 23:26:51.721 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-21 23:26:53.722 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-21 23:26:53.722 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-21 23:26:53.722 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-21 23:26:53.723 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-21 23:26:53.723 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-21 23:26:53.723 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-21 23:26:53.724 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-21 23:26:53.724 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-21 23:26:53.724 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-21 23:26:53.724 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-21 23:26:53.725 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-21 23:26:53.725 [Information] ModernUSBCommunicationService: Found 1 HID devices total
2025-07-21 23:26:53.725 [Debug] ModernUSBCommunicationService: Checking HID device: VID=10C4, PID=8108, Name=
2025-07-21 23:26:53.725 [Information] ModernUSBCommunicationService: Trying alternative device enumeration methods
2025-07-21 23:26:53.726 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-21 23:26:53.726 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-21 23:26:53.726 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-21 23:26:53.727 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-21 23:26:53.727 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-21 23:26:53.727 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-21 23:26:53.728 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-21 23:26:53.728 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-21 23:26:53.728 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-21 23:26:53.728 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-21 23:26:53.728 [Information] VocomService: Checking if PTT application is running
2025-07-21 23:26:53.738 [Information] VocomService: PTT application is not running
2025-07-21 23:26:53.740 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-21 23:26:53.741 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-21 23:26:53.741 [Debug] VocomService: Bluetooth is enabled
2025-07-21 23:26:53.742 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-21 23:26:54.546 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-21 23:26:54.546 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-21 23:26:54.547 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-21 23:26:54.547 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-21 23:26:54.549 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-21 23:26:54.550 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-21 23:26:54.566 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-21 23:26:54.568 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-21 23:26:54.585 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-21 23:26:54.590 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-21 23:26:54.593 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-21 23:26:54.604 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-21 23:26:54.605 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-21 23:26:54.605 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-21 23:26:54.606 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-21 23:26:54.606 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-21 23:26:54.606 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-21 23:26:54.606 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-21 23:26:54.606 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-21 23:26:54.607 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-21 23:26:54.607 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-21 23:26:54.607 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-21 23:26:54.607 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-21 23:26:54.608 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-21 23:26:54.608 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-21 23:26:54.608 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-21 23:26:54.608 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-21 23:26:54.609 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-21 23:26:54.613 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-21 23:26:54.619 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-21 23:26:54.620 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-21 23:26:54.622 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-21 23:26:54.624 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-21 23:26:54.631 [Information] CANRegisterAccess: Read value 0xAD from register 0x0141 (simulated)
2025-07-21 23:26:54.632 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-21 23:26:54.633 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-21 23:26:54.633 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-21 23:26:54.639 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-21 23:26:54.639 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-21 23:26:54.645 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-21 23:26:54.645 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-21 23:26:54.645 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-21 23:26:54.651 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-21 23:26:54.651 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-21 23:26:54.651 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-21 23:26:54.657 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-21 23:26:54.657 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-21 23:26:54.662 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-21 23:26:54.663 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-21 23:26:54.669 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-21 23:26:54.669 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-21 23:26:54.675 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-21 23:26:54.675 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-21 23:26:54.681 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-21 23:26:54.681 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-21 23:26:54.687 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-21 23:26:54.687 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-21 23:26:54.692 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-21 23:26:54.693 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-21 23:26:54.698 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-21 23:26:54.699 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-21 23:26:54.705 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-21 23:26:54.705 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-21 23:26:54.710 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-21 23:26:54.711 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-21 23:26:54.717 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-21 23:26:54.717 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-21 23:26:54.722 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-21 23:26:54.723 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-21 23:26:54.728 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-21 23:26:54.729 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-21 23:26:54.734 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-21 23:26:54.735 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-21 23:26:54.740 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-21 23:26:54.741 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-21 23:26:54.746 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-21 23:26:54.747 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-21 23:26:54.752 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-21 23:26:54.753 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-21 23:26:54.753 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-21 23:26:54.758 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-21 23:26:54.759 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-21 23:26:54.759 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-21 23:26:54.759 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-21 23:26:54.765 [Information] CANRegisterAccess: Read value 0x3D from register 0x0141 (simulated)
2025-07-21 23:26:54.770 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-21 23:26:54.776 [Information] CANRegisterAccess: Read value 0x7F from register 0x0141 (simulated)
2025-07-21 23:26:54.782 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-21 23:26:54.788 [Information] CANRegisterAccess: Read value 0x6B from register 0x0141 (simulated)
2025-07-21 23:26:54.794 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-21 23:26:54.800 [Information] CANRegisterAccess: Read value 0x8E from register 0x0141 (simulated)
2025-07-21 23:26:54.801 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-21 23:26:54.801 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-21 23:26:54.801 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-21 23:26:54.801 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-21 23:26:54.807 [Information] CANRegisterAccess: Read value 0xE0 from register 0x0140 (simulated)
2025-07-21 23:26:54.813 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-21 23:26:54.819 [Information] CANRegisterAccess: Read value 0x8B from register 0x0140 (simulated)
2025-07-21 23:26:54.825 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-21 23:26:54.831 [Information] CANRegisterAccess: Read value 0x7F from register 0x0140 (simulated)
2025-07-21 23:26:54.832 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-21 23:26:54.832 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-21 23:26:54.834 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-21 23:26:54.835 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-21 23:26:54.845 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-21 23:26:54.847 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-21 23:26:54.847 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-21 23:26:54.853 [Information] VocomService: Sending data and waiting for response
2025-07-21 23:26:54.853 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-21 23:26:54.904 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-21 23:26:54.906 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-21 23:26:54.906 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-21 23:26:54.907 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-21 23:26:54.907 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-21 23:26:54.918 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-21 23:26:54.919 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-21 23:26:54.920 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-21 23:26:54.930 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-21 23:26:54.941 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-21 23:26:54.952 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-21 23:26:54.963 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-21 23:26:54.974 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-21 23:26:54.976 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-21 23:26:54.976 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-21 23:26:54.987 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-21 23:26:54.988 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-21 23:26:54.988 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-21 23:26:54.999 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-21 23:26:55.010 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-21 23:26:55.021 [Information] IICProtocolHandler: Enabling IIC module
2025-07-21 23:26:55.032 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-21 23:26:55.043 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-21 23:26:55.054 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-21 23:26:55.056 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-21 23:26:55.056 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-21 23:26:55.067 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-21 23:26:55.069 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-21 23:26:55.069 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-21 23:26:55.069 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-21 23:26:55.069 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-21 23:26:55.069 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-21 23:26:55.070 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-21 23:26:55.070 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-21 23:26:55.070 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-21 23:26:55.070 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-21 23:26:55.070 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-21 23:26:55.071 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-21 23:26:55.071 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-21 23:26:55.071 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-21 23:26:55.072 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-21 23:26:55.072 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-21 23:26:55.073 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-21 23:26:55.173 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-21 23:26:55.173 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-21 23:26:55.176 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-21 23:26:55.177 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-21 23:26:55.178 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-21 23:26:55.178 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-21 23:26:55.179 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-21 23:26:55.179 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-21 23:26:55.179 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-21 23:26:55.180 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-21 23:26:55.180 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-21 23:26:55.181 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-21 23:26:55.182 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-21 23:26:55.182 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-21 23:26:55.183 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-21 23:26:55.184 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-21 23:26:55.185 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-21 23:26:55.185 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-21 23:26:55.189 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-21 23:26:55.191 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-21 23:26:55.194 [Information] BackupService: Initializing backup service
2025-07-21 23:26:55.195 [Information] BackupService: Backup service initialized successfully
2025-07-21 23:26:55.195 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-21 23:26:55.195 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-21 23:26:55.198 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-21 23:26:55.243 [Information] BackupService: Compressing backup data
2025-07-21 23:26:55.252 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (448 bytes)
2025-07-21 23:26:55.253 [Information] BackupServiceFactory: Created template for category: Production
2025-07-21 23:26:55.254 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-21 23:26:55.254 [Information] BackupService: Compressing backup data
2025-07-21 23:26:55.256 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (449 bytes)
2025-07-21 23:26:55.256 [Information] BackupServiceFactory: Created template for category: Development
2025-07-21 23:26:55.256 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-21 23:26:55.257 [Information] BackupService: Compressing backup data
2025-07-21 23:26:55.258 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (444 bytes)
2025-07-21 23:26:55.258 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-21 23:26:55.258 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-21 23:26:55.259 [Information] BackupService: Compressing backup data
2025-07-21 23:26:55.260 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-21 23:26:55.260 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-21 23:26:55.261 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-21 23:26:55.261 [Information] BackupService: Compressing backup data
2025-07-21 23:26:55.262 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (447 bytes)
2025-07-21 23:26:55.262 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-21 23:26:55.263 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-21 23:26:55.264 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-21 23:26:55.265 [Information] BackupService: Compressing backup data
2025-07-21 23:26:55.267 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (516 bytes)
2025-07-21 23:26:55.267 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-21 23:26:55.267 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-21 23:26:55.269 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-21 23:26:55.274 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-21 23:26:55.276 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-21 23:26:55.344 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-21 23:26:55.345 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-21 23:26:55.346 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-21 23:26:55.346 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-21 23:26:55.346 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-21 23:26:55.347 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-21 23:26:55.348 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-21 23:26:55.352 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-21 23:26:55.352 [Information] App: Flash operation monitor service initialized successfully
2025-07-21 23:26:55.361 [Information] LicensingService: Initializing licensing service
2025-07-21 23:26:55.406 [Information] LicensingService: License information loaded successfully
2025-07-21 23:26:55.409 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-21 23:26:55.409 [Information] App: Licensing service initialized successfully
2025-07-21 23:26:55.409 [Information] App: License status: Trial
2025-07-21 23:26:55.410 [Information] App: Trial period: 22 days remaining
2025-07-21 23:26:55.410 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-21 23:26:55.462 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-21 23:26:55.647 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-21 23:26:55.647 [Information] VocomService: Initializing enhanced Vocom services
2025-07-21 23:26:55.647 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-21 23:26:55.648 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-21 23:26:55.648 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-21 23:26:55.648 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-21 23:26:55.649 [Information] VocomService: Native USB communication service initialized
2025-07-21 23:26:55.649 [Information] VocomService: Enhanced device detection service initialized
2025-07-21 23:26:55.649 [Information] VocomService: Connection recovery service initialized
2025-07-21 23:26:55.650 [Information] VocomService: Enhanced services initialization completed
2025-07-21 23:26:55.650 [Information] VocomService: Checking if PTT application is running
2025-07-21 23:26:55.662 [Information] VocomService: PTT application is not running
2025-07-21 23:26:55.662 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-21 23:26:55.663 [Debug] VocomService: Bluetooth is enabled
2025-07-21 23:26:55.663 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-21 23:26:55.714 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-21 23:26:55.714 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-21 23:26:55.714 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-21 23:26:55.715 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-21 23:26:55.715 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-21 23:26:55.715 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: 85922c66-f50c-460a-b897-4b46b2f27d6d
2025-07-21 23:26:55.717 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-21 23:26:55.718 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-21 23:26:55.718 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-21 23:26:55.718 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-21 23:26:55.720 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-21 23:26:55.721 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-21 23:26:55.722 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-21 23:26:55.722 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-21 23:26:55.722 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-21 23:26:55.734 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-21 23:26:55.734 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-21 23:26:55.735 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-21 23:26:55.735 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-21 23:26:55.735 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-21 23:26:55.735 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-21 23:26:55.736 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-21 23:26:55.736 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-21 23:26:55.736 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-21 23:26:55.736 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-21 23:26:55.737 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-21 23:26:55.737 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-21 23:26:55.737 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-21 23:26:55.737 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-21 23:26:55.738 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-21 23:26:55.738 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-21 23:26:55.738 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-21 23:26:55.738 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-21 23:26:55.744 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-21 23:26:55.745 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-21 23:26:55.745 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-21 23:26:55.745 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-21 23:26:55.751 [Information] CANRegisterAccess: Read value 0xCF from register 0x0141 (simulated)
2025-07-21 23:26:55.752 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-21 23:26:55.752 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-21 23:26:55.753 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-21 23:26:55.758 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-21 23:26:55.759 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-21 23:26:55.765 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-21 23:26:55.765 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-21 23:26:55.765 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-21 23:26:55.771 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-21 23:26:55.772 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-21 23:26:55.772 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-21 23:26:55.778 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-21 23:26:55.779 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-21 23:26:55.785 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-21 23:26:55.786 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-21 23:26:55.792 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-21 23:26:55.793 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-21 23:26:55.798 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-21 23:26:55.799 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-21 23:26:55.804 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-21 23:26:55.805 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-21 23:26:55.811 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-21 23:26:55.812 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-21 23:26:55.818 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-21 23:26:55.819 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-21 23:26:55.825 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-21 23:26:55.826 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-21 23:26:55.831 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-21 23:26:55.832 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-21 23:26:55.837 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-21 23:26:55.838 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-21 23:26:55.846 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-21 23:26:55.847 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-21 23:26:55.852 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-21 23:26:55.853 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-21 23:26:55.859 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-21 23:26:55.860 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-21 23:26:55.865 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-21 23:26:55.866 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-21 23:26:55.871 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-21 23:26:55.872 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-21 23:26:55.877 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-21 23:26:55.878 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-21 23:26:55.883 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-21 23:26:55.884 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-21 23:26:55.884 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-21 23:26:55.890 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-21 23:26:55.891 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-21 23:26:55.891 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-21 23:26:55.891 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-21 23:26:55.897 [Information] CANRegisterAccess: Read value 0xCF from register 0x0141 (simulated)
2025-07-21 23:26:55.903 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-21 23:26:55.909 [Information] CANRegisterAccess: Read value 0xEB from register 0x0141 (simulated)
2025-07-21 23:26:55.915 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-21 23:26:55.921 [Information] CANRegisterAccess: Read value 0x80 from register 0x0141 (simulated)
2025-07-21 23:26:55.922 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-21 23:26:55.922 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-21 23:26:55.923 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-21 23:26:55.923 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-21 23:26:55.929 [Information] CANRegisterAccess: Read value 0x9E from register 0x0140 (simulated)
2025-07-21 23:26:55.930 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-21 23:26:55.930 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-21 23:26:55.930 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-21 23:26:55.931 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-21 23:26:55.941 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-21 23:26:55.942 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-21 23:26:55.942 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-21 23:26:55.942 [Information] VocomService: Sending data and waiting for response
2025-07-21 23:26:55.942 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-21 23:26:55.993 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-21 23:26:55.993 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-21 23:26:55.993 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-21 23:26:55.993 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-21 23:26:55.994 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-21 23:26:56.005 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-21 23:26:56.005 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-21 23:26:56.005 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-21 23:26:56.017 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-21 23:26:56.027 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-21 23:26:56.038 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-21 23:26:56.049 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-21 23:26:56.060 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-21 23:26:56.060 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-21 23:26:56.061 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-21 23:26:56.072 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-21 23:26:56.072 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-21 23:26:56.073 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-21 23:26:56.084 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-21 23:26:56.095 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-21 23:26:56.106 [Information] IICProtocolHandler: Enabling IIC module
2025-07-21 23:26:56.117 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-21 23:26:56.128 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-21 23:26:56.139 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-21 23:26:56.139 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-21 23:26:56.140 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-21 23:26:56.151 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-21 23:26:56.151 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-21 23:26:56.152 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-21 23:26:56.152 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-21 23:26:56.152 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-21 23:26:56.152 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-21 23:26:56.153 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-21 23:26:56.153 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-21 23:26:56.153 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-21 23:26:56.154 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-21 23:26:56.154 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-21 23:26:56.154 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-21 23:26:56.154 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-21 23:26:56.155 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-21 23:26:56.155 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-21 23:26:56.155 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-21 23:26:56.156 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-21 23:26:56.257 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-21 23:26:56.257 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-21 23:26:56.258 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-21 23:26:56.258 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-21 23:26:56.258 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-21 23:26:56.259 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-21 23:26:56.259 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-21 23:26:56.259 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-21 23:26:56.259 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-21 23:26:56.260 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-21 23:26:56.260 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-21 23:26:56.260 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-21 23:26:56.261 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-21 23:26:56.261 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-21 23:26:56.261 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-21 23:26:56.312 [Information] BackupService: Initializing backup service
2025-07-21 23:26:56.312 [Information] BackupService: Backup service initialized successfully
2025-07-21 23:26:56.364 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-21 23:26:56.364 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-21 23:26:56.365 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-21 23:26:56.366 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-21 23:26:56.417 [Information] BackupService: Getting predefined backup categories
2025-07-21 23:26:56.468 [Information] MainViewModel: Services initialized successfully
2025-07-21 23:26:56.472 [Information] MainViewModel: Scanning for Vocom devices
2025-07-21 23:26:56.473 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-21 23:26:56.473 [Information] VocomService: Using new enhanced device detection service
2025-07-21 23:26:56.474 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-21 23:26:56.474 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-21 23:26:56.927 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-21 23:26:56.927 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-21 23:26:56.928 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-21 23:26:56.928 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-21 23:26:56.929 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-21 23:26:56.929 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-21 23:26:56.929 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-21 23:26:56.930 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-21 23:26:57.187 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-21 23:26:57.188 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-21 23:26:57.188 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-21 23:26:57.189 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-21 23:26:57.190 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-21 23:26:57.192 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-21 23:26:57.192 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-21 23:26:57.192 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-21 23:26:57.194 [Debug] VocomService: Bluetooth is enabled
2025-07-21 23:26:57.195 [Debug] VocomService: Checking if WiFi is available
2025-07-21 23:26:57.195 [Debug] VocomService: WiFi is available
2025-07-21 23:26:57.196 [Information] VocomService: Found 3 Vocom devices
2025-07-21 23:26:57.198 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-21 23:27:18.092 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-07-21 23:27:18.093 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-21 23:27:18.094 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-21 23:27:18.095 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-21 23:27:18.504 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-21 23:27:18.506 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-21 23:27:18.506 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-21 23:27:18.509 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-21 23:27:18.509 [Information] ECUCommunicationService: No ECUs are connected
2025-07-21 23:27:18.510 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-21 23:27:18.510 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-21 23:27:18.510 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-21 23:27:18.510 [Information] ECUCommunicationService: No ECUs are connected
2025-07-21 23:27:18.511 [Information] VocomService: Checking if PTT application is running
2025-07-21 23:27:18.521 [Information] VocomService: PTT application is not running
2025-07-21 23:27:18.522 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-21 23:27:18.522 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-21 23:27:18.522 [Information] VocomService: Checking if PTT application is running
2025-07-21 23:27:18.533 [Information] VocomService: PTT application is not running
2025-07-21 23:27:18.533 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-21 23:27:18.533 [Information] VocomService: Native USB connection attempt 1/3 to WUDFPuma Driver
2025-07-21 23:27:18.533 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-21 23:27:18.534 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-21 23:27:18.534 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-21 23:27:18.534 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-21 23:27:20.543 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-21 23:27:21.554 [Information] VocomService: Native USB connection attempt 2/3 to WUDFPuma Driver
2025-07-21 23:27:21.554 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-21 23:27:21.555 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-21 23:27:21.555 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-21 23:27:21.555 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-21 23:27:23.562 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-21 23:27:24.576 [Information] VocomService: Native USB connection attempt 3/3 to WUDFPuma Driver
2025-07-21 23:27:24.576 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-21 23:27:24.576 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-21 23:27:24.576 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-21 23:27:24.577 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-21 23:27:26.582 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-21 23:27:26.582 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-21 23:27:26.583 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-21 23:27:26.583 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-21 23:27:26.583 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-21 23:27:26.583 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-21 23:27:26.584 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-21 23:27:26.584 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-21 23:27:26.584 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-21 23:27:26.584 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-21 23:27:26.585 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-21 23:27:26.585 [Information] ModernUSBCommunicationService: Found 1 HID devices total
2025-07-21 23:27:26.585 [Debug] ModernUSBCommunicationService: Checking HID device: VID=10C4, PID=8108, Name=
2025-07-21 23:27:26.585 [Information] ModernUSBCommunicationService: Trying alternative device enumeration methods
2025-07-21 23:27:26.585 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-21 23:27:26.586 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-21 23:27:26.586 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-21 23:27:26.586 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-21 23:27:26.586 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-21 23:27:26.587 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-21 23:27:26.587 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-21 23:27:26.587 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-21 23:27:26.587 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-21 23:27:26.588 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-21 23:27:26.588 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-21 23:27:26.588 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-21 23:27:26.588 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-21 23:27:26.588 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-21 23:27:26.589 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-21 23:27:26.589 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
2025-07-21 23:27:44.629 [Information] MainViewModel: Scanning for Vocom devices
2025-07-21 23:27:44.633 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-21 23:27:44.633 [Information] VocomService: Using new enhanced device detection service
2025-07-21 23:27:44.633 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-21 23:27:44.634 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-21 23:27:44.828 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-21 23:27:44.828 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-21 23:27:44.829 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-21 23:27:44.829 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-21 23:27:44.829 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-21 23:27:44.829 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-21 23:27:44.830 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-21 23:27:44.830 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-21 23:27:45.025 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-21 23:27:45.025 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-21 23:27:45.025 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-21 23:27:45.026 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-21 23:27:45.026 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-21 23:27:45.027 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-21 23:27:45.027 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-21 23:27:45.027 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-21 23:27:45.027 [Debug] VocomService: Bluetooth is enabled
2025-07-21 23:27:45.028 [Debug] VocomService: Checking if WiFi is available
2025-07-21 23:27:45.028 [Debug] VocomService: WiFi is available
2025-07-21 23:27:45.029 [Information] VocomService: Found 3 Vocom devices
2025-07-21 23:27:45.030 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-21 23:27:54.351 [Information] MainViewModel: Connecting to Vocom device 88890300-BT
2025-07-21 23:27:54.351 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-21 23:27:54.352 [Information] VocomService: Checking if PTT application is running
2025-07-21 23:27:54.364 [Information] VocomService: PTT application is not running
2025-07-21 23:27:54.365 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-21 23:27:54.365 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-21 23:27:54.366 [Debug] VocomService: Bluetooth is enabled
2025-07-21 23:27:54.366 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-21 23:27:55.172 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-21 23:27:55.172 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-21 23:27:55.173 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-21 23:27:55.173 [Information] MainViewModel: Vocom device 88890300-BT connected
2025-07-21 23:27:55.174 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-21 23:27:55.174 [Information] MainViewModel: Connected to Vocom device 88890300-BT
2025-07-21 23:27:55.177 [Information] MainViewModel: Scanning for ECUs
2025-07-21 23:27:55.184 [Information] ECUCommunicationService: Scanning for ECUs
2025-07-21 23:27:55.185 [Information] ECUCommunicationService: Scanning for ECUs on the CAN bus...
2025-07-21 23:27:55.703 [Information] ECUCommunicationService: Scanning for ECUs using SPI protocol...
2025-07-21 23:27:56.016 [Information] ECUCommunicationService: Scanning for ECUs using SCI protocol...
2025-07-21 23:27:56.326 [Information] ECUCommunicationService: Scanning for ECUs using IIC protocol...
2025-07-21 23:27:56.641 [Information] ECUCommunicationService: Found 10 ECUs
2025-07-21 23:27:56.643 [Information] MainViewModel: Found 10 ECU(s)
2025-07-21 23:28:26.078 [Debug] ModernUSBCommunicationService: Device list changed, refreshing
2025-07-21 23:28:26.079 [Debug] ModernUSBCommunicationService: Device list changed, refreshing
2025-07-21 23:28:26.079 [Debug] ModernUSBCommunicationService: Device list changed, refreshing
2025-07-21 23:39:09.724 [Debug] ModernUSBCommunicationService: Device list changed, refreshing
2025-07-21 23:39:09.725 [Debug] ModernUSBCommunicationService: Device list changed, refreshing
2025-07-21 23:39:09.725 [Debug] ModernUSBCommunicationService: Device list changed, refreshing
2025-07-21 23:46:24.378 [Debug] ModernUSBCommunicationService: Device list changed, refreshing
2025-07-21 23:46:24.379 [Debug] ModernUSBCommunicationService: Device list changed, refreshing
2025-07-21 23:46:24.379 [Debug] ModernUSBCommunicationService: Device list changed, refreshing
2025-07-21 23:46:24.810 [Debug] ModernUSBCommunicationService: Device list changed, refreshing
2025-07-21 23:46:24.810 [Debug] ModernUSBCommunicationService: Device list changed, refreshing
2025-07-21 23:46:24.811 [Debug] ModernUSBCommunicationService: Device list changed, refreshing
