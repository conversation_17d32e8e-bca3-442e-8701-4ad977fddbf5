using System;
using System.Diagnostics;
using System.IO;
using System.IO.Pipes;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Vocom.ArchitectureBridge
{
    /// <summary>
    /// Simple data transfer object for bridge communication
    /// This matches the BridgeVocomDevice in the bridge process
    /// </summary>
    public class BridgeVocomDevice
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string ConnectionType { get; set; } = string.Empty;
        public string ConnectionStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// Architecture bridge that handles communication between x64 main process and x86 APCI libraries
    /// This solves the Error 193 architecture mismatch issue by using process isolation
    /// </summary>
    public class VocomArchitectureBridge : IDisposable
    {
        private readonly ILoggingService _logger;
        private Process? _bridgeProcess;
        private NamedPipeServerStream? _pipeServer;
        private NamedPipeClientStream? _pipeClient;
        private string _pipeName;
        private bool _isInitialized;
        private bool _disposed;

        /// <summary>
        /// Gets whether the bridge is available and ready for use
        /// </summary>
        public bool IsAvailable => _isInitialized && _bridgeProcess != null && !_bridgeProcess.HasExited;

        public VocomArchitectureBridge(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            // Use process ID, timestamp, and random GUID to ensure unique pipe name
            _pipeName = $"VocomBridge_{Environment.ProcessId}_{DateTime.Now.Ticks}_{Guid.NewGuid():N}";
        }

        /// <summary>
        /// Checks if the bridge process is still alive and healthy
        /// </summary>
        public bool IsBridgeProcessHealthy()
        {
            try
            {
                if (_bridgeProcess == null)
                {
                    return false;
                }

                // Check if process has exited
                if (_bridgeProcess.HasExited)
                {
                    _logger.LogWarning($"Bridge process has exited with code: {_bridgeProcess.ExitCode}", "VocomArchitectureBridge");
                    return false;
                }

                // Check if pipe is still connected
                if (_pipeServer == null || !_pipeServer.IsConnected)
                {
                    _logger.LogWarning("Bridge pipe is not connected", "VocomArchitectureBridge");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Exception checking bridge process health: {ex.Message}", "VocomArchitectureBridge");
                return false;
            }
        }

        /// <summary>
        /// Attempts to restart the bridge process if it has failed
        /// </summary>
        public async Task<bool> RestartBridgeProcessAsync()
        {
            _logger.LogInformation("Attempting to restart bridge process", "VocomArchitectureBridge");

            try
            {
                // Clean up existing resources
                CleanupPipeServer();
                CleanupBridgeProcess();

                // Reset initialization state
                _isInitialized = false;

                // Reinitialize the bridge
                return await InitializeAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to restart bridge process: {ex.Message}", "VocomArchitectureBridge");
                return false;
            }
        }

        /// <summary>
        /// Initializes the architecture bridge by starting the x86 bridge process
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            const int maxRetries = 3;
            const int baseDelayMs = 1000;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    _logger.LogInformation($"Initializing Vocom Architecture Bridge (attempt {attempt}/{maxRetries})", "VocomArchitectureBridge");

                    // Check if already initialized
                    if (_isInitialized)
                    {
                        _logger.LogInformation("Bridge already initialized", "VocomArchitectureBridge");
                        return true;
                    }

                    var success = await InitializeBridgeInternalAsync();
                    if (success)
                    {
                        return true;
                    }

                    if (attempt < maxRetries)
                    {
                        var delay = baseDelayMs * attempt; // Linear backoff
                        _logger.LogInformation($"Bridge initialization failed, retrying in {delay}ms", "VocomArchitectureBridge");
                        await Task.Delay(delay);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Exception during bridge initialization attempt {attempt}: {ex.Message}", "VocomArchitectureBridge");

                    if (attempt < maxRetries)
                    {
                        var delay = baseDelayMs * attempt;
                        _logger.LogInformation($"Retrying bridge initialization in {delay}ms", "VocomArchitectureBridge");
                        await Task.Delay(delay);
                    }
                }
            }

            _logger.LogError($"Failed to initialize bridge after {maxRetries} attempts", "VocomArchitectureBridge");
            return false;
        }

        /// <summary>
        /// Internal method to perform the actual bridge initialization
        /// </summary>
        private async Task<bool> InitializeBridgeInternalAsync()
        {
            try
            {

                // Clean up any existing bridge processes first
                await CleanupExistingBridgeProcessesAsync();

                // Create named pipe server for communication with timeout
                // Allow multiple instances to handle concurrent access
                try
                {
                    _pipeServer = new NamedPipeServerStream(_pipeName, PipeDirection.InOut,
                        NamedPipeServerStream.MaxAllowedServerInstances, PipeTransmissionMode.Message);
                    _logger.LogInformation($"Created named pipe server: {_pipeName}", "VocomArchitectureBridge");
                }
                catch (Exception pipeEx)
                {
                    _logger.LogError($"Failed to create named pipe server: {pipeEx.Message}", "VocomArchitectureBridge");

                    // Try with a different pipe name if the current one is busy
                    if (pipeEx.Message.Contains("All pipe instances are busy") ||
                        pipeEx.Message.Contains("pipe is being closed"))
                    {
                        _logger.LogInformation("Retrying with new pipe name due to busy instances", "VocomArchitectureBridge");
                        _pipeName = $"VocomBridge_{Environment.ProcessId}_{DateTime.Now.Ticks}_{Guid.NewGuid():N}";

                        try
                        {
                            _pipeServer = new NamedPipeServerStream(_pipeName, PipeDirection.InOut,
                                NamedPipeServerStream.MaxAllowedServerInstances, PipeTransmissionMode.Message);
                            _logger.LogInformation($"Created named pipe server with new name: {_pipeName}", "VocomArchitectureBridge");
                        }
                        catch (Exception retryEx)
                        {
                            _logger.LogError($"Failed to create named pipe server on retry: {retryEx.Message}", "VocomArchitectureBridge");
                            return false;
                        }
                    }
                    else
                    {
                        return false;
                    }
                }

                // Start the x86 bridge process
                if (!await StartBridgeProcessAsync())
                {
                    _logger.LogError("Failed to start bridge process", "VocomArchitectureBridge");
                    CleanupPipeServer();
                    return false;
                }

                // Wait for bridge process to connect with timeout
                _logger.LogInformation("Waiting for bridge process to connect...", "VocomArchitectureBridge");
                try
                {
                    var connectTask = _pipeServer.WaitForConnectionAsync();
                    var timeoutTask = Task.Delay(10000); // 10 second timeout

                    var completedTask = await Task.WhenAny(connectTask, timeoutTask);
                    if (completedTask == timeoutTask)
                    {
                        _logger.LogError("Timeout waiting for bridge process to connect", "VocomArchitectureBridge");
                        CleanupBridgeProcess();
                        CleanupPipeServer();
                        return false;
                    }

                    _logger.LogInformation("Bridge process connected successfully", "VocomArchitectureBridge");
                }
                catch (Exception connectEx)
                {
                    _logger.LogError($"Exception waiting for bridge connection: {connectEx.Message}", "VocomArchitectureBridge");
                    CleanupBridgeProcess();
                    CleanupPipeServer();
                    return false;
                }

                // Send initialization command
                var initCommand = new BridgeCommand
                {
                    Type = "Initialize",
                    Data = JsonSerializer.Serialize(new { LibrariesPath = GetLibrariesPath() })
                };

                var response = await SendCommandAsync(initCommand);
                if (response?.Success == true)
                {
                    _isInitialized = true;
                    _logger.LogInformation("Architecture bridge initialized successfully", "VocomArchitectureBridge");
                    return true;
                }

                _logger.LogError($"Bridge initialization failed: {response?.Message}", "VocomArchitectureBridge");
                CleanupBridgeProcess();
                CleanupPipeServer();
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during bridge initialization: {ex.Message}", "VocomArchitectureBridge");
                CleanupBridgeProcess();
                CleanupPipeServer();
                return false;
            }
        }

        /// <summary>
        /// Detects Vocom devices through the architecture bridge
        /// </summary>
        public async Task<VocomDevice[]> DetectDevicesAsync()
        {
            _logger.LogInformation("=== DetectDevicesAsync called ===", "VocomArchitectureBridge");

            if (!_isInitialized)
            {
                _logger.LogWarning("Bridge not initialized, cannot detect devices", "VocomArchitectureBridge");
                return Array.Empty<VocomDevice>();
            }

            _logger.LogInformation("Bridge is initialized, proceeding with device detection", "VocomArchitectureBridge");

            try
            {
                _logger.LogInformation("=== BRIDGE MAIN DEBUG: Creating DetectDevices command ===", "VocomArchitectureBridge");
                var command = new BridgeCommand { Type = "DetectDevices" };
                _logger.LogInformation("=== BRIDGE MAIN DEBUG: About to call SendCommandAsync ===", "VocomArchitectureBridge");

                // CRITICAL FIX: Add comprehensive error handling around SendCommandAsync
                BridgeResponse? response = null;
                try
                {
                    response = await SendCommandAsync(command);
                    _logger.LogInformation("=== BRIDGE MAIN DEBUG: SendCommandAsync returned successfully ===", "VocomArchitectureBridge");
                }
                catch (Exception sendEx)
                {
                    _logger.LogError($"=== BRIDGE MAIN DEBUG: SendCommandAsync threw exception: {sendEx.Message} ===", "VocomArchitectureBridge");
                    _logger.LogError($"=== BRIDGE MAIN DEBUG: Exception type: {sendEx.GetType().FullName} ===", "VocomArchitectureBridge");
                    _logger.LogError($"=== BRIDGE MAIN DEBUG: Stack trace: {sendEx.StackTrace} ===", "VocomArchitectureBridge");
                    return Array.Empty<VocomDevice>();
                }

                if (response?.Success == true && !string.IsNullOrEmpty(response.Data))
                {
                    _logger.LogInformation($"Bridge response data: {response.Data}", "VocomArchitectureBridge");

                    try
                    {
                        // Try to deserialize as raw JSON first to see the structure
                        _logger.LogInformation($"Attempting to parse JSON response: {response.Data}", "VocomArchitectureBridge");

                        // First, let's try a simple approach - deserialize as BridgeVocomDevice[] directly
                        _logger.LogInformation("=== BRIDGE MAIN DEBUG: Attempting direct deserialization as BridgeVocomDevice[] ===", "VocomArchitectureBridge");

                        try
                        {
                            // Configure JSON options to handle string to enum conversion
                            var jsonOptions = new JsonSerializerOptions
                            {
                                PropertyNameCaseInsensitive = true,
                                Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
                            };

                            var bridgeDevices = JsonSerializer.Deserialize<BridgeVocomDevice[]>(response.Data, jsonOptions);
                            if (bridgeDevices != null)
                            {
                                _logger.LogInformation($"=== BRIDGE MAIN DEBUG: Successfully deserialized {bridgeDevices.Length} BridgeVocomDevice objects ===", "VocomArchitectureBridge");

                                foreach (var device in bridgeDevices)
                                {
                                    _logger.LogInformation($"=== BRIDGE MAIN DEBUG: Device: Id={device.Id}, Name={device.Name}, ConnectionType={device.ConnectionType}, ConnectionStatus={device.ConnectionStatus} ===", "VocomArchitectureBridge");
                                }

                                var devices = ConvertBridgeDevicesToVocomDevices(bridgeDevices);
                                _logger.LogInformation($"Detected {devices.Length} Vocom devices through bridge", "VocomArchitectureBridge");
                                return devices;
                            }
                        }
                        catch (JsonException jsonEx)
                        {
                            _logger.LogError($"=== BRIDGE MAIN DEBUG: Direct deserialization failed: {jsonEx.Message} ===", "VocomArchitectureBridge");
                            _logger.LogError($"=== BRIDGE MAIN DEBUG: JSON data: {response.Data} ===", "VocomArchitectureBridge");
                        }

                        // Fallback to manual parsing if direct deserialization fails
                        _logger.LogInformation("=== BRIDGE MAIN DEBUG: Falling back to manual JSON parsing ===", "VocomArchitectureBridge");

                        // Parse as JsonDocument first to analyze structure
                        using var jsonDocument = JsonDocument.Parse(response.Data);
                        _logger.LogInformation($"JSON root element kind: {jsonDocument.RootElement.ValueKind}", "VocomArchitectureBridge");

                        if (jsonDocument.RootElement.ValueKind == JsonValueKind.Array)
                        {
                            _logger.LogInformation($"JSON array length: {jsonDocument.RootElement.GetArrayLength()}", "VocomArchitectureBridge");

                            // Manual deserialization to avoid type conversion issues
                            var bridgeDevices = new List<BridgeVocomDevice>();

                            foreach (var element in jsonDocument.RootElement.EnumerateArray())
                            {
                                var device = new BridgeVocomDevice();

                                foreach (var property in element.EnumerateObject())
                                {
                                    switch (property.Name)
                                    {
                                        case "Id":
                                            device.Id = property.Value.GetString() ?? string.Empty;
                                            break;
                                        case "Name":
                                            device.Name = property.Value.GetString() ?? string.Empty;
                                            break;
                                        case "ConnectionType":
                                            device.ConnectionType = property.Value.GetString() ?? string.Empty;
                                            break;
                                        case "ConnectionStatus":
                                            device.ConnectionStatus = property.Value.GetString() ?? string.Empty;
                                            break;
                                    }
                                }

                                bridgeDevices.Add(device);
                                _logger.LogInformation($"Manually parsed device: Id={device.Id}, Name={device.Name}, ConnectionType={device.ConnectionType}, ConnectionStatus={device.ConnectionStatus}", "VocomArchitectureBridge");
                            }

                            _logger.LogInformation($"Successfully parsed {bridgeDevices.Count} bridge devices manually", "VocomArchitectureBridge");
                            var devices = ConvertBridgeDevicesToVocomDevices(bridgeDevices.ToArray());
                            _logger.LogInformation($"Detected {devices.Length} Vocom devices through bridge", "VocomArchitectureBridge");
                            return devices;
                        }
                        else
                        {
                            _logger.LogWarning($"Unexpected JSON structure: {jsonDocument.RootElement.ValueKind}", "VocomArchitectureBridge");
                            return Array.Empty<VocomDevice>();
                        }
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError($"JSON deserialization error: {ex.Message}", "VocomArchitectureBridge");
                        _logger.LogError($"Response data: {response.Data}", "VocomArchitectureBridge");
                        _logger.LogError($"Exception type: {ex.GetType().FullName}", "VocomArchitectureBridge");
                        _logger.LogError($"Stack trace: {ex.StackTrace}", "VocomArchitectureBridge");

                        // Return empty array instead of throwing to prevent application crash
                        _logger.LogWarning("Returning empty device array due to JSON deserialization error", "VocomArchitectureBridge");
                        return Array.Empty<VocomDevice>();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Unexpected error during JSON processing: {ex.Message}", "VocomArchitectureBridge");
                        _logger.LogError($"Exception type: {ex.GetType().FullName}", "VocomArchitectureBridge");
                        return Array.Empty<VocomDevice>();
                    }
                }

                _logger.LogWarning($"Device detection failed: {response?.Message}", "VocomArchitectureBridge");
                return Array.Empty<VocomDevice>();
            }
            catch (Exception ex)
            {
                _logger.LogError($"=== BRIDGE MAIN DEBUG v2: Exception during device detection: {ex.Message} ===", "VocomArchitectureBridge");
                _logger.LogError($"=== BRIDGE MAIN DEBUG: Exception type: {ex.GetType().FullName} ===", "VocomArchitectureBridge");
                _logger.LogError($"=== BRIDGE MAIN DEBUG: Stack trace: {ex.StackTrace} ===", "VocomArchitectureBridge");
                if (ex.InnerException != null)
                {
                    _logger.LogError($"=== BRIDGE MAIN DEBUG: Inner exception: {ex.InnerException.Message} ===", "VocomArchitectureBridge");
                    _logger.LogError($"=== BRIDGE MAIN DEBUG: Inner exception type: {ex.InnerException.GetType().FullName} ===", "VocomArchitectureBridge");
                }
                return Array.Empty<VocomDevice>();
            }
        }

        /// <summary>
        /// Connects to a Vocom device through the architecture bridge
        /// </summary>
        public async Task<bool> ConnectToDeviceAsync(string deviceId)
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("Bridge not initialized, cannot connect to device", "VocomArchitectureBridge");
                return false;
            }

            try
            {
                var command = new BridgeCommand
                {
                    Type = "ConnectDevice",
                    Data = JsonSerializer.Serialize(new { DeviceId = deviceId })
                };

                var response = await SendCommandAsync(command);
                if (response?.Success == true)
                {
                    _logger.LogInformation($"Successfully connected to device {deviceId} through bridge", "VocomArchitectureBridge");
                    return true;
                }

                _logger.LogError($"Failed to connect to device {deviceId}: {response?.Message}", "VocomArchitectureBridge");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during device connection: {ex.Message}", "VocomArchitectureBridge");
                return false;
            }
        }

        /// <summary>
        /// Disconnects from the current Vocom device through the architecture bridge
        /// </summary>
        public async Task<bool> DisconnectFromDeviceAsync()
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("Bridge not initialized, cannot disconnect from device", "VocomArchitectureBridge");
                return false;
            }

            try
            {
                var command = new BridgeCommand
                {
                    Type = "DisconnectDevice",
                    Data = null // No specific device ID needed, disconnect current device
                };

                var response = await SendCommandAsync(command);
                if (response?.Success == true)
                {
                    _logger.LogInformation("Successfully disconnected from device through bridge", "VocomArchitectureBridge");
                    return true;
                }

                _logger.LogError($"Failed to disconnect from device: {response?.Message}", "VocomArchitectureBridge");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during device disconnection: {ex.Message}", "VocomArchitectureBridge");
                return false;
            }
        }

        /// <summary>
        /// Sends a command to the bridge process and waits for response
        /// </summary>
        private async Task<BridgeResponse?> SendCommandAsync(BridgeCommand command)
        {
            _logger.LogInformation($"SendCommandAsync called with command type: {command?.Type}", "VocomArchitectureBridge");

            if (_pipeServer == null || !_pipeServer.IsConnected)
            {
                _logger.LogError($"Pipe server not connected - _pipeServer is null: {_pipeServer == null}, IsConnected: {_pipeServer?.IsConnected}", "VocomArchitectureBridge");
                return null;
            }

            _logger.LogInformation("Pipe server is connected, proceeding with command", "VocomArchitectureBridge");

            try
            {
                // Serialize and send command
                var commandJson = JsonSerializer.Serialize(command);
                var commandBytes = Encoding.UTF8.GetBytes(commandJson);
                _logger.LogInformation($"=== BRIDGE MAIN DEBUG: Sending command to bridge: {commandJson} ===", "VocomArchitectureBridge");
                _logger.LogInformation($"=== BRIDGE MAIN DEBUG: Command bytes length: {commandBytes.Length} ===", "VocomArchitectureBridge");

                _logger.LogInformation("=== BRIDGE MAIN DEBUG: About to write command to pipe ===", "VocomArchitectureBridge");
                await _pipeServer.WriteAsync(commandBytes, 0, commandBytes.Length);
                _logger.LogInformation("=== BRIDGE MAIN DEBUG: Command written to pipe ===", "VocomArchitectureBridge");
                await _pipeServer.FlushAsync();
                _logger.LogInformation("=== BRIDGE MAIN DEBUG: Pipe flushed ===", "VocomArchitectureBridge");

                // Read response
                _logger.LogInformation("=== BRIDGE MAIN DEBUG: About to read response from pipe ===", "VocomArchitectureBridge");
                var buffer = new byte[4096];
                var bytesRead = await _pipeServer.ReadAsync(buffer, 0, buffer.Length);
                _logger.LogInformation($"=== BRIDGE MAIN DEBUG: Read {bytesRead} bytes from pipe ===", "VocomArchitectureBridge");
                var responseJson = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                _logger.LogInformation($"=== BRIDGE MAIN DEBUG: Received response from bridge: {responseJson} ===", "VocomArchitectureBridge");

                // CRITICAL FIX: Use safe JSON deserialization to prevent enum conversion issues
                _logger.LogInformation("=== BRIDGE MAIN DEBUG: Attempting safe BridgeResponse deserialization ===", "VocomArchitectureBridge");

                try
                {
                    // First, validate that the JSON is well-formed
                    using var jsonDocument = JsonDocument.Parse(responseJson);
                    _logger.LogInformation($"=== BRIDGE MAIN DEBUG: JSON is well-formed, root element: {jsonDocument.RootElement.ValueKind} ===", "VocomArchitectureBridge");

                    // Manually parse the BridgeResponse to avoid any automatic type inference
                    var bridgeResponse = new BridgeResponse();

                    foreach (var property in jsonDocument.RootElement.EnumerateObject())
                    {
                        switch (property.Name)
                        {
                            case "Success":
                                bridgeResponse.Success = property.Value.GetBoolean();
                                break;
                            case "Message":
                                bridgeResponse.Message = property.Value.ValueKind == JsonValueKind.Null ? null : property.Value.GetString();
                                break;
                            case "Data":
                                bridgeResponse.Data = property.Value.ValueKind == JsonValueKind.Null ? null : property.Value.GetString();
                                break;
                        }
                    }

                    _logger.LogInformation($"=== BRIDGE MAIN DEBUG: Successfully parsed BridgeResponse manually: Success={bridgeResponse.Success}, Message={bridgeResponse.Message}, Data length={bridgeResponse.Data?.Length} ===", "VocomArchitectureBridge");
                    return bridgeResponse;
                }
                catch (JsonException jsonEx)
                {
                    _logger.LogError($"=== BRIDGE MAIN DEBUG: JSON parsing failed: {jsonEx.Message} ===", "VocomArchitectureBridge");
                    _logger.LogError($"=== BRIDGE MAIN DEBUG: Problematic JSON: {responseJson} ===", "VocomArchitectureBridge");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during command communication: {ex.Message}", "VocomArchitectureBridge");
                _logger.LogError($"Exception type: {ex.GetType().FullName}", "VocomArchitectureBridge");
                _logger.LogError($"Stack trace: {ex.StackTrace}", "VocomArchitectureBridge");
                return null;
            }
        }

        /// <summary>
        /// Starts the x86 bridge process
        /// </summary>
        private async Task<bool> StartBridgeProcessAsync()
        {
            try
            {
                var bridgeExePath = GetBridgeExecutablePath();
                if (!File.Exists(bridgeExePath))
                {
                    _logger.LogError($"Bridge executable not found at {bridgeExePath}", "VocomArchitectureBridge");
                    return false;
                }

                var startInfo = new ProcessStartInfo
                {
                    FileName = bridgeExePath,
                    Arguments = _pipeName,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                _bridgeProcess = Process.Start(startInfo);
                if (_bridgeProcess == null)
                {
                    _logger.LogError("Failed to start bridge process", "VocomArchitectureBridge");
                    return false;
                }

                _logger.LogInformation($"Started bridge process with PID {_bridgeProcess.Id}", "VocomArchitectureBridge");

                // Give the process a moment to start
                await Task.Delay(1000);

                // Check if process exited and capture error output
                if (_bridgeProcess.HasExited)
                {
                    var exitCode = _bridgeProcess.ExitCode;
                    var errorOutput = await _bridgeProcess.StandardError.ReadToEndAsync();
                    var standardOutput = await _bridgeProcess.StandardOutput.ReadToEndAsync();

                    _logger.LogError($"Bridge process exited with code {exitCode}", "VocomArchitectureBridge");
                    if (!string.IsNullOrEmpty(errorOutput))
                    {
                        _logger.LogError($"Bridge process stderr: {errorOutput}", "VocomArchitectureBridge");
                    }
                    if (!string.IsNullOrEmpty(standardOutput))
                    {
                        _logger.LogInformation($"Bridge process stdout: {standardOutput}", "VocomArchitectureBridge");
                    }
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception starting bridge process: {ex.Message}", "VocomArchitectureBridge");
                return false;
            }
        }

        /// <summary>
        /// Cleans up any existing bridge processes that might be running
        /// </summary>
        private async Task CleanupExistingBridgeProcessesAsync()
        {
            try
            {
                _logger.LogInformation("Cleaning up existing bridge processes", "VocomArchitectureBridge");

                // First, try to gracefully terminate any existing bridge processes
                var bridgeProcesses = Process.GetProcessesByName("VolvoFlashWR.VocomBridge");
                if (bridgeProcesses.Length > 0)
                {
                    _logger.LogInformation($"Found {bridgeProcesses.Length} existing bridge processes", "VocomArchitectureBridge");

                    foreach (var process in bridgeProcesses)
                    {
                        try
                        {
                            if (!process.HasExited)
                            {
                                _logger.LogInformation($"Terminating existing bridge process PID {process.Id}", "VocomArchitectureBridge");

                                // Try graceful close first
                                try
                                {
                                    process.CloseMainWindow();
                                    if (!process.WaitForExit(2000)) // Wait 2 seconds for graceful exit
                                    {
                                        _logger.LogInformation($"Forcefully killing bridge process PID {process.Id}", "VocomArchitectureBridge");
                                        process.Kill();
                                    }
                                }
                                catch
                                {
                                    // If graceful close fails, force kill
                                    if (!process.HasExited)
                                    {
                                        process.Kill();
                                    }
                                }

                                // Wait for process to fully exit
                                if (!process.HasExited)
                                {
                                    process.WaitForExit(3000); // Wait up to 3 seconds
                                }
                            }
                            process.Dispose();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"Failed to cleanup bridge process PID {process.Id}: {ex.Message}", "VocomArchitectureBridge");
                        }
                    }

                    // Give additional time for system cleanup
                    await Task.Delay(1000);
                }
                else
                {
                    _logger.LogInformation("No existing bridge processes found", "VocomArchitectureBridge");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Exception during bridge process cleanup: {ex.Message}", "VocomArchitectureBridge");
            }
        }

        /// <summary>
        /// Cleans up the pipe server
        /// </summary>
        private void CleanupPipeServer()
        {
            try
            {
                if (_pipeServer != null)
                {
                    if (_pipeServer.IsConnected)
                    {
                        _pipeServer.Disconnect();
                    }
                    _pipeServer.Dispose();
                    _pipeServer = null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Exception during pipe server cleanup: {ex.Message}", "VocomArchitectureBridge");
            }
        }

        /// <summary>
        /// Cleans up the bridge process
        /// </summary>
        private void CleanupBridgeProcess()
        {
            try
            {
                if (_bridgeProcess != null && !_bridgeProcess.HasExited)
                {
                    _logger.LogInformation($"Terminating bridge process PID {_bridgeProcess.Id}", "VocomArchitectureBridge");
                    _bridgeProcess.Kill();
                    _bridgeProcess.Dispose();
                    _bridgeProcess = null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Exception during bridge process cleanup: {ex.Message}", "VocomArchitectureBridge");
            }
        }



        /// <summary>
        /// Gets the path to the bridge executable
        /// </summary>
        private string GetBridgeExecutablePath()
        {
            var appPath = AppDomain.CurrentDomain.BaseDirectory;
            var bridgePath = Path.Combine(appPath, "Bridge", "VolvoFlashWR.VocomBridge.exe");

            _logger.LogInformation($"Looking for bridge executable at: {bridgePath}", "VocomArchitectureBridge");

            // Also check alternative locations
            if (!File.Exists(bridgePath))
            {
                var altPath = Path.Combine(appPath, "VolvoFlashWR.VocomBridge.exe");
                _logger.LogInformation($"Bridge not found at primary location, checking: {altPath}", "VocomArchitectureBridge");
                if (File.Exists(altPath))
                {
                    return altPath;
                }
            }

            return bridgePath;
        }

        /// <summary>
        /// Converts BridgeVocomDevice array to VocomDevice array
        /// </summary>
        private VocomDevice[] ConvertBridgeDevicesToVocomDevices(BridgeVocomDevice[] bridgeDevices)
        {
            var devices = new VocomDevice[bridgeDevices.Length];
            for (int i = 0; i < bridgeDevices.Length; i++)
            {
                var bridgeDevice = bridgeDevices[i];
                devices[i] = new VocomDevice
                {
                    Id = bridgeDevice.Id,
                    Name = bridgeDevice.Name,
                    SerialNumber = bridgeDevice.Id, // Use Id as SerialNumber for display
                    ConnectionType = ParseConnectionType(bridgeDevice.ConnectionType),
                    ConnectionStatus = ParseConnectionStatus(bridgeDevice.ConnectionStatus),
                    FirmwareVersion = "Unknown",
                    LastConnectionTime = DateTime.MinValue,
                    IsInUseByOtherApplication = false,
                    UsingApplicationName = string.Empty,
                    BluetoothAddress = string.Empty,
                    WiFiIPAddress = string.Empty,
                    IPAddress = string.Empty,
                    USBPortInfo = string.Empty
                };
            }
            return devices;
        }

        /// <summary>
        /// Parses connection type string to enum
        /// </summary>
        private VocomConnectionType ParseConnectionType(string connectionType)
        {
            return connectionType.ToUpperInvariant() switch
            {
                "USB" => VocomConnectionType.USB,
                "BLUETOOTH" => VocomConnectionType.Bluetooth,
                "WIFI" => VocomConnectionType.WiFi,
                _ => VocomConnectionType.USB
            };
        }

        /// <summary>
        /// Parses connection status string to enum
        /// </summary>
        private VocomConnectionStatus ParseConnectionStatus(string connectionStatus)
        {
            return connectionStatus.ToUpperInvariant() switch
            {
                "CONNECTED" => VocomConnectionStatus.Connected,
                "DISCONNECTED" => VocomConnectionStatus.Disconnected,
                "CONNECTING" => VocomConnectionStatus.Connecting,
                "ERROR" => VocomConnectionStatus.Error,
                _ => VocomConnectionStatus.Disconnected
            };
        }

        /// <summary>
        /// Gets the libraries path for the bridge process
        /// </summary>
        private string GetLibrariesPath()
        {
            var appPath = AppDomain.CurrentDomain.BaseDirectory;
            return Path.Combine(appPath, "Libraries");
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                _logger.LogInformation("Disposing Vocom Architecture Bridge", "VocomArchitectureBridge");

                CleanupPipeServer();
                CleanupBridgeProcess();

                _pipeClient?.Dispose();
                _pipeClient = null;

                _isInitialized = false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during disposal: {ex.Message}", "VocomArchitectureBridge");
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// Command sent to the bridge process
    /// </summary>
    public class BridgeCommand
    {
        public string Type { get; set; } = string.Empty;
        public string? Data { get; set; }
    }

    /// <summary>
    /// Response from the bridge process
    /// </summary>
    public class BridgeResponse
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public string? Data { get; set; }
    }
}
