using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using System.Management;
using System.Linq;
using Microsoft.Win32;

namespace VolvoFlashWR.VocomBridge
{
    /// <summary>
    /// Simple data transfer object for Vocom device information
    /// This avoids dependency on VolvoFlashWR.Core which has architecture issues
    /// </summary>
    public class BridgeVocomDevice
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string ConnectionType { get; set; } = string.Empty;
        public string ConnectionStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// Service that handles actual Vocom communication using x86 APCI libraries
    /// This runs in the x86 bridge process to avoid architecture mismatch issues
    /// </summary>
    public class VocomBridgeService : IDisposable
    {
        private readonly ILogger _logger;
        private bool _isInitialized;
        private bool _isConnected;
        private string? _connectedDeviceId;
        private IntPtr _apciHandle = IntPtr.Zero;
        private bool _disposed;

        // Vocom device identification constants based on requirements
        private static readonly string[] VocomDeviceNames = {
            "Vocom",
            "88890300",
            "Vocom - 88890300",
            "Volvo Communication Unit",
            "Volvo Adapter",
            "VOCOM1",
            "VOCOM 1",
            "88890020 Adapter",
            "Vocom 1 Adapter"
        };

        private static readonly string[] VocomHardwareIds = {
            "USB\\VID_178E&PID_0024",  // Real Vocom adapter (primary)
            "USB\\VID_1A12&PID_0001",  // CSR-based Vocom adapters
            "USB\\VID_0BDA&PID_2838",  // Alternative hardware ID from config
            "USB\\VID_0403&PID_6001",  // FTDI-based Vocom adapters
            "VID_178E&PID_0024",       // Partial match for real Vocom
            "VID_1A12&PID_0001",       // Partial match for CSR
            "VID_0BDA&PID_2838",       // Partial match
            "VID_0403&PID_6001"        // Partial match for FTDI
        };

        // APCI library function imports with proper error handling
        // Using LoadLibrary/GetProcAddress for dynamic loading to handle missing libraries gracefully

        // Dynamic function delegates
        private delegate int APCI_Initialize_Delegate();
        private delegate int APCI_Terminate_Delegate();
        private delegate int APCI_ScanDevices_Delegate(IntPtr deviceList, ref int deviceCount);
        private delegate int APCI_Connect_Delegate(string deviceId, ref IntPtr handle);
        private delegate int APCI_Disconnect_Delegate(IntPtr handle);
        private delegate int APCI_SendData_Delegate(IntPtr handle, byte[] data, int length);
        private delegate int APCI_ReceiveData_Delegate(IntPtr handle, byte[] buffer, int bufferSize, ref int receivedLength);

        // Function pointers
        private APCI_Initialize_Delegate? _apciInitialize;
        private APCI_Terminate_Delegate? _apciTerminate;
        private APCI_ScanDevices_Delegate? _apciScanDevices;
        private APCI_Connect_Delegate? _apciConnect;
        private APCI_Disconnect_Delegate? _apciDisconnect;
        private APCI_SendData_Delegate? _apciSendData;
        private APCI_ReceiveData_Delegate? _apciReceiveData;

        // Library handle
        private IntPtr _apciLibraryHandle = IntPtr.Zero;

        // Windows API imports for dynamic library loading
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        public VocomBridgeService(ILogger logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Initializes the bridge service and APCI libraries
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing Vocom Bridge Service with x86 APCI libraries");

                // Verify that we're running as x86
                if (Environment.Is64BitProcess)
                {
                    _logger.LogError("Bridge service must run as x86 process to be compatible with APCI libraries");
                    return false;
                }

                // Check if APCI libraries are available
                if (!CheckApciLibrariesAvailable())
                {
                    _logger.LogWarning("APCI libraries not found - running in simulation mode");
                    _isInitialized = true;
                    return true;
                }

                // Try to load APCI library dynamically
                if (LoadApciLibrary())
                {
                    // Try to initialize APCI
                    if (_apciInitialize != null)
                    {
                        var result = _apciInitialize();
                        if (result == 0)
                        {
                            _isInitialized = true;
                            _logger.LogInformation("Vocom Bridge Service initialized successfully (real hardware mode)");
                            return true;
                        }
                        else
                        {
                            _logger.LogWarning($"APCI initialization failed with error code: {result} - falling back to simulation mode");
                        }
                    }
                    else
                    {
                        _logger.LogWarning("APCI_Initialize function not found - falling back to simulation mode");
                    }
                }

                // Fall back to simulation mode
                _isInitialized = true;
                _logger.LogInformation("Vocom Bridge Service initialized successfully (simulation mode)");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during bridge service initialization: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Detects available Vocom devices
        /// </summary>
        public async Task<List<BridgeVocomDevice>> DetectDevicesAsync()
        {
            var devices = new List<BridgeVocomDevice>();

            try
            {
                if (!_isInitialized)
                {
                    _logger.LogWarning("Bridge service not initialized, cannot detect devices");
                    return devices;
                }

                _logger.LogInformation("Scanning for Vocom devices using bridge service");

                // Detect USB devices
                var usbDevices = await DetectUSBVocomDevicesAsync();
                devices.AddRange(usbDevices);

                // Detect Serial Port devices
                var serialDevices = await DetectSerialPortVocomDevicesAsync();
                devices.AddRange(serialDevices);

                // Detect devices via Windows Registry
                var registryDevices = await DetectRegistryVocomDevicesAsync();
                devices.AddRange(registryDevices);

                // Remove duplicates based on device ID
                devices = DeduplicateDevices(devices);

                // Try APCI device detection if library is loaded
                if (_apciLibraryHandle != IntPtr.Zero && _apciScanDevices != null)
                {
                    var apciDevices = await DetectAPCIDevicesAsync();
                    devices.AddRange(apciDevices);
                }

                // Try the original working detection methods that were used before bridge
                var originalDevices = await DetectOriginalVocomDevicesAsync();
                devices.AddRange(originalDevices);

                // If no real devices found, add a simulated device for testing
                if (devices.Count == 0)
                {
                    _logger.LogInformation("No real Vocom devices found, adding simulated device for testing");
                    var simulatedDevice = new BridgeVocomDevice
                    {
                        Id = "BRIDGE_VOCOM_001",
                        Name = "Bridge Vocom Device (Simulated)",
                        ConnectionType = "USB",
                        ConnectionStatus = "Disconnected"
                    };
                    devices.Add(simulatedDevice);
                }

                _logger.LogInformation($"Bridge service found {devices.Count} devices total");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during device detection: {ex.Message}");
            }

            return devices;
        }

        /// <summary>
        /// Connects to a specific Vocom device
        /// </summary>
        public async Task<bool> ConnectToDeviceAsync(string deviceId)
        {
            try
            {
                if (!_isInitialized)
                {
                    _logger.LogWarning("Bridge service not initialized, cannot connect to device");
                    return false;
                }

                if (_isConnected)
                {
                    _logger.LogWarning("Already connected to a device, disconnecting first");
                    await DisconnectAsync();
                }

                _logger.LogInformation($"Connecting to Vocom device: {deviceId}");

                // TODO: Implement real APCI connection when libraries are available
                // For now, simulate connection for testing
                _isConnected = true;
                _connectedDeviceId = deviceId;
                _apciHandle = new IntPtr(12345); // Fake handle for simulation
                _logger.LogInformation($"Successfully connected to device: {deviceId} (simulated)");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during device connection: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disconnects from the current device
        /// </summary>
        public async Task DisconnectAsync()
        {
            try
            {
                if (_isConnected && _apciHandle != IntPtr.Zero)
                {
                    _logger.LogInformation($"Disconnecting from device: {_connectedDeviceId}");

                    // TODO: Implement real APCI disconnect when libraries are available
                    _logger.LogInformation("Successfully disconnected from device (simulated)");

                    _apciHandle = IntPtr.Zero;
                    _isConnected = false;
                    _connectedDeviceId = null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during disconnect: {ex.Message}");
            }
        }

        /// <summary>
        /// Sends data and receives response
        /// </summary>
        public async Task<byte[]> SendAndReceiveDataAsync(byte[] data)
        {
            try
            {
                if (!_isConnected || _apciHandle == IntPtr.Zero)
                {
                    _logger.LogWarning("Not connected to any device, cannot send data");
                    return new byte[0];
                }

                _logger.LogDebug($"Sending {data.Length} bytes to device (simulated)");

                // TODO: Implement real APCI send/receive when libraries are available
                // For now, simulate echo response for testing
                var response = new byte[data.Length];
                Array.Copy(data, response, data.Length);

                _logger.LogDebug($"Received {response.Length} bytes from device (simulated echo)");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during data send/receive: {ex.Message}");
                return new byte[0];
            }
        }

        /// <summary>
        /// Detects USB Vocom devices using WMI
        /// </summary>
        private async Task<List<BridgeVocomDevice>> DetectUSBVocomDevicesAsync()
        {
            var devices = new List<BridgeVocomDevice>();

            try
            {
                _logger.LogInformation("Scanning for USB Vocom devices");

                // Search for USB devices using WMI
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PnPEntity WHERE DeviceID LIKE 'USB%'");
                using var collection = searcher.Get();

                foreach (ManagementObject device in collection)
                {
                    try
                    {
                        var deviceId = device["DeviceID"]?.ToString() ?? "";
                        var name = device["Name"]?.ToString() ?? "";
                        var description = device["Description"]?.ToString() ?? "";

                        // Check if this is a Vocom device
                        if (IsVocomDevice(deviceId, name, description))
                        {
                            var vocomDevice = new BridgeVocomDevice
                            {
                                Id = deviceId,
                                Name = FormatVocomDeviceName(name, description),
                                ConnectionType = "USB",
                                ConnectionStatus = "Disconnected"
                            };

                            devices.Add(vocomDevice);
                            _logger.LogInformation($"Found USB Vocom device: {vocomDevice.Name} (ID: {vocomDevice.Id})");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Error processing USB device: {ex.Message}");
                    }
                }

                _logger.LogInformation($"Found {devices.Count} USB Vocom devices");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error detecting USB Vocom devices: {ex.Message}");
            }

            return devices;
        }

        /// <summary>
        /// Detects Serial Port Vocom devices
        /// </summary>
        private async Task<List<BridgeVocomDevice>> DetectSerialPortVocomDevicesAsync()
        {
            var devices = new List<BridgeVocomDevice>();

            try
            {
                _logger.LogInformation("Scanning for Serial Port Vocom devices");

                // Search for serial port devices
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_SerialPort");
                using var collection = searcher.Get();

                foreach (ManagementObject port in collection)
                {
                    try
                    {
                        var portName = port["DeviceID"]?.ToString() ?? "";
                        var caption = port["Caption"]?.ToString() ?? "";
                        var description = port["Description"]?.ToString() ?? "";

                        // Check if this is a Vocom device
                        if (IsVocomDevice(portName, caption, description))
                        {
                            var vocomDevice = new BridgeVocomDevice
                            {
                                Id = portName,
                                Name = FormatVocomDeviceName(caption, description),
                                ConnectionType = "USB",
                                ConnectionStatus = "Disconnected"
                            };

                            devices.Add(vocomDevice);
                            _logger.LogInformation($"Found Serial Port Vocom device: {vocomDevice.Name} (Port: {portName})");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Error processing serial port: {ex.Message}");
                    }
                }

                _logger.LogInformation($"Found {devices.Count} Serial Port Vocom devices");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error detecting Serial Port Vocom devices: {ex.Message}");
            }

            return devices;
        }

        /// <summary>
        /// Checks if a device is a Vocom device based on its identifiers
        /// </summary>
        private bool IsVocomDevice(string deviceId, string name, string description)
        {
            var allText = $"{deviceId} {name} {description}".ToUpperInvariant();

            // Check for Vocom device names
            foreach (var vocomName in VocomDeviceNames)
            {
                if (allText.Contains(vocomName.ToUpperInvariant()))
                {
                    return true;
                }
            }

            // Check for Vocom hardware IDs
            foreach (var hardwareId in VocomHardwareIds)
            {
                if (deviceId.Contains(hardwareId, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Detects Vocom devices via Windows Registry
        /// </summary>
        private async Task<List<BridgeVocomDevice>> DetectRegistryVocomDevicesAsync()
        {
            var devices = new List<BridgeVocomDevice>();

            try
            {
                _logger.LogInformation("Scanning for Vocom devices in Windows Registry");

                // Check USB registry entries
                using var usbKey = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Enum\USB");
                if (usbKey != null)
                {
                    foreach (string subKeyName in usbKey.GetSubKeyNames())
                    {
                        if (VocomHardwareIds.Any(id => subKeyName.Contains(id.Replace("USB\\", ""), StringComparison.OrdinalIgnoreCase)))
                        {
                            using var deviceKey = usbKey.OpenSubKey(subKeyName);
                            if (deviceKey != null)
                            {
                                foreach (string instanceName in deviceKey.GetSubKeyNames())
                                {
                                    using var instanceKey = deviceKey.OpenSubKey(instanceName);
                                    if (instanceKey != null)
                                    {
                                        string friendlyName = instanceKey.GetValue("FriendlyName")?.ToString() ?? "Vocom Device";
                                        string deviceDesc = instanceKey.GetValue("DeviceDesc")?.ToString() ?? "";

                                        var vocomDevice = new BridgeVocomDevice
                                        {
                                            Id = $"USB\\{subKeyName}\\{instanceName}",
                                            Name = FormatVocomDeviceName(friendlyName, deviceDesc),
                                            ConnectionType = "USB",
                                            ConnectionStatus = "Disconnected"
                                        };

                                        devices.Add(vocomDevice);
                                        _logger.LogInformation($"Found Registry Vocom device: {vocomDevice.Name} (ID: {vocomDevice.Id})");
                                    }
                                }
                            }
                        }
                    }
                }

                _logger.LogInformation($"Found {devices.Count} Registry Vocom devices");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error detecting Registry Vocom devices: {ex.Message}");
            }

            return devices;
        }

        /// <summary>
        /// Removes duplicate devices based on device ID
        /// </summary>
        private List<BridgeVocomDevice> DeduplicateDevices(List<BridgeVocomDevice> devices)
        {
            var uniqueDevices = new List<BridgeVocomDevice>();
            var seenIds = new HashSet<string>();

            foreach (var device in devices)
            {
                if (!seenIds.Contains(device.Id))
                {
                    seenIds.Add(device.Id);
                    uniqueDevices.Add(device);
                }
                else
                {
                    _logger.LogInformation($"Skipping duplicate device: {device.Name} (ID: {device.Id})");
                }
            }

            _logger.LogInformation($"Deduplicated {devices.Count} devices to {uniqueDevices.Count} unique devices");
            return uniqueDevices;
        }

        /// <summary>
        /// Formats the device name to match the requirement "Vocom - 88890300"
        /// </summary>
        private string FormatVocomDeviceName(string name, string description)
        {
            // If the name already contains the required format, use it
            if (name.Contains("Vocom - 88890300", StringComparison.OrdinalIgnoreCase))
            {
                return name;
            }

            // If the name contains 88890300, format it properly
            if (name.Contains("88890300", StringComparison.OrdinalIgnoreCase))
            {
                return "Vocom - 88890300";
            }

            // If the description contains 88890300, use it
            if (description.Contains("88890300", StringComparison.OrdinalIgnoreCase))
            {
                return "Vocom - 88890300";
            }

            // Default format with original name for reference
            return $"Vocom - 88890300 ({name})";
        }

        /// <summary>
        /// Checks if APCI libraries are available
        /// </summary>
        private bool CheckApciLibrariesAvailable()
        {
            try
            {
                var bridgePath = AppDomain.CurrentDomain.BaseDirectory;
                _logger.LogInformation($"Bridge directory: '{bridgePath}'");

                // Remove trailing backslash if present
                bridgePath = bridgePath.TrimEnd('\\', '/');
                _logger.LogInformation($"Bridge directory (trimmed): '{bridgePath}'");

                // Libraries are in the parent directory (Application\Libraries)
                // Use Path.GetDirectoryName instead of Directory.GetParent
                var parentPath = Path.GetDirectoryName(bridgePath);
                if (string.IsNullOrEmpty(parentPath))
                {
                    _logger.LogError("Could not determine parent directory");
                    return false;
                }

                _logger.LogInformation($"Parent directory: '{parentPath}'");
                var librariesPath = Path.Combine(parentPath, "Libraries");
                _logger.LogInformation($"Looking for APCI libraries in: '{librariesPath}'");

                var requiredLibraries = new[]
                {
                    "apci.dll",
                    "Volvo.ApciPlus.dll",
                    "Volvo.ApciPlusData.dll"
                };

                foreach (var library in requiredLibraries)
                {
                    var libraryPath = Path.Combine(librariesPath, library);
                    if (!File.Exists(libraryPath))
                    {
                        _logger.LogError($"Required library not found: {libraryPath}");
                        return false;
                    }
                    _logger.LogInformation($"Found required library: {libraryPath}");
                }

                _logger.LogInformation("All required APCI libraries found");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception checking APCI libraries: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Loads the APCI library dynamically and resolves function pointers
        /// </summary>
        private bool LoadApciLibrary()
        {
            try
            {
                var bridgePath = AppDomain.CurrentDomain.BaseDirectory;
                var parentPath = Path.GetDirectoryName(bridgePath.TrimEnd('\\', '/'));
                if (string.IsNullOrEmpty(parentPath))
                {
                    _logger.LogError("Could not determine parent directory for APCI library loading");
                    return false;
                }

                var librariesPath = Path.Combine(parentPath, "Libraries");
                var apciPath = Path.Combine(librariesPath, "apci.dll");

                if (!File.Exists(apciPath))
                {
                    _logger.LogError($"APCI library not found at: {apciPath}");
                    return false;
                }

                // Load the library
                _apciLibraryHandle = LoadLibrary(apciPath);
                if (_apciLibraryHandle == IntPtr.Zero)
                {
                    _logger.LogError($"Failed to load APCI library from: {apciPath}");
                    return false;
                }

                _logger.LogInformation($"Successfully loaded APCI library from: {apciPath}");

                // Load function pointers
                LoadApciFunction("APCI_Initialize", ref _apciInitialize);
                LoadApciFunction("APCI_Terminate", ref _apciTerminate);
                LoadApciFunction("APCI_ScanDevices", ref _apciScanDevices);
                LoadApciFunction("APCI_Connect", ref _apciConnect);
                LoadApciFunction("APCI_Disconnect", ref _apciDisconnect);
                LoadApciFunction("APCI_SendData", ref _apciSendData);
                LoadApciFunction("APCI_ReceiveData", ref _apciReceiveData);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception loading APCI library: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Loads a specific function from the APCI library
        /// </summary>
        private void LoadApciFunction<T>(string functionName, ref T? functionDelegate) where T : class
        {
            try
            {
                IntPtr procAddress = GetProcAddress(_apciLibraryHandle, functionName);
                if (procAddress != IntPtr.Zero)
                {
                    functionDelegate = Marshal.GetDelegateForFunctionPointer<T>(procAddress);
                    _logger.LogInformation($"Successfully loaded APCI function: {functionName}");
                }
                else
                {
                    _logger.LogWarning($"APCI function not found: {functionName}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception loading APCI function {functionName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Detects Vocom devices using APCI library
        /// </summary>
        private async Task<List<BridgeVocomDevice>> DetectAPCIDevicesAsync()
        {
            var devices = new List<BridgeVocomDevice>();

            try
            {
                if (_apciScanDevices == null)
                {
                    _logger.LogWarning("APCI_ScanDevices function not available");
                    return devices;
                }

                _logger.LogInformation("Scanning for Vocom devices using APCI library");

                // Allocate buffer for device list (assuming max 10 devices, 256 bytes each)
                const int maxDevices = 10;
                const int deviceInfoSize = 256;
                IntPtr deviceListBuffer = Marshal.AllocHGlobal(maxDevices * deviceInfoSize);

                try
                {
                    int deviceCount = maxDevices;
                    int result = _apciScanDevices(deviceListBuffer, ref deviceCount);

                    if (result == 0 && deviceCount > 0)
                    {
                        _logger.LogInformation($"APCI scan found {deviceCount} devices");

                        for (int i = 0; i < deviceCount; i++)
                        {
                            // Read device info from buffer (this is a simplified approach)
                            IntPtr deviceInfoPtr = IntPtr.Add(deviceListBuffer, i * deviceInfoSize);
                            string deviceInfo = Marshal.PtrToStringAnsi(deviceInfoPtr) ?? "";

                            if (!string.IsNullOrEmpty(deviceInfo))
                            {
                                var device = new BridgeVocomDevice
                                {
                                    Id = $"APCI_DEVICE_{i}",
                                    Name = $"Real Vocom Device {i + 1} (APCI)",
                                    ConnectionType = "USB",
                                    ConnectionStatus = "Disconnected"
                                };
                                devices.Add(device);
                                _logger.LogInformation($"Found real Vocom device via APCI: {device.Name}");
                            }
                        }
                    }
                    else
                    {
                        _logger.LogInformation($"APCI scan completed with result: {result}, device count: {deviceCount}");
                    }
                }
                finally
                {
                    Marshal.FreeHGlobal(deviceListBuffer);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during APCI device detection: {ex.Message}");
            }

            await Task.CompletedTask;
            return devices;
        }

        /// <summary>
        /// Detects Vocom devices using the original working detection methods
        /// This replicates the detection logic that was working before the bridge was implemented
        /// </summary>
        private async Task<List<BridgeVocomDevice>> DetectOriginalVocomDevicesAsync()
        {
            var devices = new List<BridgeVocomDevice>();

            try
            {
                _logger.LogInformation("Using original Vocom detection methods (pre-bridge)");

                // Method 1: Check for Phoenix Diag installation and Vocom devices
                await DetectPhoenixVocomDevicesAsync(devices);

                // Method 2: Check Windows registry for Vocom devices
                await DetectRegistryVocomDevicesAsync(devices);

                // Method 3: Enhanced USB device detection
                await DetectUSBVocomDevicesAsync(devices);

                // Method 4: Check for system Vocom drivers
                await DetectSystemVocomDriversAsync(devices);

                _logger.LogInformation($"Original detection methods found {devices.Count} devices");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during original Vocom device detection: {ex.Message}");
            }

            await Task.CompletedTask;
            return devices;
        }

        /// <summary>
        /// Detects Vocom devices through Phoenix Diag installation
        /// </summary>
        private async Task DetectPhoenixVocomDevicesAsync(List<BridgeVocomDevice> devices)
        {
            try
            {
                string phoenixPath = @"C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021";
                if (Directory.Exists(phoenixPath))
                {
                    _logger.LogInformation($"Found Phoenix Diag installation at: {phoenixPath}");

                    // Check for Vocom adapter configuration
                    string vocomConfigPath = Path.Combine(phoenixPath, "vocom_adapter.xml");
                    if (File.Exists(vocomConfigPath))
                    {
                        var device = new BridgeVocomDevice
                        {
                            Id = "PHOENIX_VOCOM_001",
                            Name = "Phoenix Vocom Adapter (Real Hardware)",
                            ConnectionType = "USB",
                            ConnectionStatus = "Disconnected"
                        };
                        devices.Add(device);
                        _logger.LogInformation("Found Phoenix Vocom adapter configuration");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception detecting Phoenix Vocom devices: {ex.Message}");
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Detects Vocom devices through system driver installation
        /// </summary>
        private async Task DetectSystemVocomDriversAsync(List<BridgeVocomDevice> devices)
        {
            try
            {
                string vocomDriverPath = @"C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll";
                if (File.Exists(vocomDriverPath))
                {
                    _logger.LogInformation($"Found system Vocom driver at: {vocomDriverPath}");

                    var device = new BridgeVocomDevice
                    {
                        Id = "SYSTEM_VOCOM_001",
                        Name = "System Vocom Adapter (Real Hardware)",
                        ConnectionType = "USB",
                        ConnectionStatus = "Disconnected"
                    };
                    devices.Add(device);
                    _logger.LogInformation("Found system Vocom driver installation");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception detecting system Vocom drivers: {ex.Message}");
            }

            await Task.CompletedTask;
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                if (_isConnected)
                {
                    DisconnectAsync().Wait();
                }

                if (_isInitialized)
                {
                    // Call APCI_Terminate if available
                    if (_apciTerminate != null)
                    {
                        try
                        {
                            _apciTerminate();
                            _logger.LogInformation("APCI library terminated successfully");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError($"Exception terminating APCI library: {ex.Message}");
                        }
                    }
                    _logger.LogInformation("Bridge service disposed");
                }

                // Free the APCI library
                if (_apciLibraryHandle != IntPtr.Zero)
                {
                    FreeLibrary(_apciLibraryHandle);
                    _apciLibraryHandle = IntPtr.Zero;
                    _logger.LogInformation("APCI library unloaded");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during disposal: {ex.Message}");
            }

            _disposed = true;
        }
    }
}
