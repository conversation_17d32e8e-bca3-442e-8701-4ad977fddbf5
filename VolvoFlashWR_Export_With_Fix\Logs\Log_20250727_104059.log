Log started at 7/27/2025 10:40:59 AM
2025-07-27 10:40:59.953 [Information] LoggingService: Logging service initialized
2025-07-27 10:40:59.969 [Information] App: Starting integrated application initialization
2025-07-27 10:40:59.971 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-27 10:40:59.975 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-27 10:40:59.977 [Information] IntegratedStartupService: Setting up application environment
2025-07-27 10:40:59.977 [Information] IntegratedStartupService: Application environment setup completed
2025-07-27 10:40:59.979 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-27 10:40:59.982 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-27 10:40:59.985 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-27 10:40:59.989 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-27 10:41:00.005 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-27 10:41:00.014 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 10:41:00.023 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 10:41:00.032 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 10:41:00.041 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 10:41:00.052 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 10:41:00.063 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 10:41:00.066 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-27 10:41:00.069 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-27 10:41:00.069 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-27 10:41:00.085 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-27 10:41:00.085 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-27 10:41:00.087 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-27 10:41:00.087 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-27 10:41:00.088 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-27 10:41:00.155 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-27 10:41:00.157 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 10:41:00.158 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 10:41:00.158 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 10:41:00.158 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 10:41:00.159 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 10:41:00.159 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 10:41:00.167 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-27 10:41:00.168 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-27 10:41:00.168 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-27 10:41:00.175 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-27 10:41:00.175 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-27 10:41:00.177 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-27 10:41:00.180 [Information] LibraryExtractor: Starting library extraction process
2025-07-27 10:41:00.183 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-27 10:41:00.186 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-27 10:41:00.189 [Information] LibraryExtractor: Copying system libraries
2025-07-27 10:41:00.198 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-27 10:41:00.208 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-27 10:41:30.222 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 10:41:31.225 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-27 10:42:01.228 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 10:42:01.229 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 10:42:31.233 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 10:42:32.235 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-27 10:43:02.238 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 10:43:02.239 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 10:43:32.245 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 10:43:33.246 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-27 10:44:03.250 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 10:44:03.251 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-27 10:44:03.252 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-27 10:44:03.256 [Information] LibraryExtractor: Verifying library extraction
2025-07-27 10:44:03.256 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-27 10:44:03.257 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-27 10:44:03.257 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-27 10:44:03.258 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-27 10:44:03.258 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-27 10:44:03.263 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-27 10:44:03.265 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-27 10:44:03.267 [Information] DependencyManager: Initializing dependency manager
2025-07-27 10:44:03.269 [Information] DependencyManager: Setting up library search paths
2025-07-27 10:44:03.271 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 10:44:03.272 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 10:44:03.272 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 10:44:03.272 [Information] DependencyManager: Updated PATH environment variable
2025-07-27 10:44:03.274 [Information] DependencyManager: Verifying required directories
2025-07-27 10:44:03.275 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 10:44:03.275 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 10:44:03.275 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-27 10:44:03.276 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 10:44:03.278 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-27 10:44:03.301 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 10:44:03.303 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 10:44:03.305 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 10:44:03.307 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 10:44:03.310 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 10:44:03.311 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 10:44:03.326 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 10:44:24.732 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll from C:\Windows\system32\msvcp140.dll: Error 193
2025-07-27 10:44:24.733 [Warning] DependencyManager: Architecture mismatch detected for msvcp140.dll. Expected: x64
2025-07-27 10:44:24.735 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp140.dll
2025-07-27 10:44:25.333 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll: Error 193
2025-07-27 10:44:25.333 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp140.dll
2025-07-27 10:44:25.334 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 10:44:25.337 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 10:44:25.339 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 10:44:25.339 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 10:44:25.342 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 10:44:25.342 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 10:44:25.343 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 10:44:25.344 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 10:44:25.345 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 10:44:25.345 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 10:44:25.346 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 10:44:25.346 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 10:44:25.347 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 10:44:25.347 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 10:44:25.348 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 10:44:25.349 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 10:44:25.350 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 10:44:25.351 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 10:44:25.352 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 10:44:25.352 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 10:44:25.352 [Information] DependencyManager: VC++ Redistributable library loading: 3/14 (21.4%) libraries loaded
2025-07-27 10:44:25.353 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-27 10:44:25.354 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-27 10:44:25.356 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-27 10:44:25.478 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-27 10:44:25.480 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-27 10:44:25.481 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-27 10:44:25.483 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-27 10:44:25.851 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-27 10:44:25.853 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-27 10:44:25.971 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-27 10:44:25.972 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-27 10:44:25.973 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 10:44:25.975 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 10:44:25.976 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 10:44:25.976 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 10:44:25.977 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 10:44:25.977 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 10:44:25.978 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-27 10:44:25.979 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 10:44:25.981 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-27 10:44:25.982 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 10:44:25.983 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 10:44:25.984 [Information] DependencyManager: Setting up environment variables
2025-07-27 10:44:25.984 [Information] DependencyManager: Environment variables configured
2025-07-27 10:44:25.986 [Information] DependencyManager: Verifying library loading status
2025-07-27 10:44:26.347 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-27 10:44:26.348 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-27 10:44:26.348 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-27 10:44:26.351 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-27 10:44:26.353 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-27 10:44:26.402 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-27 10:44:26.405 [Information] IntegratedStartupService: Verifying system readiness
2025-07-27 10:44:26.405 [Information] IntegratedStartupService: System readiness verification passed
2025-07-27 10:44:26.406 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-27 10:44:26.408 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-27 10:44:26.408 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 10:44:26.408 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 10:44:26.409 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 10:44:26.409 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-27 10:44:26.409 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-27 10:44:26.409 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-27 10:44:26.410 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 10:44:26.410 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-27 10:44:26.411 [Information] App: Integrated startup completed successfully
2025-07-27 10:44:26.414 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-27 10:44:26.658 [Information] App: Initializing application services
2025-07-27 10:44:26.660 [Information] AppConfigurationService: Initializing configuration service
2025-07-27 10:44:26.661 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 10:44:26.751 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 10:44:26.752 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-27 10:44:26.753 [Information] App: Configuration service initialized successfully
2025-07-27 10:44:26.755 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-27 10:44:26.756 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-27 10:44:26.762 [Information] App: Environment variable exists: True, not 'false': False
2025-07-27 10:44:26.763 [Information] App: Final useDummyImplementations value: False
2025-07-27 10:44:26.763 [Information] App: Updating config to NOT use dummy implementations
2025-07-27 10:44:26.784 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 10:44:26.784 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-27 10:44:26.784 [Information] App: usePatchedImplementation flag is: True
2025-07-27 10:44:26.785 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-27 10:44:26.785 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-27 10:44:26.785 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-27 10:44:26.785 [Information] App: verboseLogging flag is: True
2025-07-27 10:44:26.788 [Information] App: Verifying real hardware requirements...
2025-07-27 10:44:26.788 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-27 10:44:26.788 [Information] App: ✓ Found critical library: apci.dll
2025-07-27 10:44:26.789 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-27 10:44:26.789 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-27 10:44:26.789 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 10:44:26.790 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-27 10:44:26.790 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-27 10:44:26.791 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-27 10:44:26.802 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-27 10:44:26.805 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-27 10:44:26.806 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-27 10:44:26.808 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-27 10:44:26.814 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-07-27 10:49:26.818 [Warning] RuntimeDependencyResolver: Failed to download msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-27 10:49:26.819 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-27 10:49:26.820 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-27 10:49:26.821 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-27 10:49:26.821 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 10:49:26.822 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 10:49:26.822 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 10:49:26.822 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 10:49:26.823 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 10:49:26.823 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 10:49:26.824 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-27 10:49:26.825 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-27 10:49:26.825 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-27 10:49:26.826 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 10:49:26.826 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-27 10:49:26.827 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-27 10:49:26.827 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-27 10:49:26.827 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-27 10:49:26.827 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-27 10:49:26.829 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-27 10:49:26.830 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-27 10:49:26.832 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-27 10:49:26.833 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-27 10:49:26.833 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-27 10:49:26.834 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-27 10:49:26.835 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-27 10:49:26.835 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-07-27 10:49:26.838 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-07-27 10:49:26.839 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-27 10:49:26.840 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-07-27 10:49:26.841 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-07-27 10:49:26.874 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-27 10:49:26.875 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-07-27 10:49:26.879 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-27 10:49:26.879 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-27 10:49:26.881 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-27 10:49:26.881 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-27 10:49:26.884 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-27 10:49:26.884 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-27 10:49:26.885 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-27 10:49:26.885 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 10:49:26.885 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-27 10:49:26.886 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-27 10:49:26.886 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-27 10:49:26.888 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-27 10:49:26.893 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-27 10:49:26.904 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-27 10:49:26.906 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 10:49:26.906 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-27 10:49:26.920 [Information] PhoenixVocomAdapter: Copied apci.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll
2025-07-27 10:49:26.924 [Information] PhoenixVocomAdapter: Copied apci.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 10:49:26.929 [Information] PhoenixVocomAdapter: Copied apcidb.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apcidb.dll
2025-07-27 10:49:26.930 [Information] PhoenixVocomAdapter: Copied apcidb.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-07-27 10:49:26.943 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.ApciPlus.dll
2025-07-27 10:49:26.947 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-07-27 10:49:26.951 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.ApciPlusData.dll
2025-07-27 10:49:26.953 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-07-27 10:49:26.955 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.ApciPlusTea2Data.dll
2025-07-27 10:49:26.956 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusTea2Data.dll
2025-07-27 10:49:26.962 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interface.dll
2025-07-27 10:49:26.963 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.NAMS.AC.Services.Interface.dll
2025-07-27 10:49:26.966 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-27 10:49:26.968 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-27 10:49:26.970 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.NVS.Core.dll
2025-07-27 10:49:26.972 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.NVS.Core.dll
2025-07-27 10:49:26.974 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.NVS.Logging.dll
2025-07-27 10:49:26.976 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.NVS.Logging.dll
2025-07-27 10:49:26.980 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.NVS.Persistence.dll
2025-07-27 10:49:26.981 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.NVS.Persistence.dll
2025-07-27 10:49:26.984 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Volvo.NVS.Persistence.NHibernate.dll
2025-07-27 10:49:26.986 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.NVS.Persistence.NHibernate.dll
2025-07-27 10:49:26.993 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\VolvoIt.Baf.Utility.dll
2025-07-27 10:49:26.995 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoIt.Baf.Utility.dll
2025-07-27 10:49:26.997 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-27 10:49:26.998 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-27 10:49:27.004 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\VolvoIt.Waf.ServiceContract.dll
2025-07-27 10:49:27.006 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoIt.Waf.ServiceContract.dll
2025-07-27 10:49:27.008 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\VolvoIt.Waf.Utility.dll
2025-07-27 10:49:27.011 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoIt.Waf.Utility.dll
2025-07-27 10:49:27.013 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Config\Volvo.ApciPlus.dll.config
2025-07-27 10:49:27.014 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll.config
2025-07-27 10:49:27.016 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Config\Volvo.ApciPlusData.dll.config
2025-07-27 10:49:27.017 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll.config
2025-07-27 10:49:27.028 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\NHibernate.dll
2025-07-27 10:49:27.032 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\NHibernate.dll
2025-07-27 10:49:27.034 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\NHibernate.Caches.SysCache2.dll
2025-07-27 10:49:27.036 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\NHibernate.Caches.SysCache2.dll
2025-07-27 10:49:27.038 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\Iesi.Collections.dll
2025-07-27 10:49:27.039 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Iesi.Collections.dll
2025-07-27 10:49:27.042 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\Ionic.Zip.Reduced.dll
2025-07-27 10:49:27.044 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Ionic.Zip.Reduced.dll
2025-07-27 10:49:27.048 [Information] PhoenixVocomAdapter: Copied SharpCompress.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\SharpCompress.dll
2025-07-27 10:49:27.051 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\DotNetZip.dll
2025-07-27 10:49:27.053 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\DotNetZip.dll
2025-07-27 10:49:27.056 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\ICSharpCode.SharpZipLib.dll
2025-07-27 10:49:27.058 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\ICSharpCode.SharpZipLib.dll
2025-07-27 10:49:27.061 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Vodia.CommonDomain.Model.dll
2025-07-27 10:49:27.062 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Vodia.CommonDomain.Model.dll
2025-07-27 10:49:27.066 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Vodia.Contracts.Common.dll
2025-07-27 10:49:27.068 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Vodia.Contracts.Common.dll
2025-07-27 10:49:27.071 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Vodia.UtilityComponent.dll
2025-07-27 10:49:27.072 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Vodia.UtilityComponent.dll
2025-07-27 10:49:27.080 [Information] PhoenixVocomAdapter: Copied log4net.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\log4net.dll
2025-07-27 10:49:27.082 [Information] PhoenixVocomAdapter: Copied log4net.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\log4net.dll
2025-07-27 10:49:27.086 [Information] PhoenixVocomAdapter: Copied Newtonsoft.Json.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\Newtonsoft.Json.dll
2025-07-27 10:49:27.091 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\Dependencies\AutoMapper.dll
2025-07-27 10:49:27.092 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\AutoMapper.dll
2025-07-27 10:49:27.094 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 10:49:27.096 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.AppContext.dll
2025-07-27 10:49:27.098 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.AppContext.dll
2025-07-27 10:49:27.101 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Buffers.dll
2025-07-27 10:49:27.103 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Buffers.dll
2025-07-27 10:49:27.105 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Collections.Concurrent.dll
2025-07-27 10:49:27.106 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Collections.Concurrent.dll
2025-07-27 10:49:27.108 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Collections.dll
2025-07-27 10:49:27.110 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Collections.dll
2025-07-27 10:49:27.112 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Collections.NonGeneric.dll
2025-07-27 10:49:27.114 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Collections.NonGeneric.dll
2025-07-27 10:49:27.116 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Collections.Specialized.dll
2025-07-27 10:49:27.118 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Collections.Specialized.dll
2025-07-27 10:49:27.120 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.ComponentModel.dll
2025-07-27 10:49:27.121 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.ComponentModel.dll
2025-07-27 10:49:27.123 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.ComponentModel.EventBasedAsync.dll
2025-07-27 10:49:27.125 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.ComponentModel.EventBasedAsync.dll
2025-07-27 10:49:27.127 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.ComponentModel.Primitives.dll
2025-07-27 10:49:27.128 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.ComponentModel.Primitives.dll
2025-07-27 10:49:27.131 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.ComponentModel.TypeConverter.dll
2025-07-27 10:49:27.133 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.ComponentModel.TypeConverter.dll
2025-07-27 10:49:27.135 [Information] PhoenixVocomAdapter: Copied System.Console.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Console.dll
2025-07-27 10:49:27.138 [Information] PhoenixVocomAdapter: Copied System.Console.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Console.dll
2025-07-27 10:49:27.143 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Data.Common.dll
2025-07-27 10:49:27.145 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Data.Common.dll
2025-07-27 10:49:27.148 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Data.SQLite.dll
2025-07-27 10:49:27.150 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Data.SQLite.dll
2025-07-27 10:49:27.155 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Data.SqlServerCe.dll
2025-07-27 10:49:27.157 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Data.SqlServerCe.dll
2025-07-27 10:49:27.163 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.Contracts.dll
2025-07-27 10:49:27.164 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Diagnostics.Contracts.dll
2025-07-27 10:49:27.167 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.Debug.dll
2025-07-27 10:49:27.168 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Diagnostics.Debug.dll
2025-07-27 10:49:27.171 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.FileVersionInfo.dll
2025-07-27 10:49:27.172 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Diagnostics.FileVersionInfo.dll
2025-07-27 10:49:27.174 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.Process.dll
2025-07-27 10:49:27.176 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Diagnostics.Process.dll
2025-07-27 10:49:27.178 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.StackTrace.dll
2025-07-27 10:49:27.179 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Diagnostics.StackTrace.dll
2025-07-27 10:49:27.181 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.TextWriterTraceListener.dll
2025-07-27 10:49:27.183 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Diagnostics.TextWriterTraceListener.dll
2025-07-27 10:49:27.187 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.Tools.dll
2025-07-27 10:49:27.188 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Diagnostics.Tools.dll
2025-07-27 10:49:27.191 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.TraceSource.dll
2025-07-27 10:49:27.193 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Diagnostics.TraceSource.dll
2025-07-27 10:49:27.195 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Diagnostics.Tracing.dll
2025-07-27 10:49:27.196 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Diagnostics.Tracing.dll
2025-07-27 10:49:27.198 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Drawing.Primitives.dll
2025-07-27 10:49:27.200 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Drawing.Primitives.dll
2025-07-27 10:49:27.202 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Dynamic.Runtime.dll
2025-07-27 10:49:27.203 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Dynamic.Runtime.dll
2025-07-27 10:49:27.205 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Globalization.Calendars.dll
2025-07-27 10:49:27.207 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Globalization.Calendars.dll
2025-07-27 10:49:27.209 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Globalization.dll
2025-07-27 10:49:27.211 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Globalization.dll
2025-07-27 10:49:27.213 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Globalization.Extensions.dll
2025-07-27 10:49:27.215 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Globalization.Extensions.dll
2025-07-27 10:49:27.217 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.Compression.dll
2025-07-27 10:49:27.219 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.IO.Compression.dll
2025-07-27 10:49:27.222 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.Compression.ZipFile.dll
2025-07-27 10:49:27.224 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.IO.Compression.ZipFile.dll
2025-07-27 10:49:27.227 [Information] PhoenixVocomAdapter: Copied System.IO.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.dll
2025-07-27 10:49:27.229 [Information] PhoenixVocomAdapter: Copied System.IO.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.IO.dll
2025-07-27 10:49:27.231 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.FileSystem.dll
2025-07-27 10:49:27.233 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.IO.FileSystem.dll
2025-07-27 10:49:27.235 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.FileSystem.DriveInfo.dll
2025-07-27 10:49:27.236 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.IO.FileSystem.DriveInfo.dll
2025-07-27 10:49:27.238 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.FileSystem.Primitives.dll
2025-07-27 10:49:27.239 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.IO.FileSystem.Primitives.dll
2025-07-27 10:49:27.244 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.FileSystem.Watcher.dll
2025-07-27 10:49:27.246 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.IO.FileSystem.Watcher.dll
2025-07-27 10:49:27.247 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.IsolatedStorage.dll
2025-07-27 10:49:27.249 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.IO.IsolatedStorage.dll
2025-07-27 10:49:27.252 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.MemoryMappedFiles.dll
2025-07-27 10:49:27.253 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.IO.MemoryMappedFiles.dll
2025-07-27 10:49:27.255 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.Pipes.dll
2025-07-27 10:49:27.257 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.IO.Pipes.dll
2025-07-27 10:49:27.259 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.IO.UnmanagedMemoryStream.dll
2025-07-27 10:49:27.261 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.IO.UnmanagedMemoryStream.dll
2025-07-27 10:49:27.263 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Linq.dll
2025-07-27 10:49:27.264 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Linq.dll
2025-07-27 10:49:27.268 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Linq.Expressions.dll
2025-07-27 10:49:27.269 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Linq.Expressions.dll
2025-07-27 10:49:27.272 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Linq.Parallel.dll
2025-07-27 10:49:27.273 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Linq.Parallel.dll
2025-07-27 10:49:27.275 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Linq.Queryable.dll
2025-07-27 10:49:27.277 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Linq.Queryable.dll
2025-07-27 10:49:27.280 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Memory.dll
2025-07-27 10:49:27.282 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Memory.dll
2025-07-27 10:49:27.284 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.Http.dll
2025-07-27 10:49:27.286 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Net.Http.dll
2025-07-27 10:49:27.288 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.NameResolution.dll
2025-07-27 10:49:27.289 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Net.NameResolution.dll
2025-07-27 10:49:27.291 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.NetworkInformation.dll
2025-07-27 10:49:27.293 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Net.NetworkInformation.dll
2025-07-27 10:49:27.295 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.Ping.dll
2025-07-27 10:49:27.296 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Net.Ping.dll
2025-07-27 10:49:27.298 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.Primitives.dll
2025-07-27 10:49:27.299 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Net.Primitives.dll
2025-07-27 10:49:27.302 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.Requests.dll
2025-07-27 10:49:27.303 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Net.Requests.dll
2025-07-27 10:49:27.305 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.Security.dll
2025-07-27 10:49:27.306 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Net.Security.dll
2025-07-27 10:49:27.308 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.Sockets.dll
2025-07-27 10:49:27.310 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Net.Sockets.dll
2025-07-27 10:49:27.312 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.WebHeaderCollection.dll
2025-07-27 10:49:27.313 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Net.WebHeaderCollection.dll
2025-07-27 10:49:27.315 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.WebSockets.Client.dll
2025-07-27 10:49:27.317 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Net.WebSockets.Client.dll
2025-07-27 10:49:27.319 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Net.WebSockets.dll
2025-07-27 10:49:27.321 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Net.WebSockets.dll
2025-07-27 10:49:27.323 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Numerics.Vectors.dll
2025-07-27 10:49:27.325 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Numerics.Vectors.dll
2025-07-27 10:49:27.327 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.ObjectModel.dll
2025-07-27 10:49:27.328 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.ObjectModel.dll
2025-07-27 10:49:27.330 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Reflection.dll
2025-07-27 10:49:27.332 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Reflection.dll
2025-07-27 10:49:27.334 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Reflection.Extensions.dll
2025-07-27 10:49:27.336 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Reflection.Extensions.dll
2025-07-27 10:49:27.337 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Reflection.Primitives.dll
2025-07-27 10:49:27.339 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Reflection.Primitives.dll
2025-07-27 10:49:27.342 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Resources.Reader.dll
2025-07-27 10:49:27.343 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Resources.Reader.dll
2025-07-27 10:49:27.345 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Resources.ResourceManager.dll
2025-07-27 10:49:27.347 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Resources.ResourceManager.dll
2025-07-27 10:49:27.348 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Resources.Writer.dll
2025-07-27 10:49:27.350 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Resources.Writer.dll
2025-07-27 10:49:27.352 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.CompilerServices.Unsafe.dll
2025-07-27 10:49:27.354 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Runtime.CompilerServices.Unsafe.dll
2025-07-27 10:49:27.355 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.CompilerServices.VisualC.dll
2025-07-27 10:49:27.357 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Runtime.CompilerServices.VisualC.dll
2025-07-27 10:49:27.359 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.dll
2025-07-27 10:49:27.361 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Runtime.dll
2025-07-27 10:49:27.363 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Extensions.dll
2025-07-27 10:49:27.364 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Runtime.Extensions.dll
2025-07-27 10:49:27.366 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Handles.dll
2025-07-27 10:49:27.367 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Runtime.Handles.dll
2025-07-27 10:49:27.369 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.InteropServices.dll
2025-07-27 10:49:27.371 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Runtime.InteropServices.dll
2025-07-27 10:49:27.373 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-27 10:49:27.375 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-27 10:49:27.377 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Numerics.dll
2025-07-27 10:49:27.378 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Runtime.Numerics.dll
2025-07-27 10:49:27.382 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Serialization.Formatters.dll
2025-07-27 10:49:27.383 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Runtime.Serialization.Formatters.dll
2025-07-27 10:49:27.386 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Serialization.Json.dll
2025-07-27 10:49:27.387 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Runtime.Serialization.Json.dll
2025-07-27 10:49:27.389 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Serialization.Primitives.dll
2025-07-27 10:49:27.391 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Runtime.Serialization.Primitives.dll
2025-07-27 10:49:27.393 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Runtime.Serialization.Xml.dll
2025-07-27 10:49:27.395 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Runtime.Serialization.Xml.dll
2025-07-27 10:49:27.396 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Claims.dll
2025-07-27 10:49:27.398 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Security.Claims.dll
2025-07-27 10:49:27.400 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Cryptography.Algorithms.dll
2025-07-27 10:49:27.401 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Security.Cryptography.Algorithms.dll
2025-07-27 10:49:27.403 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Cryptography.Csp.dll
2025-07-27 10:49:27.405 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Security.Cryptography.Csp.dll
2025-07-27 10:49:27.406 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Cryptography.Encoding.dll
2025-07-27 10:49:27.408 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Security.Cryptography.Encoding.dll
2025-07-27 10:49:27.410 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Cryptography.Primitives.dll
2025-07-27 10:49:27.412 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Security.Cryptography.Primitives.dll
2025-07-27 10:49:27.414 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Cryptography.X509Certificates.dll
2025-07-27 10:49:27.415 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Security.Cryptography.X509Certificates.dll
2025-07-27 10:49:27.417 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.Principal.dll
2025-07-27 10:49:27.419 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Security.Principal.dll
2025-07-27 10:49:27.422 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Security.SecureString.dll
2025-07-27 10:49:27.423 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Security.SecureString.dll
2025-07-27 10:49:27.425 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Text.Encoding.dll
2025-07-27 10:49:27.426 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Text.Encoding.dll
2025-07-27 10:49:27.429 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Text.Encoding.Extensions.dll
2025-07-27 10:49:27.430 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Text.Encoding.Extensions.dll
2025-07-27 10:49:27.432 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Text.RegularExpressions.dll
2025-07-27 10:49:27.434 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Text.RegularExpressions.dll
2025-07-27 10:49:27.436 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.dll
2025-07-27 10:49:27.437 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Threading.dll
2025-07-27 10:49:27.439 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.Overlapped.dll
2025-07-27 10:49:27.441 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Threading.Overlapped.dll
2025-07-27 10:49:27.444 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.Tasks.dll
2025-07-27 10:49:27.445 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Threading.Tasks.dll
2025-07-27 10:49:27.447 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.Tasks.Extensions.dll
2025-07-27 10:49:27.448 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Threading.Tasks.Extensions.dll
2025-07-27 10:49:27.451 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.Tasks.Parallel.dll
2025-07-27 10:49:27.452 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Threading.Tasks.Parallel.dll
2025-07-27 10:49:27.454 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.Thread.dll
2025-07-27 10:49:27.456 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Threading.Thread.dll
2025-07-27 10:49:27.458 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.ThreadPool.dll
2025-07-27 10:49:27.459 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Threading.ThreadPool.dll
2025-07-27 10:49:27.462 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Threading.Timer.dll
2025-07-27 10:49:27.464 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Threading.Timer.dll
2025-07-27 10:49:27.466 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.ValueTuple.dll
2025-07-27 10:49:27.467 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.ValueTuple.dll
2025-07-27 10:49:27.469 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Xml.ReaderWriter.dll
2025-07-27 10:49:27.471 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Xml.ReaderWriter.dll
2025-07-27 10:49:27.473 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Xml.XDocument.dll
2025-07-27 10:49:27.475 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Xml.XDocument.dll
2025-07-27 10:49:27.477 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Xml.XmlDocument.dll
2025-07-27 10:49:27.479 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Xml.XmlDocument.dll
2025-07-27 10:49:27.481 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Xml.XmlSerializer.dll
2025-07-27 10:49:27.482 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Xml.XmlSerializer.dll
2025-07-27 10:49:27.485 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Xml.XPath.dll
2025-07-27 10:49:27.486 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Xml.XPath.dll
2025-07-27 10:49:27.489 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\System.Xml.XPath.XDocument.dll
2025-07-27 10:49:27.491 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\System.Xml.XPath.XDocument.dll
2025-07-27 10:49:27.493 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\System\SystemInterface.dll
2025-07-27 10:49:27.495 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\SystemInterface.dll
2025-07-27 10:49:27.495 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-27 10:49:27.497 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-27 10:49:27.497 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-27 10:49:27.641 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 10:49:27.641 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-07-27 10:49:27.642 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 10:49:27.642 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-07-27 10:49:27.772 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 10:49:27.772 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-27 10:49:27.773 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-27 10:49:27.774 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-27 10:49:27.775 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-27 10:49:27.777 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-27 10:49:27.777 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-27 10:49:27.777 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-27 10:49:27.782 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-27 10:49:27.784 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-27 10:49:27.784 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 10:49:27.785 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-27 10:49:27.785 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-27 10:49:27.786 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-27 10:49:27.788 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-27 10:49:27.788 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-27 10:49:27.788 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-27 10:49:27.788 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-27 10:49:27.789 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-27 10:49:27.789 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-27 10:49:27.790 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-27 10:49:27.791 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-27 10:49:27.791 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-27 10:49:27.793 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-27 10:49:27.793 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-27 10:49:27.794 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-27 10:49:27.794 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 10:49:27.795 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-27 10:49:27.797 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-27 10:49:27.797 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 10:49:27.798 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-27 10:49:27.798 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-27 10:49:27.799 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-27 10:49:27.800 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-27 10:49:27.800 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-27 10:49:27.803 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-27 10:49:27.806 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-27 10:49:27.806 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-27 10:49:27.815 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-27 10:49:27.816 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-27 10:49:27.816 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 10:49:27.817 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 10:49:27.819 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-27 10:49:27.822 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 10:49:27.823 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 10:49:27.823 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-27 10:49:27.916 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 10:49:27.917 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 10:49:27.917 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-27 10:49:27.919 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 10:49:27.919 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-27 10:49:27.921 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 10:49:27.921 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-27 10:49:28.087 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 10:49:28.088 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 10:49:28.088 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-27 10:49:28.267 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 10:49:28.268 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 10:49:28.269 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-27 10:49:28.407 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 10:49:28.674 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 10:49:28.885 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 10:49:28.887 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 10:49:28.887 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-27 10:49:28.888 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 10:49:28.889 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 10:49:28.889 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-27 10:49:29.022 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 10:49:29.024 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 10:49:29.024 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-27 10:49:29.254 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 10:49:29.256 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 10:49:29.257 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-27 10:49:29.442 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 10:49:29.647 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 10:49:29.727 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 10:49:29.731 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-27 10:49:29.732 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 10:49:29.732 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-27 10:49:29.733 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-27 10:49:29.733 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-27 10:49:29.736 [Information] VocomDriver: Initializing Vocom driver
2025-07-27 10:49:29.738 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-27 10:49:29.742 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-27 10:49:29.743 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 10:49:29.743 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 10:49:29.745 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 10:49:29.745 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 10:49:39.932 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-27 10:49:40.705 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-27 10:49:40.707 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-27 10:49:41.353 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-27 10:49:41.905 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-27 10:49:41.906 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 10:49:41.994 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-07-27 10:49:42.203 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-07-27 10:49:42.414 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-07-27 10:49:42.415 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-27 10:49:42.415 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-27 10:49:42.416 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-27 10:49:42.417 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-27 10:49:42.418 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-27 10:49:42.419 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-27 10:49:42.419 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-27 10:49:42.419 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-27 10:49:42.420 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-27 10:49:42.420 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-27 10:49:42.420 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-27 10:49:42.421 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-27 10:49:42.421 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-27 10:49:42.421 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-27 10:49:42.421 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-27 10:49:42.422 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-27 10:49:42.422 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-27 10:49:42.422 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-27 10:49:42.422 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-27 10:49:42.423 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-27 10:49:42.423 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-27 10:49:42.423 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-27 10:49:42.423 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-27 10:49:42.424 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-27 10:49:42.424 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-27 10:49:42.424 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-27 10:49:42.424 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-27 10:49:42.425 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-27 10:49:42.425 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-27 10:49:42.425 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-27 10:49:42.425 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-27 10:49:42.426 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-27 10:49:42.426 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-27 10:49:42.426 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-27 10:49:42.426 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-27 10:49:42.427 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-27 10:49:42.427 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-27 10:49:42.427 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-27 10:49:42.427 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-27 10:49:42.428 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-27 10:49:42.428 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-27 10:49:42.428 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-27 10:49:42.428 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-27 10:49:42.429 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-27 10:49:42.429 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-27 10:49:42.429 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-27 10:49:42.431 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-27 10:49:42.432 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-27 10:49:42.433 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-27 10:49:42.434 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-27 10:49:42.434 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-27 10:49:42.434 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-27 10:49:42.434 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-27 10:49:42.435 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-27 10:49:42.440 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-27 10:49:42.442 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-27 10:49:42.443 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-27 10:49:42.445 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-27 10:49:42.562 [Information] WiFiCommunicationService: WiFi is available
2025-07-27 10:49:42.563 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-27 10:49:42.566 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-27 10:49:42.568 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-27 10:49:42.569 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-27 10:49:42.571 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-27 10:49:42.573 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 10:49:42.574 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 10:49:42.576 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 10:49:42.578 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 10:49:42.585 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 10:49:42.585 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 10:49:42.585 [Information] VocomService: Native USB communication service initialized
2025-07-27 10:49:42.586 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 10:49:42.586 [Information] VocomService: Connection recovery service initialized
2025-07-27 10:49:42.587 [Information] VocomService: Enhanced services initialization completed
2025-07-27 10:49:42.589 [Information] VocomService: Checking if PTT application is running
2025-07-27 10:49:42.607 [Information] VocomService: PTT application is not running
2025-07-27 10:49:42.611 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 10:49:42.612 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-27 10:49:42.612 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-07-27 10:49:42.616 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 10:49:42.617 [Information] VocomService: Using new enhanced device detection service
2025-07-27 10:49:42.619 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 10:49:42.621 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 10:49:43.488 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 10:49:43.489 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 10:49:43.491 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 10:49:43.494 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 10:49:43.494 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 10:49:43.496 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 10:49:43.502 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 10:49:43.506 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 10:49:43.862 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 10:49:43.865 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 10:49:43.867 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 10:49:43.869 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 10:49:43.872 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-27 10:49:43.873 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 10:49:43.874 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 10:49:43.878 [Information] VocomService: Found 3 Vocom devices
2025-07-27 10:49:43.879 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-07-27 10:49:43.881 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: cfdd8688-5dec-4ca0-92e2-d04731ce80ab, Type: USB)
2025-07-27 10:49:43.882 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: 8e392998-e7cb-4129-8db4-c5a38c11c0c2, Type: Bluetooth)
2025-07-27 10:49:43.882 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: 535bba9e-8b5b-4630-acca-5aaf8400fdb5, Type: WiFi)
2025-07-27 10:49:43.883 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-07-27 10:49:43.884 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-07-27 10:49:43.884 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-07-27 10:49:43.884 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-07-27 10:49:43.885 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-07-27 10:49:43.885 [Information] App: Architecture-aware Vocom service created successfully
2025-07-27 10:49:43.885 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 10:49:43.885 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 10:49:43.886 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 10:49:43.886 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 10:49:43.887 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 10:49:43.887 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 10:49:43.887 [Information] VocomService: Native USB communication service initialized
2025-07-27 10:49:43.888 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 10:49:43.888 [Information] VocomService: Connection recovery service initialized
2025-07-27 10:49:43.888 [Information] VocomService: Enhanced services initialization completed
2025-07-27 10:49:43.888 [Information] VocomService: Checking if PTT application is running
2025-07-27 10:49:43.905 [Information] VocomService: PTT application is not running
2025-07-27 10:49:43.905 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 10:49:43.906 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-27 10:49:43.906 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-27 10:49:43.906 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-27 10:49:43.950 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 10:49:43.950 [Information] VocomService: Using new enhanced device detection service
2025-07-27 10:49:43.951 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 10:49:43.951 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 10:49:44.316 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 10:49:44.316 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 10:49:44.317 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 10:49:44.317 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 10:49:44.317 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 10:49:44.317 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 10:49:44.318 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 10:49:44.318 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 10:49:44.684 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 10:49:44.684 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 10:49:44.685 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 10:49:44.685 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 10:49:44.686 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-27 10:49:44.686 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 10:49:44.687 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 10:49:44.687 [Information] VocomService: Found 3 Vocom devices
2025-07-27 10:49:44.688 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-27 10:49:44.691 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 10:49:44.691 [Information] VocomService: Checking if PTT application is running
2025-07-27 10:49:44.708 [Information] VocomService: PTT application is not running
2025-07-27 10:49:44.713 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 10:49:44.713 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-27 10:49:44.714 [Information] VocomService: Checking if PTT application is running
2025-07-27 10:49:44.730 [Information] VocomService: PTT application is not running
2025-07-27 10:49:44.730 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-27 10:49:44.734 [Information] VocomService: Native USB connection attempt 1/3 to WUDFPuma Driver
2025-07-27 10:49:44.736 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-27 10:49:44.739 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-27 10:49:44.741 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-27 10:49:44.742 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-27 10:49:46.743 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 10:49:47.744 [Information] VocomService: Native USB connection attempt 2/3 to WUDFPuma Driver
2025-07-27 10:49:47.744 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-27 10:49:47.745 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-27 10:49:47.745 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-27 10:49:47.745 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-27 10:49:49.746 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 10:49:50.746 [Information] VocomService: Native USB connection attempt 3/3 to WUDFPuma Driver
2025-07-27 10:49:50.746 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-27 10:49:50.747 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-27 10:49:50.747 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-27 10:49:50.747 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-27 10:49:52.747 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 10:49:52.749 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 10:49:52.749 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-27 10:49:52.798 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 10:49:52.800 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-27 10:49:52.802 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 10:49:52.803 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 10:49:52.803 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 10:49:52.804 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 10:49:52.804 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 10:49:52.807 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 10:49:52.808 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 10:49:52.811 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 10:49:52.811 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 10:49:52.812 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 10:49:52.812 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 10:49:52.813 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 10:49:52.813 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-27 10:49:52.817 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-27 10:49:52.822 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 10:49:52.823 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-27 10:49:52.828 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 10:49:52.831 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 10:49:52.832 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 10:49:52.833 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 10:49:52.833 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-07-27 10:49:52.833 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 10:49:52.834 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-27 10:49:52.834 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-27 10:49:52.838 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-27 10:49:52.838 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 10:49:52.838 [Information] VocomService: Using new enhanced device detection service
2025-07-27 10:49:52.839 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 10:49:52.839 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 10:49:53.113 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 10:49:53.114 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 10:49:53.114 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 10:49:53.114 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 10:49:53.115 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 10:49:53.115 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 10:49:53.115 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 10:49:53.116 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 10:49:53.390 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 10:49:53.392 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 10:49:53.392 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 10:49:53.393 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 10:49:53.394 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-27 10:49:53.394 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 10:49:53.395 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 10:49:53.395 [Information] VocomService: Found 3 Vocom devices
2025-07-27 10:49:53.397 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 10:49:53.397 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 10:49:53.398 [Information] VocomService: Checking if PTT application is running
2025-07-27 10:49:53.414 [Information] VocomService: PTT application is not running
2025-07-27 10:49:53.414 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 10:49:53.415 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-27 10:49:53.415 [Information] VocomService: Checking if PTT application is running
2025-07-27 10:49:53.431 [Information] VocomService: PTT application is not running
2025-07-27 10:49:53.432 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-27 10:49:53.432 [Information] VocomService: Native USB connection attempt 1/3 to WUDFPuma Driver
2025-07-27 10:49:53.433 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-27 10:49:53.433 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-27 10:49:53.433 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-27 10:49:53.434 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-27 10:49:55.434 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 10:49:56.434 [Information] VocomService: Native USB connection attempt 2/3 to WUDFPuma Driver
2025-07-27 10:49:56.435 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-27 10:49:56.435 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-27 10:49:56.435 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-27 10:49:56.436 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-27 10:49:58.436 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 10:49:59.437 [Information] VocomService: Native USB connection attempt 3/3 to WUDFPuma Driver
2025-07-27 10:49:59.437 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-27 10:49:59.437 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-27 10:49:59.438 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-27 10:49:59.438 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-27 10:50:01.439 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 10:50:01.439 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 10:50:01.439 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-27 10:50:01.440 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 10:50:01.441 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-27 10:50:01.441 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 10:50:01.441 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 10:50:01.442 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 10:50:01.442 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 10:50:01.442 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 10:50:01.443 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 10:50:01.443 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 10:50:01.443 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 10:50:01.444 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 10:50:01.444 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 10:50:01.444 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 10:50:01.445 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 10:50:01.445 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 10:50:01.446 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 10:50:01.446 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 10:50:01.446 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 10:50:01.447 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 10:50:01.447 [Information] VocomService: Checking if PTT application is running
2025-07-27 10:50:01.463 [Information] VocomService: PTT application is not running
2025-07-27 10:50:01.465 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 10:50:01.467 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-27 10:50:02.274 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 10:50:02.274 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 10:50:02.275 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-27 10:50:02.275 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 10:50:02.278 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 10:50:02.281 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 10:50:02.284 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 10:50:02.286 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 10:50:02.289 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 10:50:02.298 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 10:50:02.302 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 10:50:02.313 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 10:50:02.315 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 10:50:02.315 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 10:50:02.316 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 10:50:02.316 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 10:50:02.316 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 10:50:02.317 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 10:50:02.317 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 10:50:02.317 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 10:50:02.317 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 10:50:02.318 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 10:50:02.318 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 10:50:02.318 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 10:50:02.318 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 10:50:02.319 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 10:50:02.319 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 10:50:02.319 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 10:50:02.324 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 10:50:02.331 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 10:50:02.332 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 10:50:02.335 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 10:50:02.338 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 10:50:02.344 [Information] CANRegisterAccess: Read value 0x8A from register 0x0141 (simulated)
2025-07-27 10:50:02.352 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 10:50:02.358 [Information] CANRegisterAccess: Read value 0x30 from register 0x0141 (simulated)
2025-07-27 10:50:02.364 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 10:50:02.370 [Information] CANRegisterAccess: Read value 0xF5 from register 0x0141 (simulated)
2025-07-27 10:50:02.371 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 10:50:02.373 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 10:50:02.373 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 10:50:02.379 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 10:50:02.379 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 10:50:02.385 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 10:50:02.385 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 10:50:02.385 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 10:50:02.392 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 10:50:02.392 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 10:50:02.392 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 10:50:02.399 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 10:50:02.399 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 10:50:02.405 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 10:50:02.405 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 10:50:02.411 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 10:50:02.411 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 10:50:02.417 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 10:50:02.417 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 10:50:02.423 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 10:50:02.423 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 10:50:02.429 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 10:50:02.429 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 10:50:02.435 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 10:50:02.435 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 10:50:02.441 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 10:50:02.441 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 10:50:02.447 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 10:50:02.447 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 10:50:02.453 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 10:50:02.453 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 10:50:02.459 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 10:50:02.459 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 10:50:02.465 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 10:50:02.465 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 10:50:02.471 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 10:50:02.471 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 10:50:02.478 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 10:50:02.478 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 10:50:02.484 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 10:50:02.484 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 10:50:02.490 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 10:50:02.490 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 10:50:02.497 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 10:50:02.497 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 10:50:02.497 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 10:50:02.504 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 10:50:02.504 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 10:50:02.504 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 10:50:02.505 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 10:50:02.511 [Information] CANRegisterAccess: Read value 0xB2 from register 0x0141 (simulated)
2025-07-27 10:50:02.511 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 10:50:02.512 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 10:50:02.512 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 10:50:02.512 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 10:50:02.518 [Information] CANRegisterAccess: Read value 0x4B from register 0x0140 (simulated)
2025-07-27 10:50:02.524 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 10:50:02.530 [Information] CANRegisterAccess: Read value 0x2F from register 0x0140 (simulated)
2025-07-27 10:50:02.536 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 10:50:02.541 [Information] CANRegisterAccess: Read value 0xBD from register 0x0140 (simulated)
2025-07-27 10:50:02.541 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 10:50:02.541 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 10:50:02.545 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 10:50:02.545 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 10:50:02.556 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 10:50:02.557 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 10:50:02.557 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 10:50:02.563 [Information] VocomService: Sending data and waiting for response
2025-07-27 10:50:02.563 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 10:50:02.614 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 10:50:02.616 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 10:50:02.616 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 10:50:02.618 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 10:50:02.618 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 10:50:02.630 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 10:50:02.631 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 10:50:02.632 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 10:50:02.643 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 10:50:02.654 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 10:50:02.665 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 10:50:02.676 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 10:50:02.687 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 10:50:02.690 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 10:50:02.690 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 10:50:02.702 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 10:50:02.703 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 10:50:02.703 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 10:50:02.714 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 10:50:02.725 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 10:50:02.736 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 10:50:02.747 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 10:50:02.758 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 10:50:02.769 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 10:50:02.772 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 10:50:02.772 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 10:50:02.784 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 10:50:02.785 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 10:50:02.786 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 10:50:02.786 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 10:50:02.786 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 10:50:02.787 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 10:50:02.787 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 10:50:02.787 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 10:50:02.787 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 10:50:02.788 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 10:50:02.788 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 10:50:02.788 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 10:50:02.789 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 10:50:02.789 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 10:50:02.789 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 10:50:02.790 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 10:50:02.790 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 10:50:02.892 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 10:50:02.892 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 10:50:02.895 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 10:50:02.896 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 10:50:02.896 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 10:50:02.897 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 10:50:02.897 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 10:50:02.898 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 10:50:02.898 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 10:50:02.898 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 10:50:02.899 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 10:50:02.899 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 10:50:02.900 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 10:50:02.900 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 10:50:02.901 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 10:50:02.902 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-27 10:50:02.907 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-27 10:50:02.908 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-27 10:50:02.913 [Information] BackupService: Initializing backup service
2025-07-27 10:50:02.914 [Information] BackupService: Backup service initialized successfully
2025-07-27 10:50:02.914 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-27 10:50:02.914 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-27 10:50:02.917 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-27 10:50:02.961 [Information] BackupService: Compressing backup data
2025-07-27 10:50:03.008 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-07-27 10:50:03.009 [Information] BackupServiceFactory: Created template for category: Production
2025-07-27 10:50:03.010 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-27 10:50:03.010 [Information] BackupService: Compressing backup data
2025-07-27 10:50:03.012 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-27 10:50:03.013 [Information] BackupServiceFactory: Created template for category: Development
2025-07-27 10:50:03.013 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-27 10:50:03.014 [Information] BackupService: Compressing backup data
2025-07-27 10:50:03.015 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (446 bytes)
2025-07-27 10:50:03.016 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-27 10:50:03.016 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-27 10:50:03.017 [Information] BackupService: Compressing backup data
2025-07-27 10:50:03.018 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-27 10:50:03.018 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-27 10:50:03.018 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-27 10:50:03.019 [Information] BackupService: Compressing backup data
2025-07-27 10:50:03.024 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (451 bytes)
2025-07-27 10:50:03.024 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-27 10:50:03.024 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-27 10:50:03.025 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-27 10:50:03.025 [Information] BackupService: Compressing backup data
2025-07-27 10:50:03.027 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-07-27 10:50:03.027 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-27 10:50:03.027 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-27 10:50:03.029 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-27 10:50:03.033 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 10:50:03.035 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 10:50:03.119 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 10:50:03.120 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 10:50:03.122 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-27 10:50:03.123 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-27 10:50:03.123 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-27 10:50:03.125 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-27 10:50:03.125 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-27 10:50:03.130 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-27 10:50:03.131 [Information] App: Flash operation monitor service initialized successfully
2025-07-27 10:50:03.142 [Information] LicensingService: Initializing licensing service
2025-07-27 10:50:03.190 [Warning] LicensingService: Failed to decrypt license data (possibly corrupted or wrong key): Padding is invalid and cannot be removed.
2025-07-27 10:50:03.190 [Warning] LicensingService: License file is corrupted or incompatible, creating new trial license: License file is corrupted or was created with a different encryption key
2025-07-27 10:50:03.191 [Information] LicensingService: Deleted corrupted license file
2025-07-27 10:50:03.245 [Information] LicensingService: License information saved successfully
2025-07-27 10:50:03.249 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-27 10:50:03.250 [Information] App: Licensing service initialized successfully
2025-07-27 10:50:03.251 [Information] App: License status: Trial
2025-07-27 10:50:03.252 [Information] App: Trial period: 30 days remaining
2025-07-27 10:50:03.253 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-27 10:50:03.444 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 10:50:03.445 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 10:50:03.445 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 10:50:03.445 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 10:50:03.446 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 10:50:03.446 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 10:50:03.446 [Information] VocomService: Native USB communication service initialized
2025-07-27 10:50:03.447 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 10:50:03.447 [Information] VocomService: Connection recovery service initialized
2025-07-27 10:50:03.447 [Information] VocomService: Enhanced services initialization completed
2025-07-27 10:50:03.448 [Information] VocomService: Checking if PTT application is running
2025-07-27 10:50:03.462 [Information] VocomService: PTT application is not running
2025-07-27 10:50:03.463 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 10:50:03.514 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 10:50:03.514 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 10:50:03.514 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 10:50:03.515 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 10:50:03.515 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-27 10:50:03.515 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: 8c251568-af50-4844-bdb6-7dfe30cf25f9
2025-07-27 10:50:03.517 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-27 10:50:03.518 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 10:50:03.518 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 10:50:03.518 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 10:50:03.520 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 10:50:03.521 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 10:50:03.522 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 10:50:03.523 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 10:50:03.523 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 10:50:03.534 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 10:50:03.534 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 10:50:03.534 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 10:50:03.535 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 10:50:03.535 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 10:50:03.535 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 10:50:03.536 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 10:50:03.536 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 10:50:03.536 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 10:50:03.536 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 10:50:03.537 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 10:50:03.537 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 10:50:03.537 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 10:50:03.537 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 10:50:03.538 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 10:50:03.538 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 10:50:03.538 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 10:50:03.538 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 10:50:03.545 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 10:50:03.545 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 10:50:03.545 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 10:50:03.546 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 10:50:03.551 [Information] CANRegisterAccess: Read value 0x24 from register 0x0141 (simulated)
2025-07-27 10:50:03.557 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 10:50:03.563 [Information] CANRegisterAccess: Read value 0x72 from register 0x0141 (simulated)
2025-07-27 10:50:03.569 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 10:50:03.575 [Information] CANRegisterAccess: Read value 0x21 from register 0x0141 (simulated)
2025-07-27 10:50:03.575 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 10:50:03.576 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 10:50:03.576 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 10:50:03.582 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 10:50:03.582 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 10:50:03.588 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 10:50:03.588 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 10:50:03.589 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 10:50:03.595 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 10:50:03.595 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 10:50:03.596 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 10:50:03.602 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 10:50:03.602 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 10:50:03.608 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 10:50:03.608 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 10:50:03.614 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 10:50:03.614 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 10:50:03.621 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 10:50:03.622 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 10:50:03.628 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 10:50:03.628 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 10:50:03.635 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 10:50:03.635 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 10:50:03.642 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 10:50:03.642 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 10:50:03.649 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 10:50:03.649 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 10:50:03.655 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 10:50:03.656 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 10:50:03.662 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 10:50:03.662 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 10:50:03.668 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 10:50:03.668 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 10:50:03.674 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 10:50:03.674 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 10:50:03.681 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 10:50:03.681 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 10:50:03.688 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 10:50:03.688 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 10:50:03.695 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 10:50:03.695 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 10:50:03.701 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 10:50:03.701 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 10:50:03.708 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 10:50:03.708 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 10:50:03.708 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 10:50:03.715 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 10:50:03.715 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 10:50:03.716 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 10:50:03.716 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 10:50:03.722 [Information] CANRegisterAccess: Read value 0x62 from register 0x0141 (simulated)
2025-07-27 10:50:03.722 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 10:50:03.723 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 10:50:03.723 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 10:50:03.723 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 10:50:03.730 [Information] CANRegisterAccess: Read value 0x26 from register 0x0140 (simulated)
2025-07-27 10:50:03.736 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 10:50:03.742 [Information] CANRegisterAccess: Read value 0xC0 from register 0x0140 (simulated)
2025-07-27 10:50:03.748 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 10:50:03.754 [Information] CANRegisterAccess: Read value 0x28 from register 0x0140 (simulated)
2025-07-27 10:50:03.760 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 10:50:03.766 [Information] CANRegisterAccess: Read value 0xA1 from register 0x0140 (simulated)
2025-07-27 10:50:03.772 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 10:50:03.778 [Information] CANRegisterAccess: Read value 0x4C from register 0x0140 (simulated)
2025-07-27 10:50:03.784 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 10:50:03.790 [Information] CANRegisterAccess: Read value 0xA6 from register 0x0140 (simulated)
2025-07-27 10:50:03.796 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 10:50:03.802 [Information] CANRegisterAccess: Read value 0x01 from register 0x0140 (simulated)
2025-07-27 10:50:03.808 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 10:50:03.814 [Information] CANRegisterAccess: Read value 0x72 from register 0x0140 (simulated)
2025-07-27 10:50:03.814 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 10:50:03.814 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 10:50:03.815 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 10:50:03.815 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 10:50:03.826 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 10:50:03.826 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 10:50:03.826 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 10:50:03.827 [Information] VocomService: Sending data and waiting for response
2025-07-27 10:50:03.827 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 10:50:03.878 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 10:50:03.878 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 10:50:03.878 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 10:50:03.879 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 10:50:03.879 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 10:50:03.891 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 10:50:03.891 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 10:50:03.891 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 10:50:03.902 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 10:50:03.914 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 10:50:03.925 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 10:50:03.935 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 10:50:03.946 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 10:50:03.947 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 10:50:03.947 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 10:50:03.958 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 10:50:03.959 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 10:50:03.959 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 10:50:03.971 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 10:50:03.981 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 10:50:03.992 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 10:50:04.003 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 10:50:04.014 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 10:50:04.025 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 10:50:04.026 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 10:50:04.026 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 10:50:04.037 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 10:50:04.038 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 10:50:04.038 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 10:50:04.039 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 10:50:04.039 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 10:50:04.039 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 10:50:04.040 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 10:50:04.040 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 10:50:04.041 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 10:50:04.041 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 10:50:04.041 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 10:50:04.042 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 10:50:04.042 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 10:50:04.042 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 10:50:04.042 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 10:50:04.043 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 10:50:04.043 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 10:50:04.143 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 10:50:04.143 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 10:50:04.150 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 10:50:04.151 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 10:50:04.151 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 10:50:04.152 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 10:50:04.152 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 10:50:04.152 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 10:50:04.153 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 10:50:04.153 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 10:50:04.153 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 10:50:04.154 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 10:50:04.154 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 10:50:04.154 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 10:50:04.155 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 10:50:04.206 [Information] BackupService: Initializing backup service
2025-07-27 10:50:04.206 [Information] BackupService: Backup service initialized successfully
2025-07-27 10:50:04.258 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 10:50:04.258 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 10:50:04.260 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 10:50:04.260 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 10:50:04.313 [Information] BackupService: Getting predefined backup categories
2025-07-27 10:50:04.365 [Information] MainViewModel: Services initialized successfully
2025-07-27 10:50:04.368 [Information] MainViewModel: Scanning for Vocom devices
2025-07-27 10:50:04.369 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 10:50:04.370 [Information] VocomService: Using new enhanced device detection service
2025-07-27 10:50:04.370 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 10:50:04.371 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 10:50:04.654 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 10:50:04.654 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 10:50:04.654 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 10:50:04.655 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 10:50:04.655 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 10:50:04.655 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 10:50:04.655 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 10:50:04.656 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 10:50:05.007 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 10:50:05.008 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 10:50:05.008 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 10:50:05.009 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 10:50:05.010 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-27 10:50:05.010 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 10:50:05.011 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 10:50:05.040 [Information] VocomService: Found 3 Vocom devices
2025-07-27 10:50:05.043 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-27 10:50:56.688 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-07-27 10:50:56.689 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 10:50:56.691 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-27 10:50:56.693 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-27 10:50:57.094 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-27 10:50:57.095 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-27 10:50:57.096 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-27 10:50:57.099 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-27 10:50:57.100 [Information] ECUCommunicationService: No ECUs are connected
2025-07-27 10:50:57.101 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-27 10:50:57.101 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-27 10:50:57.102 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-27 10:50:57.102 [Information] ECUCommunicationService: No ECUs are connected
2025-07-27 10:50:57.103 [Information] VocomService: Checking if PTT application is running
2025-07-27 10:50:57.115 [Information] VocomService: PTT application is not running
2025-07-27 10:50:57.116 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 10:50:57.116 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-27 10:50:57.116 [Information] VocomService: Checking if PTT application is running
2025-07-27 10:50:57.131 [Information] VocomService: PTT application is not running
2025-07-27 10:50:57.132 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-27 10:50:57.132 [Information] VocomService: Native USB connection attempt 1/3 to WUDFPuma Driver
2025-07-27 10:50:57.132 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-27 10:50:57.133 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-27 10:50:57.133 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-27 10:50:57.133 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-27 10:50:59.134 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 10:51:00.135 [Information] VocomService: Native USB connection attempt 2/3 to WUDFPuma Driver
2025-07-27 10:51:00.135 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-27 10:51:00.135 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-27 10:51:00.136 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-27 10:51:00.136 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-27 10:51:02.135 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 10:51:03.135 [Information] VocomService: Native USB connection attempt 3/3 to WUDFPuma Driver
2025-07-27 10:51:03.136 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-27 10:51:03.136 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2 - The system cannot find the file specified. The device may not be properly connected or drivers may be missing.
2025-07-27 10:51:03.136 [Information] NativeVocomUSBCommunication: Trying alternative device access methods
2025-07-27 10:51:03.137 [Information] NativeVocomUSBCommunication: Waiting for device to become available...
2025-07-27 10:51:05.138 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 10:51:05.138 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 10:51:05.139 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-27 10:51:05.139 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 10:51:05.139 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-27 10:51:05.140 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 10:51:05.140 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 10:51:05.141 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 10:51:05.141 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 10:51:05.141 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 10:51:05.142 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 10:51:05.142 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 10:51:05.142 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 10:51:05.143 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 10:51:05.143 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 10:51:05.143 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 10:51:05.143 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 10:51:05.144 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 10:51:05.144 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 10:51:05.145 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 10:51:05.145 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 10:51:05.145 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 10:51:05.146 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 10:51:05.146 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 10:51:05.146 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 10:51:05.147 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 10:51:05.147 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 10:51:05.148 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
