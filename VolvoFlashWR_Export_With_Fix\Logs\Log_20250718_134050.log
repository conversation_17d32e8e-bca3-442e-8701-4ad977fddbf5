Log started at 7/18/2025 1:40:50 PM
2025-07-18 13:40:50.744 [Information] LoggingService: Logging service initialized
2025-07-18 13:40:50.763 [Information] App: Starting integrated application initialization
2025-07-18 13:40:50.765 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-18 13:40:50.768 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-18 13:40:50.771 [Information] IntegratedStartupService: Setting up application environment
2025-07-18 13:40:50.772 [Information] IntegratedStartupService: Application environment setup completed
2025-07-18 13:40:50.774 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-18 13:40:50.776 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-18 13:40:50.778 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-18 13:40:50.784 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-18 13:40:50.792 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-18 13:40:50.798 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 13:40:50.803 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 13:40:50.807 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-18 13:40:50.812 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-18 13:40:50.818 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-18 13:40:50.822 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-18 13:40:50.824 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-18 13:40:50.826 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-18 13:40:50.826 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-18 13:40:50.829 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-18 13:40:50.830 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-18 13:40:50.831 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-18 13:40:50.831 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-18 13:40:50.832 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-18 13:40:50.834 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-18 13:40:50.836 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 13:40:50.836 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 13:40:50.837 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-18 13:40:50.837 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-18 13:40:50.837 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-18 13:40:50.838 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-18 13:40:50.845 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-18 13:40:50.846 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-18 13:40:50.847 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-18 13:40:50.852 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-18 13:40:50.852 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-18 13:40:50.854 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-18 13:40:50.855 [Information] LibraryExtractor: Starting library extraction process
2025-07-18 13:40:50.858 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-18 13:40:50.862 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-18 13:40:50.864 [Information] LibraryExtractor: Copying system libraries
2025-07-18 13:40:50.873 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-18 13:40:50.883 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-18 13:41:20.894 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-18 13:41:21.896 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-18 13:41:51.898 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-18 13:41:51.899 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 13:42:21.902 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-18 13:42:22.902 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-18 13:42:52.906 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-18 13:42:52.907 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 13:43:22.908 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-18 13:43:23.909 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-18 13:43:53.912 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-18 13:43:53.913 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-18 13:43:53.913 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-18 13:43:53.916 [Information] LibraryExtractor: Verifying library extraction
2025-07-18 13:43:53.917 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-18 13:43:53.917 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-18 13:43:53.917 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-18 13:43:53.918 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-18 13:43:53.918 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-18 13:43:53.921 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-18 13:43:53.923 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-18 13:43:53.924 [Information] DependencyManager: Initializing dependency manager
2025-07-18 13:43:53.925 [Information] DependencyManager: Setting up library search paths
2025-07-18 13:43:53.927 [Information] DependencyManager: Added library path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-18 13:43:53.928 [Information] DependencyManager: Added driver path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-18 13:43:53.928 [Information] DependencyManager: Added application path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-18 13:43:53.928 [Information] DependencyManager: Updated PATH environment variable
2025-07-18 13:43:53.930 [Information] DependencyManager: Verifying required directories
2025-07-18 13:43:53.930 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-18 13:43:53.930 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-18 13:43:53.931 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-18 13:43:53.931 [Information] DependencyManager: ✓ Directory exists: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-18 13:43:53.932 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-18 13:43:53.961 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-18 13:43:54.005 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-18 13:43:54.008 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcr120.dll: Error 193
2025-07-18 13:43:54.008 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr120.dll
2025-07-18 13:43:54.029 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-18 13:43:54.031 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-18 13:43:54.032 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp120.dll: Error 193
2025-07-18 13:43:54.032 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp120.dll
2025-07-18 13:43:54.032 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-18 13:43:54.033 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-18 13:43:54.045 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-18 13:43:54.061 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll from C:\Windows\system32\msvcp140.dll: Error 193
2025-07-18 13:43:54.062 [Warning] DependencyManager: Architecture mismatch detected for msvcp140.dll. Expected: x64
2025-07-18 13:43:54.063 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp140.dll
2025-07-18 13:43:54.064 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll: Error 193
2025-07-18 13:43:54.064 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp140.dll
2025-07-18 13:43:54.064 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-18 13:43:54.066 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-18 13:43:54.067 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 13:43:54.067 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-18 13:43:54.068 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 13:43:54.069 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-18 13:43:54.069 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-18 13:43:54.070 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-18 13:43:54.071 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-18 13:43:54.071 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-18 13:43:54.072 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-18 13:43:54.072 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-18 13:43:54.073 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-18 13:43:54.073 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-18 13:43:54.074 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-18 13:43:54.074 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-18 13:43:54.074 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-18 13:43:54.075 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-18 13:43:54.076 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-18 13:43:54.076 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-18 13:43:54.076 [Information] DependencyManager: VC++ Redistributable library loading: 1/14 (7.1%) libraries loaded
2025-07-18 13:43:54.077 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-18 13:43:54.078 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-18 13:43:54.131 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-18 13:43:54.263 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-18 13:43:54.264 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-18 13:43:54.354 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-18 13:43:54.356 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-18 13:43:54.642 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-18 13:43:54.643 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-18 13:43:54.759 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-18 13:43:54.759 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-18 13:43:54.829 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-18 13:43:54.830 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-18 13:43:54.831 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-18 13:43:54.831 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-18 13:43:54.832 [Warning] DependencyManager: Failed to load Critical library msvcr120.dll: Error 193
2025-07-18 13:43:54.832 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-18 13:43:54.833 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-18 13:43:54.833 [Warning] DependencyManager: Failed to load Critical library msvcp120.dll: Error 193
2025-07-18 13:43:54.834 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-18 13:43:54.834 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-18 13:43:54.837 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-18 13:43:54.837 [Warning] DependencyManager: Architecture incompatible library skipped: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-18 13:43:54.838 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-18 13:43:54.839 [Information] DependencyManager: Setting up environment variables
2025-07-18 13:43:54.839 [Information] DependencyManager: Environment variables configured
2025-07-18 13:43:54.840 [Information] DependencyManager: Verifying library loading status
2025-07-18 13:43:55.160 [Information] DependencyManager: Library loading verification: 4/11 (36.4%) critical libraries loaded
2025-07-18 13:43:55.161 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-18 13:43:55.161 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-18 13:43:55.164 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-18 13:43:55.166 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-18 13:43:55.169 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-18 13:43:55.171 [Information] IntegratedStartupService: Verifying system readiness
2025-07-18 13:43:55.172 [Information] IntegratedStartupService: System readiness verification passed
2025-07-18 13:43:55.172 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-18 13:43:55.174 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-18 13:43:55.174 [Information] IntegratedStartupService: Application Path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-18 13:43:55.174 [Information] IntegratedStartupService: Libraries Path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-18 13:43:55.175 [Information] IntegratedStartupService: Drivers Path: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-18 13:43:55.175 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-18 13:43:55.175 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-18 13:43:55.176 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-18 13:43:55.176 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-18 13:43:55.176 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-18 13:43:55.177 [Information] App: Integrated startup completed successfully
2025-07-18 13:43:55.179 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-18 13:43:55.418 [Information] App: Initializing application services
2025-07-18 13:43:55.420 [Information] AppConfigurationService: Initializing configuration service
2025-07-18 13:43:55.420 [Information] AppConfigurationService: Created configuration directory: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-18 13:43:55.471 [Information] AppConfigurationService: Configuration loaded from D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-18 13:43:55.471 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-18 13:43:55.472 [Information] App: Configuration service initialized successfully
2025-07-18 13:43:55.474 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-18 13:43:55.474 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-18 13:43:55.479 [Information] App: Environment variable exists: True, not 'false': False
2025-07-18 13:43:55.479 [Information] App: Final useDummyImplementations value: False
2025-07-18 13:43:55.479 [Information] App: Updating config to NOT use dummy implementations
2025-07-18 13:43:55.497 [Information] AppConfigurationService: Configuration saved to D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-18 13:43:55.498 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-18 13:43:55.499 [Information] App: usePatchedImplementation flag is: True
2025-07-18 13:43:55.499 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-18 13:43:55.499 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-18 13:43:55.500 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-18 13:43:55.500 [Information] App: verboseLogging flag is: True
2025-07-18 13:43:55.502 [Information] App: Verifying real hardware requirements...
2025-07-18 13:43:55.503 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-18 13:43:55.503 [Information] App: ✓ Found critical library: apci.dll
2025-07-18 13:43:55.503 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-18 13:43:55.504 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-18 13:43:55.505 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-18 13:43:55.505 [Information] App: Phoenix Diag not found at: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021 (optional)
2025-07-18 13:43:55.505 [Information] App: ✓ Found Vocom driver config: D:\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-18 13:43:55.506 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-18 13:43:55.517 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-18 13:43:55.519 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-18 13:43:55.520 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-18 13:43:55.521 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-18 13:43:55.525 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
