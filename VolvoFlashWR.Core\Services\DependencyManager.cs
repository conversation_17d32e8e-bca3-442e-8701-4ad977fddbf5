using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Manages dependencies and libraries required for Vocom adapter communication
    /// Automatically handles library loading and dependency resolution with architecture awareness
    /// </summary>
    public class DependencyManager
    {
        private readonly ILoggingService _logger;
        private readonly string _applicationPath;
        private readonly string _librariesPath;
        private readonly string _driversPath;
        private bool _isInitialized = false;
        private readonly bool _is64BitProcess;
        private readonly string _architectureString;

        // Windows API imports for library management
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool SetDllDirectory(string lpPathName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool AddDllDirectory(string newDirectory);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool IsWow64Process(IntPtr hProcess, out bool wow64Process);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetCurrentProcess();

        // Critical libraries for Vocom communication
        private static readonly string[] CriticalLibraries = new[]
        {
            "WUDFPuma.dll",
            "apci.dll",
            "apcidb.dll",
            "Volvo.ApciPlus.dll",
            "Volvo.ApciPlusData.dll",
            "PhoenixGeneral.dll",
            "msvcr120.dll",
            "msvcp120.dll",
            "msvcr140.dll",
            "msvcp140.dll",
            "vcruntime140.dll"
        };

        // Visual C++ Redistributable libraries - Enhanced for x64 support
        private static readonly string[] VCRedistLibraries = new[]
        {
            "msvcr120.dll",
            "msvcp120.dll",
            "msvcr140.dll",
            "msvcp140.dll",
            "vcruntime140.dll",
            "api-ms-win-crt-runtime-l1-1-0.dll",
            "api-ms-win-crt-heap-l1-1-0.dll",
            "api-ms-win-crt-string-l1-1-0.dll",
            "api-ms-win-crt-stdio-l1-1-0.dll",
            "api-ms-win-crt-math-l1-1-0.dll",
            "api-ms-win-crt-locale-l1-1-0.dll",
            "api-ms-win-crt-convert-l1-1-0.dll",
            "api-ms-win-crt-time-l1-1-0.dll",
            "api-ms-win-crt-filesystem-l1-1-0.dll"
        };

        public DependencyManager(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory;
            _librariesPath = Path.Combine(_applicationPath, "Libraries");
            _driversPath = Path.Combine(_applicationPath, "Drivers", "Vocom");

            // Detect architecture
            _is64BitProcess = Environment.Is64BitProcess;
            _architectureString = _is64BitProcess ? "x64" : "x86";

            _logger.LogInformation($"Dependency manager initialized for {_architectureString} architecture", "DependencyManager");
        }

        /// <summary>
        /// Initializes the dependency manager and loads all required libraries
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            if (_isInitialized)
                return true;

            try
            {
                _logger.LogInformation("Initializing dependency manager", "DependencyManager");

                // Step 1: Setup library search paths
                SetupLibraryPaths();

                // Step 2: Verify critical directories exist
                await VerifyDirectoriesAsync();

                // Step 3: Load Visual C++ Redistributables first
                await LoadVCRedistLibrariesAsync();

                // Step 4: Load critical Vocom libraries
                await LoadCriticalLibrariesAsync();

                // Step 5: Setup environment variables
                SetupEnvironmentVariables();

                // Step 6: Verify library loading
                await VerifyLibraryLoadingAsync();

                _isInitialized = true;
                _logger.LogInformation("Dependency manager initialized successfully", "DependencyManager");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to initialize dependency manager: {ex.Message}", "DependencyManager", ex);
                return false;
            }
        }

        private void SetupLibraryPaths()
        {
            try
            {
                _logger.LogInformation("Setting up library search paths", "DependencyManager");

                // Add our libraries directory to DLL search path
                if (Directory.Exists(_librariesPath))
                {
                    SetDllDirectory(_librariesPath);
                    AddDllDirectory(_librariesPath);
                    _logger.LogInformation($"Added library path: {_librariesPath}", "DependencyManager");
                }

                // Add drivers directory to DLL search path
                if (Directory.Exists(_driversPath))
                {
                    AddDllDirectory(_driversPath);
                    _logger.LogInformation($"Added driver path: {_driversPath}", "DependencyManager");
                }

                // Add application directory
                AddDllDirectory(_applicationPath);
                _logger.LogInformation($"Added application path: {_applicationPath}", "DependencyManager");

                // Update PATH environment variable
                string currentPath = Environment.GetEnvironmentVariable("PATH") ?? "";
                string newPath = $"{_librariesPath};{_driversPath};{_applicationPath};{currentPath}";
                Environment.SetEnvironmentVariable("PATH", newPath);
                _logger.LogInformation("Updated PATH environment variable", "DependencyManager");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error setting up library paths: {ex.Message}", "DependencyManager");
            }
        }

        private async Task VerifyDirectoriesAsync()
        {
            _logger.LogInformation("Verifying required directories", "DependencyManager");

            var requiredDirectories = new[]
            {
                _librariesPath,
                _driversPath,
                Path.Combine(_librariesPath, "System"),
                Path.Combine(_applicationPath, "Config")
            };

            foreach (string directory in requiredDirectories)
            {
                if (!Directory.Exists(directory))
                {
                    _logger.LogWarning($"Required directory not found: {directory}", "DependencyManager");
                    try
                    {
                        Directory.CreateDirectory(directory);
                        _logger.LogInformation($"Created directory: {directory}", "DependencyManager");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Failed to create directory {directory}: {ex.Message}", "DependencyManager");
                    }
                }
                else
                {
                    _logger.LogInformation($"✓ Directory exists: {directory}", "DependencyManager");
                }
            }

            await Task.CompletedTask;
        }

        private async Task LoadVCRedistLibrariesAsync()
        {
            _logger.LogInformation("Loading Visual C++ Redistributable libraries", "DependencyManager");

            int loadedCount = 0;
            int totalCount = VCRedistLibraries.Length;

            foreach (string library in VCRedistLibraries)
            {
                bool loaded = await LoadLibraryAsync(library, "VC++ Redistributable");
                if (loaded)
                {
                    loadedCount++;
                }
                else
                {
                    // For missing VC++ libraries, log as warning but continue
                    _logger.LogWarning($"VC++ Redistributable library not found: {library}", "DependencyManager");
                }
            }

            _logger.LogInformation($"VC++ Redistributable library loading: {loadedCount}/{totalCount} ({(loadedCount * 100.0 / totalCount):F1}%) libraries loaded", "DependencyManager");

            if (loadedCount < (totalCount * 0.5)) // Less than 50% loaded
            {
                _logger.LogWarning("Low VC++ Redistributable library loading success rate - some functionality may be limited", "DependencyManager");
            }
        }

        private async Task LoadCriticalLibrariesAsync()
        {
            _logger.LogInformation("Loading critical Vocom libraries", "DependencyManager");

            foreach (string library in CriticalLibraries)
            {
                await LoadLibraryAsync(library, "Critical");
            }
        }

        private async Task<bool> LoadLibraryAsync(string libraryName, string category)
        {
            try
            {
                // First try architecture-aware loading
                bool loaded = await LoadLibraryWithArchitectureAwarenessAsync(libraryName, category);
                if (loaded)
                {
                    return true;
                }

                // Fallback to standard loading
                return await LoadLibraryStandardAsync(libraryName, category);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Exception loading {category} library {libraryName}: {ex.Message}", "DependencyManager");
                return false;
            }
        }

        private async Task<bool> LoadLibraryWithArchitectureAwarenessAsync(string libraryName, string category)
        {
            try
            {
                // Create architecture-specific search paths
                var searchPaths = new List<string>();

                // Add architecture-specific library paths
                string archLibPath = Path.Combine(_librariesPath, _architectureString);
                if (Directory.Exists(archLibPath))
                {
                    searchPaths.Add(Path.Combine(archLibPath, libraryName));
                }

                // Add standard paths with architecture preference
                searchPaths.AddRange(new[]
                {
                    Path.Combine(_librariesPath, libraryName),
                    Path.Combine(_driversPath, libraryName),
                    Path.Combine(_applicationPath, libraryName)
                });

                // Add system paths based on architecture
                if (_is64BitProcess)
                {
                    searchPaths.Add(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.System), libraryName));
                    searchPaths.Add(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.SystemX86), libraryName));
                }
                else
                {
                    searchPaths.Add(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.SystemX86), libraryName));
                    searchPaths.Add(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.System), libraryName));
                }

                foreach (string path in searchPaths)
                {
                    if (File.Exists(path))
                    {
                        // Check if the library is compatible with current architecture
                        if (await IsLibraryArchitectureCompatibleAsync(path))
                        {
                            IntPtr handle = LoadLibrary(path);
                            if (handle != IntPtr.Zero)
                            {
                                _logger.LogInformation($"✓ Loaded {category} library: {libraryName} from {path} ({_architectureString})", "DependencyManager");
                                return true;
                            }
                            else
                            {
                                int errorCode = Marshal.GetLastWin32Error();
                                _logger.LogWarning($"Failed to load {category} library {libraryName} from {path}: Error {errorCode}", "DependencyManager");

                                // Analyze the error
                                if (errorCode == 193) // ERROR_BAD_EXE_FORMAT
                                {
                                    _logger.LogWarning($"Architecture mismatch detected for {libraryName}. Expected: {_architectureString}", "DependencyManager");
                                }
                            }
                        }
                        else
                        {
                            _logger.LogWarning($"Architecture incompatible library skipped: {path}", "DependencyManager");
                        }
                    }
                }

                await Task.CompletedTask;
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error in architecture-aware loading for {libraryName}: {ex.Message}", "DependencyManager");
                await Task.CompletedTask;
                return false;
            }
        }

        private async Task<bool> LoadLibraryStandardAsync(string libraryName, string category)
        {
            try
            {
                // Standard search paths as fallback
                var searchPaths = new[]
                {
                    Path.Combine(_librariesPath, libraryName),
                    Path.Combine(_driversPath, libraryName),
                    Path.Combine(_applicationPath, libraryName),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.System), libraryName),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.SystemX86), libraryName)
                };

                string foundPath = null;
                foreach (string path in searchPaths)
                {
                    if (File.Exists(path))
                    {
                        foundPath = path;
                        break;
                    }
                }

                if (foundPath == null)
                {
                    _logger.LogWarning($"{category} library not found: {libraryName}", "DependencyManager");
                    return false;
                }

                // Try to load the library
                IntPtr handle = LoadLibrary(foundPath);
                if (handle == IntPtr.Zero)
                {
                    int errorCode = Marshal.GetLastWin32Error();
                    _logger.LogWarning($"Failed to load {category} library {libraryName}: Error {errorCode}", "DependencyManager");
                    return false;
                }

                _logger.LogInformation($"✓ Loaded {category} library: {libraryName} from {foundPath}", "DependencyManager");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Exception in standard loading for {libraryName}: {ex.Message}", "DependencyManager");
                return false;
            }
            finally
            {
                await Task.CompletedTask;
            }
        }

        private async Task<bool> IsLibraryArchitectureCompatibleAsync(string libraryPath)
        {
            try
            {
                // Read the PE header to determine architecture
                using (var fileStream = new FileStream(libraryPath, FileMode.Open, FileAccess.Read))
                using (var reader = new BinaryReader(fileStream))
                {
                    // Check DOS header
                    if (reader.ReadUInt16() != 0x5A4D) // "MZ"
                        return false;

                    // Jump to PE header
                    fileStream.Seek(60, SeekOrigin.Begin);
                    int peHeaderOffset = reader.ReadInt32();
                    fileStream.Seek(peHeaderOffset, SeekOrigin.Begin);

                    // Check PE signature
                    if (reader.ReadUInt32() != 0x00004550) // "PE\0\0"
                        return false;

                    // Read machine type
                    ushort machineType = reader.ReadUInt16();

                    // Determine if compatible
                    bool is64BitLibrary = (machineType == 0x8664); // IMAGE_FILE_MACHINE_AMD64
                    bool is32BitLibrary = (machineType == 0x014c); // IMAGE_FILE_MACHINE_I386

                    bool compatible = (_is64BitProcess && is64BitLibrary) || (!_is64BitProcess && is32BitLibrary);

                    if (!compatible)
                    {
                        string libArch = is64BitLibrary ? "x64" : (is32BitLibrary ? "x86" : "unknown");
                        _logger.LogDebug($"Architecture mismatch: Library {Path.GetFileName(libraryPath)} is {libArch}, process is {_architectureString}", "DependencyManager");
                    }

                    return compatible;
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug($"Error checking architecture compatibility for {libraryPath}: {ex.Message}", "DependencyManager");
                // If we can't determine, assume it's compatible and let LoadLibrary handle it
                return true;
            }
            finally
            {
                await Task.CompletedTask;
            }
        }

        private void SetupEnvironmentVariables()
        {
            try
            {
                _logger.LogInformation("Setting up environment variables", "DependencyManager");

                // Set library paths
                Environment.SetEnvironmentVariable("APCI_LIBRARY_PATH", _librariesPath);
                Environment.SetEnvironmentVariable("VOCOM_DRIVER_PATH", _driversPath);
                Environment.SetEnvironmentVariable("USE_PATCHED_IMPLEMENTATION", "true");
                Environment.SetEnvironmentVariable("PHOENIX_VOCOM_ENABLED", "true");
                Environment.SetEnvironmentVariable("VERBOSE_LOGGING", "true");
                Environment.SetEnvironmentVariable("PROCESS_ARCHITECTURE", _architectureString);

                _logger.LogInformation("Environment variables configured", "DependencyManager");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error setting environment variables: {ex.Message}", "DependencyManager");
            }
        }

        private async Task VerifyLibraryLoadingAsync()
        {
            _logger.LogInformation("Verifying library loading status", "DependencyManager");

            int loadedCount = 0;
            int totalCount = CriticalLibraries.Length;

            foreach (string library in CriticalLibraries)
            {
                if (await IsLibraryLoadedAsync(library))
                {
                    loadedCount++;
                }
            }

            double successRate = (double)loadedCount / totalCount * 100;
            _logger.LogInformation($"Library loading verification: {loadedCount}/{totalCount} ({successRate:F1}%) critical libraries loaded", "DependencyManager");

            if (successRate < 70)
            {
                _logger.LogWarning("Low library loading success rate - some functionality may be limited", "DependencyManager");
            }
        }

        private async Task<bool> IsLibraryLoadedAsync(string libraryName)
        {
            try
            {
                // Try to find the library in loaded modules
                var processes = System.Diagnostics.Process.GetProcessesByName(System.Diagnostics.Process.GetCurrentProcess().ProcessName);
                foreach (var process in processes)
                {
                    try
                    {
                        foreach (System.Diagnostics.ProcessModule module in process.Modules)
                        {
                            if (module.ModuleName.Equals(libraryName, StringComparison.OrdinalIgnoreCase))
                            {
                                return true;
                            }
                        }
                    }
                    catch
                    {
                        // Ignore access denied errors
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug($"Error checking if library {libraryName} is loaded: {ex.Message}", "DependencyManager");
            }

            await Task.CompletedTask;
            return false;
        }

        /// <summary>
        /// Gets the status of dependency loading
        /// </summary>
        public async Task<DependencyStatus> GetStatusAsync()
        {
            var status = new DependencyStatus
            {
                IsInitialized = _isInitialized,
                LibrariesPath = _librariesPath,
                DriversPath = _driversPath,
                CriticalLibrariesFound = new List<string>(),
                MissingLibraries = new List<string>()
            };

            foreach (string library in CriticalLibraries)
            {
                var searchPaths = new[]
                {
                    Path.Combine(_librariesPath, library),
                    Path.Combine(_driversPath, library),
                    Path.Combine(_applicationPath, library)
                };

                bool found = false;
                foreach (string path in searchPaths)
                {
                    if (File.Exists(path))
                    {
                        status.CriticalLibrariesFound.Add($"{library} ({path})");
                        found = true;
                        break;
                    }
                }

                if (!found)
                {
                    status.MissingLibraries.Add(library);
                }
            }

            await Task.CompletedTask;
            return status;
        }
    }

    /// <summary>
    /// Represents the status of dependency loading
    /// </summary>
    public class DependencyStatus
    {
        public bool IsInitialized { get; set; }
        public string LibrariesPath { get; set; } = string.Empty;
        public string DriversPath { get; set; } = string.Empty;
        public List<string> CriticalLibrariesFound { get; set; } = new();
        public List<string> MissingLibraries { get; set; } = new();
    }
}
