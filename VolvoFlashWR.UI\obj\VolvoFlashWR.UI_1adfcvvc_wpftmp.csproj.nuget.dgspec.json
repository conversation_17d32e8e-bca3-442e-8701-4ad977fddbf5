{"format": 1, "restore": {"D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.UI\\VolvoFlashWR.UI.csproj": {}}, "projects": {"D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Communication\\VolvoFlashWR.Communication.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Communication\\VolvoFlashWR.Communication.csproj", "projectName": "VolvoFlashWR.Communication", "projectPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Communication\\VolvoFlashWR.Communication.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Communication\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\VolvoFlashWR.Core.csproj": {"projectPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\VolvoFlashWR.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"HidSharp": {"target": "Package", "version": "[2.1.0, )"}, "InTheHand.Net.Bluetooth": {"target": "Package", "version": "[4.1.40, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Win32.Registry": {"target": "Package", "version": "[5.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "SharpCompress": {"target": "Package", "version": "[0.37.2, )"}, "System.Buffers": {"target": "Package", "version": "[4.5.1, )"}, "System.Device.Gpio": {"target": "Package", "version": "[3.2.0, )"}, "System.Diagnostics.Process": {"target": "Package", "version": "[4.3.0, )"}, "System.IO.Compression": {"target": "Package", "version": "[4.3.0, )"}, "System.IO.Pipelines": {"target": "Package", "version": "[8.0.0, )"}, "System.IO.Pipes": {"target": "Package", "version": "[4.3.0, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.4, )"}, "System.Management": {"target": "Package", "version": "[9.0.5, )"}, "System.Memory": {"target": "Package", "version": "[4.5.5, )"}, "System.Net.NetworkInformation": {"target": "Package", "version": "[4.3.0, )"}, "System.Runtime.InteropServices": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.408/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\VolvoFlashWR.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\VolvoFlashWR.Core.csproj", "projectName": "VolvoFlashWR.Core", "projectPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\VolvoFlashWR.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Buffers": {"target": "Package", "version": "[4.5.1, )"}, "System.Collections.Immutable": {"target": "Package", "version": "[8.0.0, )"}, "System.Management": {"target": "Package", "version": "[9.0.5, )"}, "System.Memory": {"target": "Package", "version": "[4.5.5, )"}, "System.Threading.Tasks.Extensions": {"target": "Package", "version": "[4.5.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.408/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.UI\\VolvoFlashWR.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.UI\\VolvoFlashWR.UI.csproj", "projectName": "VolvoFlashWR.UI", "projectPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.UI\\VolvoFlashWR.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.UI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Communication\\VolvoFlashWR.Communication.csproj": {"projectPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Communication\\VolvoFlashWR.Communication.csproj"}, "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\VolvoFlashWR.Core.csproj": {"projectPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\VolvoFlashWR.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"LiveChartsCore.SkiaSharpView.WPF": {"target": "Package", "version": "[2.0.0-rc5.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Xaml.Behaviors.Wpf": {"target": "Package", "version": "[1.1.77, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Threading.Tasks.Extensions": {"target": "Package", "version": "[4.5.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.408/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}