using System;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Factory for creating Vocom services with patched implementation
    /// </summary>
    public class PatchedVocomServiceFactory
    {
        private readonly ILoggingService _logger;
        private static bool _loggedAssemblyInfo = false;

        /// <summary>
        /// Initializes a new instance of the PatchedVocomServiceFactory class
        /// </summary>
        /// <param name="logger">The logging service</param>
        public PatchedVocomServiceFactory(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            _logger.LogInformation("*** PatchedVocomServiceFactory initialized ***", "PatchedVocomServiceFactory");

            // Log assembly information
            if (!_loggedAssemblyInfo)
            {
                LogAssemblyInfo();
                _loggedAssemblyInfo = true;
            }

            // Create a file to indicate that the patched factory was created
            try
            {
                string markerPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "patched_factory_created.txt");
                File.WriteAllText(markerPath, $"PatchedVocomServiceFactory created at {DateTime.Now}");
                _logger.LogInformation($"Created marker file at {markerPath}", "PatchedVocomServiceFactory");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to create marker file: {ex.Message}", "PatchedVocomServiceFactory", ex);
            }
        }

        private void LogAssemblyInfo()
        {
            try
            {
                Assembly assembly = Assembly.GetExecutingAssembly();
                _logger.LogInformation($"Assembly: {assembly.FullName}", "PatchedVocomServiceFactory");
                _logger.LogInformation($"Assembly location: {assembly.Location}", "PatchedVocomServiceFactory");

                // Log all types in the assembly that contain "Patch" in their name
                StringBuilder sb = new StringBuilder();
                sb.AppendLine("Patched types in assembly:");

                foreach (Type type in assembly.GetTypes())
                {
                    if (type.Name.Contains("Patch") || type.Name.Contains("patch"))
                    {
                        sb.AppendLine($"- {type.FullName}");
                    }
                }

                _logger.LogInformation(sb.ToString(), "PatchedVocomServiceFactory");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error logging assembly info: {ex.Message}", "PatchedVocomServiceFactory", ex);
            }
        }

        /// <summary>
        /// Creates and initializes a new Vocom service with default settings
        /// </summary>
        /// <returns>The initialized Vocom service</returns>
        public async Task<IVocomService> CreateServiceAsync()
        {
            try
            {
                _logger.LogInformation("PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings", "PatchedVocomServiceFactory");
                _logger.LogInformation("PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used", "PatchedVocomServiceFactory");

                // Perform system compatibility checks
                CheckArchitectureCompatibility();
                CheckRuntimeDependencies();

                // Check if we should use dummy implementations
                string dummyEnv = Environment.GetEnvironmentVariable("USE_DUMMY_IMPLEMENTATIONS");
                bool useDummyImplementations = !string.IsNullOrEmpty(dummyEnv) && dummyEnv.ToLower() != "false";

                if (useDummyImplementations)
                {
                    _logger.LogInformation("Using dummy implementations as specified by environment variable", "PatchedVocomServiceFactory");
                    var dummyService = new DummyVocomService(_logger);
                    await dummyService.InitializeAsync();
                    return dummyService;
                }

                // Check if Phoenix Vocom adapter is enabled
                string phoenixEnabledEnv = Environment.GetEnvironmentVariable("PHOENIX_VOCOM_ENABLED");
                _logger.LogInformation($"PHOENIX_VOCOM_ENABLED environment variable: '{phoenixEnabledEnv}'", "PatchedVocomServiceFactory");
                bool usePhoenixAdapter = !string.IsNullOrEmpty(phoenixEnabledEnv) && phoenixEnabledEnv.ToLower() != "false";
                _logger.LogInformation($"usePhoenixAdapter flag: {usePhoenixAdapter}", "PatchedVocomServiceFactory");

                if (usePhoenixAdapter)
                {
                    // Try Phoenix adapter first
                    _logger.LogInformation("Phoenix Vocom adapter enabled, attempting to create it", "PatchedVocomServiceFactory");

                    try
                    {
                        var phoenixAdapter = new PhoenixVocomAdapter(_logger);
                        bool phoenixInitialized = await phoenixAdapter.InitializeAsync();

                        if (phoenixInitialized)
                        {
                            _logger.LogInformation("Phoenix Vocom adapter initialized successfully", "PatchedVocomServiceFactory");

                            // Create WiFi and Bluetooth services
                            var wifiService = new WiFiCommunicationService(_logger);
                            var bluetoothService = new BluetoothCommunicationService(_logger);

                            // Create the service with default settings
                            var service = new VocomService(_logger);

                            // Initialize the service with dependencies using modern USB service
                            var usbService = new ModernUSBCommunicationService(_logger);
                            bool initialized = await service.InitializeAsync(phoenixAdapter, usbService, wifiService, bluetoothService);
                            if (initialized)
                            {
                                _logger.LogInformation("Vocom service created and initialized successfully with Phoenix adapter", "PatchedVocomServiceFactory");
                                return service;
                            }
                            else
                            {
                                _logger.LogWarning("Phoenix adapter initialized but service initialization failed", "PatchedVocomServiceFactory");
                            }
                        }
                        else
                        {
                            _logger.LogWarning("Phoenix adapter initialization failed", "PatchedVocomServiceFactory");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Exception during Phoenix adapter creation: {ex.Message}", "PatchedVocomServiceFactory", ex);
                    }

                    _logger.LogWarning("Phoenix adapter initialization failed, falling back to patched driver", "PatchedVocomServiceFactory");
                }

                // Try to create a patched Vocom device driver
                _logger.LogInformation("Attempting to create patched Vocom device driver", "PatchedVocomServiceFactory");
                var patchedDriver = new PatchedVocomDeviceDriver(_logger);
                bool patchedInitialized = await patchedDriver.InitializeAsync();

                if (patchedInitialized)
                {
                    _logger.LogInformation("Patched Vocom device driver initialized successfully", "PatchedVocomServiceFactory");

                    // Create WiFi and Bluetooth services
                    var wifiService = new WiFiCommunicationService(_logger);
                    var bluetoothService = new BluetoothCommunicationService(_logger);

                    // Create the service with default settings
                    var service = new VocomService(_logger);

                    // Initialize the service with dependencies using modern USB service
                    var usbService = new ModernUSBCommunicationService(_logger);
                    bool initialized = await service.InitializeAsync(patchedDriver, usbService, wifiService, bluetoothService);
                    if (!initialized)
                    {
                        _logger.LogError("Failed to initialize Vocom service with patched driver", "PatchedVocomServiceFactory");
                        return null;
                    }

                    _logger.LogInformation("Vocom service created and initialized successfully with patched driver", "PatchedVocomServiceFactory");
                    return service;
                }

                // If patched driver fails, try the standard Vocom driver
                _logger.LogWarning("Patched driver initialization failed, falling back to standard driver", "PatchedVocomServiceFactory");
                var vocomDriver = new VocomDriver(_logger);
                bool driverInitialized = await vocomDriver.InitializeAsync();

                if (!driverInitialized)
                {
                    _logger.LogWarning("Failed to initialize standard Vocom driver, falling back to device driver", "PatchedVocomServiceFactory");

                    // Try the device driver as fallback
                    var deviceDriver = new VocomDeviceDriver(_logger);
                    bool deviceDriverInitialized = await deviceDriver.InitializeAsync();

                    if (!deviceDriverInitialized)
                    {
                        _logger.LogError("Failed to initialize Vocom device driver, falling back to dummy implementation", "PatchedVocomServiceFactory");

                        // Fall back to dummy implementation as last resort
                        var dummyService = new DummyVocomService(_logger);
                        await dummyService.InitializeAsync();
                        return dummyService;
                    }

                    // Create WiFi and Bluetooth services
                    var wifiService = new WiFiCommunicationService(_logger);
                    var bluetoothService = new BluetoothCommunicationService(_logger);

                    // Create the service with default settings
                    var service = new VocomService(_logger);

                    // Initialize the service with dependencies using modern USB service
                    bool initialized = await service.InitializeAsync(deviceDriver, new ModernUSBCommunicationService(_logger), wifiService, bluetoothService);
                    if (!initialized)
                    {
                        _logger.LogError("Failed to initialize Vocom service", "PatchedVocomServiceFactory");
                        return null;
                    }

                    _logger.LogInformation("Vocom service created and initialized successfully with device driver", "PatchedVocomServiceFactory");
                    return service;
                }
                else
                {
                    // Create WiFi and Bluetooth services
                    var wifiService = new WiFiCommunicationService(_logger);
                    var bluetoothService = new BluetoothCommunicationService(_logger);

                    // Create the service with default settings
                    var service = new VocomService(_logger);

                    // Initialize the service with dependencies using modern USB service
                    var usbService = new ModernUSBCommunicationService(_logger);
                    bool initialized = await service.InitializeAsync(vocomDriver as IVocomDeviceDriver, usbService, wifiService, bluetoothService);
                    if (!initialized)
                    {
                        _logger.LogError("Failed to initialize Vocom service", "PatchedVocomServiceFactory");
                        return null;
                    }

                    _logger.LogInformation("Vocom service created and initialized successfully with real driver", "PatchedVocomServiceFactory");
                    return service;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error creating Vocom service", "PatchedVocomServiceFactory", ex);
                return null;
            }
        }

        /// <summary>
        /// Creates and initializes a new Vocom service with custom connection settings
        /// </summary>
        /// <param name="connectionSettings">Custom connection settings</param>
        /// <returns>The initialized Vocom service</returns>
        public async Task<IVocomService> CreateServiceWithSettingsAsync(ConnectionSettings connectionSettings)
        {
            try
            {
                _logger.LogInformation("Creating patched Vocom service with custom settings", "PatchedVocomServiceFactory");

                // Create the service first
                IVocomService service = await CreateServiceAsync();
                if (service == null)
                {
                    _logger.LogError("Failed to create Vocom service", "PatchedVocomServiceFactory");
                    return null;
                }

                // Update the connection settings
                service.UpdateConnectionSettings(connectionSettings);

                _logger.LogInformation("Vocom service created and initialized successfully with custom settings", "PatchedVocomServiceFactory");
                return service;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error creating Vocom service with custom settings", "PatchedVocomServiceFactory", ex);
                return null;
            }
        }

        /// <summary>
        /// Checks if the current process architecture is compatible with the required libraries
        /// </summary>
        private void CheckArchitectureCompatibility()
        {
            string processArchitecture = RuntimeInformation.ProcessArchitecture.ToString();
            _logger.LogInformation($"Current process architecture: {processArchitecture}", "PatchedVocomServiceFactory");

            // Check if we're running as x64 (required for WUDFPuma.dll)
            if (RuntimeInformation.ProcessArchitecture != Architecture.X64)
            {
                _logger.LogWarning($"Process is running as {processArchitecture}, but x64 is required for WUDFPuma.dll compatibility", "PatchedVocomServiceFactory");
                _logger.LogWarning("This may cause library loading failures. Consider rebuilding the application for x64 architecture.", "PatchedVocomServiceFactory");
            }
            else
            {
                _logger.LogInformation("Process architecture is x64, compatible with WUDFPuma.dll", "PatchedVocomServiceFactory");
            }
        }

        /// <summary>
        /// Checks if critical runtime dependencies are available
        /// </summary>
        private void CheckRuntimeDependencies()
        {
            var requiredLibraries = new[]
            {
                "msvcr140.dll",
                "msvcp140.dll",
                "vcruntime140.dll",
                "api-ms-win-crt-runtime-l1-1-0.dll"
            };

            foreach (var library in requiredLibraries)
            {
                try
                {
                    var handle = LoadLibrary(library);
                    if (handle != IntPtr.Zero)
                    {
                        FreeLibrary(handle);
                        _logger.LogInformation($"✓ Runtime dependency available: {library}", "PatchedVocomServiceFactory");
                    }
                    else
                    {
                        _logger.LogWarning($"✗ Runtime dependency missing: {library}", "PatchedVocomServiceFactory");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"✗ Error checking runtime dependency {library}: {ex.Message}", "PatchedVocomServiceFactory");
                }
            }
        }

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);
    }
}
