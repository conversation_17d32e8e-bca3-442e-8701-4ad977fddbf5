@echo off
echo === VolvoFlashWR x64 Architecture Library Fix ===
echo This script will fix library loading issues while maintaining x64 architecture
echo.

REM Create Libraries directory if it doesn't exist
if not exist "VolvoFlashWR_Export_With_Fix\Libraries" (
    mkdir "VolvoFlashWR_Export_With_Fix\Libraries"
    echo Created Libraries directory
)

echo Step 1: Downloading and installing Visual C++ Redistributables...
echo.

REM Download and install VC++ 2015-2022 Redistributable (x64)
echo Downloading Visual C++ 2015-2022 Redistributable (x64)...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vc_redist.x64.exe' -OutFile '%TEMP%\vc_redist_x64.exe'}"

if exist "%TEMP%\vc_redist_x64.exe" (
    echo Installing Visual C++ 2015-2022 Redistributable (x64)...
    "%TEMP%\vc_redist_x64.exe" /quiet /norestart
    if %errorlevel% equ 0 (
        echo ✓ Successfully installed VC++ 2015-2022 Redistributable (x64)
    ) else (
        echo ⚠ Installation completed with exit code: %errorlevel%
    )
    del "%TEMP%\vc_redist_x64.exe"
) else (
    echo ✗ Failed to download VC++ 2015-2022 Redistributable
)

echo.
REM Download and install VC++ 2013 Redistributable (x64)
echo Downloading Visual C++ 2013 Redistributable (x64)...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x64.exe' -OutFile '%TEMP%\vc2013_redist_x64.exe'}"

if exist "%TEMP%\vc2013_redist_x64.exe" (
    echo Installing Visual C++ 2013 Redistributable (x64)...
    "%TEMP%\vc2013_redist_x64.exe" /quiet /norestart
    if %errorlevel% equ 0 (
        echo ✓ Successfully installed VC++ 2013 Redistributable (x64)
    ) else (
        echo ⚠ Installation completed with exit code: %errorlevel%
    )
    del "%TEMP%\vc2013_redist_x64.exe"
) else (
    echo ✗ Failed to download VC++ 2013 Redistributable
)

echo.
echo Step 2: Copying system libraries to application directory...
echo.

REM Copy required libraries from system directories
set "LIBS_DIR=VolvoFlashWR_Export_With_Fix\Libraries"

REM List of required libraries
set "REQUIRED_LIBS=msvcr140.dll msvcp140.dll vcruntime140.dll api-ms-win-crt-runtime-l1-1-0.dll api-ms-win-crt-heap-l1-1-0.dll api-ms-win-crt-string-l1-1-0.dll api-ms-win-crt-stdio-l1-1-0.dll api-ms-win-crt-math-l1-1-0.dll api-ms-win-crt-locale-l1-1-0.dll"

for %%L in (%REQUIRED_LIBS%) do (
    echo Searching for %%L...
    
    REM Check System32 first (x64 libraries)
    if exist "%SystemRoot%\System32\%%L" (
        copy "%SystemRoot%\System32\%%L" "%LIBS_DIR%\%%L" >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✓ Copied %%L from System32 (x64)
        ) else (
            echo ⚠ Failed to copy %%L from System32
        )
    ) else if exist "%SystemRoot%\SysWOW64\%%L" (
        copy "%SystemRoot%\SysWOW64\%%L" "%LIBS_DIR%\%%L" >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✓ Copied %%L from SysWOW64 (x86)
        ) else (
            echo ⚠ Failed to copy %%L from SysWOW64
        )
    ) else (
        echo ⚠ Not found: %%L
    )
)

echo.
echo Step 3: Creating enhanced startup script...
echo.

REM Create enhanced startup script
(
echo @echo off
echo echo === VolvoFlashWR x64 Enhanced Startup ===
echo echo Setting up environment for x64 architecture compatibility...
echo echo.
echo.
echo REM Set environment variables for proper library loading
echo set USE_PATCHED_IMPLEMENTATION=true
echo set PHOENIX_VOCOM_ENABLED=true
echo set VERBOSE_LOGGING=true
echo set APCI_LIBRARY_PATH=%%~dp0Libraries
echo set FORCE_ARCHITECTURE_BRIDGE=true
echo.
echo REM Add Libraries directory to PATH
echo set PATH=%%~dp0Libraries;%%PATH%%
echo.
echo echo Environment configured for x64 compatibility
echo echo Architecture Bridge: Enabled for x86 library compatibility
echo echo Starting VolvoFlashWR...
echo echo.
echo.
echo REM Start the application
echo "%%~dp0VolvoFlashWR.Launcher.exe"
echo.
echo if errorlevel 1 ^(
echo     echo.
echo     echo === Application Error Detected ===
echo     echo Check the logs in the Logs directory for details
echo     echo Common issues:
echo     echo - Missing Visual C++ Redistributables
echo     echo - Architecture compatibility problems
echo     echo - Hardware connection issues
echo     echo.
echo     pause
echo ^)
) > "VolvoFlashWR_Export_With_Fix\Run_x64_Compatible.bat"

echo ✓ Created enhanced startup script: Run_x64_Compatible.bat

echo.
echo Step 4: Analyzing APCI library compatibility...
echo.

REM Check APCI libraries
set "APP_PATH=VolvoFlashWR_Export_With_Fix"
echo Checking APCI library architectures:

if exist "%APP_PATH%\apci.dll" (
    echo ⚠ Found apci.dll - likely x86 architecture (requires bridge)
) else (
    echo ? apci.dll not found
)

if exist "%APP_PATH%\Volvo.ApciPlus.dll" (
    echo ⚠ Found Volvo.ApciPlus.dll - likely x86 architecture (requires bridge)
) else (
    echo ? Volvo.ApciPlus.dll not found
)

if exist "%APP_PATH%\WUDFPuma.dll" (
    echo ✓ Found WUDFPuma.dll
) else (
    echo ? WUDFPuma.dll not found
)

echo.
echo === SUMMARY ===
echo ✓ Visual C++ Redistributables installed
echo ✓ System libraries copied to application
echo ✓ Enhanced startup script created
echo ⚠ Architecture bridge enabled for x86 APCI library compatibility
echo.
echo === NEXT STEPS ===
echo 1. Use the enhanced startup script: VolvoFlashWR_Export_With_Fix\Run_x64_Compatible.bat
echo 2. The application will handle architecture compatibility automatically
echo 3. Check logs in the Logs directory if issues persist
echo.
echo === FIX COMPLETED ===
echo The x64 architecture library issues have been addressed.
echo.
pause
