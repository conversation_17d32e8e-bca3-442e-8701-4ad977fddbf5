@echo off
echo ========================================
echo VolvoFlashWR USB Device Path Fix Deployment
echo ========================================
echo.

echo This script will help you deploy the USB device path fix.
echo.
echo IMPORTANT: Make sure you have:
echo 1. Backed up your original source files
echo 2. Closed Visual Studio or any IDE
echo 3. Have write permissions to the source directory
echo.

set /p SOURCE_DIR="Enter the path to your VolvoFlashWR source directory: "

if not exist "%SOURCE_DIR%" (
    echo ERROR: Source directory does not exist!
    pause
    exit /b 1
)

echo.
echo Deploying fix files...
echo.

echo Copying EnhancedVocomDeviceDetector.cs...
copy /Y "VolvoFlashWR.Communication\Vocom\EnhancedVocomDeviceDetector.cs" "%SOURCE_DIR%\VolvoFlashWR.Communication\Vocom\"
if errorlevel 1 (
    echo ERROR: Failed to copy EnhancedVocomDeviceDetector.cs
    pause
    exit /b 1
)

echo Copying EnhancedVocomDeviceDetection.cs...
copy /Y "VolvoFlashWR.Communication\Vocom\EnhancedVocomDeviceDetection.cs" "%SOURCE_DIR%\VolvoFlashWR.Communication\Vocom\"
if errorlevel 1 (
    echo ERROR: Failed to copy EnhancedVocomDeviceDetection.cs
    pause
    exit /b 1
)

echo Copying PatchedVocomServiceFactory.cs...
copy /Y "VolvoFlashWR.Communication\Vocom\PatchedVocomServiceFactory.cs" "%SOURCE_DIR%\VolvoFlashWR.Communication\Vocom\"
if errorlevel 1 (
    echo ERROR: Failed to copy PatchedVocomServiceFactory.cs
    pause
    exit /b 1
)

echo Copying VocomBridgeService.cs...
copy /Y "VocomBridgeService.cs" "%SOURCE_DIR%\VolvoFlashWR.VocomBridge\"
if errorlevel 1 (
    echo ERROR: Failed to copy VocomBridgeService.cs
    pause
    exit /b 1
)

echo.
echo ========================================
echo Fix deployment completed successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Open your VolvoFlashWR solution in Visual Studio
echo 2. Rebuild the entire solution (Build -> Rebuild Solution)
echo 3. Test the application with a real Vocom adapter
echo.
echo Check the logs for:
echo - "Found actual Vocom USB device: USB\VID_178E&PID_0024\..."
echo - "Using USB hardware ID as port info: USB\VID_178E&PID_0024\..."
echo - No more "WUDFPuma Driver" errors
echo.
echo Good luck with testing!
echo.
pause
