[2024-10-29 06:42:57.125-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-10-29 06:42:57.140-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-10-29 06:42:57.140-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-10-29 06:42:57.546-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-10-29 06:42:57.821-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-10-29 06:43:27.924-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2024-10-29 06:43:27.939-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2024-10-29 06:43:27.939-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2024-10-29 09:07:43.499-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-10-29 09:07:43.514-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-10-29 09:07:43.514-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-10-29 09:07:43.946-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-10-29 09:07:44.216-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-11-07 03:25:43.083-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-11-07 03:25:43.099-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-11-07 03:25:43.099-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-11-07 03:25:43.567-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-11-07 03:25:43.880-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-11-07 03:33:55.696-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2024-11-07 03:33:55.711-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2024-11-07 03:33:55.711-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2024-11-07 03:36:11.066-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-11-07 03:36:11.066-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-11-07 03:36:11.082-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-11-07 03:36:11.581-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-11-07 03:36:11.878-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-11-07 05:28:59.239-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-11-09 02:10:42.317-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-11-09 02:47:38.892-08]	[  6]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2024-11-09 02:47:49.042-08]	[  6]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 02:48:52.390-08]	[  3]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 02:50:12.999-08]	[ 14]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2024-11-09 02:50:13.734-08]	[ 14]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2024-11-09 02:50:13.874-08]	[ 14]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 02:51:15.891-08]	[ 11]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2024-11-09 02:51:16.641-08]	[ 11]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2024-11-09 02:51:16.766-08]	[ 11]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 02:52:22.972-08]	[ 14]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 02:54:12.122-08]	[ 10]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 02:54:54.743-08]	[ 16]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 02:56:18.278-08]	[ 18]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2024-11-09 02:56:48.287-08]	[ 11]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 04:46:41.847-08]	[  4]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2024-11-09 04:46:48.296-08]	[  4]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 04:49:01.015-08]	[  8]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 04:50:24.683-08]	[ 10]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2024-11-09 04:50:25.386-08]	[ 10]	[ERROR]	VCConn - cConnectionAPCI.ReadParameter  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: cParameter.New_1 (1206) Apcidb.dll ********, ERR: 4 in FN: 1018. Data is missing in APCI database., MID:140, MSW:20801279, DS1:20760501, Parameter:MXQ
- VCAPCI: cParameter.New_1 (-)
- VCAPCI: cMIDParameters.AddParameter (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cMIDParameters.AddParameter(String sParameterCode)
   at Volvo.VCADSPro.VCConn.Parameters.Add(String parameterCode)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadParameter(IParameters parameters, cSIParameter SIPar)

[2024-11-09 04:50:25.793-08]	[ 10]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2024-11-09 04:50:25.918-08]	[ 10]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 04:51:06.354-08]	[ 10]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 04:52:12.151-08]	[  8]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 04:53:23.742-08]	[  9]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 04:54:18.748-08]	[  8]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2024-11-09 04:54:19.389-08]	[  8]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2024-11-09 04:54:19.514-08]	[  8]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 04:58:48.520-08]	[ 12]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 04:59:40.190-08]	[ 17]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 05:00:07.410-08]	[ 19]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-09 05:38:56.852-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2024-11-09 05:38:56.899-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2024-11-09 05:38:56.899-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2024-11-10 10:11:45.637-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-11-10 10:11:45.652-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-11-10 10:11:45.652-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-11-10 10:11:46.074-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-11-10 10:11:46.404-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-11-10 10:13:55.656-08]	[  3]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2024-11-10 10:14:03.493-08]	[  3]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-10 10:15:50.751-08]	[  6]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2024-11-10 10:16:03.797-08]	[  6]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2024-11-10 10:16:04.859-08]	[  6]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2024-11-10 10:16:04.984-08]	[  6]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-10 10:16:30.653-08]	[  7]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-10 10:18:07.410-08]	[ 11]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-11-10 11:38:06.609-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2024-11-10 11:38:06.625-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2024-11-10 11:38:06.625-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2024-11-28 07:16:26.451-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-11-28 07:16:26.461-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-11-28 07:16:26.464-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-11-28 07:16:26.951-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-11-28 07:16:27.291-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-11-28 07:17:46.645-08]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-11-28 07:22:41.328-08]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-11-28 07:40:08.173-08]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-01 04:31:03.689-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2024-12-01 04:31:04.202-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2024-12-01 04:31:04.202-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2024-12-08 05:01:16.747-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-12-08 05:01:16.763-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-12-08 05:01:16.763-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-12-08 05:01:17.262-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-12-08 05:01:17.575-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-12-08 05:03:10.848-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-08 05:09:34.419-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2024-12-08 05:09:34.435-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2024-12-08 05:09:34.435-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2024-12-14 07:45:02.579-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-12-14 07:45:02.579-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-12-14 07:45:02.595-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-12-14 07:45:03.079-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-12-14 07:45:03.392-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-12-14 07:46:15.673-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-14 07:50:45.484-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-14 10:58:48.720-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-14 11:07:33.112-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2024-12-14 11:07:33.128-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2024-12-14 11:07:33.128-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2024-12-15 00:01:46.247-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-12-15 00:01:46.262-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-12-15 00:01:46.262-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-12-15 00:01:46.793-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-12-15 00:01:47.137-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-12-15 00:14:09.722-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2024-12-15 00:14:09.737-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2024-12-15 00:14:09.737-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2024-12-15 08:41:58.827-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-12-15 08:41:58.843-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-12-15 08:41:58.843-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-12-15 08:41:59.358-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-12-15 08:41:59.655-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-12-15 08:43:05.566-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-15 08:49:24.350-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-15 11:05:50.302-08]	[  5]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2024-12-15 11:06:13.327-08]	[  5]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2024-12-15 11:06:17.114-08]	[  5]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2024-12-15 11:06:17.268-08]	[  5]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-15 11:06:21.393-08]	[  3]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2024-12-15 11:07:20.357-08]	[  8]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2024-12-15 11:07:21.876-08]	[  8]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2024-12-15 11:07:22.007-08]	[  8]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-15 11:07:27.503-08]	[ 10]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2024-12-15 11:10:28.330-08]	[ 12]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2024-12-15 11:10:59.928-08]	[ 12]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2024-12-15 11:11:01.246-08]	[ 12]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2024-12-15 11:11:01.384-08]	[ 12]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-15 11:11:04.755-08]	[  9]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2024-12-15 11:12:26.425-08]	[  9]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2024-12-15 11:12:52.230-08]	[ 16]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-15 11:12:54.903-08]	[ 17]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2024-12-15 11:28:37.228-08]	[ 15]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2024-12-15 11:28:38.631-08]	[ 15]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2024-12-15 11:28:38.763-08]	[ 15]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-15 11:28:45.594-08]	[ 15]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2024-12-15 11:46:02.693-08]	[ 23]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2024-12-16 04:44:20.348-08]	[ 10]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-16 05:24:32.868-08]	[ 10]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-16 10:09:35.856-08]	[ 10]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-18 00:47:30.634-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2024-12-18 00:47:30.697-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2024-12-18 00:47:30.697-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2024-12-18 06:10:26.748-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-12-18 06:10:26.764-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-12-18 06:10:26.764-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-12-18 06:10:27.279-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-12-18 06:10:27.592-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-12-18 06:12:15.373-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-19 10:01:01.989-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-19 10:25:35.553-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: mscorlib
ArgumentOutOfRangeException: Index was out of range. Must be non-negative and less than the size of the collection.
Parameter name: index
StackTrace:
   at System.ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument argument, ExceptionResource resource)
   at Volvo.VCADSPro.VCServices.CurrentVehicle.GetSelectedAdapter()
   at Volvo.VCADSPro.VCServices.CurrentVehicle.GetCurrentVehicle()
   at Volvo.VCADSPro.VCUIService.Proxies.IdentificationInfoRepositoryProxy.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-23 08:19:19.649-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-24 06:11:10.274-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-25 06:45:34.389-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-12-25 06:45:34.405-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-12-25 06:45:34.405-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-12-25 06:45:34.796-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-12-25 06:45:35.108-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-12-25 09:01:29.728-08]	[  6]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2024-12-25 09:01:35.611-08]	[  3]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:03:37.916-08]	[ 10]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2024-12-25 09:03:44.389-08]	[ 10]	[ERROR]	VCConn - cConnectionAPCI.ReadParameter  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: cParameter.New_1 (1206) Apcidb.dll ********, ERR: 4 in FN: 1018. Data is missing in APCI database., MID:128, MSW:20762948, DS1:11423745, Parameter:IVI
- VCAPCI: cParameter.New_1 (-)
- VCAPCI: cMIDParameters.AddParameter (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cMIDParameters.AddParameter(String sParameterCode)
   at Volvo.VCADSPro.VCConn.Parameters.Add(String parameterCode)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadParameter(IParameters parameters, cSIParameter SIPar)

[2024-12-25 09:03:44.452-08]	[ 10]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2024-12-25 09:03:44.577-08]	[ 10]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:03:57.924-08]	[ 10]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:06:25.629-08]	[ 15]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:07:54.823-08]	[ 20]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:09:45.344-08]	[ 17]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:11:24.656-08]	[ 19]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:12:28.744-08]	[ 12]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:13:05.358-08]	[ 13]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:13:32.613-08]	[ 23]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:13:44.831-08]	[ 23]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:15:00.947-08]	[ 23]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:15:11.484-08]	[ 23]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:16:49.339-08]	[ 11]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:17:30.558-08]	[ 12]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2024-12-25 09:17:33.792-08]	[ 12]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2024-12-25 09:17:33.916-08]	[ 12]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:17:40.128-08]	[ 15]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:18:26.168-08]	[ 21]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:19:35.060-08]	[ 28]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2024-12-25 09:19:40.309-08]	[ 27]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:22:36.810-08]	[ 29]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2024-12-25 09:22:37.278-08]	[ 29]	[ERROR]	VCConn - cConnectionAPCI.ReadParameter  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: cParameter.New_1 (1206) Apcidb.dll ********, ERR: 4 in FN: 1018. Data is missing in APCI database., MID:128, MSW:20762948, DS1:11423745, Parameter:IVI
- VCAPCI: cParameter.New_1 (-)
- VCAPCI: cMIDParameters.AddParameter (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cMIDParameters.AddParameter(String sParameterCode)
   at Volvo.VCADSPro.VCConn.Parameters.Add(String parameterCode)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadParameter(IParameters parameters, cSIParameter SIPar)

[2024-12-25 09:22:37.341-08]	[ 29]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2024-12-25 09:22:37.466-08]	[ 29]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:22:59.192-08]	[ 34]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:24:03.573-08]	[ 35]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:25:37.610-08]	[ 36]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:27:35.499-08]	[ 32]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:31:47.251-08]	[ 27]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:32:24.627-08]	[  4]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-25 09:37:28.335-08]	[ 34]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2024-12-26 05:31:41.997-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2024-12-26 05:31:42.371-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2024-12-26 05:31:42.371-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2024-12-26 05:36:06.483-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-12-26 05:36:06.499-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-12-26 05:36:06.499-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-12-26 05:36:06.889-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-12-26 05:36:07.202-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-12-28 00:02:13.243-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-12-28 00:02:13.256-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-12-28 00:02:13.259-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-12-28 00:02:13.646-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-12-28 00:02:14.017-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-12-28 01:13:36.227-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-12-28 01:13:36.242-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-12-28 01:13:36.242-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-12-28 01:13:36.680-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-12-28 01:13:36.976-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-12-28 01:29:48.057-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-12-28 01:29:48.057-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-12-28 01:29:48.057-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-12-28 01:29:48.496-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-12-28 01:29:48.793-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-12-28 01:30:58.733-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-28 23:51:06.696-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-29 06:59:20.380-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-29 10:39:23.423-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-30 05:16:39.151-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2024-12-30 05:16:39.151-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2024-12-30 05:16:39.166-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2024-12-30 05:16:39.541-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2024-12-30 05:16:39.822-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2024-12-30 05:19:42.173-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-30 06:33:40.805-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-30 06:38:59.761-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-30 13:00:30.989-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-30 13:01:28.146-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2024-12-31 08:38:09.988-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-01 05:32:10.988-08]	[  3]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-01 05:32:17.981-08]	[  4]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 05:32:56.133-08]	[  9]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 05:34:26.526-08]	[ 11]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 05:35:20.039-08]	[ 12]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 05:37:39.700-08]	[ 15]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 05:38:46.316-08]	[ 12]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 05:41:02.854-08]	[ 12]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 09:57:15.477-08]	[ 16]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 09:59:26.448-08]	[ 15]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 09:59:59.781-08]	[ 21]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 10:01:51.222-08]	[ 23]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 10:02:40.409-08]	[ 24]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 10:03:45.191-08]	[ 22]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 10:05:06.123-08]	[ 22]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 10:06:05.976-08]	[  8]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 10:06:42.426-08]	[  9]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 10:07:47.009-08]	[ 25]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-01 10:07:47.509-08]	[ 25]	[ERROR]	VCConn - cConnectionAPCI.ReadParameter  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: cParameter.New_1 (1206) Apcidb.dll ********, ERR: 4 in FN: 1018. Data is missing in APCI database., MID:128, MSW:20486144, DS1:11423180, Parameter:IVI
- VCAPCI: cParameter.New_1 (-)
- VCAPCI: cMIDParameters.AddParameter (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cMIDParameters.AddParameter(String sParameterCode)
   at Volvo.VCADSPro.VCConn.Parameters.Add(String parameterCode)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadParameter(IParameters parameters, cSIParameter SIPar)

[2025-01-01 10:07:47.587-08]	[ 25]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-01 10:07:47.712-08]	[ 25]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-01 10:08:09.540-08]	[  9]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-01 10:08:25.823-08]	[ 27]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoChassisIdFoundException: VCADS Needs Chassis Id for the product, Go back to Product Identification and enter a Chassis Id
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-01-01 10:08:33.009-08]	[ 23]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-02 08:58:46.086-08]	[ 17]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-02 09:03:06.144-08]	[ 17]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-04 04:42:29.869-08]	[ 17]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-04 04:42:43.325-08]	[ 21]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-04 04:44:07.633-08]	[ 17]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-04 04:45:31.122-08]	[  5]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-04 04:54:21.624-08]	[  5]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-04 05:34:02.432-08]	[ 20]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-04 06:01:55.260-08]	[  6]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-04 06:04:02.019-08]	[  6]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-04 06:04:34.968-08]	[ 10]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-05 03:34:01.628-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-01-05 03:34:01.643-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-01-05 03:34:01.643-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-01-05 03:37:49.105-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-01-05 03:37:49.121-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-01-05 03:37:49.137-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-01-05 03:37:49.621-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-01-05 03:37:49.949-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-01-05 09:03:53.888-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-05 09:09:26.017-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-06 08:23:08.607-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-06 08:27:44.974-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-07 11:40:30.923-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-13 04:35:47.340-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-16 05:42:32.283-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-01-16 05:42:32.299-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-01-16 05:42:32.299-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-01-16 05:54:06.761-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-01-16 05:54:06.777-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-01-16 05:54:06.777-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-01-16 05:54:07.261-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-01-16 05:54:07.636-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-01-17 05:26:37.193-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-17 05:43:26.601-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-20 06:51:07.505-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-01-20 06:51:07.521-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-01-20 06:51:07.521-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-01-20 07:31:52.527-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-01-20 07:31:52.542-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-01-20 07:31:52.542-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-01-20 07:31:53.027-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-01-20 07:31:53.339-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-01-20 07:42:45.210-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-20 07:45:25.550-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-20 10:15:36.481-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-21 08:10:03.009-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-21 08:27:43.990-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-22 00:41:04.374-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-22 08:38:55.973-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-22 09:39:04.809-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-01-22 09:39:04.809-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-01-22 09:39:04.809-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-01-22 09:41:04.266-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-01-22 09:41:04.282-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-01-22 09:41:04.282-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-01-22 09:41:04.672-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-01-22 09:41:04.985-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-01-23 00:11:52.278-08]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-23 00:14:51.857-08]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-23 00:30:57.702-08]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-23 00:54:21.796-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-01-23 00:54:21.812-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-01-23 00:54:21.812-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-01-23 00:56:49.535-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-01-23 00:56:49.644-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-01-23 00:56:49.785-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-01-23 00:56:50.238-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-01-23 00:56:50.550-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-01-23 05:58:51.421-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-01-23 05:58:51.437-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-01-23 05:58:51.437-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-01-23 05:58:51.807-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-01-23 05:58:52.054-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-01-23 06:58:10.591-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-25 04:14:15.825-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-01-25 04:14:15.825-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-01-25 04:14:15.841-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-01-25 04:14:16.231-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-01-25 04:14:16.622-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-01-25 04:15:29.621-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-25 07:22:28.814-08]	[  5]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-25 07:22:37.327-08]	[  3]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-25 07:24:36.246-08]	[  4]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-25 07:24:45.224-08]	[  4]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoChassisIdFoundException: VCADS Needs Chassis Id for the product, Go back to Product Identification and enter a Chassis Id
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-01-25 07:25:03.037-08]	[  9]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoChassisIdFoundException: VCADS Needs Chassis Id for the product, Go back to Product Identification and enter a Chassis Id
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-01-25 07:25:11.726-08]	[ 12]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-25 07:25:18.166-08]	[ 12]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-25 07:26:50.577-08]	[  8]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-25 07:27:12.963-08]	[  8]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-25 07:28:00.093-08]	[  4]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-25 07:28:54.763-08]	[ 13]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-25 08:44:44.828-08]	[ 13]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-25 08:45:01.534-08]	[  5]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-25 08:45:06.580-08]	[  3]	[INFO ]	VCServices - CurrentVehicle.RefreshLockCommunication  VCADS Renewing lock of PS.
[2025-01-25 08:45:08.548-08]	[  5]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-25 08:45:08.689-08]	[  5]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-25 08:49:08.390-08]	[  3]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-25 10:19:01.597-08]	[ 16]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-25 10:21:19.351-08]	[ 16]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-25 10:21:25.349-08]	[ 18]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-25 10:32:20.667-08]	[  3]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-25 10:32:47.656-08]	[  3]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-25 10:32:48.734-08]	[  3]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-25 10:32:48.875-08]	[  3]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-25 10:32:53.312-08]	[  3]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-25 10:35:00.166-08]	[  5]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-25 10:35:02.650-08]	[  6]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-25 10:37:27.231-08]	[ 15]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-25 10:37:35.434-08]	[ 15]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-25 10:37:35.715-08]	[  7]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-25 11:19:00.111-08]	[ 10]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-25 11:19:08.055-08]	[ 10]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-25 11:23:10.953-08]	[  7]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-25 11:23:21.130-08]	[  9]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-25 11:24:36.869-08]	[ 12]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 05:16:17.618-08]	[  5]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-26 05:16:29.396-08]	[  9]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 05:17:53.992-08]	[ 11]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 05:18:34.078-08]	[ 11]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-26 05:19:02.070-08]	[ 16]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 05:19:02.117-08]	[ 16]	[ERROR]	VCConn - cConnectionAPCI.InitAPCI  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: mAPCICom.subAPCI_1 (1121) Apci.dll *********, ERR: 17 in FN: 1. Selected 88890020 is used by another user.
- VCAPCI: mAPCICom.subAPCI_1 (-)
- VCAPCI: cAPCI.OpenCommunication (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cAPCI.OpenCommunication(Int32[] arrPassword)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.InitAPCI(String sLanguageCode, Boolean bSimulated, Boolean bFillMIDs, VTIdentInfo udtIdentinfo, cUser oUser, Boolean& simulationDataMissing)

[2025-01-26 05:19:02.180-08]	[ 16]	[ERROR]	VCConn - cConnectionAPCI.ReadSIParameters  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: mAPCICom.subAPCI_1 (1121) Apci.dll *********, ERR: 17 in FN: 1. Selected 88890020 is used by another user.
- VCAPCI: mAPCICom.subAPCI_1 (-)
- VCAPCI: cAPCI.OpenCommunication (-)
- VCConn: cConnectionApci.InitAPCI (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.InitAPCI(String sLanguageCode, Boolean bSimulated, Boolean bFillMIDs, VTIdentInfo udtIdentinfo, cUser oUser, Boolean& simulationDataMissing)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadSIParameters(cSIParameters ocolSIParameters, String sLanguageCode, VTIdentInfo udtIdentinfo, cUser oUser, VTConnectionInfo udtConnectionInfo)

[2025-01-26 05:19:02.180-08]	[ 16]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 05:19:02.305-08]	[ 16]	[ERROR]	VCUIService - ApciConnectionProxy.ReadVehicleParameters  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: mAPCICom.subAPCI_1 (1121) Apci.dll *********, ERR: 17 in FN: 1. Selected 88890020 is used by another user.
- VCAPCI: mAPCICom.subAPCI_1 (-)
- VCAPCI: cAPCI.OpenCommunication (-)
- VCConn: cConnectionApci.InitAPCI (-)
- VCConn: cConnectionAPCI.ReadSIParameters (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadSIParameters(cSIParameters ocolSIParameters, String sLanguageCode, VTIdentInfo udtIdentinfo, cUser oUser, VTConnectionInfo udtConnectionInfo)
   at Volvo.VCADSPro.VCUIService.Proxies.ApciConnectionProxy.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)

[2025-01-26 05:19:02.305-08]	[ 16]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: mAPCICom.subAPCI_1 (1121) Apci.dll *********, ERR: 17 in FN: 1. Selected 88890020 is used by another user.
- VCAPCI: mAPCICom.subAPCI_1 (-)
- VCAPCI: cAPCI.OpenCommunication (-)
- VCConn: cConnectionApci.InitAPCI (-)
- VCConn: cConnectionAPCI.ReadSIParameters (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadSIParameters(cSIParameters ocolSIParameters, String sLanguageCode, VTIdentInfo udtIdentinfo, cUser oUser, VTConnectionInfo udtConnectionInfo)
   at Volvo.VCADSPro.VCUIService.Proxies.ApciConnectionProxy.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ApciConnectionInfoManager.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.VssManager.GetFilteredVsses(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user, Boolean simulated)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-01-26 05:19:13.951-08]	[ 16]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 05:19:14.686-08]	[ 16]	[ERROR]	VCConn - cConnectionAPCI.ReadParameter  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: cParameter.New_1 (1206) Apcidb.dll ********, ERR: 4 in FN: 1018. Data is missing in APCI database., MID:140, MSW:21548702, DS1:21003731, Parameter:MXQ
- VCAPCI: cParameter.New_1 (-)
- VCAPCI: cMIDParameters.AddParameter (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cMIDParameters.AddParameter(String sParameterCode)
   at Volvo.VCADSPro.VCConn.Parameters.Add(String parameterCode)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadParameter(IParameters parameters, cSIParameter SIPar)

[2025-01-26 05:19:15.388-08]	[ 16]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 05:19:15.513-08]	[ 16]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 05:20:02.173-08]	[  4]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 05:20:03.157-08]	[  4]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 05:20:03.282-08]	[  4]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 05:21:12.639-08]	[ 23]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 05:21:13.608-08]	[ 23]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 05:21:13.733-08]	[ 23]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 05:23:03.697-08]	[ 14]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 05:23:57.495-08]	[ 14]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 05:23:59.479-08]	[ 14]	[ERROR]	VCConn - cConnectionAPCI.ReadParameter  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: cParameter.New_1 (1206) Apcidb.dll ********, ERR: 4 in FN: 1018. Data is missing in APCI database., MID:128, MSW:21247403, DS1:21390457, Parameter:MHG
- VCAPCI: cParameter.New_1 (-)
- VCAPCI: cMIDParameters.AddParameter (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cMIDParameters.AddParameter(String sParameterCode)
   at Volvo.VCADSPro.VCConn.Parameters.Add(String parameterCode)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadParameter(IParameters parameters, cSIParameter SIPar)

[2025-01-26 05:23:59.635-08]	[ 14]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 05:23:59.760-08]	[ 14]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 05:25:25.157-08]	[ 18]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 05:25:26.126-08]	[ 18]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 05:25:26.251-08]	[ 18]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 05:27:27.677-08]	[ 12]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 05:27:28.380-08]	[ 12]	[ERROR]	VCConn - cConnectionAPCI.ReadParameter  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: cParameter.New_1 (1206) Apcidb.dll ********, ERR: 4 in FN: 1018. Data is missing in APCI database., MID:130, MSW:22401922, DS1:21444591, Parameter:QTR
- VCAPCI: cParameter.New_1 (-)
- VCAPCI: cMIDParameters.AddParameter (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cMIDParameters.AddParameter(String sParameterCode)
   at Volvo.VCADSPro.VCConn.Parameters.Add(String parameterCode)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadParameter(IParameters parameters, cSIParameter SIPar)

[2025-01-26 05:27:28.489-08]	[ 12]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 05:27:28.614-08]	[ 12]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 05:28:45.263-08]	[ 26]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 05:29:05.227-08]	[  4]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-26 05:30:03.567-08]	[  4]	[ERROR]	VCUIService - ProductServiceProxy.GetProductId  Error in ProductService Task: Product was not identified within the allotted time (productIdTaskTimeout in Config)
[2025-01-26 05:30:03.567-08]	[  4]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
CommunicationErrorException: Error in ProductService Task: Product was not identified within the allotted time (productIdTaskTimeout in Config)
StackTrace:
   at Volvo.VCADSPro.VCUIService.Proxies.ProductServiceProxy.GetProductId(String adapterId)
   at Volvo.VCADSPro.VCUIService.Proxies.ProductServiceProxy.GetConnectionInfo()
   at Volvo.VCADSPro.VCUIService.Managers.ApciConnectionInfoManager.GetRealConnectionInfo()
   at Volvo.VCADSPro.VCUIService.Managers.ApciConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-01-26 05:32:37.766-08]	[  3]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-26 05:34:44.017-08]	[  3]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 05:35:34.351-08]	[ 14]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 06:42:06.107-08]	[ 16]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-26 06:45:11.320-08]	[ 16]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-26 07:09:22.431-08]	[ 16]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-26 07:09:29.273-08]	[ 14]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 07:09:29.617-08]	[ 23]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 07:10:54.314-08]	[ 21]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 07:10:54.580-08]	[ 17]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 07:11:54.501-08]	[ 18]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 07:11:54.766-08]	[ 12]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 07:13:19.108-08]	[ 10]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 07:13:19.373-08]	[ 22]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 07:17:03.992-08]	[  9]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 07:17:04.273-08]	[  9]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 07:18:05.995-08]	[  6]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 07:18:06.276-08]	[  9]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 07:18:46.644-08]	[  4]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 07:18:49.987-08]	[  4]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 07:20:16.337-08]	[ 17]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 07:20:16.602-08]	[ 20]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 07:21:52.038-08]	[  3]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 07:21:52.397-08]	[  7]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 07:23:51.803-08]	[ 24]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 07:23:52.162-08]	[  3]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 07:24:51.241-08]	[  3]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 07:24:51.772-08]	[  3]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 07:26:00.488-08]	[  5]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 07:26:00.847-08]	[ 24]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 08:45:45.781-08]	[  8]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-26 08:46:13.243-08]	[  8]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 08:46:14.118-08]	[  8]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 08:46:14.258-08]	[  8]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 08:48:15.986-08]	[ 27]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-26 08:48:24.931-08]	[ 27]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 08:48:25.915-08]	[ 27]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 08:48:26.040-08]	[ 27]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 09:57:17.372-08]	[ 33]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-26 09:57:31.864-08]	[ 33]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 09:58:39.062-08]	[ 26]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 09:59:52.427-08]	[ 32]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 09:59:53.318-08]	[ 32]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 09:59:53.443-08]	[ 32]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 10:01:02.627-08]	[ 27]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 10:01:03.284-08]	[ 27]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 10:01:03.409-08]	[ 27]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 10:02:29.866-08]	[ 31]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 10:06:35.843-08]	[ 19]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 10:07:24.268-08]	[ 17]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 10:09:05.506-08]	[  4]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 10:10:38.583-08]	[ 15]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 10:10:40.520-08]	[ 15]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 10:10:40.645-08]	[ 15]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 10:12:09.359-08]	[  9]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 10:13:47.363-08]	[ 17]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 10:14:51.977-08]	[ 10]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-26 10:14:59.165-08]	[ 10]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 12:27:01.844-08]	[ 20]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-26 12:27:11.019-08]	[ 20]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 12:27:11.347-08]	[ 20]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 12:27:54.359-08]	[ 22]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 12:27:54.702-08]	[ 22]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 12:28:50.565-08]	[ 19]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 12:28:51.705-08]	[ 19]	[ERROR]	VCConn - cConnectionAPCI.ReadParameter  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: cParameter.New_1 (1206) Apcidb.dll ********, ERR: 4 in FN: 1018. Data is missing in APCI database., MID:128, MSW:22991818, DS1:22381211, Parameter:KG
- VCAPCI: cParameter.New_1 (-)
- VCAPCI: cMIDParameters.AddParameter (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cMIDParameters.AddParameter(String sParameterCode)
   at Volvo.VCADSPro.VCConn.Parameters.Add(String parameterCode)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadParameter(IParameters parameters, cSIParameter SIPar)

[2025-01-26 12:28:51.799-08]	[ 19]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 12:28:51.924-08]	[ 19]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 12:28:55.814-08]	[ 22]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 12:29:54.738-08]	[  6]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 12:29:54.831-08]	[  6]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 12:30:40.315-08]	[  4]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 12:30:40.908-08]	[  4]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 12:30:41.033-08]	[  4]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 12:30:44.521-08]	[  9]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 12:31:48.262-08]	[  5]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 12:31:48.919-08]	[  5]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 12:31:49.044-08]	[  5]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 12:31:51.543-08]	[  3]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 12:32:53.843-08]	[ 25]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 12:32:58.873-08]	[ 22]	[INFO ]	VCServices - CurrentVehicle.RefreshLockCommunication  VCADS Renewing lock of PS.
[2025-01-26 12:33:00.639-08]	[ 25]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 12:33:00.764-08]	[ 25]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 12:33:09.056-08]	[ 25]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 12:34:42.081-08]	[ 26]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-26 12:34:42.753-08]	[ 26]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-26 12:34:42.878-08]	[ 26]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 12:34:48.267-08]	[ 26]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 12:36:24.349-08]	[ 22]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 12:36:24.693-08]	[ 28]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 12:39:46.543-08]	[  7]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 12:39:46.996-08]	[ 23]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 12:40:04.108-08]	[ 11]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 12:42:08.228-08]	[ 16]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 12:42:08.322-08]	[ 24]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 12:45:04.170-08]	[ 11]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-26 12:45:04.592-08]	[ 11]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-26 23:57:04.722-08]	[ 31]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-26 23:57:12.232-08]	[ 31]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-27 05:03:38.528-08]	[ 24]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-27 05:04:54.189-08]	[ 24]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-27 05:25:51.498-08]	[ 24]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-28 00:57:00.992-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-01-28 00:57:01.008-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-01-28 00:57:01.008-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-01-28 00:57:01.414-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-01-28 00:57:01.695-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-01-28 06:46:10.986-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-28 08:42:54.926-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-28 08:56:31.198-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-29 09:09:06.021-08]	[  3]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-29 09:09:15.095-08]	[  7]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-29 09:09:15.470-08]	[  7]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-29 09:14:26.118-08]	[ 11]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-29 09:14:26.509-08]	[  6]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-29 09:17:09.700-08]	[  9]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-01-29 09:18:25.329-08]	[  9]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-29 09:18:25.595-08]	[  4]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-29 09:22:49.662-08]	[ 11]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-01-29 09:22:50.490-08]	[ 11]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-01-29 09:22:50.616-08]	[ 11]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-01-29 09:22:53.927-08]	[ 11]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-01-29 10:30:39.954-08]	[ 14]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-29 10:32:39.708-08]	[ 14]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-30 04:21:38.557-08]	[ 14]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-30 04:32:29.415-08]	[ 14]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-01-31 23:42:50.304-08]	[ 14]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-01 00:09:01.794-08]	[ 14]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-01 04:40:15.167-08]	[ 14]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-02 06:44:59.711-08]	[ 14]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-02 06:49:50.582-08]	[ 14]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-02 11:38:57.822-08]	[ 14]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-02-02 11:39:05.079-08]	[ 14]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-02 11:39:05.579-08]	[ 14]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-02-02 11:39:55.655-08]	[ 13]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-02-02 11:40:03.082-08]	[ 13]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-02-02 11:40:07.532-08]	[ 13]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-02-02 11:40:15.171-08]	[ 13]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-02-02 11:40:40.821-08]	[  7]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-02-02 11:41:21.288-08]	[  4]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-02-02 11:41:27.405-08]	[  4]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-02 11:41:27.842-08]	[  7]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-02-02 11:42:23.307-08]	[  3]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-02 11:42:23.619-08]	[  3]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-02-02 11:48:34.553-08]	[  6]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-02-02 11:53:28.954-08]	[  9]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-02 11:53:29.438-08]	[ 12]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-02-02 11:57:20.147-08]	[  6]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-02 11:57:24.098-08]	[  6]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-02-02 12:19:40.362-08]	[ 11]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-02 12:19:44.653-08]	[ 12]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-02-02 12:20:47.913-08]	[ 15]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-02 12:20:51.474-08]	[  7]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-02-03 05:01:02.182-08]	[  9]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-02-03 05:01:21.300-08]	[ 12]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-03 05:02:12.701-08]	[  3]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-02-03 05:02:52.229-08]	[  3]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-03 05:04:05.793-08]	[  5]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-03 05:04:46.955-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-02-03 05:04:46.970-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-02-03 05:04:46.970-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-02-03 05:06:39.401-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-02-03 05:06:39.401-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-02-03 05:06:39.417-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-02-03 05:06:39.823-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-02-03 05:06:40.104-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-02-03 05:08:51.663-08]	[  5]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-02-03 05:08:58.542-08]	[  5]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-03 06:22:40.105-08]	[ 10]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-02-03 06:22:54.255-08]	[ 10]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-03 06:28:33.553-08]	[  6]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-02-03 06:28:41.523-08]	[  6]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-03 06:29:20.517-08]	[  7]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-02-03 06:29:57.192-08]	[  7]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-03 09:39:29.456-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-03 10:52:35.150-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-03 12:30:48.031-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-03 12:36:41.629-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-04 06:05:24.922-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-02-04 06:05:24.922-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-02-04 06:07:51.146-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-02-04 06:07:51.146-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-02-04 06:07:51.146-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-02-04 06:07:51.599-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-02-04 06:07:51.911-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-02-05 11:05:02.857-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-05 11:18:08.213-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-06 08:31:28.637-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-02-06 08:31:28.653-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-02-06 08:31:28.653-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-02-08 06:05:47.938-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-02-08 06:05:47.954-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-02-08 06:05:47.954-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-02-08 06:05:48.376-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-02-08 06:05:48.688-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-02-08 06:09:09.723-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-09 09:03:34.663-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-09 09:17:00.641-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-09 09:29:41.237-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-09 09:46:19.630-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-02-09 09:46:19.678-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-02-09 09:46:19.678-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-02-09 10:19:50.065-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-02-09 10:19:50.065-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-02-09 10:19:50.065-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-02-09 10:19:50.502-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-02-09 10:19:50.815-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-02-09 10:20:56.598-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-09 10:46:56.301-08]	[  3]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-02-09 10:47:01.898-08]	[  8]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 10:48:52.913-08]	[  9]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 10:49:44.971-08]	[ 11]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-02-09 10:49:45.314-08]	[ 11]	[ERROR]	VCConn - cConnectionAPCI.ReadParameter  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: cParameter.New_1 (1206) Apcidb.dll ********, ERR: 4 in FN: 1018. Data is missing in APCI database., MID:140, MSW:20752437, DS1:20428654, Parameter:MXQ
- VCAPCI: cParameter.New_1 (-)
- VCAPCI: cMIDParameters.AddParameter (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cMIDParameters.AddParameter(String sParameterCode)
   at Volvo.VCADSPro.VCConn.Parameters.Add(String parameterCode)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadParameter(IParameters parameters, cSIParameter SIPar)

[2025-02-09 10:49:45.720-08]	[ 11]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-02-09 10:49:45.845-08]	[ 11]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 10:52:06.164-08]	[  4]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-02-09 10:52:16.278-08]	[ 12]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-02-09 10:52:16.794-08]	[ 12]	[ERROR]	VCConn - cConnectionAPCI.ReadParameter  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: cParameter.New_1 (1206) Apcidb.dll ********, ERR: 4 in FN: 1018. Data is missing in APCI database., MID:140, MSW:20752437, DS1:20428654, Parameter:MXQ
- VCAPCI: cParameter.New_1 (-)
- VCAPCI: cMIDParameters.AddParameter (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cMIDParameters.AddParameter(String sParameterCode)
   at Volvo.VCADSPro.VCConn.Parameters.Add(String parameterCode)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadParameter(IParameters parameters, cSIParameter SIPar)

[2025-02-09 10:52:17.200-08]	[ 12]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-02-09 10:52:17.340-08]	[ 12]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 10:53:36.463-08]	[  9]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 10:54:41.975-08]	[  6]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 10:56:00.878-08]	[ 13]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 10:56:37.067-08]	[  7]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 10:57:28.090-08]	[  3]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-02-09 10:57:28.856-08]	[  3]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-02-09 10:57:28.981-08]	[  3]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 10:59:58.735-08]	[ 12]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-02-09 10:59:59.407-08]	[ 12]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-02-09 10:59:59.532-08]	[ 12]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 11:01:03.974-08]	[ 10]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 11:02:07.995-08]	[ 16]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-02-09 11:02:37.098-08]	[ 17]	[INFO ]	VCServices - CurrentVehicle.RefreshLockCommunication  VCADS Renewing lock of PS.
[2025-02-09 11:02:37.160-08]	[ 16]	[ERROR]	VCConn - cConnectionAPCI.InitAPCI  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: mAPCICom.subAPCI_1 (1118) Apci.dll *********, ERR: 12 in FN: 1. No Volvo 88890020 adapter can be detected, or wrong W-LAN settings.
- VCAPCI: mAPCICom.subAPCI_1 (-)
- VCAPCI: cAPCI.OpenCommunication (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cAPCI.OpenCommunication(Int32[] arrPassword)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.InitAPCI(String sLanguageCode, Boolean bSimulated, Boolean bFillMIDs, VTIdentInfo udtIdentinfo, cUser oUser, Boolean& simulationDataMissing)

[2025-02-09 11:02:37.223-08]	[ 16]	[ERROR]	VCConn - cConnectionAPCI.ReadSIParameters  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: mAPCICom.subAPCI_1 (1118) Apci.dll *********, ERR: 12 in FN: 1. No Volvo 88890020 adapter can be detected, or wrong W-LAN settings.
- VCAPCI: mAPCICom.subAPCI_1 (-)
- VCAPCI: cAPCI.OpenCommunication (-)
- VCConn: cConnectionApci.InitAPCI (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.InitAPCI(String sLanguageCode, Boolean bSimulated, Boolean bFillMIDs, VTIdentInfo udtIdentinfo, cUser oUser, Boolean& simulationDataMissing)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadSIParameters(cSIParameters ocolSIParameters, String sLanguageCode, VTIdentInfo udtIdentinfo, cUser oUser, VTConnectionInfo udtConnectionInfo)

[2025-02-09 11:02:37.223-08]	[ 16]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-02-09 11:02:42.284-08]	[ 16]	[ERROR]	VCUIService - ApciConnectionProxy.ReadVehicleParameters  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: mAPCICom.subAPCI_1 (1118) Apci.dll *********, ERR: 12 in FN: 1. No Volvo 88890020 adapter can be detected, or wrong W-LAN settings.
- VCAPCI: mAPCICom.subAPCI_1 (-)
- VCAPCI: cAPCI.OpenCommunication (-)
- VCConn: cConnectionApci.InitAPCI (-)
- VCConn: cConnectionAPCI.ReadSIParameters (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadSIParameters(cSIParameters ocolSIParameters, String sLanguageCode, VTIdentInfo udtIdentinfo, cUser oUser, VTConnectionInfo udtConnectionInfo)
   at Volvo.VCADSPro.VCUIService.Proxies.ApciConnectionProxy.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)

[2025-02-09 11:02:42.284-08]	[ 16]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
CommunicationErrorException: No adapter connected.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Proxies.ApciConnectionProxy.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ApciConnectionInfoManager.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.VssManager.GetFilteredVsses(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user, Boolean simulated)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

   Level: 1
   Source: VCAPCI
   Exception: - VCAPCI: mAPCICom.subAPCI_1 (1118) Apci.dll *********, ERR: 12 in FN: 1. No Volvo 88890020 adapter can be detected, or wrong W-LAN settings.
- VCAPCI: mAPCICom.subAPCI_1 (-)
- VCAPCI: cAPCI.OpenCommunication (-)
- VCConn: cConnectionApci.InitAPCI (-)
- VCConn: cConnectionAPCI.ReadSIParameters (-)
   StackTrace:
      at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
      at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadSIParameters(cSIParameters ocolSIParameters, String sLanguageCode, VTIdentInfo udtIdentinfo, cUser oUser, VTConnectionInfo udtConnectionInfo)
      at Volvo.VCADSPro.VCUIService.Proxies.ApciConnectionProxy.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)

[2025-02-09 11:03:46.212-08]	[ 18]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-02-09 11:03:51.680-08]	[ 13]	[INFO ]	VCServices - CurrentVehicle.RefreshLockCommunication  VCADS Renewing lock of PS.
[2025-02-09 11:03:51.742-08]	[ 18]	[ERROR]	VCConn - cConnectionAPCI.InitAPCI  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: mAPCICom.subAPCI_1 (1118) Apci.dll *********, ERR: 12 in FN: 1. No Volvo 88890020 adapter can be detected, or wrong W-LAN settings.
- VCAPCI: mAPCICom.subAPCI_1 (-)
- VCAPCI: cAPCI.OpenCommunication (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cAPCI.OpenCommunication(Int32[] arrPassword)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.InitAPCI(String sLanguageCode, Boolean bSimulated, Boolean bFillMIDs, VTIdentInfo udtIdentinfo, cUser oUser, Boolean& simulationDataMissing)

[2025-02-09 11:03:51.789-08]	[ 18]	[ERROR]	VCConn - cConnectionAPCI.ReadSIParameters  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: mAPCICom.subAPCI_1 (1118) Apci.dll *********, ERR: 12 in FN: 1. No Volvo 88890020 adapter can be detected, or wrong W-LAN settings.
- VCAPCI: mAPCICom.subAPCI_1 (-)
- VCAPCI: cAPCI.OpenCommunication (-)
- VCConn: cConnectionApci.InitAPCI (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.InitAPCI(String sLanguageCode, Boolean bSimulated, Boolean bFillMIDs, VTIdentInfo udtIdentinfo, cUser oUser, Boolean& simulationDataMissing)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadSIParameters(cSIParameters ocolSIParameters, String sLanguageCode, VTIdentInfo udtIdentinfo, cUser oUser, VTConnectionInfo udtConnectionInfo)

[2025-02-09 11:03:51.789-08]	[ 18]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-02-09 11:03:51.914-08]	[ 18]	[ERROR]	VCUIService - ApciConnectionProxy.ReadVehicleParameters  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: mAPCICom.subAPCI_1 (1118) Apci.dll *********, ERR: 12 in FN: 1. No Volvo 88890020 adapter can be detected, or wrong W-LAN settings.
- VCAPCI: mAPCICom.subAPCI_1 (-)
- VCAPCI: cAPCI.OpenCommunication (-)
- VCConn: cConnectionApci.InitAPCI (-)
- VCConn: cConnectionAPCI.ReadSIParameters (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadSIParameters(cSIParameters ocolSIParameters, String sLanguageCode, VTIdentInfo udtIdentinfo, cUser oUser, VTConnectionInfo udtConnectionInfo)
   at Volvo.VCADSPro.VCUIService.Proxies.ApciConnectionProxy.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)

[2025-02-09 11:03:51.914-08]	[ 18]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
CommunicationErrorException: No adapter connected.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Proxies.ApciConnectionProxy.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ApciConnectionInfoManager.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.VssManager.GetFilteredVsses(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user, Boolean simulated)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

   Level: 1
   Source: VCAPCI
   Exception: - VCAPCI: mAPCICom.subAPCI_1 (1118) Apci.dll *********, ERR: 12 in FN: 1. No Volvo 88890020 adapter can be detected, or wrong W-LAN settings.
- VCAPCI: mAPCICom.subAPCI_1 (-)
- VCAPCI: cAPCI.OpenCommunication (-)
- VCConn: cConnectionApci.InitAPCI (-)
- VCConn: cConnectionAPCI.ReadSIParameters (-)
   StackTrace:
      at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
      at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadSIParameters(cSIParameters ocolSIParameters, String sLanguageCode, VTIdentInfo udtIdentinfo, cUser oUser, VTConnectionInfo udtConnectionInfo)
      at Volvo.VCADSPro.VCUIService.Proxies.ApciConnectionProxy.ReadVehicleParameters(OperationItem operationItem, String languageCode, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)

[2025-02-09 11:04:28.121-08]	[ 16]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-02-09 11:04:28.809-08]	[ 16]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-02-09 11:04:28.934-08]	[ 16]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 11:05:33.814-08]	[ 11]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-02-09 11:05:34.470-08]	[ 11]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-02-09 11:05:34.595-08]	[ 11]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 11:06:41.255-08]	[ 21]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 11:07:28.615-08]	[ 23]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-09 11:08:17.867-08]	[ 19]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-02-10 00:31:03.119-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-02-10 00:31:03.135-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-02-10 00:31:03.135-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-02-10 04:58:17.353-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-02-10 04:58:17.369-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-02-10 04:58:17.369-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-02-10 04:58:17.853-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-02-10 04:58:18.165-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-02-10 05:10:00.799-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-10 06:10:02.292-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-10 06:13:52.762-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-12 05:18:23.628-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-13 04:23:37.069-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-13 05:45:32.497-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-17 05:38:50.278-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-17 05:46:40.096-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-18 06:39:16.604-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-18 06:40:49.861-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-18 07:43:34.165-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-18 08:44:46.955-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-18 10:07:37.883-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-18 10:49:17.773-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-19 07:14:53.047-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-19 09:06:41.666-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-19 09:35:22.753-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-20 07:58:35.073-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-20 08:01:48.542-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-23 23:54:31.987-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-24 06:14:43.885-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-24 06:20:13.122-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-24 09:28:40.619-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-24 09:34:33.433-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-25 07:03:21.475-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-27 05:38:33.353-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-27 06:56:07.731-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-27 07:00:55.752-08]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-02-27 07:11:47.507-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-02-27 07:11:47.507-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-02-27 07:13:56.171-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-02-27 07:13:56.171-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-02-27 07:13:56.187-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-02-27 07:13:56.657-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-02-27 07:13:56.951-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-03-01 13:36:21.171-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-02 15:04:56.774-08]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-04 13:51:18.092-08]	[  7]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-06 10:51:02.655-08]	[  7]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-06 11:03:20.719-08]	[  7]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-07 12:17:04.350-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-03-07 12:17:04.381-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-03-07 12:17:04.381-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-03-07 12:42:49.761-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-03-07 12:42:49.777-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-03-07 12:42:49.783-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-03-07 12:42:50.184-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-03-07 12:42:50.485-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-03-07 12:55:31.202-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-08 11:51:07.905-08]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-08 12:28:58.711-08]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-03-08 12:28:58.743-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-03-08 12:28:58.743-08]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-03-10 13:36:17.770-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-03-10 13:36:17.786-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-03-10 13:36:17.786-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-03-10 13:36:18.177-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-03-10 13:36:18.458-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-03-10 13:39:37.497-07]	[  3]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-03-10 13:39:47.429-07]	[  3]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-10 13:43:37.097-07]	[  8]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-10 13:45:59.489-07]	[ 13]	[INFO ]	VCServices - CurrentVehicle.LockCommunication  VCADS Getting lock of PS.
[2025-03-10 13:46:04.955-07]	[ 13]	[ERROR]	VCConn - cConnectionAPCI.ReadParameter  
Level: 0
Source: VCAPCI
Exception: - VCAPCI: cParameter.New_1 (1206) Apcidb.dll ********, ERR: 4 in FN: 1018. Data is missing in APCI database., MID:128, MSW:20762948, DS1:11423745, Parameter:IVI
- VCAPCI: cParameter.New_1 (-)
- VCAPCI: cMIDParameters.AddParameter (-)
StackTrace:
   at Microsoft.VisualBasic.ErrObject.Raise(Int32 Number, Object Source, Object Description, Object HelpFile, Object HelpContext)
   at Volvo.VCADSPro.VCAPCI.cMIDParameters.AddParameter(String sParameterCode)
   at Volvo.VCADSPro.VCConn.Parameters.Add(String parameterCode)
   at Volvo.VCADSPro.VCConn.cConnectionAPCI.ReadParameter(IParameters parameters, cSIParameter SIPar)

[2025-03-10 13:46:05.033-07]	[ 13]	[INFO ]	VCServices - CurrentVehicle.UnlockCommunication  VCADS Releasing lock of PS.
[2025-03-10 13:46:05.158-07]	[ 13]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-10 13:46:27.368-07]	[ 16]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-10 13:49:10.008-07]	[  6]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-10 13:50:30.718-07]	[ 11]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-10 13:51:26.648-07]	[ 14]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-10 13:54:30.662-07]	[ 19]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-10 13:56:31.642-07]	[ 19]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-10 13:58:31.707-07]	[ 24]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-10 13:59:46.584-07]	[ 22]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-13 14:48:36.205-07]	[ 24]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-03-13 14:48:44.397-07]	[ 24]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-13 14:48:44.522-07]	[ 24]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-03-13 14:52:43.712-07]	[ 28]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-13 14:52:43.805-07]	[ 27]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-03-13 14:54:13.901-07]	[ 31]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-03-13 14:54:18.334-07]	[ 31]	[INFO ]	VCUIService - DevelopmentModeManager.GetDevelopmentModeClasses  User is not developer for this profile.
[2025-03-13 14:54:18.412-07]	[  9]	[WARN ]	VCUIService - ServiceFacade.GetConnectionInfo  Please note that some operations may not work as intended until thechassis ID diff is resolved.
[2025-03-16 12:52:34.667-07]	[ 19]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-03-16 12:52:48.171-07]	[ 19]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 12:52:52.032-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 12:52:58.783-07]	[ 29]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 12:53:22.595-07]	[ 27]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 12:53:32.307-07]	[ 24]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-03-16 12:53:38.039-07]	[ 26]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 12:54:01.333-07]	[ 25]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 12:54:15.616-07]	[ 20]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 12:54:42.094-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-03-16 12:54:42.110-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-03-16 12:54:42.110-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-03-16 12:56:22.826-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-03-16 12:56:22.842-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-03-16 12:56:22.842-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-03-16 12:56:23.311-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-03-16 12:56:23.654-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-03-16 12:59:24.168-07]	[  3]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-03-16 12:59:31.185-07]	[  3]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 12:59:42.639-07]	[  8]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-03-16 12:59:46.185-07]	[  7]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 12:59:54.229-07]	[  3]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:00:00.357-07]	[  8]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:00:16.146-07]	[  7]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:00:27.042-07]	[ 10]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:00:39.334-07]	[ 10]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:04:19.824-07]	[  4]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-03-16 13:04:25.479-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:04:32.328-07]	[ 10]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:04:45.210-07]	[ 10]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:05:11.114-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:06:32.638-07]	[  7]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:22:50.530-07]	[ 11]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-03-16 13:22:56.201-07]	[ 11]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:23:00.561-07]	[ 11]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:23:08.151-07]	[ 14]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-03-16 13:23:19.952-07]	[ 14]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoChassisIdFoundException: VCADS Needs Chassis Id for the product, Go back to Product Identification and enter a Chassis Id
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:23:29.577-07]	[ 12]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:23:47.801-07]	[ 10]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:24:09.753-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:24:17.522-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:40:35.818-07]	[  4]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-03-16 13:40:40.576-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:40:46.142-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:41:46.048-07]	[  3]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:47:18.000-07]	[ 16]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-03-16 13:47:27.705-07]	[ 16]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:47:50.262-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:48:24.589-07]	[ 18]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-16 13:48:50.881-07]	[ 19]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoVehicleConnectionInTechToolException: VCADS Needs connection in TechTool for this product. All empty nodes is not alowed for this operation.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-03-18 11:51:55.818-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-03-18 11:51:55.833-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-03-18 11:51:55.833-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-03-18 11:54:38.525-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-03-18 11:54:38.539-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-03-18 11:54:38.539-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-03-18 11:54:39.039-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-03-18 11:54:39.368-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-03-18 15:15:25.068-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-03-18 15:15:25.084-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-03-18 15:15:25.084-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-03-18 15:17:31.920-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-03-18 15:17:31.936-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-03-18 15:17:31.936-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-03-18 15:17:32.373-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-03-18 15:17:32.686-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-03-18 15:22:39.599-07]	[  3]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-18 16:45:33.291-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-03-18 16:45:33.291-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-03-18 16:45:33.291-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-03-18 16:55:07.362-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-03-18 16:55:07.378-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-03-18 16:55:07.378-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-03-18 16:55:07.753-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-03-18 16:55:08.065-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-03-18 17:03:22.921-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-18 17:04:31.538-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-19 14:20:23.994-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-19 14:50:07.866-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-03-19 14:50:07.866-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-03-19 14:52:18.734-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-03-19 14:52:18.745-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-03-19 14:52:18.749-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-03-19 14:52:19.305-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-03-19 14:52:19.616-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-03-19 14:55:33.011-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-03-19 14:55:33.027-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-03-19 14:55:33.027-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-03-19 14:57:57.089-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-03-19 14:57:57.104-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-03-19 14:57:57.104-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-03-19 14:57:57.636-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-03-19 14:57:57.948-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-03-19 15:01:31.594-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-03-19 15:01:31.594-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-03-19 15:03:31.825-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-03-19 15:03:31.841-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-03-19 15:03:31.841-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-03-19 15:03:32.387-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-03-19 15:03:32.669-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-03-20 10:55:58.061-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-03-20 10:55:58.077-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-03-20 10:55:58.108-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-03-20 11:05:04.945-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-03-20 11:05:04.961-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-03-20 11:05:04.961-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-03-20 11:05:05.398-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-03-20 11:05:05.836-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-03-25 16:23:16.826-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-03-25 16:23:17.009-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-03-25 16:23:17.009-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-03-25 16:25:25.258-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-03-25 16:25:25.258-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-03-25 16:25:25.274-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-03-25 16:25:52.970-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-03-25 16:25:53.283-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-03-25 16:27:39.561-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-26 14:22:10.212-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-28 14:14:39.695-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-29 12:42:58.001-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-29 12:45:11.511-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-03-31 03:52:40.300-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-03-31 03:52:40.316-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-03-31 03:52:40.316-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-04-06 06:33:37.358-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-04-06 06:33:37.374-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-04-06 06:33:37.374-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-04-06 06:33:37.842-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-04-06 06:33:38.186-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-04-06 06:35:37.161-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-07 02:19:03.698-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-08 10:05:53.489-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-08 10:13:55.564-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-08 11:34:04.812-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-08 11:41:30.402-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-10 05:44:05.460-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-10 05:46:33.686-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-13 11:55:13.410-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-13 11:55:40.675-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-16 11:43:05.137-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-16 11:47:45.855-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-16 11:52:57.284-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-16 11:54:46.818-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-16 12:05:08.369-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-04-16 12:05:08.384-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-04-16 12:05:08.384-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-04-17 04:11:40.362-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-04-17 04:11:40.378-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-04-17 04:11:40.378-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-04-17 04:11:40.893-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-04-17 04:11:41.206-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-04-17 04:15:13.095-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-04-17 04:15:13.095-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-04-17 04:16:50.933-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-04-17 04:16:50.948-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-04-17 04:16:50.948-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-04-17 04:16:51.495-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-04-17 04:16:51.807-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-04-17 04:17:31.424-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-04-17 04:17:31.439-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-04-17 04:17:31.439-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-04-17 04:19:32.971-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-04-17 04:19:32.971-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-04-17 04:19:32.987-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-04-17 04:19:33.471-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-04-17 04:19:33.784-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-04-17 04:44:03.459-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-17 04:50:39.786-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-19 07:40:08.675-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-19 10:59:49.359-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-19 12:44:52.196-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-19 12:48:42.817-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-19 13:08:02.940-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-19 13:11:06.546-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-21 10:25:52.370-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-04-21 10:25:52.370-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-04-21 10:25:52.370-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-04-21 10:29:48.210-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-04-21 10:29:48.226-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-04-21 10:29:48.226-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-04-21 10:29:49.569-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-04-21 10:29:49.913-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-04-21 10:38:40.035-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-21 10:41:21.154-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-22 13:14:58.337-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-24 05:33:03.310-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-26 12:14:28.852-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-26 12:18:54.330-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-27 07:38:05.094-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-27 07:40:47.882-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-27 09:34:33.563-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-04-27 09:38:15.874-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-03 00:07:21.171-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-03 00:32:34.741-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-03 05:51:39.687-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-03 06:01:01.194-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-08 13:07:44.484-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-10 06:12:07.490-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-05-10 06:12:07.506-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-05-10 06:12:07.506-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-05-10 06:12:08.193-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-05-10 06:12:08.506-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-05-12 10:58:46.050-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-15 10:09:15.530-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-17 01:25:06.147-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-19 05:23:12.288-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-22 11:24:47.783-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-24 01:05:39.230-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-26 09:18:46.370-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-26 11:11:00.238-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-26 11:13:04.278-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-29 06:16:06.345-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-05-29 06:19:56.543-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-01 08:29:28.735-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-01 12:37:20.300-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-06-01 12:37:20.425-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-06-01 12:37:20.441-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-06-01 12:41:42.053-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-06-01 12:41:42.068-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-06-01 12:41:42.068-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-06-01 12:41:42.537-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-06-01 12:41:42.881-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-06-01 12:43:10.317-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-01 14:51:02.867-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-02 06:02:12.795-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-02 07:20:02.931-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-02 09:22:02.877-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-03 08:33:34.523-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-03 08:36:30.525-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-03 09:35:24.239-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-14 01:51:26.350-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-14 09:23:06.339-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-14 09:29:42.546-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-15 01:10:34.260-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-06-15 01:10:34.260-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-06-15 01:14:35.890-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-06-15 01:14:35.890-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-06-15 01:14:35.890-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-06-15 01:14:36.561-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-06-15 01:14:37.154-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-06-15 01:16:45.817-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-06-15 01:16:45.833-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-06-15 01:16:45.833-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-06-15 01:16:46.364-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-06-15 01:16:46.692-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-06-15 01:18:10.259-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-15 01:24:29.769-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-15 05:10:06.363-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-15 05:13:05.874-07]	[  6]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-17 10:40:24.754-07]	[  5]	[INFO ]	VCSI - cOpGroups.Fill  Manual matching.ManualMatching
[2025-06-17 10:40:39.263-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.ViewOperation  
Level: 0
Source: VCUIService
NoEcuFoundException: No control units were found.
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.ValidateConnectionInfo(Boolean simulated, Boolean operationAllowsAllEmptyNodes, ConnectionInfo connectionInfo, IdentificationInfo identificationInfo, User user)
   at Volvo.VCADSPro.VCUIService.Managers.ConnectionInfoManager.GetConnectionInfo(Boolean simulated, Boolean isMode, Boolean vehicleConnectionInIsMode, Boolean operationAllowsAllEmptyNodes, IList`1 vsses, IdentificationInfo identificationInfo, User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetConnectionInfo(String operationId, Boolean simulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationFilteredVsses(String operationId, Boolean isSimulated, Boolean isMode)
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.ViewOperation(ViewOperationRequest viewOperationRequest)

[2025-06-21 00:44:45.570-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-21 07:32:21.443-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-22 07:30:32.069-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-22 07:51:38.185-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-25 11:55:58.219-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-25 12:01:27.008-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-25 13:05:18.288-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-26 07:24:19.256-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-26 07:31:49.152-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-27 08:56:51.942-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-28 07:57:40.712-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-28 10:58:12.472-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-06-28 11:06:28.414-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-07-03 12:16:05.054-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-07-08 07:19:41.284-07]	[  9]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-07-08 08:11:16.395-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-07-08 08:11:16.441-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-07-08 08:11:16.457-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-07-08 08:13:41.295-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-07-08 08:13:41.310-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-07-08 08:13:41.310-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-07-08 08:13:41.795-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-07-08 08:13:42.107-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-07-08 08:14:58.181-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-07-09 12:48:26.588-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-07-09 12:48:26.635-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-07-09 12:48:26.635-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-07-09 12:50:12.139-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-07-09 12:50:12.154-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-07-09 12:50:12.154-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-07-09 12:50:12.529-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-07-09 12:50:12.810-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-07-09 14:32:10.836-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-07-09 14:32:10.851-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-07-09 14:32:10.851-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-07-09 14:33:58.199-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-07-09 14:33:58.215-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-07-09 14:33:58.215-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-07-09 14:33:58.621-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-07-09 14:33:58.918-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-07-09 14:35:50.066-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-07-09 14:35:50.066-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-07-09 14:37:28.394-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-07-09 14:37:28.409-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-07-09 14:37:28.409-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-07-09 14:37:28.862-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-07-09 14:37:29.112-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-07-10 06:26:57.735-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-07-14 14:34:59.474-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-07-14 14:41:53.301-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-07-15 06:30:30.480-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-07-16 05:49:12.470-07]	[  5]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-07-16 10:46:41.442-07]	[  1]	[ERROR]	VolvoIt.Baf.ServiceHostProcess - Program.WaitMessage  The pipe is broken. Shutting down.
[2025-07-16 10:46:41.489-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed ServiceFacade
[2025-07-16 10:46:41.504-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostClosed  Closed VssGroupServicePort
[2025-07-16 10:50:33.004-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHostProcess - Program.RunServiceHostController  Starting: Name VCADS, CrashAction Restart, ModuleCount 2
[2025-07-16 10:50:33.036-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\Waf-VcadsPro.dll
[2025-07-16 10:50:33.036-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - AssemblyPathStrategy.Load  Loading dll C:\Program Files (x86)\Tech Tool\Tech Tool\..\..\VCADS Pro\VCADSPro\System\VCUIService.dll
[2025-07-16 10:50:33.582-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened VssGroupServicePort
[2025-07-16 10:50:34.004-07]	[  1]	[INFO ]	VolvoIt.Baf.ServiceHost - WcfServiceHost.HostOpened  Opened ServiceFacade
[2025-07-16 10:51:54.150-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-07-16 10:54:17.165-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

[2025-07-17 04:58:37.523-07]	[  4]	[ERROR]	VCUIService - ServiceFacade.GetOperationList  
Level: 0
Source: VCUIService
UnsupportedVehicleException: This vehicle is not supported by VCADS
StackTrace:
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.VerifyIdentificationInfo(User user, String languageCode, IdentificationInfo identificationInfo)
   at Volvo.VCADSPro.VCUIService.Managers.IdentificationInfoManager.GetIdentificationInfo(User user, SessionSettings sessionSettings)
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetIdentificationInfo()
   at Volvo.VCADSPro.VCUIService.Managers.SessionManager.GetOperationItems()
   at Volvo.VCADSPro.VCUIService.Managers.OperationManager.GetOperationList()
   at Volvo.VCADSPro.VCUIService.Services.ServiceFacade.GetOperationList(GetOperationListRequest getOperationListRequest)

