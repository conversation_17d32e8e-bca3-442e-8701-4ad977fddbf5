@echo off
echo Starting VolvoFlashWR in Normal Mode...
echo.
echo Setting up environment...
set USE_PATCHED_IMPLEMENTATION=true
set PHOENIX_VOCOM_ENABLED=true
set VERBOSE_LOGGING=true
set APCI_LIBRARY_PATH=%~dp0Libraries
set PATH=%~dp0Libraries;%~dp0Libraries\VCRedist;%~dp0Drivers\Vocom;%PATH%

echo Starting application...
"%~dp0VolvoFlashWR.Launcher.exe"

if errorlevel 1 (
    echo.
    echo Application exited with error code %errorlevel%
    echo Check the logs for more information.
    pause
) else (
    echo.
    echo Application completed successfully.
    pause
)
