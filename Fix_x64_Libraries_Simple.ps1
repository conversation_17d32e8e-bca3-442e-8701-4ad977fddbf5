# VolvoFlashWR x64 Architecture Library Fix Script (Simplified)
# This script resolves library loading issues while maintaining x64 architecture

param(
    [switch]$Force,
    [string]$OutputPath = ".\VolvoFlashWR_Export_With_Fix\Libraries"
)

$ErrorActionPreference = "Continue"

Write-Host "=== VolvoFlashWR x64 Architecture Library Fix ===" -ForegroundColor Green
Write-Host "This script will fix library loading issues while maintaining x64 architecture" -ForegroundColor Yellow
Write-Host ""

# Create output directory
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "Created output directory: $OutputPath" -ForegroundColor Green
}

# Function to get file architecture
function Get-FileArchitecture {
    param([string]$FilePath)
    
    if (!(Test-Path $FilePath)) {
        return "NotFound"
    }
    
    try {
        $bytes = [System.IO.File]::ReadAllBytes($FilePath)
        if ($bytes.Length -lt 64) { return "Invalid" }
        
        # Get PE header offset
        $peOffset = [BitConverter]::ToInt32($bytes, 60)
        if ($peOffset + 24 -ge $bytes.Length) { return "Invalid" }
        
        # Get machine type
        $machineType = [BitConverter]::ToUInt16($bytes, $peOffset + 4)
        
        switch ($machineType) {
            0x014c { return "x86" }
            0x8664 { return "x64" }
            default { return "Unknown" }
        }
    }
    catch {
        return "Error"
    }
}

# Step 1: Download and install Visual C++ Redistributables
Write-Host "Step 1: Installing Visual C++ Redistributables" -ForegroundColor Cyan

$vcRedistUrls = @{
    "VC2015-2022_x64" = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
    "VC2013_x64" = "https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x64.exe"
}

$tempDir = [System.IO.Path]::GetTempPath()

foreach ($redist in $vcRedistUrls.GetEnumerator()) {
    $tempFile = Join-Path $tempDir "$($redist.Key).exe"
    
    Write-Host "Downloading $($redist.Key)..." -ForegroundColor Yellow
    try {
        $webClient = New-Object System.Net.WebClient
        $webClient.DownloadFile($redist.Value, $tempFile)
        Write-Host "✓ Downloaded: $($redist.Key)" -ForegroundColor Green
        
        Write-Host "Installing $($redist.Key)..." -ForegroundColor Yellow
        $process = Start-Process -FilePath $tempFile -ArgumentList "/quiet", "/norestart" -Wait -PassThru
        if ($process.ExitCode -eq 0) {
            Write-Host "✓ Installed: $($redist.Key)" -ForegroundColor Green
        } else {
            Write-Host "⚠ Installation completed with exit code: $($process.ExitCode)" -ForegroundColor Yellow
        }
        
        if (Test-Path $tempFile) { 
            Remove-Item $tempFile -Force 
        }
    }
    catch {
        Write-Host "✗ Failed to download/install $($redist.Key): $($_.Exception.Message)" -ForegroundColor Red
    }
    finally {
        if ($webClient) { 
            $webClient.Dispose() 
        }
    }
}

# Step 2: Copy system libraries to application directory
Write-Host "`nStep 2: Copying system libraries" -ForegroundColor Cyan

$requiredLibraries = @(
    "msvcr140.dll",
    "msvcp140.dll", 
    "vcruntime140.dll",
    "api-ms-win-crt-runtime-l1-1-0.dll",
    "api-ms-win-crt-heap-l1-1-0.dll",
    "api-ms-win-crt-string-l1-1-0.dll",
    "api-ms-win-crt-stdio-l1-1-0.dll",
    "api-ms-win-crt-math-l1-1-0.dll",
    "api-ms-win-crt-locale-l1-1-0.dll"
)

$systemPaths = @(
    "$env:SystemRoot\System32",
    "$env:SystemRoot\SysWOW64"
)

foreach ($library in $requiredLibraries) {
    $found = $false
    $targetPath = Join-Path $OutputPath $library
    
    # Skip if already exists and not forcing
    if ((Test-Path $targetPath) -and !$Force) {
        $arch = Get-FileArchitecture -FilePath $targetPath
        Write-Host "✓ Already exists: $library ($arch)" -ForegroundColor Green
        continue
    }
    
    foreach ($systemPath in $systemPaths) {
        $sourcePath = Join-Path $systemPath $library
        if (Test-Path $sourcePath) {
            $arch = Get-FileArchitecture -FilePath $sourcePath
            
            # Prefer x64 libraries for x64 application
            if ($arch -eq "x64" -or !$found) {
                try {
                    Copy-Item -Path $sourcePath -Destination $targetPath -Force
                    Write-Host "✓ Copied: $library ($arch) from $systemPath" -ForegroundColor Green
                    $found = $true
                    if ($arch -eq "x64") { break } # Prefer x64, so break if found
                }
                catch {
                    Write-Host "✗ Failed to copy $library from $systemPath`: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
    }
    
    if (!$found) {
        Write-Host "⚠ Not found: $library" -ForegroundColor Yellow
    }
}

# Step 3: Analyze APCI library compatibility
Write-Host "`nStep 3: Analyzing APCI library compatibility" -ForegroundColor Cyan

$apciLibraries = @(
    "apci.dll",
    "apcidb.dll", 
    "Volvo.ApciPlus.dll",
    "Volvo.ApciPlusData.dll",
    "WUDFPuma.dll"
)

$appPath = ".\VolvoFlashWR_Export_With_Fix"
$librariesPath = Join-Path $appPath "Libraries"

Write-Host "Checking APCI library architectures:" -ForegroundColor Yellow

$requiresBridge = $false
foreach ($library in $apciLibraries) {
    $paths = @(
        (Join-Path $appPath $library),
        (Join-Path $librariesPath $library)
    )
    
    foreach ($path in $paths) {
        if (Test-Path $path) {
            $arch = Get-FileArchitecture -FilePath $path
            if ($arch -eq "x86") {
                Write-Host "⚠ Architecture mismatch: $library is x86 (needs x64 process compatibility)" -ForegroundColor Yellow
                $requiresBridge = $true
            } elseif ($arch -eq "x64") {
                Write-Host "✓ Compatible: $library is x64" -ForegroundColor Green
            } else {
                Write-Host "? Unknown architecture: $library ($arch)" -ForegroundColor Gray
            }
            break
        }
    }
}

# Step 4: Configure environment and create startup script
Write-Host "`nStep 4: Creating enhanced startup configuration" -ForegroundColor Cyan

# Create enhanced startup script
$startupScript = @"
@echo off
echo === VolvoFlashWR x64 Enhanced Startup ===
echo Setting up environment for x64 architecture compatibility...

REM Set environment variables for proper library loading
set USE_PATCHED_IMPLEMENTATION=true
set PHOENIX_VOCOM_ENABLED=true
set VERBOSE_LOGGING=true
set APCI_LIBRARY_PATH=%~dp0Libraries
set FORCE_ARCHITECTURE_BRIDGE=$($requiresBridge.ToString().ToLower())

REM Add Libraries directory to PATH
set PATH=%~dp0Libraries;%PATH%

echo Environment configured for x64 compatibility
echo Architecture Bridge Required: $requiresBridge
echo Starting VolvoFlashWR...
echo.

REM Start the application
"%~dp0VolvoFlashWR.Launcher.exe"

if errorlevel 1 (
    echo.
    echo === Application Error Detected ===
    echo Check the logs in the Logs directory for details
    echo Common issues:
    echo - Missing Visual C++ Redistributables
    echo - Architecture compatibility problems
    echo - Hardware connection issues
    echo.
    pause
)
"@

$startupScriptPath = Join-Path $appPath "Run_x64_Compatible.bat"
$startupScript | Set-Content $startupScriptPath -Encoding ASCII
Write-Host "✓ Created enhanced startup script: Run_x64_Compatible.bat" -ForegroundColor Green

# Step 5: Summary
Write-Host "`n=== SUMMARY ===" -ForegroundColor Green
Write-Host "✓ Visual C++ Redistributables installed" -ForegroundColor Green
Write-Host "✓ System libraries copied to application" -ForegroundColor Green
Write-Host "✓ APCI library compatibility analyzed" -ForegroundColor Green
Write-Host "✓ Enhanced startup script created" -ForegroundColor Green

if ($requiresBridge) {
    Write-Host "⚠ Architecture bridge required for x86 APCI libraries" -ForegroundColor Yellow
    Write-Host "  The application will automatically use bridge mode" -ForegroundColor Gray
}

Write-Host "`n=== NEXT STEPS ===" -ForegroundColor Cyan
Write-Host "1. Use the enhanced startup script: .\VolvoFlashWR_Export_With_Fix\Run_x64_Compatible.bat" -ForegroundColor White
Write-Host "2. The application will handle architecture compatibility automatically" -ForegroundColor White  
Write-Host "3. Check logs in the Logs directory if issues persist" -ForegroundColor White

Write-Host "`n=== FIX COMPLETED ===" -ForegroundColor Green
Write-Host "The x64 architecture library issues have been addressed." -ForegroundColor Green
