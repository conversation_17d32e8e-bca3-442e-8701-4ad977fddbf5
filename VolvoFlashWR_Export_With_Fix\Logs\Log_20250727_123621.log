Log started at 7/27/2025 12:36:21 PM
2025-07-27 12:36:21.516 [Information] LoggingService: Logging service initialized
2025-07-27 12:36:21.533 [Information] App: Starting integrated application initialization
2025-07-27 12:36:21.535 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-27 12:36:21.538 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-27 12:36:21.540 [Information] IntegratedStartupService: Setting up application environment
2025-07-27 12:36:21.541 [Information] IntegratedStartupService: Application environment setup completed
2025-07-27 12:36:21.543 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-27 12:36:21.545 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-27 12:36:21.546 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-27 12:36:21.552 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-27 12:36:21.558 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 12:36:21.560 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.562 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.563 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-27 12:36:21.566 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 12:36:21.569 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.572 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.573 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 12:36:21.576 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 12:36:21.579 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.581 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.582 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 12:36:21.585 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 12:36:21.588 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.591 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.592 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 12:36:21.595 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 12:36:21.599 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.601 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.602 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 12:36:21.606 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 12:36:21.608 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.610 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.611 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 12:36:21.614 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 12:36:21.616 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.618 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 12:36:21.619 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 12:36:21.621 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-27 12:36:21.623 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-27 12:36:21.624 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-27 12:36:21.625 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-27 12:36:21.626 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-27 12:36:21.626 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-27 12:36:21.627 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-27 12:36:21.627 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-27 12:36:21.630 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-27 12:36:21.630 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-27 12:36:21.632 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-27 12:36:21.632 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 12:36:21.632 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 12:36:21.633 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 12:36:21.633 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 12:36:21.633 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 12:36:21.634 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 12:36:21.642 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-27 12:36:21.642 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-27 12:36:21.643 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-27 12:36:21.649 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-27 12:36:21.649 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-27 12:36:21.651 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-27 12:36:21.653 [Information] LibraryExtractor: Starting library extraction process
2025-07-27 12:36:21.656 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-27 12:36:21.660 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-27 12:36:21.662 [Information] LibraryExtractor: Copying system libraries
2025-07-27 12:36:21.668 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-27 12:36:21.676 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-27 12:36:51.690 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 12:36:52.693 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-27 12:37:22.698 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 12:37:22.699 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 12:37:52.702 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 12:37:53.703 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-27 12:38:23.706 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 12:38:23.707 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 12:38:53.710 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 12:38:54.711 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-27 12:39:24.715 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 12:39:24.717 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-27 12:39:24.718 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-27 12:39:24.722 [Information] LibraryExtractor: Verifying library extraction
2025-07-27 12:39:24.723 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-27 12:39:24.723 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-27 12:39:24.723 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-27 12:39:24.724 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-27 12:39:24.724 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-27 12:39:24.728 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-27 12:39:24.730 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-27 12:39:24.732 [Information] DependencyManager: Initializing dependency manager
2025-07-27 12:39:24.733 [Information] DependencyManager: Setting up library search paths
2025-07-27 12:39:24.734 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 12:39:24.734 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 12:39:24.734 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 12:39:24.736 [Information] DependencyManager: Updated PATH environment variable
2025-07-27 12:39:24.738 [Information] DependencyManager: Verifying required directories
2025-07-27 12:39:24.739 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 12:39:24.739 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 12:39:24.739 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-27 12:39:24.740 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 12:39:24.742 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-27 12:39:24.748 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 12:39:24.750 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 12:39:24.750 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 12:39:24.752 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 12:39:24.754 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 12:39:24.754 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 12:39:24.755 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 12:39:32.009 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll from C:\Windows\system32\msvcp140.dll: Error 193
2025-07-27 12:39:32.013 [Warning] DependencyManager: Architecture mismatch detected for msvcp140.dll. Expected: x64
2025-07-27 12:39:32.014 [Debug] DependencyManager: Architecture mismatch: Library msvcp140.dll is x86, process is x64
2025-07-27 12:39:32.014 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp140.dll
2025-07-27 12:39:32.592 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll: Error 193
2025-07-27 12:39:32.593 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp140.dll
2025-07-27 12:39:32.598 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 12:39:32.601 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 12:39:32.609 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 12:39:32.609 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 12:39:32.611 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 12:39:32.611 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 12:39:32.612 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 12:39:32.613 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 12:39:32.613 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 12:39:32.614 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 12:39:32.619 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 12:39:32.621 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 12:39:32.623 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 12:39:32.624 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 12:39:32.625 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 12:39:32.625 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 12:39:32.627 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 12:39:32.627 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 12:39:32.629 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 12:39:32.629 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 12:39:32.630 [Information] DependencyManager: VC++ Redistributable library loading: 3/14 (21.4%) libraries loaded
2025-07-27 12:39:32.630 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-27 12:39:32.632 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-27 12:39:32.633 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-27 12:39:32.635 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 12:39:32.637 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-27 12:39:32.638 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 12:39:32.639 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 12:39:32.640 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-27 12:39:32.640 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-27 12:39:32.640 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-27 12:39:32.641 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-27 12:39:32.641 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-07-27 12:39:32.642 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-27 12:39:32.643 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-27 12:39:32.643 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-27 12:39:32.643 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-27 12:39:32.644 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-07-27 12:39:32.645 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-27 12:39:32.645 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-27 12:39:32.646 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-27 12:39:32.646 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-27 12:39:32.647 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-07-27 12:39:32.648 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-27 12:39:32.648 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-27 12:39:32.649 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 12:39:32.649 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 12:39:32.650 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 12:39:32.651 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 12:39:32.651 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 12:39:32.652 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 12:39:32.654 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-27 12:39:32.655 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 12:39:32.659 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-27 12:39:32.659 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 12:39:32.660 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 12:39:32.661 [Information] DependencyManager: Setting up environment variables
2025-07-27 12:39:32.661 [Information] DependencyManager: Environment variables configured
2025-07-27 12:39:32.663 [Information] DependencyManager: Verifying library loading status
2025-07-27 12:39:33.043 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-27 12:39:33.044 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-27 12:39:33.044 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-27 12:39:33.047 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-27 12:39:33.049 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-27 12:39:33.053 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-27 12:39:33.055 [Information] IntegratedStartupService: Verifying system readiness
2025-07-27 12:39:33.055 [Information] IntegratedStartupService: System readiness verification passed
2025-07-27 12:39:33.056 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-27 12:39:33.058 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-27 12:39:33.058 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 12:39:33.059 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 12:39:33.059 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 12:39:33.059 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-27 12:39:33.059 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-27 12:39:33.060 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-27 12:39:33.060 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 12:39:33.060 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-27 12:39:33.061 [Information] App: Integrated startup completed successfully
2025-07-27 12:39:33.064 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-27 12:39:33.080 [Information] App: Initializing application services
2025-07-27 12:39:33.082 [Information] AppConfigurationService: Initializing configuration service
2025-07-27 12:39:33.083 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 12:39:33.132 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 12:39:33.133 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-27 12:39:33.134 [Information] App: Configuration service initialized successfully
2025-07-27 12:39:33.137 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-27 12:39:33.138 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-27 12:39:33.144 [Information] App: Environment variable exists: True, not 'false': False
2025-07-27 12:39:33.144 [Information] App: Final useDummyImplementations value: False
2025-07-27 12:39:33.145 [Information] App: Updating config to NOT use dummy implementations
2025-07-27 12:39:33.147 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-27 12:39:33.163 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 12:39:33.164 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-27 12:39:33.164 [Information] App: usePatchedImplementation flag is: True
2025-07-27 12:39:33.165 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-27 12:39:33.165 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-27 12:39:33.165 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-27 12:39:33.166 [Information] App: verboseLogging flag is: True
2025-07-27 12:39:33.171 [Information] App: Verifying real hardware requirements...
2025-07-27 12:39:33.171 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-27 12:39:33.172 [Information] App: ✓ Found critical library: apci.dll
2025-07-27 12:39:33.172 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-27 12:39:33.172 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-27 12:39:33.173 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 12:39:33.173 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-27 12:39:33.173 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-27 12:39:33.174 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-27 12:39:33.185 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-27 12:39:33.188 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-27 12:39:33.188 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-27 12:39:33.190 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-27 12:39:33.194 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-07-27 12:44:33.197 [Warning] RuntimeDependencyResolver: Failed to download msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 300 seconds elapsing.
2025-07-27 12:44:33.199 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-27 12:44:33.199 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-27 12:44:33.200 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-27 12:44:33.200 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 12:44:33.201 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 12:44:33.201 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 12:44:33.201 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 12:44:33.202 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 12:44:33.203 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 12:44:33.204 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-27 12:44:33.205 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-27 12:44:33.205 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-27 12:44:33.206 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 12:44:33.206 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-27 12:44:33.206 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-27 12:44:33.207 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-27 12:44:33.207 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-27 12:44:33.207 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-27 12:44:33.209 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-27 12:44:33.209 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-27 12:44:33.211 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-27 12:44:33.212 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-27 12:44:33.212 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-27 12:44:33.212 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-27 12:44:33.215 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-27 12:44:33.215 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-07-27 12:44:33.217 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-07-27 12:44:33.218 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-27 12:44:33.220 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-07-27 12:44:33.220 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-07-27 12:44:33.249 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-27 12:44:33.250 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-07-27 12:44:33.256 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-27 12:44:33.256 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-27 12:44:33.257 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-27 12:44:33.257 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-27 12:44:33.261 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-27 12:44:33.261 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-27 12:44:33.261 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-27 12:44:33.262 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 12:44:33.262 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-27 12:44:33.263 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-27 12:44:33.264 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-27 12:44:33.266 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-27 12:44:33.269 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-27 12:44:33.272 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-27 12:44:33.276 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 12:44:33.276 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-27 12:44:33.287 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 12:44:33.311 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-27 12:44:33.313 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-27 12:44:33.313 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-27 12:44:33.315 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 12:44:33.315 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-07-27 12:44:33.315 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 12:44:33.316 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-07-27 12:44:33.316 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 12:44:33.316 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-27 12:44:33.317 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-27 12:44:33.318 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-27 12:44:33.318 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-27 12:44:33.319 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-27 12:44:33.320 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-27 12:44:33.320 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-27 12:44:33.322 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-27 12:44:33.324 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-27 12:44:33.324 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 12:44:33.325 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-27 12:44:33.325 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-27 12:44:33.326 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-27 12:44:33.327 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-27 12:44:33.328 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-27 12:44:33.328 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-27 12:44:33.328 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-27 12:44:33.328 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-27 12:44:33.329 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-27 12:44:33.330 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-27 12:44:33.330 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-27 12:44:33.330 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-27 12:44:33.332 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-27 12:44:33.332 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-27 12:44:33.333 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-27 12:44:33.333 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 12:44:33.335 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-27 12:44:33.337 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-27 12:44:33.337 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 12:44:33.338 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-27 12:44:33.338 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-27 12:44:33.340 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-27 12:44:33.340 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-27 12:44:33.340 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-27 12:44:33.342 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-27 12:44:33.345 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-27 12:44:33.345 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-27 12:44:33.350 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-27 12:44:33.351 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-27 12:44:33.351 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 12:44:33.351 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 12:44:33.356 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-27 12:44:33.357 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 12:44:33.358 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 12:44:33.358 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-27 12:44:33.359 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 12:44:33.359 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 12:44:33.360 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-27 12:44:33.360 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 12:44:33.360 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-27 12:44:33.361 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 12:44:33.361 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-27 12:44:33.362 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 12:44:33.362 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 12:44:33.363 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-27 12:44:33.364 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 12:44:33.364 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 12:44:33.365 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-27 12:44:33.366 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 12:44:33.366 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 12:44:33.367 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 12:44:33.367 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 12:44:33.368 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-27 12:44:33.368 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 12:44:33.369 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 12:44:33.370 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-27 12:44:33.370 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 12:44:33.371 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 12:44:33.371 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-27 12:44:33.372 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 12:44:33.372 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 12:44:33.373 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-27 12:44:33.373 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 12:44:33.374 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 12:44:33.375 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 12:44:33.378 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-27 12:44:33.378 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 12:44:33.379 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-27 12:44:33.380 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-27 12:44:33.380 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-27 12:44:33.381 [Information] VocomDriver: Initializing Vocom driver
2025-07-27 12:44:33.383 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-27 12:44:33.387 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-27 12:44:33.387 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 12:44:33.388 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 12:44:33.389 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 12:44:33.390 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 12:45:17.782 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-27 12:45:18.877 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-27 12:45:18.883 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-27 12:45:19.629 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-27 12:45:20.357 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-27 12:45:20.360 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 12:45:20.366 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-07-27 12:45:20.367 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-07-27 12:45:20.369 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-07-27 12:45:20.369 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-27 12:45:20.370 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-27 12:45:20.370 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-27 12:45:20.372 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-27 12:45:20.374 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-27 12:45:20.374 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-27 12:45:20.375 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-27 12:45:20.375 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-27 12:45:20.375 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-27 12:45:20.376 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-27 12:45:20.376 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-27 12:45:20.377 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-27 12:45:20.377 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-27 12:45:20.378 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-27 12:45:20.378 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-27 12:45:20.378 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-27 12:45:20.379 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-27 12:45:20.379 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-27 12:45:20.379 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-27 12:45:20.380 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-27 12:45:20.380 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-27 12:45:20.380 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-27 12:45:20.381 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-27 12:45:20.381 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-27 12:45:20.381 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-27 12:45:20.381 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-27 12:45:20.382 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-27 12:45:20.382 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-27 12:45:20.384 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-27 12:45:20.385 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-27 12:45:20.385 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-27 12:45:20.385 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-27 12:45:20.386 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-27 12:45:20.386 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-27 12:45:20.386 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-27 12:45:20.386 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-27 12:45:20.387 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-27 12:45:20.387 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-27 12:45:20.388 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-27 12:45:20.388 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-27 12:45:20.388 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-27 12:45:20.389 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-27 12:45:20.389 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-27 12:45:20.390 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-27 12:45:20.390 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-27 12:45:20.390 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-27 12:45:20.392 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-27 12:45:20.394 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-27 12:45:20.395 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-27 12:45:20.395 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-27 12:45:20.395 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-27 12:45:20.396 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-27 12:45:20.396 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-27 12:45:20.396 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-27 12:45:20.400 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-27 12:45:20.401 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-27 12:45:20.403 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-27 12:45:20.405 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-27 12:45:20.485 [Information] WiFiCommunicationService: WiFi is available
2025-07-27 12:45:20.486 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-27 12:45:20.488 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-27 12:45:20.489 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-27 12:45:20.491 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-27 12:45:20.492 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-27 12:45:20.494 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 12:45:20.496 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 12:45:20.497 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 12:45:20.499 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 12:45:20.504 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 12:45:20.505 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 12:45:20.505 [Information] VocomService: Native USB communication service initialized
2025-07-27 12:45:20.506 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 12:45:20.506 [Information] VocomService: Connection recovery service initialized
2025-07-27 12:45:20.507 [Information] VocomService: Enhanced services initialization completed
2025-07-27 12:45:20.509 [Information] VocomService: Checking if PTT application is running
2025-07-27 12:45:20.529 [Information] VocomService: PTT application is not running
2025-07-27 12:45:20.531 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 12:45:20.535 [Debug] VocomService: Bluetooth is enabled
2025-07-27 12:45:20.536 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 12:45:20.537 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-27 12:45:20.537 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-07-27 12:45:20.540 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 12:45:20.540 [Information] VocomService: Using new enhanced device detection service
2025-07-27 12:45:20.542 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 12:45:20.545 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 12:45:20.943 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 12:45:20.944 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 12:45:20.946 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 12:45:20.947 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 12:45:20.947 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 12:45:20.949 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 12:45:20.951 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 12:45:20.954 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 12:45:21.241 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 12:45:21.244 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 12:45:21.246 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 12:45:21.247 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 12:45:21.248 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 12:45:21.258 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 12:45:21.259 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 12:45:21.260 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 12:45:21.260 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 12:45:21.260 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 12:45:21.261 [Debug] VocomService: Bluetooth is enabled
2025-07-27 12:45:21.265 [Debug] VocomService: Checking if WiFi is available
2025-07-27 12:45:21.266 [Debug] VocomService: WiFi is available
2025-07-27 12:45:21.267 [Information] VocomService: Found 3 Vocom devices
2025-07-27 12:45:21.267 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-07-27 12:45:21.269 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: 440139a2-b230-493f-ac33-76fec32643d0, Type: USB)
2025-07-27 12:45:21.270 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: b3ff1897-854a-43c5-933a-37a9f32f83ea, Type: Bluetooth)
2025-07-27 12:45:21.270 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: 4125287f-f765-4717-96b3-1e9b99297b3e, Type: WiFi)
2025-07-27 12:45:21.271 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-07-27 12:45:21.271 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-07-27 12:45:21.272 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-07-27 12:45:21.272 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-07-27 12:45:21.272 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-07-27 12:45:21.273 [Information] App: Architecture-aware Vocom service created successfully
2025-07-27 12:45:21.273 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 12:45:21.274 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 12:45:21.274 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 12:45:21.274 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 12:45:21.275 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 12:45:21.275 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 12:45:21.276 [Information] VocomService: Native USB communication service initialized
2025-07-27 12:45:21.276 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 12:45:21.276 [Information] VocomService: Connection recovery service initialized
2025-07-27 12:45:21.276 [Information] VocomService: Enhanced services initialization completed
2025-07-27 12:45:21.277 [Information] VocomService: Checking if PTT application is running
2025-07-27 12:45:21.291 [Information] VocomService: PTT application is not running
2025-07-27 12:45:21.291 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 12:45:21.292 [Debug] VocomService: Bluetooth is enabled
2025-07-27 12:45:21.293 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 12:45:21.294 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-27 12:45:21.294 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-27 12:45:21.294 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-27 12:45:21.337 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 12:45:21.337 [Information] VocomService: Using new enhanced device detection service
2025-07-27 12:45:21.337 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 12:45:21.338 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 12:45:21.632 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 12:45:21.632 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 12:45:21.634 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 12:45:21.634 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 12:45:21.635 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 12:45:21.635 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 12:45:21.635 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 12:45:21.636 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 12:45:21.913 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 12:45:21.914 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 12:45:21.915 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 12:45:21.915 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 12:45:21.916 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 12:45:21.923 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 12:45:21.924 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 12:45:21.924 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 12:45:21.925 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 12:45:21.925 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 12:45:21.926 [Debug] VocomService: Bluetooth is enabled
2025-07-27 12:45:21.926 [Debug] VocomService: Checking if WiFi is available
2025-07-27 12:45:21.926 [Debug] VocomService: WiFi is available
2025-07-27 12:45:21.927 [Information] VocomService: Found 3 Vocom devices
2025-07-27 12:45:21.927 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-27 12:45:21.930 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 12:45:21.930 [Information] VocomService: Checking if PTT application is running
2025-07-27 12:45:21.946 [Information] VocomService: PTT application is not running
2025-07-27 12:45:21.949 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 12:45:21.949 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-27 12:45:21.950 [Information] VocomService: Checking if PTT application is running
2025-07-27 12:45:21.964 [Information] VocomService: PTT application is not running
2025-07-27 12:45:21.964 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-27 12:45:21.967 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-27 12:45:21.968 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 12:45:21.970 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 12:45:21.972 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:21.974 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:21.974 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:21.975 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:21.975 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:21.975 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:21.976 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:21.976 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:21.976 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:21.976 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 12:45:21.977 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:21.977 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:21.977 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:21.977 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:21.978 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:21.978 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 12:45:21.979 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 12:45:21.979 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 12:45:21.980 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 12:45:21.980 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 12:45:21.980 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 12:45:21.980 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 12:45:21.981 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 12:45:22.982 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-27 12:45:22.982 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 12:45:22.983 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 12:45:22.984 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:22.984 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:22.985 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:22.985 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:22.985 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:22.986 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:22.986 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:22.986 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:22.987 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:22.987 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 12:45:22.987 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:22.987 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:22.988 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:22.988 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:22.988 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:22.989 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 12:45:22.989 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 12:45:22.989 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 12:45:22.990 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 12:45:22.990 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 12:45:22.990 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 12:45:22.991 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 12:45:22.991 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 12:45:23.992 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-27 12:45:23.994 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 12:45:23.995 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 12:45:23.995 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:23.996 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:23.996 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:23.996 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:23.996 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:23.997 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:23.997 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:23.997 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:23.998 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:23.998 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 12:45:23.998 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:23.999 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:23.999 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:23.999 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:24.000 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:24.000 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 12:45:24.000 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 12:45:24.001 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 12:45:24.001 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 12:45:24.005 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 12:45:24.006 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 12:45:24.006 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 12:45:24.007 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 12:45:24.008 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 12:45:24.008 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-27 12:45:24.046 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 12:45:24.048 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-27 12:45:24.049 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 12:45:24.050 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 12:45:24.051 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 12:45:24.051 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 12:45:24.051 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 12:45:24.055 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 12:45:24.056 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 12:45:24.060 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-27 12:45:24.062 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 12:45:24.062 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 12:45:24.062 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 12:45:24.063 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 12:45:24.065 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 12:45:24.065 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-27 12:45:24.069 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-27 12:45:24.072 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 12:45:24.073 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-27 12:45:24.079 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 12:45:24.084 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 12:45:24.085 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 12:45:24.085 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 12:45:24.086 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-07-27 12:45:24.086 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 12:45:24.087 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-27 12:45:24.087 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-27 12:45:24.089 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-27 12:45:24.090 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 12:45:24.090 [Information] VocomService: Using new enhanced device detection service
2025-07-27 12:45:24.090 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 12:45:24.090 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 12:45:24.355 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 12:45:24.355 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 12:45:24.356 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 12:45:24.356 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 12:45:24.356 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 12:45:24.357 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 12:45:24.357 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 12:45:24.357 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 12:45:24.708 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 12:45:24.708 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 12:45:24.709 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 12:45:24.709 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 12:45:24.710 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 12:45:24.716 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 12:45:24.716 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 12:45:24.717 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 12:45:24.717 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 12:45:24.717 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 12:45:24.718 [Debug] VocomService: Bluetooth is enabled
2025-07-27 12:45:24.718 [Debug] VocomService: Checking if WiFi is available
2025-07-27 12:45:24.718 [Debug] VocomService: WiFi is available
2025-07-27 12:45:24.719 [Information] VocomService: Found 3 Vocom devices
2025-07-27 12:45:24.720 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 12:45:24.721 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 12:45:24.721 [Information] VocomService: Checking if PTT application is running
2025-07-27 12:45:24.735 [Information] VocomService: PTT application is not running
2025-07-27 12:45:24.736 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 12:45:24.736 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-27 12:45:24.736 [Information] VocomService: Checking if PTT application is running
2025-07-27 12:45:24.750 [Information] VocomService: PTT application is not running
2025-07-27 12:45:24.750 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-27 12:45:24.750 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-27 12:45:24.751 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 12:45:24.751 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 12:45:24.752 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:24.752 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:24.753 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:24.753 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:24.753 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:24.754 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:24.754 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:24.755 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:24.755 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:24.755 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 12:45:24.755 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:24.756 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:24.756 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:24.756 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:24.757 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:24.757 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 12:45:24.757 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 12:45:24.758 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 12:45:24.758 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 12:45:24.758 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 12:45:24.758 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 12:45:24.759 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 12:45:24.759 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 12:45:25.759 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-27 12:45:25.759 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 12:45:25.760 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 12:45:25.760 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:25.761 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:25.761 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:25.762 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:25.762 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:25.762 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:25.763 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:25.763 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:25.764 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:25.764 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 12:45:25.764 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:25.765 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:25.765 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:25.765 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:25.765 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:25.766 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 12:45:25.766 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 12:45:25.767 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 12:45:25.767 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 12:45:25.768 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 12:45:25.768 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 12:45:25.768 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 12:45:25.769 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 12:45:26.769 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-27 12:45:26.770 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 12:45:26.771 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 12:45:26.771 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:26.772 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:26.772 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:26.774 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:26.775 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:26.776 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:26.776 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:26.777 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:26.777 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:26.778 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 12:45:26.779 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:26.779 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:26.780 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 12:45:26.780 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 12:45:26.780 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 12:45:26.781 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 12:45:26.781 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 12:45:26.781 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 12:45:26.782 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 12:45:26.782 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 12:45:26.782 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 12:45:26.783 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 12:45:26.783 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 12:45:26.786 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 12:45:26.786 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-27 12:45:26.787 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 12:45:26.787 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-27 12:45:26.787 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 12:45:26.787 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 12:45:26.788 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 12:45:26.788 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 12:45:26.789 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 12:45:26.790 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 12:45:26.790 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 12:45:26.790 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-27 12:45:26.791 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 12:45:26.791 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 12:45:26.791 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 12:45:26.792 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 12:45:26.792 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 12:45:26.793 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 12:45:26.794 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 12:45:26.795 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 12:45:26.795 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 12:45:26.796 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 12:45:26.796 [Information] VocomService: Checking if PTT application is running
2025-07-27 12:45:26.816 [Information] VocomService: PTT application is not running
2025-07-27 12:45:26.819 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 12:45:26.821 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 12:45:26.825 [Debug] VocomService: Bluetooth is enabled
2025-07-27 12:45:26.827 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-27 12:45:27.630 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 12:45:27.631 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 12:45:27.632 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-27 12:45:27.633 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 12:45:27.636 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 12:45:27.638 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 12:45:27.641 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 12:45:27.644 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 12:45:27.646 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 12:45:27.655 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 12:45:27.659 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 12:45:27.670 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 12:45:27.675 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 12:45:27.676 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 12:45:27.676 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 12:45:27.677 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 12:45:27.677 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 12:45:27.678 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 12:45:27.679 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 12:45:27.679 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 12:45:27.680 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 12:45:27.680 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 12:45:27.682 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 12:45:27.684 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 12:45:27.685 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 12:45:27.686 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 12:45:27.686 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 12:45:27.687 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 12:45:27.691 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 12:45:27.700 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 12:45:27.704 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 12:45:27.708 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 12:45:27.711 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 12:45:27.719 [Information] CANRegisterAccess: Read value 0xBE from register 0x0141 (simulated)
2025-07-27 12:45:27.727 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 12:45:27.734 [Information] CANRegisterAccess: Read value 0xB9 from register 0x0141 (simulated)
2025-07-27 12:45:27.735 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 12:45:27.736 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 12:45:27.737 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 12:45:27.743 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 12:45:27.745 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 12:45:27.751 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 12:45:27.753 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 12:45:27.754 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 12:45:27.759 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 12:45:27.760 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 12:45:27.760 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 12:45:27.766 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 12:45:27.767 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 12:45:27.773 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 12:45:27.774 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 12:45:27.779 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 12:45:27.780 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 12:45:27.785 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 12:45:27.786 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 12:45:27.791 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 12:45:27.793 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 12:45:27.799 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 12:45:27.800 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 12:45:27.805 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 12:45:27.806 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 12:45:27.811 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 12:45:27.812 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 12:45:27.819 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 12:45:27.820 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 12:45:27.826 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 12:45:27.827 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 12:45:27.833 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 12:45:27.834 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 12:45:27.839 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 12:45:27.840 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 12:45:27.845 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 12:45:27.846 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 12:45:27.851 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 12:45:27.852 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 12:45:27.858 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 12:45:27.859 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 12:45:27.863 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 12:45:27.864 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 12:45:27.869 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 12:45:27.870 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 12:45:27.870 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 12:45:27.876 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 12:45:27.877 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 12:45:27.877 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 12:45:27.877 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 12:45:27.883 [Information] CANRegisterAccess: Read value 0x85 from register 0x0141 (simulated)
2025-07-27 12:45:27.889 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 12:45:27.895 [Information] CANRegisterAccess: Read value 0xDD from register 0x0141 (simulated)
2025-07-27 12:45:27.901 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 12:45:27.907 [Information] CANRegisterAccess: Read value 0x3C from register 0x0141 (simulated)
2025-07-27 12:45:27.908 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 12:45:27.908 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 12:45:27.908 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 12:45:27.909 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 12:45:27.913 [Information] CANRegisterAccess: Read value 0x9C from register 0x0140 (simulated)
2025-07-27 12:45:27.914 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 12:45:27.914 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 12:45:27.916 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 12:45:27.917 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 12:45:27.928 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 12:45:27.929 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 12:45:27.930 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 12:45:27.936 [Information] VocomService: Sending data and waiting for response
2025-07-27 12:45:27.937 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 12:45:27.988 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 12:45:27.989 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 12:45:27.990 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 12:45:27.991 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 12:45:27.992 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 12:45:28.005 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 12:45:28.006 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 12:45:28.007 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 12:45:28.017 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 12:45:28.028 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 12:45:28.039 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 12:45:28.050 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 12:45:28.061 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 12:45:28.064 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 12:45:28.065 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 12:45:28.076 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 12:45:28.077 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 12:45:28.078 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 12:45:28.088 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 12:45:28.100 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 12:45:28.111 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 12:45:28.122 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 12:45:28.133 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 12:45:28.145 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 12:45:28.147 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 12:45:28.148 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 12:45:28.159 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 12:45:28.161 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 12:45:28.162 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 12:45:28.163 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 12:45:28.163 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 12:45:28.163 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 12:45:28.164 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 12:45:28.164 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 12:45:28.165 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 12:45:28.165 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 12:45:28.165 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 12:45:28.166 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 12:45:28.166 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 12:45:28.167 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 12:45:28.167 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 12:45:28.167 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 12:45:28.168 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 12:45:28.269 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 12:45:28.270 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 12:45:28.274 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 12:45:28.276 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 12:45:28.276 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 12:45:28.276 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 12:45:28.277 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 12:45:28.278 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 12:45:28.279 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 12:45:28.279 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 12:45:28.280 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 12:45:28.280 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 12:45:28.281 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 12:45:28.281 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 12:45:28.282 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 12:45:28.284 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-27 12:45:28.285 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-27 12:45:28.285 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-27 12:45:28.289 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-27 12:45:28.290 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-27 12:45:28.294 [Information] BackupService: Initializing backup service
2025-07-27 12:45:28.295 [Information] BackupService: Backup service initialized successfully
2025-07-27 12:45:28.295 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-27 12:45:28.296 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-27 12:45:28.300 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-27 12:45:28.347 [Information] BackupService: Compressing backup data
2025-07-27 12:45:28.356 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (447 bytes)
2025-07-27 12:45:28.357 [Information] BackupServiceFactory: Created template for category: Production
2025-07-27 12:45:28.358 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-27 12:45:28.358 [Information] BackupService: Compressing backup data
2025-07-27 12:45:28.360 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-27 12:45:28.361 [Information] BackupServiceFactory: Created template for category: Development
2025-07-27 12:45:28.361 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-27 12:45:28.362 [Information] BackupService: Compressing backup data
2025-07-27 12:45:28.367 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (448 bytes)
2025-07-27 12:45:28.367 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-27 12:45:28.368 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-27 12:45:28.368 [Information] BackupService: Compressing backup data
2025-07-27 12:45:28.369 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (451 bytes)
2025-07-27 12:45:28.370 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-27 12:45:28.370 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-27 12:45:28.371 [Information] BackupService: Compressing backup data
2025-07-27 12:45:28.372 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (451 bytes)
2025-07-27 12:45:28.374 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-27 12:45:28.375 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-27 12:45:28.375 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-27 12:45:28.376 [Information] BackupService: Compressing backup data
2025-07-27 12:45:28.377 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-07-27 12:45:28.377 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-27 12:45:28.378 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-27 12:45:28.379 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-27 12:45:28.384 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 12:45:28.386 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 12:45:28.462 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 12:45:28.463 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 12:45:28.466 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-27 12:45:28.466 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-27 12:45:28.466 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-27 12:45:28.468 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-27 12:45:28.469 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-27 12:45:28.474 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-27 12:45:28.475 [Information] App: Flash operation monitor service initialized successfully
2025-07-27 12:45:28.487 [Information] LicensingService: Initializing licensing service
2025-07-27 12:45:28.549 [Information] LicensingService: License information loaded successfully
2025-07-27 12:45:28.552 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-27 12:45:28.553 [Information] App: Licensing service initialized successfully
2025-07-27 12:45:28.554 [Information] App: License status: Trial
2025-07-27 12:45:28.554 [Information] App: Trial period: 30 days remaining
2025-07-27 12:45:28.555 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-27 12:45:28.588 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-27 12:45:28.746 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 12:45:28.746 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 12:45:28.747 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 12:45:28.747 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 12:45:28.748 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 12:45:28.748 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 12:45:28.749 [Information] VocomService: Native USB communication service initialized
2025-07-27 12:45:28.749 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 12:45:28.749 [Information] VocomService: Connection recovery service initialized
2025-07-27 12:45:28.750 [Information] VocomService: Enhanced services initialization completed
2025-07-27 12:45:28.750 [Information] VocomService: Checking if PTT application is running
2025-07-27 12:45:28.764 [Information] VocomService: PTT application is not running
2025-07-27 12:45:28.764 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 12:45:28.765 [Debug] VocomService: Bluetooth is enabled
2025-07-27 12:45:28.765 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 12:45:28.816 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 12:45:28.816 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 12:45:28.817 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 12:45:28.817 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 12:45:28.817 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-27 12:45:28.817 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: a2daf84c-a84d-43e5-a986-e1848b262142
2025-07-27 12:45:28.819 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-27 12:45:28.819 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 12:45:28.819 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 12:45:28.820 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 12:45:28.821 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 12:45:28.822 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 12:45:28.824 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 12:45:28.824 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 12:45:28.825 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 12:45:28.836 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 12:45:28.836 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 12:45:28.837 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 12:45:28.837 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 12:45:28.837 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 12:45:28.837 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 12:45:28.838 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 12:45:28.838 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 12:45:28.838 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 12:45:28.839 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 12:45:28.839 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 12:45:28.839 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 12:45:28.839 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 12:45:28.840 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 12:45:28.840 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 12:45:28.840 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 12:45:28.841 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 12:45:28.841 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 12:45:28.847 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 12:45:28.847 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 12:45:28.848 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 12:45:28.848 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 12:45:28.854 [Information] CANRegisterAccess: Read value 0x33 from register 0x0141 (simulated)
2025-07-27 12:45:28.854 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 12:45:28.855 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 12:45:28.855 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 12:45:28.861 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 12:45:28.861 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 12:45:28.867 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 12:45:28.868 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 12:45:28.868 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 12:45:28.873 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 12:45:28.874 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 12:45:28.874 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 12:45:28.880 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 12:45:28.881 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 12:45:28.887 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 12:45:28.888 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 12:45:28.894 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 12:45:28.895 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 12:45:28.901 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 12:45:28.902 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 12:45:28.908 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 12:45:28.909 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 12:45:28.915 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 12:45:28.916 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 12:45:28.920 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 12:45:28.921 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 12:45:28.927 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 12:45:28.928 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 12:45:28.934 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 12:45:28.935 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 12:45:28.940 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 12:45:28.941 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 12:45:28.947 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 12:45:28.948 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 12:45:28.953 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 12:45:28.954 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 12:45:28.960 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 12:45:28.961 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 12:45:28.967 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 12:45:28.968 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 12:45:28.974 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 12:45:28.975 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 12:45:28.981 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 12:45:28.982 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 12:45:28.987 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 12:45:28.988 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 12:45:28.988 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 12:45:28.994 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 12:45:28.995 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 12:45:28.995 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 12:45:28.995 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 12:45:29.001 [Information] CANRegisterAccess: Read value 0xE6 from register 0x0141 (simulated)
2025-07-27 12:45:29.002 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 12:45:29.002 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 12:45:29.002 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 12:45:29.003 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 12:45:29.009 [Information] CANRegisterAccess: Read value 0x58 from register 0x0140 (simulated)
2025-07-27 12:45:29.010 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 12:45:29.010 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 12:45:29.010 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 12:45:29.011 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 12:45:29.021 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 12:45:29.022 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 12:45:29.022 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 12:45:29.023 [Information] VocomService: Sending data and waiting for response
2025-07-27 12:45:29.023 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 12:45:29.074 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 12:45:29.075 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 12:45:29.075 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 12:45:29.076 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 12:45:29.076 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 12:45:29.087 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 12:45:29.088 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 12:45:29.088 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 12:45:29.099 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 12:45:29.110 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 12:45:29.121 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 12:45:29.132 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 12:45:29.143 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 12:45:29.144 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 12:45:29.144 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 12:45:29.155 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 12:45:29.156 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 12:45:29.156 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 12:45:29.167 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 12:45:29.178 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 12:45:29.189 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 12:45:29.200 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 12:45:29.211 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 12:45:29.222 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 12:45:29.223 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 12:45:29.223 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 12:45:29.234 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 12:45:29.235 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 12:45:29.235 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 12:45:29.235 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 12:45:29.235 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 12:45:29.236 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 12:45:29.236 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 12:45:29.236 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 12:45:29.237 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 12:45:29.237 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 12:45:29.237 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 12:45:29.237 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 12:45:29.238 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 12:45:29.238 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 12:45:29.238 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 12:45:29.239 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 12:45:29.239 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 12:45:29.339 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 12:45:29.340 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 12:45:29.340 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 12:45:29.341 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 12:45:29.341 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 12:45:29.341 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 12:45:29.342 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 12:45:29.342 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 12:45:29.342 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 12:45:29.343 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 12:45:29.343 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 12:45:29.344 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 12:45:29.344 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 12:45:29.346 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 12:45:29.346 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 12:45:29.397 [Information] BackupService: Initializing backup service
2025-07-27 12:45:29.398 [Information] BackupService: Backup service initialized successfully
2025-07-27 12:45:29.448 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 12:45:29.449 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 12:45:29.451 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 12:45:29.452 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 12:45:29.502 [Information] BackupService: Getting predefined backup categories
2025-07-27 12:45:29.554 [Information] MainViewModel: Services initialized successfully
2025-07-27 12:45:29.557 [Information] MainViewModel: Scanning for Vocom devices
2025-07-27 12:45:29.558 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 12:45:29.559 [Information] VocomService: Using new enhanced device detection service
2025-07-27 12:45:29.559 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 12:45:29.559 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 12:45:29.920 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 12:45:29.921 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 12:45:29.921 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 12:45:29.921 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 12:45:29.921 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 12:45:29.923 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 12:45:29.924 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 12:45:29.924 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 12:45:30.373 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 12:45:30.373 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 12:45:30.374 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 12:45:30.376 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 12:45:30.377 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 12:45:30.391 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 12:45:30.404 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 12:45:30.407 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 12:45:30.408 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 12:45:30.408 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 12:45:30.410 [Debug] VocomService: Bluetooth is enabled
2025-07-27 12:45:30.411 [Debug] VocomService: Checking if WiFi is available
2025-07-27 12:45:30.412 [Debug] VocomService: WiFi is available
2025-07-27 12:45:30.412 [Information] VocomService: Found 3 Vocom devices
2025-07-27 12:45:30.415 [Information] MainViewModel: Found 3 Vocom device(s)
