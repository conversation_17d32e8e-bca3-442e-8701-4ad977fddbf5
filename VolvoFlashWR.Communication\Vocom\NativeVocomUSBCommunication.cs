using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Text;
using Microsoft.Win32.SafeHandles;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Communication.Interfaces;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Native USB communication service for direct Vocom adapter communication
    /// Uses Windows USB APIs for low-level hardware access
    /// </summary>
    public class NativeVocomUSBCommunication : IUSBCommunicationService
    {
        private readonly ILoggingService _logger;
        private bool _isConnected = false;
        private SafeFileHandle? _deviceHandle;
        private string? _connectedDevicePath;

        // Windows USB API constants
        private const uint GENERIC_READ = 0x80000000;
        private const uint GENERIC_WRITE = 0x40000000;
        private const uint FILE_SHARE_READ = 0x00000001;
        private const uint FILE_SHARE_WRITE = 0x00000002;
        private const uint OPEN_EXISTING = 3;
        private const uint FILE_ATTRIBUTE_NORMAL = 0x80;

        // SetupAPI constants for device enumeration
        private const int DIGCF_PRESENT = 0x00000002;
        private const int DIGCF_DEVICEINTERFACE = 0x00000010;
        private const int SPDRP_HARDWAREID = 0x00000001;
        private const int ERROR_NO_MORE_ITEMS = 259;
        private const uint FILE_FLAG_OVERLAPPED = 0x40000000;

        // Vocom device identifiers
        private const ushort VOCOM_VENDOR_ID = 0x178E;
        private const ushort VOCOM_PRODUCT_ID = 0x0024;
        private const string VOCOM_DEVICE_NAME = "88890300";

        // Windows API imports
        [DllImport("kernel32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern SafeFileHandle CreateFile(
            string lpFileName,
            uint dwDesiredAccess,
            uint dwShareMode,
            IntPtr lpSecurityAttributes,
            uint dwCreationDisposition,
            uint dwFlagsAndAttributes,
            IntPtr hTemplateFile);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool ReadFile(
            SafeFileHandle hFile,
            byte[] lpBuffer,
            uint nNumberOfBytesToRead,
            out uint lpNumberOfBytesRead,
            IntPtr lpOverlapped);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool WriteFile(
            SafeFileHandle hFile,
            byte[] lpBuffer,
            uint nNumberOfBytesToWrite,
            out uint lpNumberOfBytesWritten,
            IntPtr lpOverlapped);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern IntPtr SetupDiGetClassDevs(
            ref Guid classGuid,
            string? enumerator,
            IntPtr hwndParent,
            uint flags);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiEnumDeviceInfo(
            IntPtr deviceInfoSet,
            uint memberIndex,
            ref SP_DEVINFO_DATA deviceInfoData);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiGetDeviceInstanceId(
            IntPtr deviceInfoSet,
            ref SP_DEVINFO_DATA deviceInfoData,
            StringBuilder deviceInstanceId,
            uint deviceInstanceIdSize,
            out uint requiredSize);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiDestroyDeviceInfoList(IntPtr deviceInfoSet);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiEnumDeviceInterfaces(
            IntPtr deviceInfoSet,
            IntPtr deviceInfoData,
            ref Guid interfaceClassGuid,
            uint memberIndex,
            ref SP_DEVICE_INTERFACE_DATA deviceInterfaceData);

        [DllImport("setupapi.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern bool SetupDiGetDeviceInterfaceDetail(
            IntPtr deviceInfoSet,
            ref SP_DEVICE_INTERFACE_DATA deviceInterfaceData,
            IntPtr deviceInterfaceDetailData,
            uint deviceInterfaceDetailDataSize,
            out uint requiredSize,
            IntPtr deviceInfoData);

        [StructLayout(LayoutKind.Sequential)]
        private struct SP_DEVINFO_DATA
        {
            public uint cbSize;
            public Guid classGuid;
            public uint devInst;
            public IntPtr reserved;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct SP_DEVICE_INTERFACE_DATA
        {
            public uint cbSize;
            public Guid interfaceClassGuid;
            public uint flags;
            public IntPtr reserved;
        }

        // HID class GUID
        private static readonly Guid HID_CLASS_GUID = new Guid("4d1e55b2-f16f-11cf-88cb-001111000030");

        public NativeVocomUSBCommunication(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public bool IsConnected => _isConnected;

        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing native Vocom USB communication", "NativeVocomUSBCommunication");
                
                // Check if we can access USB devices
                var devices = await DetectVocomDevicesAsync();
                
                _logger.LogInformation($"Found {devices.Count} potential Vocom devices", "NativeVocomUSBCommunication");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to initialize native USB communication", "NativeVocomUSBCommunication", ex);
                return false;
            }
        }

        public async Task<List<string>> DetectVocomDevicesAsync()
        {
            var devices = new List<string>();

            try
            {
                _logger.LogInformation("Detecting Vocom devices using native USB enumeration", "NativeVocomUSBCommunication");

                // Get all HID devices
                Guid hidClassGuid = HID_CLASS_GUID; // Create local copy for ref parameter
                IntPtr deviceInfoSet = SetupDiGetClassDevs(
                    ref hidClassGuid,
                    null,
                    IntPtr.Zero,
                    0x00000010 | 0x00000002); // DIGCF_PRESENT | DIGCF_DEVICEINTERFACE

                if (deviceInfoSet == IntPtr.Zero || deviceInfoSet == new IntPtr(-1))
                {
                    _logger.LogWarning("Failed to get device information set", "NativeVocomUSBCommunication");
                    return devices;
                }

                try
                {
                    uint deviceIndex = 0;
                    var deviceInfoData = new SP_DEVINFO_DATA();
                    deviceInfoData.cbSize = (uint)Marshal.SizeOf(deviceInfoData);

                    while (SetupDiEnumDeviceInfo(deviceInfoSet, deviceIndex, ref deviceInfoData))
                    {
                        var deviceInstanceId = new StringBuilder(256);
                        if (SetupDiGetDeviceInstanceId(deviceInfoSet, ref deviceInfoData, deviceInstanceId, 256, out _))
                        {
                            string deviceId = deviceInstanceId.ToString();
                            
                            // Check if this is a Vocom device
                            if (IsVocomDevice(deviceId))
                            {
                                _logger.LogInformation($"Found Vocom device: {deviceId}", "NativeVocomUSBCommunication");
                                devices.Add(deviceId);
                            }
                        }

                        deviceIndex++;
                    }
                }
                finally
                {
                    SetupDiDestroyDeviceInfoList(deviceInfoSet);
                }

                _logger.LogInformation($"Native USB detection found {devices.Count} Vocom devices", "NativeVocomUSBCommunication");
            }
            catch (Exception ex)
            {
                _logger.LogError("Error during native USB device detection", "NativeVocomUSBCommunication", ex);
            }

            await Task.CompletedTask;
            return devices;
        }

        private bool IsVocomDevice(string deviceId)
        {
            // Check for Vocom vendor and product IDs
            return deviceId.Contains($"VID_{VOCOM_VENDOR_ID:X4}", StringComparison.OrdinalIgnoreCase) &&
                   deviceId.Contains($"PID_{VOCOM_PRODUCT_ID:X4}", StringComparison.OrdinalIgnoreCase);
        }

        public async Task<bool> ConnectToDeviceAsync(string portName, int baudRate = 115200, int timeout = 5000)
        {
            try
            {
                _logger.LogInformation($"Attempting native USB connection to: {portName}", "NativeVocomUSBCommunication");

                // If already connected, disconnect first
                if (_isConnected)
                {
                    await DisconnectAsync();
                }

                // Try to open the device
                string devicePath = GetDevicePath(portName);
                if (string.IsNullOrEmpty(devicePath))
                {
                    _logger.LogWarning($"Could not determine device path for: {portName}", "NativeVocomUSBCommunication");
                    return false;
                }

                _deviceHandle = CreateFile(
                    devicePath,
                    GENERIC_READ | GENERIC_WRITE,
                    FILE_SHARE_READ | FILE_SHARE_WRITE,
                    IntPtr.Zero,
                    OPEN_EXISTING,
                    FILE_ATTRIBUTE_NORMAL,
                    IntPtr.Zero);

                if (_deviceHandle.IsInvalid)
                {
                    int error = Marshal.GetLastWin32Error();
                    string errorMessage = GetErrorMessage(error);
                    _logger.LogError($"Failed to open device {devicePath}. Error: {error} - {errorMessage}", "NativeVocomUSBCommunication");

                    // Try alternative device access methods
                    bool alternativeSuccess = await TryAlternativeDeviceAccess(devicePath, portName);
                    if (alternativeSuccess)
                    {
                        return true;
                    }

                    return false;
                }

                _connectedDevicePath = devicePath;
                _isConnected = true;

                _logger.LogInformation($"Successfully connected to Vocom device: {devicePath}", "NativeVocomUSBCommunication");
                USBConnected?.Invoke(this, portName);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error connecting to device {portName}", "NativeVocomUSBCommunication", ex);
                USBError?.Invoke(this, $"Connection error: {ex.Message}");
                return false;
            }
        }

        private string GetDevicePath(string portName)
        {
            try
            {
                // For USB device IDs like "USB\VID_178E&PID_0024\0000007658", we need to find the actual device interface path
                if (portName.StartsWith("USB\\"))
                {
                    // First, try to find the actual HID device interface path using SetupAPI
                    string hidInterfacePath = FindHidDeviceInterfacePath(portName);
                    if (!string.IsNullOrEmpty(hidInterfacePath))
                    {
                        _logger.LogInformation($"Found HID interface path: {hidInterfacePath}", "NativeVocomUSBCommunication");
                        return hidInterfacePath;
                    }

                    // Try common Vocom device interface paths
                    string[] vocomPaths = {
                        @"\\.\Vocom1",
                        @"\\.\Vocom",
                        @"\\.\88890020",
                        @"\\.\88890300", // Add the actual device ID from logs
                        @"\\.\VocomAdapter",
                        @"\\.\Global\Vocom1",
                        @"\\.\Global\88890020",
                        @"\\.\Global\88890300"
                    };

                    foreach (string path in vocomPaths)
                    {
                        // Test if the path exists by trying to open it with minimal access
                        var testHandle = CreateFile(
                            path,
                            0, // No access needed for testing
                            FILE_SHARE_READ | FILE_SHARE_WRITE,
                            IntPtr.Zero,
                            OPEN_EXISTING,
                            FILE_ATTRIBUTE_NORMAL,
                            IntPtr.Zero);

                        if (!testHandle.IsInvalid)
                        {
                            testHandle.Close();
                            _logger.LogInformation($"Found valid device path: {path}", "NativeVocomUSBCommunication");
                            return path;
                        }
                    }

                    // If no standard paths work, try to construct a proper HID path with instance ID
                    // Extract VID, PID, and instance ID from the USB device ID
                    if (portName.Contains("VID_") && portName.Contains("PID_"))
                    {
                        var vidStart = portName.IndexOf("VID_") + 4;
                        var pidStart = portName.IndexOf("PID_") + 4;

                        if (vidStart > 3 && pidStart > 3)
                        {
                            var vid = portName.Substring(vidStart, 4);
                            var pid = portName.Substring(pidStart, 4);

                            // Extract instance ID if available
                            string instanceId = "";
                            var lastBackslash = portName.LastIndexOf('\\');
                            if (lastBackslash > 0 && lastBackslash < portName.Length - 1)
                            {
                                instanceId = portName.Substring(lastBackslash + 1);
                            }

                            // Try different HID path formats
                            string[] hidPaths = {
                                $@"\\.\HID#VID_{vid}&PID_{pid}#{instanceId}",
                                $@"\\.\HID#VID_{vid}&PID_{pid}",
                                $@"\\?\HID#VID_{vid}&PID_{pid}#{instanceId}",
                                $@"\\?\HID#VID_{vid}&PID_{pid}"
                            };

                            foreach (string hidPath in hidPaths)
                            {
                                _logger.LogInformation($"Trying HID path: {hidPath}", "NativeVocomUSBCommunication");

                                // Test the path
                                var testHandle = CreateFile(
                                    hidPath,
                                    0,
                                    FILE_SHARE_READ | FILE_SHARE_WRITE,
                                    IntPtr.Zero,
                                    OPEN_EXISTING,
                                    FILE_ATTRIBUTE_NORMAL,
                                    IntPtr.Zero);

                                if (!testHandle.IsInvalid)
                                {
                                    testHandle.Close();
                                    _logger.LogInformation($"Found valid HID path: {hidPath}", "NativeVocomUSBCommunication");
                                    return hidPath;
                                }
                            }
                        }
                    }

                    _logger.LogWarning($"Could not find valid device path for USB device: {portName}", "NativeVocomUSBCommunication");
                    return string.Empty;
                }

                // For COM ports or other device types
                if (portName.StartsWith("COM"))
                {
                    return $"\\\\.\\{portName}";
                }

                return $"\\\\.\\{portName}";
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error determining device path for {portName}: {ex.Message}", "NativeVocomUSBCommunication");
                return string.Empty;
            }
        }

        private string FindHidDeviceInterfacePath(string usbDeviceId)
        {
            try
            {
                _logger.LogInformation($"Searching for HID interface path for device: {usbDeviceId}", "NativeVocomUSBCommunication");

                // Get HID device information set
                var hidGuid = HID_CLASS_GUID;
                IntPtr deviceInfoSet = SetupDiGetClassDevs(
                    ref hidGuid,
                    null,
                    IntPtr.Zero,
                    DIGCF_PRESENT | DIGCF_DEVICEINTERFACE);

                if (deviceInfoSet == IntPtr.Zero || deviceInfoSet == new IntPtr(-1))
                {
                    _logger.LogWarning("Failed to get HID device information set", "NativeVocomUSBCommunication");
                    return string.Empty;
                }

                try
                {
                    uint memberIndex = 0;
                    var deviceInterfaceData = new SP_DEVICE_INTERFACE_DATA();
                    deviceInterfaceData.cbSize = (uint)Marshal.SizeOf(deviceInterfaceData);

                    // Enumerate device interfaces
                    while (SetupDiEnumDeviceInterfaces(
                        deviceInfoSet,
                        IntPtr.Zero,
                        ref hidGuid,
                        memberIndex,
                        ref deviceInterfaceData))
                    {
                        // Get the required size for the device interface detail
                        SetupDiGetDeviceInterfaceDetail(
                            deviceInfoSet,
                            ref deviceInterfaceData,
                            IntPtr.Zero,
                            0,
                            out uint requiredSize,
                            IntPtr.Zero);

                        // Allocate buffer for device interface detail
                        IntPtr detailDataBuffer = Marshal.AllocHGlobal((int)requiredSize);
                        try
                        {
                            // Set the size field (first 4 bytes for 32-bit, 8 bytes for 64-bit)
                            Marshal.WriteInt32(detailDataBuffer, IntPtr.Size == 8 ? 8 : 6);

                            // Get the device interface detail
                            if (SetupDiGetDeviceInterfaceDetail(
                                deviceInfoSet,
                                ref deviceInterfaceData,
                                detailDataBuffer,
                                requiredSize,
                                out _,
                                IntPtr.Zero))
                            {
                                // Extract the device path (skip the size field)
                                string devicePath = Marshal.PtrToStringAuto(
                                    IntPtr.Add(detailDataBuffer, 4));

                                if (!string.IsNullOrEmpty(devicePath))
                                {
                                    _logger.LogDebug($"Found HID device path: {devicePath}", "NativeVocomUSBCommunication");

                                    // Check if this device path matches our Vocom device
                                    if (IsVocomDevice(devicePath, usbDeviceId))
                                    {
                                        _logger.LogInformation($"Found matching Vocom HID device: {devicePath}", "NativeVocomUSBCommunication");
                                        return devicePath;
                                    }
                                }
                            }
                        }
                        finally
                        {
                            Marshal.FreeHGlobal(detailDataBuffer);
                        }

                        memberIndex++;
                    }
                }
                finally
                {
                    SetupDiDestroyDeviceInfoList(deviceInfoSet);
                }

                _logger.LogWarning($"No matching HID interface found for device: {usbDeviceId}", "NativeVocomUSBCommunication");
                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error finding HID device interface path: {ex.Message}", "NativeVocomUSBCommunication", ex);
                return string.Empty;
            }
        }

        private bool IsVocomDevice(string devicePath, string usbDeviceId)
        {
            try
            {
                _logger.LogDebug($"Checking if device path '{devicePath}' matches USB device '{usbDeviceId}'", "NativeVocomUSBCommunication");

                string devicePathUpper = devicePath.ToUpper();
                string usbDeviceIdUpper = usbDeviceId.ToUpper();

                // Extract serial number from USB device ID (e.g., "0000007658" from "USB\VID_178E&PID_0024\0000007658")
                string serialNumber = null;
                if (usbDeviceIdUpper.Contains("\\"))
                {
                    var parts = usbDeviceIdUpper.Split('\\');
                    if (parts.Length > 2)
                    {
                        serialNumber = parts[2];
                        _logger.LogDebug($"Extracted serial number: {serialNumber}", "NativeVocomUSBCommunication");
                    }
                }

                // Check if the device path contains the VID and PID from the USB device ID
                if (usbDeviceIdUpper.Contains("VID_178E") && usbDeviceIdUpper.Contains("PID_0024"))
                {
                    // Primary check: VID and PID match
                    bool vidPidMatch = devicePathUpper.Contains("VID_178E") && devicePathUpper.Contains("PID_0024");

                    // Secondary check: serial number match (if available)
                    bool serialMatch = string.IsNullOrEmpty(serialNumber) || devicePathUpper.Contains(serialNumber);

                    // Tertiary check: Vocom-specific identifiers
                    bool vocomMatch = devicePathUpper.Contains("VOCOM") || devicePathUpper.Contains("88890300");

                    _logger.LogDebug($"VID/PID match: {vidPidMatch}, Serial match: {serialMatch}, Vocom match: {vocomMatch}", "NativeVocomUSBCommunication");

                    // Accept if VID/PID match OR if we have Vocom identifiers with serial match
                    return vidPidMatch || (vocomMatch && serialMatch);
                }

                // Fallback: check for any Vocom identifiers
                bool fallbackMatch = devicePathUpper.Contains("VID_178E") ||
                                   devicePathUpper.Contains("VOCOM") ||
                                   devicePathUpper.Contains("88890300");

                _logger.LogDebug($"Fallback match: {fallbackMatch}", "NativeVocomUSBCommunication");
                return fallbackMatch;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error checking if device is Vocom: {ex.Message}", "NativeVocomUSBCommunication");
                return false;
            }
        }

        public async Task<bool> DisconnectAsync()
        {
            try
            {
                if (_deviceHandle != null && !_deviceHandle.IsInvalid)
                {
                    _deviceHandle.Dispose();
                    _deviceHandle = null;
                }

                _isConnected = false;
                _connectedDevicePath = null;

                _logger.LogInformation("Disconnected from Vocom device", "NativeVocomUSBCommunication");
                USBDisconnected?.Invoke(this, "Device disconnected");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error during disconnect", "NativeVocomUSBCommunication", ex);
                return false;
            }
            finally
            {
                await Task.CompletedTask;
            }
        }

        public async Task<byte[]?> SendAndReceiveDataAsync(byte[] data, int expectedResponseLength, int timeoutMs = 5000)
        {
            if (!_isConnected || _deviceHandle == null || _deviceHandle.IsInvalid)
            {
                _logger.LogWarning("Cannot send data - device not connected", "NativeVocomUSBCommunication");
                return null;
            }

            try
            {
                _logger.LogDebug($"Sending {data.Length} bytes to Vocom device", "NativeVocomUSBCommunication");

                // Send data
                bool writeResult = WriteFile(_deviceHandle, data, (uint)data.Length, out uint bytesWritten, IntPtr.Zero);
                if (!writeResult || bytesWritten != data.Length)
                {
                    int error = Marshal.GetLastWin32Error();
                    _logger.LogError($"Failed to write data. Error: {error}, Bytes written: {bytesWritten}/{data.Length}", "NativeVocomUSBCommunication");
                    return null;
                }

                // Read response
                byte[] responseBuffer = new byte[expectedResponseLength];
                bool readResult = ReadFile(_deviceHandle, responseBuffer, (uint)expectedResponseLength, out uint bytesRead, IntPtr.Zero);
                
                if (!readResult)
                {
                    int error = Marshal.GetLastWin32Error();
                    _logger.LogError($"Failed to read response. Error: {error}", "NativeVocomUSBCommunication");
                    return null;
                }

                if (bytesRead > 0)
                {
                    byte[] response = new byte[bytesRead];
                    Array.Copy(responseBuffer, response, bytesRead);
                    
                    _logger.LogDebug($"Received {bytesRead} bytes from Vocom device", "NativeVocomUSBCommunication");
                    return response;
                }

                _logger.LogWarning("No response received from Vocom device", "NativeVocomUSBCommunication");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error during data communication", "NativeVocomUSBCommunication", ex);
                return null;
            }
            finally
            {
                await Task.CompletedTask;
            }
        }

        public async Task<bool> IsUSBAvailableAsync()
        {
            try
            {
                var devices = await DetectVocomDevicesAsync();
                return devices.Count > 0;
            }
            catch
            {
                return false;
            }
        }

        // Events
        public event EventHandler<string>? USBConnected;
        public event EventHandler<string>? USBDisconnected;
        public event EventHandler<string>? USBError;

        // Additional methods required by IUSBCommunicationService interface
        public async Task<bool> DisconnectFromDeviceAsync(string portName)
        {
            try
            {
                _logger.LogInformation($"Disconnecting from device: {portName}", "NativeVocomUSBCommunication");

                if (_connectedDevicePath == portName || _connectedDevicePath?.Contains(portName) == true)
                {
                    return await DisconnectAsync();
                }

                _logger.LogWarning($"Device {portName} is not currently connected", "NativeVocomUSBCommunication");
                return true; // Not an error if already disconnected
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error disconnecting from device {portName}", "NativeVocomUSBCommunication", ex);
                return false;
            }
        }

        public async Task<bool> SendDataAsync(string portName, byte[] data)
        {
            try
            {
                if (!_isConnected || _connectedDevicePath != portName)
                {
                    _logger.LogWarning($"Cannot send data - not connected to {portName}", "NativeVocomUSBCommunication");
                    return false;
                }

                var response = await SendAndReceiveDataAsync(data, 0, 5000);
                return response != null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending data to {portName}", "NativeVocomUSBCommunication", ex);
                return false;
            }
        }

        public async Task<byte[]> ReceiveDataAsync(string portName, int timeout = 5000)
        {
            try
            {
                if (!_isConnected || _connectedDevicePath != portName)
                {
                    _logger.LogWarning($"Cannot receive data - not connected to {portName}", "NativeVocomUSBCommunication");
                    return Array.Empty<byte>();
                }

                // For this implementation, we'll return empty data since we don't have a separate receive operation
                // In a real implementation, this would read from a buffer or perform a read operation
                await Task.Delay(100); // Simulate receive operation
                return Array.Empty<byte>();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error receiving data from {portName}", "NativeVocomUSBCommunication", ex);
                return Array.Empty<byte>();
            }
        }

        public void Dispose()
        {
            DisconnectAsync().Wait();
        }

        private string GetErrorMessage(int errorCode)
        {
            return errorCode switch
            {
                2 => "The system cannot find the file specified. The device may not be properly connected or drivers may be missing.",
                5 => "Access is denied. The device may be in use by another application or requires administrator privileges.",
                21 => "The device is not ready. Please check the device connection and try again.",
                31 => "A device attached to the system is not functioning. The device may be malfunctioning or drivers may be corrupted.",
                87 => "The parameter is incorrect. The device path may be invalid.",
                1167 => "The device is not connected. Please check the USB connection.",
                _ => $"Unknown error code: {errorCode}"
            };
        }

        private async Task<bool> TryAlternativeDeviceAccess(string devicePath, string portName)
        {
            try
            {
                _logger.LogInformation("Trying alternative device access methods", "NativeVocomUSBCommunication");

                // Method 1: Try with different sharing modes
                var sharingModes = new[]
                {
                    (FILE_SHARE_READ, "Read sharing"),
                    (FILE_SHARE_WRITE, "Write sharing"),
                    (0u, "Exclusive access")
                };

                foreach (var (shareMode, description) in sharingModes)
                {
                    try
                    {
                        var handle = CreateFile(
                            devicePath,
                            GENERIC_READ,
                            shareMode,
                            IntPtr.Zero,
                            OPEN_EXISTING,
                            FILE_ATTRIBUTE_NORMAL,
                            IntPtr.Zero);

                        if (!handle.IsInvalid)
                        {
                            _deviceHandle = handle;
                            _connectedDevicePath = devicePath;
                            _isConnected = true;
                            _logger.LogInformation($"Successfully connected using {description}", "NativeVocomUSBCommunication");
                            USBConnected?.Invoke(this, portName);
                            return true;
                        }
                        else
                        {
                            handle?.Dispose();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug($"Failed {description}: {ex.Message}", "NativeVocomUSBCommunication");
                    }
                }

                // Method 2: Try to wait and retry (device might be initializing)
                _logger.LogInformation("Waiting for device to become available...", "NativeVocomUSBCommunication");
                await Task.Delay(2000);

                var retryHandle = CreateFile(
                    devicePath,
                    GENERIC_READ | GENERIC_WRITE,
                    FILE_SHARE_READ | FILE_SHARE_WRITE,
                    IntPtr.Zero,
                    OPEN_EXISTING,
                    FILE_ATTRIBUTE_NORMAL,
                    IntPtr.Zero);

                if (!retryHandle.IsInvalid)
                {
                    _deviceHandle = retryHandle;
                    _connectedDevicePath = devicePath;
                    _isConnected = true;
                    _logger.LogInformation("Successfully connected after retry", "NativeVocomUSBCommunication");
                    USBConnected?.Invoke(this, portName);
                    return true;
                }
                else
                {
                    retryHandle?.Dispose();
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Alternative device access failed: {ex.Message}", "NativeVocomUSBCommunication", ex);
                return false;
            }
        }
    }
}
