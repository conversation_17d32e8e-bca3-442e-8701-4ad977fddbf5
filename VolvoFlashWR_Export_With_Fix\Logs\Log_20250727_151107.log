Log started at 7/27/2025 3:11:07 PM
2025-07-27 15:11:07.721 [Information] LoggingService: Logging service initialized
2025-07-27 15:11:07.741 [Information] App: Starting integrated application initialization
2025-07-27 15:11:07.743 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-27 15:11:07.747 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-27 15:11:07.749 [Information] IntegratedStartupService: Setting up application environment
2025-07-27 15:11:07.750 [Information] IntegratedStartupService: Application environment setup completed
2025-07-27 15:11:07.752 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-27 15:11:07.755 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-27 15:11:07.757 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-27 15:11:07.762 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-27 15:11:07.779 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 15:11:07.789 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.792 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.793 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-27 15:11:07.797 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 15:11:07.799 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.802 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.803 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 15:11:07.807 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 15:11:07.810 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.813 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.814 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 15:11:07.818 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 15:11:07.821 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.823 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.824 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 15:11:07.828 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 15:11:07.831 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.835 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.836 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 15:11:07.839 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 15:11:07.842 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.845 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.846 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 15:11:07.850 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-27 15:11:07.853 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.856 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-27 15:11:07.857 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 15:11:07.859 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-27 15:11:07.862 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-27 15:11:07.863 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-27 15:11:07.881 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-27 15:11:07.881 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-27 15:11:07.893 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-27 15:11:07.893 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-27 15:11:07.894 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-27 15:11:07.986 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-27 15:11:07.987 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-27 15:11:08.040 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-27 15:11:08.040 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 15:11:08.040 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 15:11:08.041 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 15:11:08.041 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 15:11:08.042 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 15:11:08.042 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 15:11:08.050 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-27 15:11:08.051 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-27 15:11:08.051 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-27 15:11:08.058 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-27 15:11:08.058 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-27 15:11:08.061 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-27 15:11:08.063 [Information] LibraryExtractor: Starting library extraction process
2025-07-27 15:11:08.067 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-27 15:11:08.072 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-27 15:11:08.074 [Information] LibraryExtractor: Copying system libraries
2025-07-27 15:11:08.090 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-27 15:11:08.122 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-27 15:11:38.139 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 15:11:39.142 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-27 15:12:09.148 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 15:12:09.149 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 15:12:39.153 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 15:12:40.154 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll (attempt 2/2)
2025-07-27 15:13:10.157 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-runtime-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 15:13:10.158 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 15:13:40.162 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 15:13:41.163 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll (attempt 2/2)
2025-07-27 15:14:11.166 [Warning] LibraryExtractor: Download timeout for redistributable api-ms-win-crt-heap-l1-1-0.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 15:14:11.167 [Warning] LibraryExtractor: Reached maximum download attempts (3), skipping remaining libraries to prevent hanging
2025-07-27 15:14:11.168 [Information] LibraryExtractor: Completed download phase with 3 attempts
2025-07-27 15:14:11.172 [Information] LibraryExtractor: Verifying library extraction
2025-07-27 15:14:11.173 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-27 15:14:11.173 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-27 15:14:11.174 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-27 15:14:11.174 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-27 15:14:11.174 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-27 15:14:11.178 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-27 15:14:11.182 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-27 15:14:11.184 [Information] DependencyManager: Initializing dependency manager
2025-07-27 15:14:11.185 [Information] DependencyManager: Setting up library search paths
2025-07-27 15:14:11.186 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 15:14:11.187 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 15:14:11.187 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 15:14:11.187 [Information] DependencyManager: Updated PATH environment variable
2025-07-27 15:14:11.189 [Information] DependencyManager: Verifying required directories
2025-07-27 15:14:11.189 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 15:14:11.190 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 15:14:11.190 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\System
2025-07-27 15:14:11.191 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 15:14:11.193 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-27 15:14:11.226 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 15:14:11.235 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 15:14:11.254 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 15:14:11.262 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 15:14:11.265 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 15:14:11.266 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-27 15:14:11.283 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 15:14:36.863 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll from C:\Windows\system32\msvcp140.dll: Error 193
2025-07-27 15:14:36.867 [Warning] DependencyManager: Architecture mismatch detected for msvcp140.dll. Expected: x64
2025-07-27 15:14:36.869 [Debug] DependencyManager: Architecture mismatch: Library msvcp140.dll is x86, process is x64
2025-07-27 15:14:36.869 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp140.dll
2025-07-27 15:14:37.351 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp140.dll: Error 193
2025-07-27 15:14:37.351 [Warning] DependencyManager: VC++ Redistributable library not found: msvcp140.dll
2025-07-27 15:14:37.353 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 15:14:37.369 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 15:14:37.375 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 15:14:37.375 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 15:14:37.377 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 15:14:37.377 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 15:14:37.378 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 15:14:37.378 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 15:14:37.380 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 15:14:37.380 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 15:14:37.382 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 15:14:37.382 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 15:14:37.384 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 15:14:37.384 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 15:14:37.386 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 15:14:37.387 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-07-27 15:14:37.389 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 15:14:37.389 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-07-27 15:14:37.391 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 15:14:37.391 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-07-27 15:14:37.392 [Information] DependencyManager: VC++ Redistributable library loading: 3/14 (21.4%) libraries loaded
2025-07-27 15:14:37.392 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-07-27 15:14:37.394 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-27 15:14:37.478 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\WUDFPuma.dll (x64)
2025-07-27 15:14:37.649 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 15:14:37.650 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll
2025-07-27 15:14:37.781 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 15:14:37.782 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 15:14:37.783 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-27 15:14:37.900 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-27 15:14:37.901 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apcidb.dll
2025-07-27 15:14:37.976 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-27 15:14:37.977 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apcidb.dll
2025-07-27 15:14:37.978 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-27 15:14:38.305 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-27 15:14:38.308 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlus.dll
2025-07-27 15:14:38.696 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-27 15:14:38.696 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlus.dll
2025-07-27 15:14:38.697 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-27 15:14:38.854 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-27 15:14:38.855 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\Volvo.ApciPlusData.dll
2025-07-27 15:14:39.150 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-27 15:14:39.150 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Volvo.ApciPlusData.dll
2025-07-27 15:14:39.152 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-27 15:14:39.288 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-27 15:14:39.288 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 15:14:39.289 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\PhoenixGeneral.dll
2025-07-27 15:14:39.292 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcr120.dll
2025-07-27 15:14:39.293 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-27 15:14:39.294 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp120.dll
2025-07-27 15:14:39.294 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-27 15:14:39.295 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-27 15:14:39.296 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\msvcp140.dll
2025-07-27 15:14:39.298 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-27 15:14:39.299 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\vcruntime140.dll
2025-07-27 15:14:39.299 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-27 15:14:39.301 [Information] DependencyManager: Setting up environment variables
2025-07-27 15:14:39.301 [Information] DependencyManager: Environment variables configured
2025-07-27 15:14:39.303 [Information] DependencyManager: Verifying library loading status
2025-07-27 15:14:39.765 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-27 15:14:39.765 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-27 15:14:39.765 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-27 15:14:39.768 [Information] IntegratedStartupService: Dependency status: 10 found, 1 missing
2025-07-27 15:14:39.769 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-27 15:14:39.782 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-27 15:14:39.784 [Information] IntegratedStartupService: Verifying system readiness
2025-07-27 15:14:39.785 [Information] IntegratedStartupService: System readiness verification passed
2025-07-27 15:14:39.785 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-27 15:14:39.787 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-27 15:14:39.787 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix
2025-07-27 15:14:39.788 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 15:14:39.788 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 15:14:39.788 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-27 15:14:39.788 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-27 15:14:39.789 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-27 15:14:39.789 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 15:14:39.790 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-27 15:14:39.791 [Information] App: Integrated startup completed successfully
2025-07-27 15:14:39.794 [Information] App: System Status - Libraries: 3 available, Dependencies: 10 loaded
2025-07-27 15:14:40.111 [Information] App: Initializing application services
2025-07-27 15:14:40.113 [Information] AppConfigurationService: Initializing configuration service
2025-07-27 15:14:40.114 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config
2025-07-27 15:14:40.179 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 15:14:40.180 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-27 15:14:40.181 [Information] App: Configuration service initialized successfully
2025-07-27 15:14:40.183 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-27 15:14:40.184 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-27 15:14:40.189 [Information] App: Environment variable exists: True, not 'false': False
2025-07-27 15:14:40.190 [Information] App: Final useDummyImplementations value: False
2025-07-27 15:14:40.191 [Information] App: Updating config to NOT use dummy implementations
2025-07-27 15:14:40.193 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-27 15:14:40.222 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Config\app_config.json
2025-07-27 15:14:40.228 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-27 15:14:40.230 [Information] App: usePatchedImplementation flag is: True
2025-07-27 15:14:40.231 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-27 15:14:40.231 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries'
2025-07-27 15:14:40.232 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-27 15:14:40.232 [Information] App: verboseLogging flag is: True
2025-07-27 15:14:40.235 [Information] App: Verifying real hardware requirements...
2025-07-27 15:14:40.235 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-27 15:14:40.235 [Information] App: ✓ Found critical library: apci.dll
2025-07-27 15:14:40.236 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-27 15:14:40.236 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-27 15:14:40.236 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 15:14:40.237 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-27 15:14:40.237 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\config.json
2025-07-27 15:14:40.237 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-27 15:14:40.248 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-27 15:14:40.253 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-27 15:14:40.254 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-07-27 15:14:40.261 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-07-27 15:14:40.271 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-07-27 15:17:01.615 [Warning] RuntimeDependencyResolver: Failed to download msvcr140.dll: Error while copying content to a stream.
2025-07-27 15:17:01.616 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-07-27 15:17:01.617 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-07-27 15:17:01.617 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-07-27 15:17:01.618 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 15:17:01.618 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 15:17:01.619 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 15:17:01.620 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 15:17:01.620 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 15:17:01.621 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 15:17:01.621 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-07-27 15:17:01.623 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-27 15:17:01.623 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-27 15:17:01.623 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 15:17:01.624 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-27 15:17:01.624 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-27 15:17:01.624 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-07-27 15:17:01.624 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-27 15:17:01.625 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-27 15:17:01.626 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-27 15:17:01.626 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-27 15:17:01.629 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-27 15:17:01.629 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-27 15:17:01.630 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-27 15:17:01.630 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-27 15:17:01.632 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-27 15:17:01.632 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - trying direct detection first before falling back to bridge
2025-07-27 15:17:01.635 [Information] ArchitectureAwareVocomServiceFactory: Attempting to create direct Vocom service to preserve original working detection
2025-07-27 15:17:01.635 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-27 15:17:01.637 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=*******, Culture=neutral, PublicKeyToken=null
2025-07-27 15:17:01.637 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\VolvoFlashWR.Communication.dll
2025-07-27 15:17:01.664 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-27 15:17:01.665 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\patched_factory_created.txt
2025-07-27 15:17:01.670 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-27 15:17:01.671 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-27 15:17:01.671 [Information] PatchedVocomServiceFactory: Current process architecture: X64
2025-07-27 15:17:01.672 [Information] PatchedVocomServiceFactory: Process architecture is x64, compatible with WUDFPuma.dll
2025-07-27 15:17:01.674 [Warning] PatchedVocomServiceFactory: ✗ Runtime dependency missing: msvcr140.dll
2025-07-27 15:17:01.675 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: msvcp140.dll
2025-07-27 15:17:01.675 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: vcruntime140.dll
2025-07-27 15:17:01.675 [Information] PatchedVocomServiceFactory: ✓ Runtime dependency available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 15:17:01.676 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-27 15:17:01.676 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-27 15:17:01.676 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-27 15:17:01.679 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-27 15:17:01.683 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-27 15:17:01.695 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-27 15:17:01.696 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 15:17:01.696 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-27 15:17:01.716 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-27 15:17:01.735 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-27 15:17:01.737 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-27 15:17:01.737 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-27 15:17:01.738 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 15:17:01.739 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll has incompatible architecture
2025-07-27 15:17:01.740 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 15:17:01.740 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries\apci.dll has incompatible architecture
2025-07-27 15:17:01.845 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-27 15:17:01.845 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-27 15:17:01.846 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-27 15:17:01.847 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-27 15:17:01.847 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-27 15:17:01.849 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-27 15:17:01.850 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-27 15:17:01.850 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-27 15:17:01.852 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-27 15:17:01.854 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-27 15:17:01.854 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-27 15:17:01.855 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-27 15:17:01.855 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-27 15:17:01.859 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-27 15:17:01.861 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-27 15:17:01.861 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-27 15:17:01.861 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-27 15:17:01.862 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-27 15:17:01.862 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-27 15:17:01.862 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-27 15:17:01.863 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-27 15:17:01.864 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-27 15:17:01.864 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-27 15:17:01.866 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-27 15:17:01.866 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-27 15:17:01.867 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-27 15:17:01.867 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 15:17:01.867 [Information] VocomDiagnosticTool: === Solution Recommendations ===
2025-07-27 15:17:01.870 [Warning] VocomDiagnosticTool: RECOMMENDATION: Missing Visual C++ 2015-2022 Redistributable
2025-07-27 15:17:01.870 [Warning] VocomDiagnosticTool: SOLUTION: Install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-27 15:17:01.871 [Information] VocomDiagnosticTool: === End Recommendations ===
2025-07-27 15:17:01.871 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-27 15:17:01.872 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed
2025-07-27 15:17:01.873 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-27 15:17:01.873 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-27 15:17:01.875 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-27 15:17:01.877 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-27 15:17:01.878 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-27 15:17:02.164 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-27 15:17:02.164 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-27 15:17:02.165 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 15:17:02.165 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 15:17:02.168 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-27 15:17:02.170 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 15:17:02.170 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 15:17:02.171 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-27 15:17:02.171 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 15:17:02.172 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 15:17:02.172 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-27 15:17:02.407 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 15:17:02.407 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-27 15:17:02.522 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 15:17:02.523 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-27 15:17:02.523 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 15:17:02.524 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 15:17:02.525 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-27 15:17:02.646 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 15:17:02.766 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 15:17:02.766 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-27 15:17:02.862 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 15:17:03.034 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 15:17:03.176 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 15:17:03.318 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 15:17:03.319 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-27 15:17:03.319 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries
2025-07-27 15:17:03.604 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 15:17:03.604 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-27 15:17:03.711 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 15:17:03.813 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 15:17:03.813 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-27 15:17:03.986 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\, error: 0
2025-07-27 15:17:04.148 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Libraries, error: 0
2025-07-27 15:17:04.148 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-27 15:17:04.310 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 15:17:04.479 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 15:17:04.547 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\
2025-07-27 15:17:04.552 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-27 15:17:04.552 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\apci.dll
2025-07-27 15:17:04.553 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-27 15:17:04.554 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-27 15:17:04.554 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-27 15:17:04.556 [Information] VocomDriver: Initializing Vocom driver
2025-07-27 15:17:04.559 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-27 15:17:04.562 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-27 15:17:04.562 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 15:17:04.563 [Information] VocomNativeInterop: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 15:17:04.565 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFPuma.dll
2025-07-27 15:17:04.565 [Information] WUDFPumaDependencyResolver: Set DLL directory to: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom
2025-07-27 15:17:08.111 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-27 15:17:08.677 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-27 15:17:08.680 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-27 15:17:09.134 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-27 15:17:09.638 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-27 15:17:09.640 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 15:17:09.721 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WdfCoInstaller01009.dll
2025-07-27 15:17:09.920 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\WUDFUpdate_01009.dll
2025-07-27 15:17:10.100 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Drivers\Vocom\winusbcoinstaller2.dll
2025-07-27 15:17:10.149 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-27 15:17:10.149 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-27 15:17:10.150 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-27 15:17:10.152 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-27 15:17:10.152 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-27 15:17:10.153 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-27 15:17:10.153 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-27 15:17:10.153 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-27 15:17:10.154 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-27 15:17:10.154 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-27 15:17:10.154 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-27 15:17:10.155 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-27 15:17:10.155 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-27 15:17:10.155 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-27 15:17:10.155 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-27 15:17:10.156 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-27 15:17:10.156 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-27 15:17:10.156 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-27 15:17:10.156 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-27 15:17:10.157 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-27 15:17:10.157 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-27 15:17:10.157 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-27 15:17:10.157 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-27 15:17:10.158 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-27 15:17:10.158 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-27 15:17:10.158 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-27 15:17:10.158 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-27 15:17:10.159 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-27 15:17:10.159 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-27 15:17:10.160 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-27 15:17:10.160 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-27 15:17:10.161 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-27 15:17:10.161 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-27 15:17:10.161 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-27 15:17:10.162 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-27 15:17:10.162 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-27 15:17:10.162 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-27 15:17:10.162 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-27 15:17:10.163 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-27 15:17:10.163 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-27 15:17:10.163 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-27 15:17:10.163 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-27 15:17:10.164 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-27 15:17:10.164 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-27 15:17:10.164 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-27 15:17:10.164 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-27 15:17:10.166 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-27 15:17:10.167 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-27 15:17:10.168 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-27 15:17:10.168 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-27 15:17:10.169 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-27 15:17:10.169 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-27 15:17:10.169 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-27 15:17:10.170 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-27 15:17:10.175 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-27 15:17:10.176 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-27 15:17:10.178 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-27 15:17:10.180 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-27 15:17:10.266 [Information] WiFiCommunicationService: WiFi is available
2025-07-27 15:17:10.267 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-27 15:17:10.268 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-27 15:17:10.270 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-27 15:17:10.272 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-27 15:17:10.273 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-27 15:17:10.275 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 15:17:10.277 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 15:17:10.279 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 15:17:10.281 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 15:17:10.287 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 15:17:10.287 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 15:17:10.288 [Information] VocomService: Native USB communication service initialized
2025-07-27 15:17:10.288 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 15:17:10.289 [Information] VocomService: Connection recovery service initialized
2025-07-27 15:17:10.290 [Information] VocomService: Enhanced services initialization completed
2025-07-27 15:17:10.293 [Information] VocomService: Checking if PTT application is running
2025-07-27 15:17:10.310 [Information] VocomService: PTT application is not running
2025-07-27 15:17:10.312 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 15:17:10.315 [Debug] VocomService: Bluetooth is enabled
2025-07-27 15:17:10.316 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 15:17:10.317 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-27 15:17:10.317 [Information] ArchitectureAwareVocomServiceFactory: Testing direct service device detection capability
2025-07-27 15:17:10.321 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 15:17:10.321 [Information] VocomService: Using new enhanced device detection service
2025-07-27 15:17:10.323 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 15:17:10.325 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 15:17:10.777 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 15:17:10.778 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 15:17:10.780 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 15:17:10.782 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 15:17:10.782 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 15:17:10.784 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 15:17:10.786 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 15:17:10.790 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 15:17:11.284 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 15:17:11.286 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 15:17:11.288 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 15:17:11.289 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 15:17:11.291 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 15:17:11.303 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 15:17:11.304 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 15:17:11.304 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 15:17:11.304 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 15:17:11.305 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 15:17:11.305 [Debug] VocomService: Bluetooth is enabled
2025-07-27 15:17:11.307 [Debug] VocomService: Checking if WiFi is available
2025-07-27 15:17:11.309 [Debug] VocomService: WiFi is available
2025-07-27 15:17:11.312 [Information] VocomService: Found 3 Vocom devices
2025-07-27 15:17:11.313 [Information] ArchitectureAwareVocomServiceFactory: Direct service detected 3 total devices
2025-07-27 15:17:11.314 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Driver) (ID: 39451ee2-9ef2-422e-93b9-9878a30decaf, Type: USB)
2025-07-27 15:17:11.314 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (Bluetooth) (ID: 35e65fe1-678e-408b-9058-6763b482dc6d, Type: Bluetooth)
2025-07-27 15:17:11.315 [Information] ArchitectureAwareVocomServiceFactory:   - Device: Vocom - 88890300 (WiFi) (ID: c914565e-8c35-4975-ba48-71fef53e7448, Type: WiFi)
2025-07-27 15:17:11.316 [Information] ArchitectureAwareVocomServiceFactory: Direct service found 3 real Vocom devices - using direct service
2025-07-27 15:17:11.316 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Driver) (Serial: 88890300-DRIVER)
2025-07-27 15:17:11.317 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (Bluetooth) (Serial: 88890300-BT)
2025-07-27 15:17:11.317 [Information] ArchitectureAwareVocomServiceFactory:   - Real device: Vocom - 88890300 (WiFi) (Serial: 88890300-WiFi)
2025-07-27 15:17:11.317 [Information] ArchitectureAwareVocomServiceFactory: Direct service successfully detected real devices - using direct service despite architecture mismatch
2025-07-27 15:17:11.318 [Information] App: Architecture-aware Vocom service created successfully
2025-07-27 15:17:11.318 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 15:17:11.318 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 15:17:11.318 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 15:17:11.319 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 15:17:11.320 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 15:17:11.321 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 15:17:11.321 [Information] VocomService: Native USB communication service initialized
2025-07-27 15:17:11.321 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 15:17:11.321 [Information] VocomService: Connection recovery service initialized
2025-07-27 15:17:11.322 [Information] VocomService: Enhanced services initialization completed
2025-07-27 15:17:11.322 [Information] VocomService: Checking if PTT application is running
2025-07-27 15:17:11.338 [Information] VocomService: PTT application is not running
2025-07-27 15:17:11.339 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 15:17:11.340 [Debug] VocomService: Bluetooth is enabled
2025-07-27 15:17:11.340 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 15:17:11.340 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-27 15:17:11.341 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-27 15:17:11.341 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-27 15:17:11.379 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 15:17:11.380 [Information] VocomService: Using new enhanced device detection service
2025-07-27 15:17:11.380 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 15:17:11.380 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 15:17:11.678 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 15:17:11.679 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 15:17:11.680 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 15:17:11.680 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 15:17:11.681 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 15:17:11.681 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 15:17:11.681 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 15:17:11.682 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 15:17:11.988 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 15:17:11.989 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 15:17:11.990 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 15:17:11.990 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 15:17:11.991 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 15:17:11.999 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 15:17:11.999 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 15:17:12.000 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 15:17:12.000 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 15:17:12.000 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 15:17:12.001 [Debug] VocomService: Bluetooth is enabled
2025-07-27 15:17:12.001 [Debug] VocomService: Checking if WiFi is available
2025-07-27 15:17:12.002 [Debug] VocomService: WiFi is available
2025-07-27 15:17:12.002 [Information] VocomService: Found 3 Vocom devices
2025-07-27 15:17:12.002 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-27 15:17:12.004 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 15:17:12.005 [Information] VocomService: Checking if PTT application is running
2025-07-27 15:17:12.018 [Information] VocomService: PTT application is not running
2025-07-27 15:17:12.023 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 15:17:12.023 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-27 15:17:12.023 [Information] VocomService: Checking if PTT application is running
2025-07-27 15:17:12.039 [Information] VocomService: PTT application is not running
2025-07-27 15:17:12.040 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-27 15:17:12.042 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-27 15:17:12.044 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 15:17:12.046 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 15:17:12.048 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:12.050 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:12.050 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:12.050 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:12.051 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:12.051 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:12.051 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:12.052 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:12.052 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:12.052 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 15:17:12.052 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:12.053 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:12.053 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:12.053 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:12.053 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:12.054 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 15:17:12.055 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 15:17:12.055 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 15:17:12.055 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 15:17:12.056 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 15:17:12.056 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 15:17:12.056 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 15:17:12.057 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 15:17:13.058 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-27 15:17:13.059 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 15:17:13.059 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 15:17:13.060 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:13.061 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:13.061 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:13.062 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:13.062 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:13.062 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:13.062 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:13.063 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:13.063 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:13.063 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 15:17:13.064 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:13.064 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:13.064 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:13.064 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:13.065 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:13.065 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 15:17:13.065 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 15:17:13.066 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 15:17:13.066 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 15:17:13.066 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 15:17:13.067 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 15:17:13.067 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 15:17:13.067 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 15:17:14.067 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-27 15:17:14.067 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 15:17:14.068 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 15:17:14.068 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:14.069 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:14.069 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:14.069 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:14.070 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:14.070 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:14.070 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:14.071 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:14.071 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:14.071 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 15:17:14.071 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:14.072 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:14.072 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:14.072 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:14.073 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:14.073 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 15:17:14.073 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 15:17:14.074 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 15:17:14.074 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 15:17:14.074 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 15:17:14.074 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 15:17:14.075 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 15:17:14.075 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 15:17:14.076 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 15:17:14.076 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-27 15:17:14.274 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 15:17:14.276 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-27 15:17:14.277 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 15:17:14.278 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 15:17:14.279 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 15:17:14.280 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 15:17:14.280 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 15:17:14.283 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 15:17:14.284 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 15:17:14.288 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-27 15:17:14.290 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 15:17:14.290 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 15:17:14.290 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 15:17:14.291 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 15:17:14.292 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 15:17:14.292 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-27 15:17:14.297 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-27 15:17:14.301 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 15:17:14.302 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-27 15:17:14.306 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 15:17:14.309 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 15:17:14.309 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 15:17:14.310 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 15:17:14.311 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-07-27 15:17:14.311 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 15:17:14.311 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-27 15:17:14.312 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-27 15:17:14.315 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-27 15:17:14.315 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 15:17:14.315 [Information] VocomService: Using new enhanced device detection service
2025-07-27 15:17:14.316 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 15:17:14.316 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 15:17:14.633 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 15:17:14.633 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 15:17:14.634 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 15:17:14.634 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 15:17:14.634 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 15:17:14.635 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 15:17:14.635 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 15:17:14.635 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 15:17:14.947 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 15:17:14.947 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 15:17:14.948 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 15:17:14.948 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 15:17:14.949 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 15:17:14.955 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 15:17:14.955 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 15:17:14.956 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 15:17:14.956 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 15:17:14.956 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 15:17:14.957 [Debug] VocomService: Bluetooth is enabled
2025-07-27 15:17:14.957 [Debug] VocomService: Checking if WiFi is available
2025-07-27 15:17:14.957 [Debug] VocomService: WiFi is available
2025-07-27 15:17:14.958 [Information] VocomService: Found 3 Vocom devices
2025-07-27 15:17:14.959 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 15:17:14.959 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-27 15:17:14.961 [Information] VocomService: Checking if PTT application is running
2025-07-27 15:17:14.975 [Information] VocomService: PTT application is not running
2025-07-27 15:17:14.975 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-27 15:17:14.976 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024
2025-07-27 15:17:14.976 [Information] VocomService: Checking if PTT application is running
2025-07-27 15:17:14.991 [Information] VocomService: PTT application is not running
2025-07-27 15:17:14.992 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024
2025-07-27 15:17:14.992 [Information] VocomService: Native USB connection attempt 1/3 to USB\VID_178E&PID_0024
2025-07-27 15:17:14.992 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 15:17:14.992 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 15:17:14.993 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:14.993 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:14.994 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:14.994 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:14.994 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:14.995 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:14.995 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:14.995 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:14.996 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:14.996 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 15:17:14.996 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:14.997 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:14.997 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:14.997 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:14.997 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:15.000 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 15:17:15.000 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 15:17:15.001 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 15:17:15.001 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 15:17:15.001 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 15:17:15.001 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 15:17:15.002 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 15:17:15.002 [Information] VocomService: Native USB connection attempt 1 failed, retrying in 1000ms
2025-07-27 15:17:16.002 [Information] VocomService: Native USB connection attempt 2/3 to USB\VID_178E&PID_0024
2025-07-27 15:17:16.002 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 15:17:16.003 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 15:17:16.003 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:16.004 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:16.004 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:16.004 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:16.004 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:16.005 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:16.005 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:16.005 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:16.005 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:16.006 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 15:17:16.006 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:16.006 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:16.007 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:16.007 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:16.007 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:16.008 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 15:17:16.008 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 15:17:16.008 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 15:17:16.009 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 15:17:16.009 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 15:17:16.009 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 15:17:16.010 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 15:17:16.010 [Information] VocomService: Native USB connection attempt 2 failed, retrying in 1000ms
2025-07-27 15:17:17.011 [Information] VocomService: Native USB connection attempt 3/3 to USB\VID_178E&PID_0024
2025-07-27 15:17:17.011 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024
2025-07-27 15:17:17.011 [Information] NativeVocomUSBCommunication: Searching for HID interface path for device: USB\VID_178E&PID_0024
2025-07-27 15:17:17.012 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:17.012 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col01#8&6eed0c0&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:17.013 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:17.013 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:17.013 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#btiahiddevice#9&b470d89&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:17.014 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:17.014 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:17.014 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_01&col02#8&6eed0c0&0&0001#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:17.015 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:17.015 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd
2025-07-27 15:17:17.015 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_1a2c&pid_6004&mi_00#8&1ce86d02&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}\kbd' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:17.016 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:17.016 [Debug] NativeVocomUSBCommunication: Found HID device path: \\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}
2025-07-27 15:17:17.016 [Debug] NativeVocomUSBCommunication: Checking if device path '\\?\hid#vid_10c4&pid_8108#6&286dc939&0&0000#{4d1e55b2-f16f-11cf-88cb-001111000030}' matches USB device 'USB\VID_178E&PID_0024'
2025-07-27 15:17:17.016 [Debug] NativeVocomUSBCommunication: VID/PID match: False, Serial match: True, Vocom match: False
2025-07-27 15:17:17.017 [Warning] NativeVocomUSBCommunication: No matching HID interface found for device: USB\VID_178E&PID_0024
2025-07-27 15:17:17.017 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 15:17:17.017 [Information] NativeVocomUSBCommunication: Trying HID path: \\.\HID#VID_178E&PID_0024
2025-07-27 15:17:17.018 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024#VID_178E&PID_0024
2025-07-27 15:17:17.018 [Information] NativeVocomUSBCommunication: Trying HID path: \\?\HID#VID_178E&PID_0024
2025-07-27 15:17:17.018 [Warning] NativeVocomUSBCommunication: Could not find valid device path for USB device: USB\VID_178E&PID_0024
2025-07-27 15:17:17.018 [Warning] NativeVocomUSBCommunication: Could not determine device path for: USB\VID_178E&PID_0024
2025-07-27 15:17:17.019 [Warning] VocomService: All 3 native USB connection attempts failed
2025-07-27 15:17:17.019 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-27 15:17:17.020 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024
2025-07-27 15:17:17.020 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-27 15:17:17.020 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024
2025-07-27 15:17:17.021 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-27 15:17:17.021 [Information] ModernUSBCommunicationService: Operating System: Microsoft Windows NT 10.0.19045.0
2025-07-27 15:17:17.021 [Information] ModernUSBCommunicationService: Is 64-bit OS: True
2025-07-27 15:17:17.022 [Information] ModernUSBCommunicationService: Is 64-bit Process: True
2025-07-27 15:17:17.022 [Information] ModernUSBCommunicationService: CLR Version: 8.0.18
2025-07-27 15:17:17.022 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-27 15:17:17.022 [Information] ModernUSBCommunicationService: Found 5 HID devices total
2025-07-27 15:17:17.023 [Debug] ModernUSBCommunicationService: Checking HID device: VID=1A2C, PID=6004, Name=USB Keyboard
2025-07-27 15:17:17.023 [Warning] ModernUSBCommunicationService: HidSharp connection failed: Failed to get info.
2025-07-27 15:17:17.024 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-27 15:17:17.024 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-27 15:17:17.024 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 15:17:17.025 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-27 15:17:17.025 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 15:17:17.025 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 15:17:17.026 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-27 15:17:17.026 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 15:17:17.026 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 15:17:17.027 [Information] VocomService: Checking if PTT application is running
2025-07-27 15:17:17.043 [Information] VocomService: PTT application is not running
2025-07-27 15:17:17.045 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-27 15:17:17.045 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 15:17:17.046 [Debug] VocomService: Bluetooth is enabled
2025-07-27 15:17:17.047 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-27 15:17:17.853 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 15:17:17.854 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-27 15:17:17.854 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-27 15:17:17.855 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-27 15:17:17.858 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 15:17:17.860 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 15:17:17.881 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 15:17:17.884 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 15:17:17.887 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 15:17:17.896 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 15:17:17.900 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 15:17:17.912 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 15:17:17.913 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 15:17:17.914 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 15:17:17.914 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 15:17:17.915 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 15:17:17.915 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 15:17:17.915 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 15:17:17.915 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 15:17:17.916 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 15:17:17.916 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 15:17:17.916 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 15:17:17.917 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 15:17:17.917 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 15:17:17.917 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 15:17:17.917 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 15:17:17.918 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 15:17:17.918 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 15:17:17.922 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 15:17:17.929 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 15:17:17.931 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 15:17:17.934 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 15:17:17.936 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 15:17:17.943 [Information] CANRegisterAccess: Read value 0x43 from register 0x0141 (simulated)
2025-07-27 15:17:17.944 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 15:17:17.946 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 15:17:17.946 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 15:17:17.952 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 15:17:17.953 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 15:17:17.958 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 15:17:17.959 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 15:17:17.959 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 15:17:17.965 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 15:17:17.966 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 15:17:17.966 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 15:17:17.971 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 15:17:17.972 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 15:17:17.977 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 15:17:17.978 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 15:17:17.983 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 15:17:17.984 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 15:17:17.989 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 15:17:17.990 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 15:17:17.995 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 15:17:17.996 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 15:17:18.000 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 15:17:18.001 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 15:17:18.006 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 15:17:18.007 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 15:17:18.012 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 15:17:18.013 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 15:17:18.018 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 15:17:18.019 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 15:17:18.025 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 15:17:18.026 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 15:17:18.031 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 15:17:18.032 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 15:17:18.037 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 15:17:18.038 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 15:17:18.043 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 15:17:18.044 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 15:17:18.049 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 15:17:18.050 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 15:17:18.055 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 15:17:18.056 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 15:17:18.060 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 15:17:18.062 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 15:17:18.068 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 15:17:18.069 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 15:17:18.070 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 15:17:18.075 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 15:17:18.076 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 15:17:18.076 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 15:17:18.076 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 15:17:18.081 [Information] CANRegisterAccess: Read value 0x5D from register 0x0141 (simulated)
2025-07-27 15:17:18.087 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 15:17:18.093 [Information] CANRegisterAccess: Read value 0x07 from register 0x0141 (simulated)
2025-07-27 15:17:18.099 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 15:17:18.105 [Information] CANRegisterAccess: Read value 0x10 from register 0x0141 (simulated)
2025-07-27 15:17:18.106 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 15:17:18.106 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 15:17:18.106 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 15:17:18.107 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 15:17:18.111 [Information] CANRegisterAccess: Read value 0x2B from register 0x0140 (simulated)
2025-07-27 15:17:18.117 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 15:17:18.122 [Information] CANRegisterAccess: Read value 0xC3 from register 0x0140 (simulated)
2025-07-27 15:17:18.128 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 15:17:18.135 [Information] CANRegisterAccess: Read value 0xF2 from register 0x0140 (simulated)
2025-07-27 15:17:18.136 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 15:17:18.136 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 15:17:18.139 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 15:17:18.140 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 15:17:18.150 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 15:17:18.151 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 15:17:18.152 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 15:17:18.157 [Information] VocomService: Sending data and waiting for response
2025-07-27 15:17:18.157 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 15:17:18.208 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 15:17:18.210 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 15:17:18.211 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 15:17:18.213 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 15:17:18.214 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 15:17:18.224 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 15:17:18.225 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 15:17:18.226 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 15:17:18.236 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 15:17:18.247 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 15:17:18.258 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 15:17:18.269 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 15:17:18.280 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 15:17:18.283 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 15:17:18.283 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 15:17:18.294 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 15:17:18.295 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 15:17:18.296 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 15:17:18.307 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 15:17:18.318 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 15:17:18.329 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 15:17:18.341 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 15:17:18.352 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 15:17:18.363 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 15:17:18.366 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 15:17:18.366 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 15:17:18.377 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 15:17:18.379 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 15:17:18.379 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 15:17:18.380 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 15:17:18.380 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 15:17:18.381 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 15:17:18.381 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 15:17:18.381 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 15:17:18.382 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 15:17:18.382 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 15:17:18.382 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 15:17:18.383 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 15:17:18.383 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 15:17:18.383 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 15:17:18.383 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 15:17:18.384 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 15:17:18.384 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 15:17:18.484 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 15:17:18.485 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 15:17:18.487 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 15:17:18.489 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 15:17:18.489 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 15:17:18.490 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 15:17:18.491 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 15:17:18.491 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 15:17:18.491 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 15:17:18.492 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 15:17:18.492 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 15:17:18.492 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 15:17:18.493 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 15:17:18.493 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 15:17:18.494 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 15:17:18.495 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-27 15:17:18.496 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-27 15:17:18.496 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-27 15:17:18.500 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-27 15:17:18.502 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-27 15:17:18.506 [Information] BackupService: Initializing backup service
2025-07-27 15:17:18.507 [Information] BackupService: Backup service initialized successfully
2025-07-27 15:17:18.508 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-27 15:17:18.508 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-27 15:17:18.512 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-27 15:17:18.555 [Information] BackupService: Compressing backup data
2025-07-27 15:17:18.570 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (448 bytes)
2025-07-27 15:17:18.572 [Information] BackupServiceFactory: Created template for category: Production
2025-07-27 15:17:18.572 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-27 15:17:18.573 [Information] BackupService: Compressing backup data
2025-07-27 15:17:18.574 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (453 bytes)
2025-07-27 15:17:18.575 [Information] BackupServiceFactory: Created template for category: Development
2025-07-27 15:17:18.575 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-27 15:17:18.576 [Information] BackupService: Compressing backup data
2025-07-27 15:17:18.577 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (446 bytes)
2025-07-27 15:17:18.577 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-27 15:17:18.577 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-27 15:17:18.582 [Information] BackupService: Compressing backup data
2025-07-27 15:17:18.589 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (451 bytes)
2025-07-27 15:17:18.590 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-27 15:17:18.592 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-27 15:17:18.593 [Information] BackupService: Compressing backup data
2025-07-27 15:17:18.594 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (450 bytes)
2025-07-27 15:17:18.594 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-27 15:17:18.597 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-27 15:17:18.597 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-27 15:17:18.598 [Information] BackupService: Compressing backup data
2025-07-27 15:17:18.601 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-07-27 15:17:18.601 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-27 15:17:18.601 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-27 15:17:18.604 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-27 15:17:18.607 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 15:17:18.610 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 15:17:18.692 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 15:17:18.693 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 15:17:18.694 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-27 15:17:18.695 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-27 15:17:18.695 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-27 15:17:18.697 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-27 15:17:18.697 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-27 15:17:18.703 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-27 15:17:18.703 [Information] App: Flash operation monitor service initialized successfully
2025-07-27 15:17:18.716 [Information] LicensingService: Initializing licensing service
2025-07-27 15:17:18.783 [Information] LicensingService: License information loaded successfully
2025-07-27 15:17:18.786 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-27 15:17:18.786 [Information] App: Licensing service initialized successfully
2025-07-27 15:17:18.786 [Information] App: License status: Trial
2025-07-27 15:17:18.787 [Information] App: Trial period: 30 days remaining
2025-07-27 15:17:18.788 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-27 15:17:18.815 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-27 15:17:18.978 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-27 15:17:18.978 [Information] VocomService: Initializing enhanced Vocom services
2025-07-27 15:17:18.978 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-27 15:17:18.979 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-27 15:17:18.980 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-27 15:17:18.980 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-27 15:17:18.981 [Information] VocomService: Native USB communication service initialized
2025-07-27 15:17:18.981 [Information] VocomService: Enhanced device detection service initialized
2025-07-27 15:17:18.982 [Information] VocomService: Connection recovery service initialized
2025-07-27 15:17:18.982 [Information] VocomService: Enhanced services initialization completed
2025-07-27 15:17:18.982 [Information] VocomService: Checking if PTT application is running
2025-07-27 15:17:18.997 [Information] VocomService: PTT application is not running
2025-07-27 15:17:18.997 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 15:17:18.998 [Debug] VocomService: Bluetooth is enabled
2025-07-27 15:17:18.999 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-27 15:17:19.050 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-27 15:17:19.050 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-27 15:17:19.051 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-27 15:17:19.051 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 15:17:19.051 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: True
2025-07-27 15:17:19.052 [Information] ECUCommunicationService: _vocomService.CurrentDevice.Id: 7c9c3536-18a3-4462-b4af-d0767748d9dd
2025-07-27 15:17:19.053 [Information] ECUCommunicationService: _vocomService.CurrentDevice.ConnectionStatus: Connected
2025-07-27 15:17:19.054 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-07-27 15:17:19.054 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-27 15:17:19.054 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-27 15:17:19.056 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-27 15:17:19.056 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-27 15:17:19.057 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-27 15:17:19.058 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-27 15:17:19.058 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-27 15:17:19.069 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 15:17:19.069 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-27 15:17:19.070 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-27 15:17:19.070 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-27 15:17:19.070 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-27 15:17:19.071 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-27 15:17:19.071 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-27 15:17:19.071 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-27 15:17:19.071 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-27 15:17:19.072 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-27 15:17:19.072 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-27 15:17:19.072 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-27 15:17:19.072 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-27 15:17:19.073 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-27 15:17:19.073 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-27 15:17:19.073 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-27 15:17:19.074 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-27 15:17:19.074 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-27 15:17:19.080 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-27 15:17:19.080 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-27 15:17:19.081 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-27 15:17:19.081 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 15:17:19.087 [Information] CANRegisterAccess: Read value 0x33 from register 0x0141 (simulated)
2025-07-27 15:17:19.087 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-27 15:17:19.088 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-27 15:17:19.088 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-27 15:17:19.095 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-27 15:17:19.096 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-27 15:17:19.102 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-27 15:17:19.102 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-27 15:17:19.103 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-27 15:17:19.108 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-27 15:17:19.109 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-27 15:17:19.109 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-27 15:17:19.114 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-27 15:17:19.114 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-27 15:17:19.121 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-27 15:17:19.121 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-27 15:17:19.127 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-27 15:17:19.127 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-27 15:17:19.134 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-27 15:17:19.134 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-27 15:17:19.140 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-27 15:17:19.141 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-27 15:17:19.147 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-27 15:17:19.147 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-27 15:17:19.153 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-27 15:17:19.153 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-27 15:17:19.160 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-27 15:17:19.160 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-27 15:17:19.167 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-27 15:17:19.167 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-27 15:17:19.173 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-27 15:17:19.173 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-27 15:17:19.180 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-27 15:17:19.181 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-27 15:17:19.187 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-27 15:17:19.187 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-27 15:17:19.194 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-27 15:17:19.194 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-27 15:17:19.200 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-27 15:17:19.200 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-27 15:17:19.207 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-27 15:17:19.207 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-27 15:17:19.214 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-27 15:17:19.214 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-27 15:17:19.220 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-27 15:17:19.220 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-27 15:17:19.221 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-27 15:17:19.227 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-27 15:17:19.227 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-27 15:17:19.228 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-27 15:17:19.228 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 15:17:19.235 [Information] CANRegisterAccess: Read value 0x41 from register 0x0141 (simulated)
2025-07-27 15:17:19.241 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-27 15:17:19.247 [Information] CANRegisterAccess: Read value 0x00 from register 0x0141 (simulated)
2025-07-27 15:17:19.247 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-27 15:17:19.248 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-27 15:17:19.248 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-27 15:17:19.248 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-27 15:17:19.255 [Information] CANRegisterAccess: Read value 0xDE from register 0x0140 (simulated)
2025-07-27 15:17:19.255 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-27 15:17:19.256 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-27 15:17:19.256 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-27 15:17:19.257 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-27 15:17:19.268 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-27 15:17:19.268 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-27 15:17:19.269 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-27 15:17:19.269 [Information] VocomService: Sending data and waiting for response
2025-07-27 15:17:19.269 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-27 15:17:19.320 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-27 15:17:19.321 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-27 15:17:19.321 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-27 15:17:19.322 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-27 15:17:19.322 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-27 15:17:19.333 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 15:17:19.334 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-27 15:17:19.334 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-27 15:17:19.345 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-27 15:17:19.356 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-27 15:17:19.367 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-27 15:17:19.379 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-27 15:17:19.390 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-27 15:17:19.391 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-27 15:17:19.391 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-27 15:17:19.402 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 15:17:19.403 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-27 15:17:19.403 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-27 15:17:19.414 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-27 15:17:19.425 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-27 15:17:19.436 [Information] IICProtocolHandler: Enabling IIC module
2025-07-27 15:17:19.447 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-27 15:17:19.458 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-27 15:17:19.469 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-27 15:17:19.469 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-27 15:17:19.469 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-27 15:17:19.481 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 15:17:19.481 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-27 15:17:19.481 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-27 15:17:19.482 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-27 15:17:19.482 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-27 15:17:19.482 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-27 15:17:19.482 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-27 15:17:19.483 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-27 15:17:19.483 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-27 15:17:19.483 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-27 15:17:19.483 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-27 15:17:19.484 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-27 15:17:19.484 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-27 15:17:19.484 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-27 15:17:19.484 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-27 15:17:19.485 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-27 15:17:19.485 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-27 15:17:19.585 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-27 15:17:19.585 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-27 15:17:19.586 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-27 15:17:19.586 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 15:17:19.587 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-27 15:17:19.587 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-27 15:17:19.587 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 15:17:19.588 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-27 15:17:19.588 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-27 15:17:19.588 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 15:17:19.589 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-27 15:17:19.589 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-27 15:17:19.590 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-27 15:17:19.590 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-27 15:17:19.590 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-27 15:17:19.642 [Information] BackupService: Initializing backup service
2025-07-27 15:17:19.642 [Information] BackupService: Backup service initialized successfully
2025-07-27 15:17:19.694 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-27 15:17:19.694 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-27 15:17:19.695 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR_Export_With_Fix\Schedules\backup_schedules.json
2025-07-27 15:17:19.696 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-27 15:17:19.748 [Information] BackupService: Getting predefined backup categories
2025-07-27 15:17:19.799 [Information] MainViewModel: Services initialized successfully
2025-07-27 15:17:19.803 [Information] MainViewModel: Scanning for Vocom devices
2025-07-27 15:17:19.804 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-27 15:17:19.804 [Information] VocomService: Using new enhanced device detection service
2025-07-27 15:17:19.805 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-27 15:17:19.805 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-27 15:17:20.110 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-27 15:17:20.110 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-27 15:17:20.111 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-27 15:17:20.111 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-27 15:17:20.111 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-27 15:17:20.111 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-27 15:17:20.112 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-27 15:17:20.112 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-27 15:17:20.527 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-27 15:17:20.527 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-27 15:17:20.528 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-27 15:17:20.528 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-27 15:17:20.529 [Information] EnhancedVocomDeviceDetector: Searching for actual Vocom USB device path
2025-07-27 15:17:20.563 [Error] EnhancedVocomDeviceDetector: Error finding actual Vocom USB path: Invalid query 
2025-07-27 15:17:20.564 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry with USB path: USB\VID_178E&PID_0024
2025-07-27 15:17:20.564 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-27 15:17:20.564 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-27 15:17:20.565 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-27 15:17:20.566 [Debug] VocomService: Bluetooth is enabled
2025-07-27 15:17:20.567 [Debug] VocomService: Checking if WiFi is available
2025-07-27 15:17:20.581 [Debug] VocomService: WiFi is available
2025-07-27 15:17:20.597 [Information] VocomService: Found 3 Vocom devices
2025-07-27 15:17:20.601 [Information] MainViewModel: Found 3 Vocom device(s)
