Log started at 7/27/2025 10:17:23 AM
2025-07-27 10:17:23.219 [Information] LoggingService: Logging service initialized
2025-07-27 10:17:23.231 [Information] App: Starting integrated application initialization
2025-07-27 10:17:23.234 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-27 10:17:23.238 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-27 10:17:23.240 [Information] IntegratedStartupService: Setting up application environment
2025-07-27 10:17:23.241 [Information] IntegratedStartupService: Application environment setup completed
2025-07-27 10:17:23.243 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-27 10:17:23.245 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-27 10:17:23.248 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-07-27 10:17:23.253 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-27 10:17:23.266 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-27 10:17:23.274 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 10:17:23.282 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 10:17:23.290 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 10:17:23.298 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 10:17:23.307 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 10:17:23.316 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 10:17:23.318 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-27 10:17:23.320 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-27 10:17:23.320 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-27 10:17:23.323 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-27 10:17:23.326 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-27 10:17:23.327 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-27 10:17:23.328 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-27 10:17:23.330 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-27 10:17:23.330 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-27 10:17:23.330 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-27 10:17:23.331 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-07-27 10:17:23.331 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-07-27 10:17:23.331 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-07-27 10:17:23.339 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-07-27 10:17:23.339 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-27 10:17:23.340 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-27 10:17:23.347 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-07-27 10:17:23.347 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-07-27 10:17:23.349 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-27 10:17:23.352 [Information] LibraryExtractor: Starting library extraction process
2025-07-27 10:17:23.355 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-07-27 10:17:23.358 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-27 10:17:23.360 [Information] LibraryExtractor: Copying system libraries
2025-07-27 10:17:23.367 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-27 10:17:23.375 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-27 10:17:53.387 [Warning] LibraryExtractor: Download timeout for redistributable msvcr120.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 10:17:54.387 [Information] LibraryExtractor: Retrying download for msvcr120.dll (attempt 2/2)
2025-07-27 10:18:24.390 [Warning] LibraryExtractor: Download timeout for redistributable msvcr120.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 10:18:24.392 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-27 10:18:54.395 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 10:18:55.396 [Information] LibraryExtractor: Retrying download for msvcr140.dll (attempt 2/2)
2025-07-27 10:19:25.400 [Warning] LibraryExtractor: Download timeout for redistributable msvcr140.dll: The request was canceled due to the configured HttpClient.Timeout of 30 seconds elapsing.
2025-07-27 10:19:25.401 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
