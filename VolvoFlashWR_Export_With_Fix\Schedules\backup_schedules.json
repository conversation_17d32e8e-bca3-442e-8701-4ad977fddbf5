[{"Id": "a22b3e03-d456-4817-ad6a-dd6e9bc95091", "Name": "Daily Backup - EMS", "Description": "Automatic daily backup at 3:00 AM", "ECUId": "4b5ad2cd-2102-41a0-9434-51acff6f371f", "ECUName": "EMS", "IsEnabled": true, "FrequencyType": 1, "Frequency": 1, "Interval": 1, "TimeOfDay": "03:00:00", "StartHour": 3, "StartMinute": 0, "DaysOfWeek": [1], "StartDayOfWeek": 1, "DayOfMonth": 1, "StartDate": "2025-07-14T00:00:00+03:00", "EndDate": null, "LastExecutionTime": null, "NextExecutionTime": "2025-07-15T03:00:00+03:00", "Category": "Automated", "Tags": ["Daily", "Automated"], "IncludeEEPROM": true, "IncludeMicrocontrollerCode": true, "IncludeParameters": true, "MaxBackupsToKeep": 7, "MaxBackupAge": 30, "RetryCount": 3, "CustomIntervalDays": 1, "ScheduleType": "Standard", "CreatedBackupIds": []}, {"Id": "9983cd71-400b-4826-ad8f-20a6ff2d10bb", "Name": "Weekly Backup - EMS", "Description": "Automatic weekly backup on Sunday at 4:00 AM", "ECUId": "4b5ad2cd-2102-41a0-9434-51acff6f371f", "ECUName": "EMS", "IsEnabled": true, "FrequencyType": 2, "Frequency": 2, "Interval": 1, "TimeOfDay": "04:00:00", "StartHour": 4, "StartMinute": 0, "DaysOfWeek": [0], "StartDayOfWeek": 1, "DayOfMonth": 1, "StartDate": "2025-07-14T00:00:00+03:00", "EndDate": null, "LastExecutionTime": null, "NextExecutionTime": "2025-07-20T04:00:00+03:00", "Category": "Automated", "Tags": ["Weekly", "Automated"], "IncludeEEPROM": true, "IncludeMicrocontrollerCode": true, "IncludeParameters": true, "MaxBackupsToKeep": 4, "MaxBackupAge": 30, "RetryCount": 3, "CustomIntervalDays": 1, "ScheduleType": "Standard", "CreatedBackupIds": []}]