﻿<?xml version="1.0" encoding="UTF-8"?>
<ApplicationReport>
  <ApplicationName>R009</ApplicationName>
  <ReportType>ErrorLog</ReportType>
  <Content>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1000</ErrorNumber>
      <ReportCode>1000</ReportCode>
      <DateAndTime>2024-12-15T11:12:26.4940504-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VOHCDiag: cArchitecture.fnMakeCommandCall (9) Collection index must be in the range 1 to the size of the collection.
- VOHCDiag: cArchitecture.fnMakeCommandCall (1000) Could not make command call. Indata: 63, 1
- VOHCDiag: cArchitecture.MakeCall (-) Could not make call. MID: 130, MID type: 0, APCI type: 4, Command type: 47.
- VOHCDiag: c_Clutch.subMakeCall (-) An error occured communicating with the ECU.
- VOHCDiag: c_Clutch.Perform (-) Could not perform the calibration operation.
- VOHCDiag: cDiag.subPerformOp (-) Could not perform the operation.
- VOHCDiag: cDiag.Operation_Start (-) Could not start the operation.</Details>
      <ChassisSeries>A</ChassisSeries>
      <ChassisNumber xsi:nil="false">622052</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>40070-3</OperationNumber6>
      <Source>VOHCDiag</Source>
      <SourceMethod>cArchitecture.fnMakeCommandCall</SourceMethod>
      <ProfileID>0</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>0</ProductCategoryID>
      <Product>FH</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1114</ErrorNumber>
      <ReportCode>10114</ReportCode>
      <DateAndTime>2024-12-15T11:46:02.7251911-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_21 (1114) Apci.dll *********, ERR: 129 in FN: 21. Unknown communication error. See &lt;Unknown error&gt; code for more information., MID:144, PPID:71
- VCAPCI: mAPCICom.subAPCI_21 (-)
- VCAPCI: cPPID.Refresh (-)
- VCAPCI: cArchitecture.fnMakePPIDCall (-) Could not make PPID call. PPID: 71.
- VOTstCal: cArchitecture.MakeCall (-) Could not make call. MID: 144, MID type: 0, APCI type: 3, Command type: 0.
- VOTstCal: cTstCal.subPerformCall (-) Could not make call for pinout or Read Status GUI.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>A</ChassisSeries>
      <ChassisNumber xsi:nil="false">622052</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>27503-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_21</SourceMethod>
      <ProfileID>0</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>0</ProductCategoryID>
      <Product>FH</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1103</ErrorNumber>
      <ReportCode>10103</ReportCode>
      <DateAndTime>2024-12-25T09:04:57.0472375-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_19 (1103) Apci.dll *********, ERR: 1 in FN: 19. No DSR. The interface adapter has no voltage, is not connected or is broken., MID:128, PID:94, Acc. method:0
- VCAPCI: mAPCICom.subAPCI_19 (-)
- VCAPCI: cPID.Refresh (-)
- VCAPCI: cArchitecture.MakePIDCall (-) Could not make PID call. PID: 94.
- VOTstCal: cArchitecture.MakeCall (-) Could not make call. MID: 128, MID type: 0, APCI type: 2, Command type: 0.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>L180E</ChassisSeries>
      <ChassisNumber xsi:nil="false">9150</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>28407-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_19</SourceMethod>
      <ProfileID>3</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>30</ProductCategoryID>
      <Product>L180E</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1140</ErrorNumber>
      <ReportCode>10140</ReportCode>
      <DateAndTime>2024-12-25T09:12:49.6835644-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_15a (1140) Apci.dll *********, ERR: 31 in FN: 15. Wrong answer from ECU, MID:128, MSW:20762948, Parameter:FK
- VCAPCI: mAPCICom.subAPCI_15a (-)
- VCAPCI: cParameter.Refresh (-)
- VOTstCal: cEMSTests.CompressionTestCrank (-)
- VOTstCal: cEMSTests.CompressionTestStart (-)
- VOTstCal: cEMSTests.EMSTestsExecute (-) Number of cylinders: 6.
- VOTstCal: cTstCal.fnMakeEMSCall (-) Could not make call to engine ECU.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>L180E</ChassisSeries>
      <ChassisNumber xsi:nil="false">9150</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>21006-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_15a</SourceMethod>
      <ProfileID>3</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>30</ProductCategoryID>
      <Product>L180E</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1140</ErrorNumber>
      <ReportCode>10140</ReportCode>
      <DateAndTime>2024-12-25T09:13:13.5286341-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_15a (1140) Apci.dll *********, ERR: 31 in FN: 15. Wrong answer from ECU, MID:128, MSW:20762948, Parameter:FK
- VCAPCI: mAPCICom.subAPCI_15a (-)
- VCAPCI: cParameter.Refresh (-)
- VOTstCal: cEMSTests.CompressionTestCrank (-)
- VOTstCal: cEMSTests.CompressionTestStart (-)
- VOTstCal: cEMSTests.EMSTestsExecute (-) Number of cylinders: 6.
- VOTstCal: cTstCal.fnMakeEMSCall (-) Could not make call to engine ECU.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>L180E</ChassisSeries>
      <ChassisNumber xsi:nil="false">9150</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>21006-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_15a</SourceMethod>
      <ProfileID>3</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>30</ProductCategoryID>
      <Product>L180E</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1140</ErrorNumber>
      <ReportCode>10140</ReportCode>
      <DateAndTime>2024-12-25T09:13:59.6233360-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_15a (1140) Apci.dll *********, ERR: 31 in FN: 15. Wrong answer from ECU, MID:128, MSW:20762948, Parameter:FK
- VCAPCI: mAPCICom.subAPCI_15a (-)
- VCAPCI: cParameter.Refresh (-)
- VOTstCal: cEMSTests.CompressionTestCrank (-)
- VOTstCal: cEMSTests.CompressionTestStart (-)
- VOTstCal: cEMSTests.EMSTestsExecute (-) Number of cylinders: 6.
- VOTstCal: cTstCal.fnMakeEMSCall (-) Could not make call to engine ECU.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>L180E</ChassisSeries>
      <ChassisNumber xsi:nil="false">9150</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>21006-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_15a</SourceMethod>
      <ProfileID>3</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>30</ProductCategoryID>
      <Product>L180E</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1140</ErrorNumber>
      <ReportCode>10140</ReportCode>
      <DateAndTime>2024-12-25T09:18:03.4446620-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_15a (1140) Apci.dll *********, ERR: 31 in FN: 15. Wrong answer from ECU, MID:128, MSW:20762948, Parameter:FK
- VCAPCI: mAPCICom.subAPCI_15a (-)
- VCAPCI: cParameter.Refresh (-)
- VOTstCal: cEMSTests.CompressionTestCrank (-)
- VOTstCal: cEMSTests.CompressionTestStart (-)
- VOTstCal: cEMSTests.EMSTestsExecute (-) Number of cylinders: 6.
- VOTstCal: cTstCal.fnMakeEMSCall (-) Could not make call to engine ECU.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>L180E</ChassisSeries>
      <ChassisNumber xsi:nil="false">9150</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>21006-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_15a</SourceMethod>
      <ProfileID>3</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>30</ProductCategoryID>
      <Product>L180E</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1140</ErrorNumber>
      <ReportCode>10140</ReportCode>
      <DateAndTime>2024-12-25T09:24:18.0393636-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_15a (1140) Apci.dll *********, ERR: 31 in FN: 15. Wrong answer from ECU, MID:128, MSW:20762948, Parameter:FK
- VCAPCI: mAPCICom.subAPCI_15a (-)
- VCAPCI: cParameter.Refresh (-)
- VOTstCal: cEMSTests.CompressionTestCrank (-)
- VOTstCal: cEMSTests.CompressionTestStart (-)
- VOTstCal: cEMSTests.EMSTestsExecute (-) Number of cylinders: 6.
- VOTstCal: cTstCal.fnMakeEMSCall (-) Could not make call to engine ECU.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>L180E</ChassisSeries>
      <ChassisNumber xsi:nil="false">9150</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>21006-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_15a</SourceMethod>
      <ProfileID>3</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>30</ProductCategoryID>
      <Product>L180E</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1140</ErrorNumber>
      <ReportCode>10140</ReportCode>
      <DateAndTime>2024-12-25T09:32:13.5947970-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_15a (1140) Apci.dll *********, ERR: 31 in FN: 15. Wrong answer from ECU, MID:128, MSW:20762948, Parameter:FK
- VCAPCI: mAPCICom.subAPCI_15a (-)
- VCAPCI: cParameter.Refresh (-)
- VOTstCal: cEMSTests.CompressionTestCrank (-)
- VOTstCal: cEMSTests.CompressionTestStart (-)
- VOTstCal: cEMSTests.EMSTestsExecute (-) Number of cylinders: 6.
- VOTstCal: cTstCal.fnMakeEMSCall (-) Could not make call to engine ECU.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>L180E</ChassisSeries>
      <ChassisNumber xsi:nil="false">9150</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>21006-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_15a</SourceMethod>
      <ProfileID>3</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>30</ProductCategoryID>
      <Product>L180E</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1103</ErrorNumber>
      <ReportCode>10103</ReportCode>
      <DateAndTime>2025-01-04T05:35:06.8793994-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_19 (1103) Apci.dll *********, ERR: 1 in FN: 19. No DSR. The interface adapter has no voltage, is not connected or is broken., MID:128, PID:190, Acc. method:0
- VCAPCI: mAPCICom.subAPCI_19 (-)
- VCAPCI: cPID.Refresh (-)
- VCAPCI: cArchitecture.MakePIDCall (-) Could not make PID call. PID: 190.
- VOTstCal: cArchitecture.MakeCall (-) Could not make call. MID: 128, MID type: 0, APCI type: 2, Command type: 0.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>L180E</ChassisSeries>
      <ChassisNumber xsi:nil="false">5736</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>28407-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_19</SourceMethod>
      <ProfileID>3</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>30</ProductCategoryID>
      <Product>L180E</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>0</ErrorNumber>
      <ReportCode>1000</ReportCode>
      <DateAndTime>2025-01-25T07:23:39.6014784-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>The request channel timed out while waiting for a reply after 00:01:00. Increase the timeout value passed to the call to Request or increase the SendTimeout value on the Binding. The time allotted to this operation may have been a portion of a longer timeout.

Server stack trace: 
   at System.ServiceModel.Channels.RequestChannel.Request(Message message, TimeSpan timeout)
   at System.ServiceModel.Dispatcher.RequestChannelBinder.Request(Message message, TimeSpan timeout)
   at System.ServiceModel.Channels.ServiceChannel.Call(String action, Boolean oneway, ProxyOperationRuntime operation, Object[] ins, Object[] outs, TimeSpan timeout)
   at System.ServiceModel.Channels.ServiceChannelProxy.InvokeService(IMethodCallMessage methodCall, ProxyOperationRuntime operation)
   at System.ServiceModel.Channels.ServiceChannelProxy.Invoke(IMessage message)

Exception rethrown at [0]: 
   at System.Runtime.Remoting.Proxies.RealProxy.HandleReturnMessage(IMessage reqMsg, IMessage retMsg)
   at System.Runtime.Remoting.Proxies.RealProxy.PrivateInvoke(MessageData&amp; msgData, Int32 type)
   at VolvoIt.Waf.ProductService.Services.IProductService.ExecuteSystemRequest(SystemRequest systemRequest)
   at Volvo.VCADSPro.VCServices.CurrentVehicle.ExecuteLockTask()
   at Volvo.VCADSPro.VCServices.CurrentVehicle.LockCommunication()
   at Volvo.VCADSPro.VCUI.Proxies.LockCommunicationProxy.LockCommunication()
   at Volvo.VCADSPro.VCUI.Domain.Operation.View(Panel panel, SessionSettings sessionSettings, IdentificationInfo identificationInfo, ConnectionInfo connectionInfo, User user, DeviceTypeEnum deviceType)
   at Volvo.VCADSPro.VCUI.Managers.SessionManager.ShowOperation(Panel operationPanel)
   at Volvo.VCADSPro.VCUI.Managers.EventManager.ViewOperation(ViewOperationParameter viewOperationParameter, Panel operationPanel)
   at Volvo.VCADSPro.VCUI.Views.OperationForm.OnLoad(EventArgs e)
   at System.Windows.Forms.Form.OnCreateControl()
   at System.Windows.Forms.Control.CreateControl(Boolean fIgnoreVisible)
   at System.Windows.Forms.Control.CreateControl()
   at System.Windows.Forms.Control.WmShowWindow(Message&amp; m)
   at System.Windows.Forms.Control.WndProc(Message&amp; m)
   at System.Windows.Forms.ScrollableControl.WndProc(Message&amp; m)
   at System.Windows.Forms.Form.WmShowWindow(Message&amp; m)
   at System.Windows.Forms.Form.WndProc(Message&amp; m)
   at System.Windows.Forms.Control.ControlNativeWindow.OnMessage(Message&amp; m)
   at System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message&amp; m)
   at System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, Int32 msg, IntPtr wparam, IntPtr lparam)</Details>
      <ChassisSeries>A</ChassisSeries>
      <ChassisNumber xsi:nil="false">751005</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>17034-3</OperationNumber6>
      <Source>mscorlib</Source>
      <SourceMethod>1:00. Increase the timeout value passed to the call to Request or increase the SendTimeout value on the Binding. The time allotted to this operation may have been a portion of a longer timeout.

Server stack trace: 
   at System.ServiceModel.Channels.RequestChannel.Reques</SourceMethod>
      <ProfileID>0</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>0</ProductCategoryID>
      <Product>FH</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1103</ErrorNumber>
      <ReportCode>10103</ReportCode>
      <DateAndTime>2025-01-25T08:48:54.9799767-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_21 (1103) Apci.dll *********, ERR: 1 in FN: 21. No DSR. The interface adapter has no voltage, is not connected or is broken., MID:144, PPID:71
- VCAPCI: mAPCICom.subAPCI_21 (-)
- VCAPCI: cPPID.Refresh (-)
- VCAPCI: cArchitecture.fnMakePPIDCall (-) Could not make PPID call. PPID: 71.
- VOTstCal: cArchitecture.MakeCall (-) Could not make call. MID: 144, MID type: 0, APCI type: 3, Command type: 0.
- VOTstCal: cTstCal.subPerformCall (-) Could not make call for pinout or Read Status GUI.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>0</ChassisSeries>
      <ChassisNumber xsi:nil="false">898609</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>25336-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_21</SourceMethod>
      <ProfileID>0</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>0</ProductCategoryID>
      <Product>FH</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>0</ErrorNumber>
      <ReportCode>1000</ReportCode>
      <DateAndTime>2025-01-26T05:19:02.4457302-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>VCAPCI
- VCAPCI: mAPCICom.subAPCI_1 (1121) Apci.dll *********, ERR: 17 in FN: 1. Selected 88890020 is used by another user.
- VCAPCI: mAPCICom.subAPCI_1 (-)
- VCAPCI: cAPCI.OpenCommunication (-)
- VCConn: cConnectionApci.InitAPCI (-)
- VCConn: cConnectionAPCI.ReadSIParameters (-)
   at Volvo.VCADSPro.VCUI.Proxies.UiServiceProxy.ThrowUiServiceException(Error error)
   at Volvo.VCADSPro.VCUI.Proxies.UiServiceProxy.GetViewOperationData(String operationId, Boolean simulated)
   at Volvo.VCADSPro.VCUI.Managers.SessionManager.Initialize(ViewOperationParameter viewOperationParameterParam)
   at Volvo.VCADSPro.VCUI.Managers.EventManager.ViewOperation(ViewOperationParameter viewOperationParameter, Panel operationPanel)
   at Volvo.VCADSPro.VCUI.Views.OperationForm.Reinitialize(IntPtr windowHandle, ViewOperationParameter viewOperationParameterParam)</Details>
      <ChassisSeries>B</ChassisSeries>
      <ChassisNumber xsi:nil="false">432958</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>20005-3</OperationNumber6>
      <Source>VCUI</Source>
      <SourceMethod>mAPCICom.subAPCI_1</SourceMethod>
      <ProfileID>0</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>0</ProductCategoryID>
      <Product>FH</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1140</ErrorNumber>
      <ReportCode>10140</ReportCode>
      <DateAndTime>2025-01-26T12:40:04.1556834-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_15a (1140) Apci.dll *********, ERR: 31 in FN: 15. Wrong answer from ECU, MID:128, MSW:22991818, Parameter:FK
- VCAPCI: mAPCICom.subAPCI_15a (-)
- VCAPCI: cParameter.Refresh (-)
- VOTstCal: cEMSTests.CompressionTestCrank (-)
- VOTstCal: cEMSTests.CompressionTestStart (-)
- VOTstCal: cEMSTests.EMSTestsExecute (-) Number of cylinders: 6.
- VOTstCal: cTstCal.fnMakeEMSCall (-) Could not make call to engine ECU.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>B</ChassisSeries>
      <ChassisNumber xsi:nil="false">592648</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>21006-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_15a</SourceMethod>
      <ProfileID>0</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>0</ProductCategoryID>
      <Product>FH</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1117</ErrorNumber>
      <ReportCode>0</ReportCode>
      <DateAndTime>2025-01-26T23:57:35.4941363-08:00</DateAndTime>
      <VCADSProVersion>Toolbox Basic Package 4.0.0</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCOEPI: modOEPICom.subOEPI_19 (1117) , MID:128, PID:190, Acc. method:0
- VCOEPI: modOEPICom.subOEPI_19 (-)
- VCOEPI: cPID.Refresh (-)
- PTFunc: PTDELUI.fnLUIGetEngSpeed (-) Could not get engine speed.
- PTFunc: PTGENTST.fnGetEngSpeed (-) Could not get engine speed.
- PTFunc: PTCOMP.subStartStopComp (-) Could not start or stop the compression test.
- PTFunc: FuncForm.subStartSTopTest (-)
- PTFunc: FuncForm.CentralErrorHandler (-)</Details>
      <ChassisSeries>
      </ChassisSeries>
      <ChassisNumber xsi:nil="true" />
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>
      </OperationNumber6>
      <Source>VCOEPI</Source>
      <SourceMethod>modOEPICom.subOEPI_19</SourceMethod>
      <ProfileID>
      </ProfileID>
      <ProductGenerationID>
      </ProductGenerationID>
      <ProductCategoryID>
      </ProductCategoryID>
      <Product>
      </Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1104</ErrorNumber>
      <ReportCode>0</ReportCode>
      <DateAndTime>2025-01-26T23:58:09.0124935-08:00</DateAndTime>
      <VCADSProVersion>Toolbox Basic Package 4.0.0</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCOEPI: modOEPICom.subOEPI_19 (1104) , MID:128, PID:190, Acc. method:0
- VCOEPI: modOEPICom.subOEPI_19 (-)
- VCOEPI: cPID.Refresh (-)
- PTFunc: PTDELUI.fnLUIGetEngSpeed (-) Could not get engine speed.
- PTFunc: PTGENTST.fnGetEngSpeed (-) Could not get engine speed.
- PTFunc: PTCOMP.subStartStopComp (-) Could not start or stop the compression test.
- PTFunc: FuncForm.subStartSTopTest (-)
- PTFunc: FuncForm.CentralErrorHandler (-)</Details>
      <ChassisSeries>
      </ChassisSeries>
      <ChassisNumber xsi:nil="true" />
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>
      </OperationNumber6>
      <Source>VCOEPI</Source>
      <SourceMethod>modOEPICom.subOEPI_19</SourceMethod>
      <ProfileID>
      </ProfileID>
      <ProductGenerationID>
      </ProductGenerationID>
      <ProductCategoryID>
      </ProductCategoryID>
      <Product>
      </Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1117</ErrorNumber>
      <ReportCode>0</ReportCode>
      <DateAndTime>2025-01-26T23:59:36.8606867-08:00</DateAndTime>
      <VCADSProVersion>Toolbox Basic Package 4.0.0</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCOEPI: modOEPICom.subOEPI_19 (1117) , MID:128, PID:190, Acc. method:0
- VCOEPI: modOEPICom.subOEPI_19 (-)
- VCOEPI: cPID.Refresh (-)
- PTFunc: PTDELUI.fnLUIGetEngSpeed (-) Could not get engine speed.
- PTFunc: PTGENTST.fnGetEngSpeed (-) Could not get engine speed.
- PTFunc: PTCOMP.subStartStopComp (-) Could not start or stop the compression test.
- PTFunc: FuncForm.subStartSTopTest (-)
- PTFunc: FuncForm.CentralErrorHandler (-)</Details>
      <ChassisSeries>
      </ChassisSeries>
      <ChassisNumber xsi:nil="true" />
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>
      </OperationNumber6>
      <Source>VCOEPI</Source>
      <SourceMethod>modOEPICom.subOEPI_19</SourceMethod>
      <ProfileID>
      </ProfileID>
      <ProductGenerationID>
      </ProductGenerationID>
      <ProductCategoryID>
      </ProductCategoryID>
      <Product>
      </Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1117</ErrorNumber>
      <ReportCode>0</ReportCode>
      <DateAndTime>2025-01-27T00:00:08.5260462-08:00</DateAndTime>
      <VCADSProVersion>Toolbox Basic Package 4.0.0</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCOEPI: modOEPICom.subOEPI_19 (1117) , MID:128, PID:190, Acc. method:0
- VCOEPI: modOEPICom.subOEPI_19 (-)
- VCOEPI: cPID.Refresh (-)
- PTFunc: PTDELUI.fnLUIGetEngSpeed (-) Could not get engine speed.
- PTFunc: PTGENTST.fnGetEngSpeed (-) Could not get engine speed.
- PTFunc: PTCOMP.subStartStopComp (-) Could not start or stop the compression test.
- PTFunc: FuncForm.subStartSTopTest (-)
- PTFunc: FuncForm.CentralErrorHandler (-)</Details>
      <ChassisSeries>
      </ChassisSeries>
      <ChassisNumber xsi:nil="true" />
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>
      </OperationNumber6>
      <Source>VCOEPI</Source>
      <SourceMethod>modOEPICom.subOEPI_19</SourceMethod>
      <ProfileID>
      </ProfileID>
      <ProductGenerationID>
      </ProductGenerationID>
      <ProductCategoryID>
      </ProductCategoryID>
      <Product>
      </Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1117</ErrorNumber>
      <ReportCode>0</ReportCode>
      <DateAndTime>2025-01-27T00:00:47.0585919-08:00</DateAndTime>
      <VCADSProVersion>Toolbox Basic Package 4.0.0</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCOEPI: modOEPICom.subOEPI_19 (1117) , MID:128, PID:190, Acc. method:0
- VCOEPI: modOEPICom.subOEPI_19 (-)
- VCOEPI: cPID.Refresh (-)
- PTFunc: PTDELUI.fnLUIGetEngSpeed (-) Could not get engine speed.
- PTFunc: PTGENTST.fnGetEngSpeed (-) Could not get engine speed.
- PTFunc: PTCOMP.subStartStopComp (-) Could not start or stop the compression test.
- PTFunc: FuncForm.subStartSTopTest (-)
- PTFunc: FuncForm.CentralErrorHandler (-)</Details>
      <ChassisSeries>
      </ChassisSeries>
      <ChassisNumber xsi:nil="true" />
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>
      </OperationNumber6>
      <Source>VCOEPI</Source>
      <SourceMethod>modOEPICom.subOEPI_19</SourceMethod>
      <ProfileID>
      </ProfileID>
      <ProductGenerationID>
      </ProductGenerationID>
      <ProductCategoryID>
      </ProductCategoryID>
      <Product>
      </Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1000</ErrorNumber>
      <ReportCode>1000</ReportCode>
      <DateAndTime>2025-02-02T11:39:55.7182040-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VOV98Par: frmParam.grParameters_ValidateEdit (13) Conversion from string "83
83" to type 'Double' is not valid.
- VOV98Par: frmParam.grParameters_ValidateEdit (1000)</Details>
      <ChassisSeries>B</ChassisSeries>
      <ChassisNumber xsi:nil="false">329526</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>17030-3</OperationNumber6>
      <Source>VOV98Par</Source>
      <SourceMethod>frmParam.grParameters_ValidateEdit</SourceMethod>
      <ProfileID>0</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>0</ProductCategoryID>
      <Product>FH12</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1000</ErrorNumber>
      <ReportCode>1000</ReportCode>
      <DateAndTime>2025-02-02T11:40:03.0826563-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VOV98Par: frmParam.grParameters_ValidateEdit (13) Conversion from string "83
83" to type 'Double' is not valid.
- VOV98Par: frmParam.grParameters_ValidateEdit (1000)</Details>
      <ChassisSeries>B</ChassisSeries>
      <ChassisNumber xsi:nil="false">329526</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>17030-3</OperationNumber6>
      <Source>VOV98Par</Source>
      <SourceMethod>frmParam.grParameters_ValidateEdit</SourceMethod>
      <ProfileID>0</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>0</ProductCategoryID>
      <Product>FH12</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1000</ErrorNumber>
      <ReportCode>1000</ReportCode>
      <DateAndTime>2025-02-02T11:40:07.5478204-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VOV98Par: frmParam.grParameters_ValidateEdit (13) Conversion from string "83
83" to type 'Double' is not valid.
- VOV98Par: frmParam.grParameters_ValidateEdit (1000)</Details>
      <ChassisSeries>B</ChassisSeries>
      <ChassisNumber xsi:nil="false">329526</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>17030-3</OperationNumber6>
      <Source>VOV98Par</Source>
      <SourceMethod>frmParam.grParameters_ValidateEdit</SourceMethod>
      <ProfileID>0</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>0</ProductCategoryID>
      <Product>FH12</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1000</ErrorNumber>
      <ReportCode>1000</ReportCode>
      <DateAndTime>2025-02-02T11:40:15.1866503-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VOV98Par: frmParam.grParameters_ValidateEdit (13) Conversion from string "83
83" to type 'Double' is not valid.
- VOV98Par: frmParam.grParameters_ValidateEdit (1000)</Details>
      <ChassisSeries>B</ChassisSeries>
      <ChassisNumber xsi:nil="false">329526</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>17030-3</OperationNumber6>
      <Source>VOV98Par</Source>
      <SourceMethod>frmParam.grParameters_ValidateEdit</SourceMethod>
      <ProfileID>0</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>0</ProductCategoryID>
      <Product>FH12</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1000</ErrorNumber>
      <ReportCode>1000</ReportCode>
      <DateAndTime>2025-02-02T11:40:40.8369265-08:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VOV98Par: frmParam.grParameters_ValidateEdit (13) Conversion from string "83
85" to type 'Double' is not valid.
- VOV98Par: frmParam.grParameters_ValidateEdit (1000)</Details>
      <ChassisSeries>B</ChassisSeries>
      <ChassisNumber xsi:nil="false">329526</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>17030-3</OperationNumber6>
      <Source>VOV98Par</Source>
      <SourceMethod>frmParam.grParameters_ValidateEdit</SourceMethod>
      <ProfileID>0</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>0</ProductCategoryID>
      <Product>FH12</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1103</ErrorNumber>
      <ReportCode>10103</ReportCode>
      <DateAndTime>2025-03-10T13:56:44.6525462-07:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_19 (1103) Apci.dll *********, ERR: 1 in FN: 19. No DSR. The interface adapter has no voltage, is not connected or is broken., MID:187, PID:127, Acc. method:0
- VCAPCI: mAPCICom.subAPCI_19 (-)
- VCAPCI: cPID.Refresh (-)
- VCAPCI: cArchitecture.MakePIDCall (-) Could not make PID call. PID: 127.
- VOTstCal: cArchitecture.MakeCall (-) Could not make call. MID: 187, MID type: 0, APCI type: 2, Command type: 0.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>L180E</ChassisSeries>
      <ChassisNumber xsi:nil="false">9150</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>40901-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_19</SourceMethod>
      <ProfileID>3</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>30</ProductCategoryID>
      <Product>L180E</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1140</ErrorNumber>
      <ReportCode>10140</ReportCode>
      <DateAndTime>2025-03-10T13:58:39.9869306-07:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_15a (1140) Apci.dll *********, ERR: 31 in FN: 15. Wrong answer from ECU, MID:128, MSW:20762948, Parameter:FK
- VCAPCI: mAPCICom.subAPCI_15a (-)
- VCAPCI: cParameter.Refresh (-)
- VOTstCal: cEMSTests.CompressionTestCrank (-)
- VOTstCal: cEMSTests.CompressionTestStart (-)
- VOTstCal: cEMSTests.EMSTestsExecute (-) Number of cylinders: 6.
- VOTstCal: cTstCal.fnMakeEMSCall (-) Could not make call to engine ECU.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>L180E</ChassisSeries>
      <ChassisNumber xsi:nil="false">9150</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>21006-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_15a</SourceMethod>
      <ProfileID>3</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>30</ProductCategoryID>
      <Product>L180E</Product>
    </VCADSProError>
    <VCADSProError xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://volvo.com/schemas/VCADSPro/v1">
      <ErrorNumber>1140</ErrorNumber>
      <ReportCode>10140</ReportCode>
      <DateAndTime>2025-03-10T13:58:59.6825584-07:00</DateAndTime>
      <VCADSProVersion>3.00.00 TT</VCADSProVersion>
      <APCIDbVersion xsi:nil="true" />
      <ClientID>447886</ClientID>
      <Details>- VCAPCI: mAPCICom.subAPCI_15a (1140) Apci.dll *********, ERR: 31 in FN: 15. Wrong answer from ECU, MID:128, MSW:20762948, Parameter:FK
- VCAPCI: mAPCICom.subAPCI_15a (-)
- VCAPCI: cParameter.Refresh (-)
- VOTstCal: cEMSTests.CompressionTestCrank (-)
- VOTstCal: cEMSTests.CompressionTestStart (-)
- VOTstCal: cEMSTests.EMSTestsExecute (-) Number of cylinders: 6.
- VOTstCal: cTstCal.fnMakeEMSCall (-) Could not make call to engine ECU.
- VOTstCal: cTstCal.PerformCall (-) Could not perform call.</Details>
      <ChassisSeries>L180E</ChassisSeries>
      <ChassisNumber xsi:nil="false">9150</ChassisNumber>
      <OperationNumber10>
      </OperationNumber10>
      <OperationNumber6>21006-3</OperationNumber6>
      <Source>VCAPCI</Source>
      <SourceMethod>mAPCICom.subAPCI_15a</SourceMethod>
      <ProfileID>3</ProfileID>
      <ProductGenerationID>2</ProductGenerationID>
      <ProductCategoryID>30</ProductCategoryID>
      <Product>L180E</Product>
    </VCADSProError>
  </Content>
</ApplicationReport>