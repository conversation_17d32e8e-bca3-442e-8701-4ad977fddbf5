using System;
using System.IO;
using System.IO.Pipes;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Console;
using Microsoft.Extensions.DependencyInjection;

namespace VolvoFlashWR.VocomBridge
{
    /// <summary>
    /// Bridge executable that runs as x86 process to communicate with x86 APCI libraries
    /// This solves the architecture mismatch issue between x64 main application and x86 libraries
    /// </summary>
    class Program
    {
        private static ILogger<Program>? _logger;
        private static string? _pipeName;
        private static NamedPipeClientStream? _pipeClient;
        private static VocomBridgeService? _vocomService;

        static async Task<int> Main(string[] args)
        {
            try
            {
                // Setup assembly resolution for the bridge process
                AppDomain.CurrentDomain.AssemblyResolve += OnAssemblyResolve;

                // Setup logging
                var services = new ServiceCollection();
                services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Information));
                var serviceProvider = services.BuildServiceProvider();
                _logger = serviceProvider.GetRequiredService<ILogger<Program>>();

                _logger.LogInformation("=== Vocom Architecture Bridge Starting ===");
                _logger.LogInformation($"Process Architecture: {(Environment.Is64BitProcess ? "x64" : "x86")}");
                _logger.LogInformation($"OS Architecture: {(Environment.Is64BitOperatingSystem ? "x64" : "x86")}");
                _logger.LogInformation($"Bridge executable directory: {AppDomain.CurrentDomain.BaseDirectory}");

                // Get pipe name from command line arguments
                if (args.Length == 0)
                {
                    _logger.LogError("No pipe name provided. Usage: VolvoFlashWR.VocomBridge.exe <pipeName>");
                    return 1;
                }

                _pipeName = args[0];
                _logger.LogInformation($"Connecting to pipe: {_pipeName}");

                // Initialize Vocom service
                _vocomService = new VocomBridgeService(_logger);
                await _vocomService.InitializeAsync();

                // Connect to named pipe
                _pipeClient = new NamedPipeClientStream(".", _pipeName, PipeDirection.InOut, PipeOptions.Asynchronous);
                await _pipeClient.ConnectAsync(5000); // 5 second timeout
                _logger.LogInformation("Connected to main process via named pipe");

                // Start message processing loop
                await ProcessMessagesAsync();

                return 0;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Bridge process failed: {ex.Message}");
                _logger?.LogError($"Stack trace: {ex.StackTrace}");
                return 1;
            }
            finally
            {
                _pipeClient?.Dispose();
                _vocomService?.Dispose();
                _logger?.LogInformation("=== Vocom Architecture Bridge Stopped ===");
            }
        }

        private static async Task ProcessMessagesAsync()
        {
            var buffer = new byte[4096];
            
            while (_pipeClient?.IsConnected == true)
            {
                try
                {
                    // Read command from main process
                    var bytesRead = await _pipeClient.ReadAsync(buffer, 0, buffer.Length);
                    if (bytesRead == 0) break;

                    var commandJson = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                    _logger?.LogInformation($"=== BRIDGE DEBUG: Received command JSON: {commandJson} ===");
                    var command = JsonSerializer.Deserialize<BridgeCommand>(commandJson);
                    _logger?.LogInformation($"=== BRIDGE DEBUG: Deserialized command type: {command?.Type} ===");

                    if (command == null)
                    {
                        _logger?.LogWarning("Received null command");
                        continue;
                    }

                    _logger?.LogDebug($"Processing command: {command.Type}");

                    // Process command and get response
                    var response = await ProcessCommandAsync(command);

                    // Send response back to main process
                    var responseJson = JsonSerializer.Serialize(response);
                    var responseBytes = Encoding.UTF8.GetBytes(responseJson);
                    await _pipeClient.WriteAsync(responseBytes, 0, responseBytes.Length);
                    await _pipeClient.FlushAsync();
                }
                catch (Exception ex)
                {
                    _logger?.LogError($"Error processing message: {ex.Message}");
                    
                    // Send error response
                    var errorResponse = new BridgeResponse
                    {
                        Success = false,
                        Message = ex.Message
                    };
                    
                    try
                    {
                        var errorJson = JsonSerializer.Serialize(errorResponse);
                        var errorBytes = Encoding.UTF8.GetBytes(errorJson);
                        await _pipeClient.WriteAsync(errorBytes, 0, errorBytes.Length);
                        await _pipeClient.FlushAsync();
                    }
                    catch
                    {
                        // If we can't send error response, break the loop
                        break;
                    }
                }
            }
        }

        private static async Task<BridgeResponse> ProcessCommandAsync(BridgeCommand command)
        {
            _logger?.LogInformation($"=== BRIDGE DEBUG: ProcessCommandAsync called with command type: {command.Type} ===");
            try
            {
                switch (command.Type)
                {
                    case "Initialize":
                        return await HandleInitializeAsync(command);
                    
                    case "DetectDevices":
                        return await HandleDetectDevicesAsync();
                    
                    case "ConnectDevice":
                        return await HandleConnectDeviceAsync(command);
                    
                    case "DisconnectDevice":
                        return await HandleDisconnectDeviceAsync();
                    
                    case "SendData":
                        return await HandleSendDataAsync(command);
                    
                    default:
                        return new BridgeResponse
                        {
                            Success = false,
                            Message = $"Unknown command type: {command.Type}"
                        };
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error processing command {command.Type}: {ex.Message}");
                return new BridgeResponse
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        private static async Task<BridgeResponse> HandleInitializeAsync(BridgeCommand command)
        {
            _logger?.LogInformation("Initializing bridge service with x86 APCI libraries");
            
            // Initialize the Vocom service with the provided libraries path
            var success = await _vocomService!.InitializeAsync();
            
            return new BridgeResponse
            {
                Success = success,
                Message = success ? "Bridge service initialized successfully" : "Bridge service initialization failed"
            };
        }

        private static async Task<BridgeResponse> HandleDetectDevicesAsync()
        {
            _logger?.LogInformation("Detecting Vocom devices through x86 APCI libraries");

            var devices = await _vocomService!.DetectDevicesAsync();
            _logger?.LogInformation($"Bridge service returned {devices.Count} devices");

            // Log each device for debugging
            for (int i = 0; i < devices.Count; i++)
            {
                var device = devices[i];
                _logger?.LogInformation($"Device {i}: Id={device.Id}, Name={device.Name}, ConnectionType={device.ConnectionType}, ConnectionStatus={device.ConnectionStatus}");
            }

            var jsonData = JsonSerializer.Serialize(devices);
            _logger?.LogInformation($"=== BRIDGE DEBUG: Serialized JSON data: {jsonData} ===");
            _logger?.LogInformation($"=== BRIDGE DEBUG: Device count: {devices.Count} ===");
            if (devices.Count > 0)
            {
                var firstDevice = devices[0];
                _logger?.LogInformation($"=== BRIDGE DEBUG: First device type: {firstDevice.GetType().FullName} ===");
                _logger?.LogInformation($"=== BRIDGE DEBUG: First device ConnectionType type: {firstDevice.ConnectionType?.GetType().FullName} ===");
                _logger?.LogInformation($"=== BRIDGE DEBUG: First device ConnectionType value: '{firstDevice.ConnectionType}' ===");
            }

            return new BridgeResponse
            {
                Success = true,
                Data = jsonData,
                Message = $"Found {devices.Count} devices"
            };
        }

        private static async Task<BridgeResponse> HandleConnectDeviceAsync(BridgeCommand command)
        {
            if (string.IsNullOrEmpty(command.Data))
            {
                return new BridgeResponse
                {
                    Success = false,
                    Message = "No device ID provided"
                };
            }

            var connectData = JsonSerializer.Deserialize<dynamic>(command.Data);
            var deviceId = connectData?.GetProperty("DeviceId").GetString();

            if (string.IsNullOrEmpty(deviceId))
            {
                return new BridgeResponse
                {
                    Success = false,
                    Message = "Invalid device ID"
                };
            }

            _logger?.LogInformation($"Connecting to device: {deviceId}");
            
            var success = await _vocomService!.ConnectToDeviceAsync(deviceId);
            
            return new BridgeResponse
            {
                Success = success,
                Message = success ? $"Connected to device {deviceId}" : $"Failed to connect to device {deviceId}"
            };
        }

        private static async Task<BridgeResponse> HandleDisconnectDeviceAsync()
        {
            _logger?.LogInformation("Disconnecting from current device");
            
            await _vocomService!.DisconnectAsync();
            
            return new BridgeResponse
            {
                Success = true,
                Message = "Disconnected from device"
            };
        }

        private static async Task<BridgeResponse> HandleSendDataAsync(BridgeCommand command)
        {
            if (string.IsNullOrEmpty(command.Data))
            {
                return new BridgeResponse
                {
                    Success = false,
                    Message = "No data provided"
                };
            }

            // Deserialize the data to send
            var sendData = JsonSerializer.Deserialize<byte[]>(command.Data);
            if (sendData == null)
            {
                return new BridgeResponse
                {
                    Success = false,
                    Message = "Invalid data format"
                };
            }

            _logger?.LogDebug($"Sending {sendData.Length} bytes through bridge");
            
            var responseData = await _vocomService!.SendAndReceiveDataAsync(sendData);
            
            return new BridgeResponse
            {
                Success = true,
                Data = JsonSerializer.Serialize(responseData),
                Message = $"Sent {sendData.Length} bytes, received {responseData.Length} bytes"
            };
        }

        /// <summary>
        /// Assembly resolver for the bridge process to find assemblies in the bridge directory
        /// </summary>
        private static Assembly? OnAssemblyResolve(object? sender, ResolveEventArgs args)
        {
            try
            {
                var assemblyName = new AssemblyName(args.Name);
                var fileName = assemblyName.Name + ".dll";
                var bridgeDirectory = AppDomain.CurrentDomain.BaseDirectory;
                var assemblyPath = Path.Combine(bridgeDirectory, fileName);

                _logger?.LogInformation($"Assembly resolve request: {args.Name}");
                _logger?.LogInformation($"Bridge directory: {bridgeDirectory}");
                _logger?.LogInformation($"Looking for: {assemblyPath}");

                if (File.Exists(assemblyPath))
                {
                    _logger?.LogInformation($"Found assembly, loading from: {assemblyPath}");
                    var assembly = Assembly.LoadFrom(assemblyPath);
                    _logger?.LogInformation($"Successfully loaded assembly: {assembly.FullName}");
                    return assembly;
                }

                // Try alternative locations
                var parentDirectory = Directory.GetParent(bridgeDirectory)?.FullName;
                if (!string.IsNullOrEmpty(parentDirectory))
                {
                    var parentAssemblyPath = Path.Combine(parentDirectory, fileName);
                    _logger?.LogInformation($"Trying parent directory: {parentAssemblyPath}");

                    if (File.Exists(parentAssemblyPath))
                    {
                        _logger?.LogInformation($"Found assembly in parent directory, loading from: {parentAssemblyPath}");
                        var assembly = Assembly.LoadFrom(parentAssemblyPath);
                        _logger?.LogInformation($"Successfully loaded assembly from parent: {assembly.FullName}");
                        return assembly;
                    }
                }

                _logger?.LogWarning($"Assembly not found in any location: {fileName}");
                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error resolving assembly {args.Name}: {ex.Message}");
                _logger?.LogError($"Stack trace: {ex.StackTrace}");
                return null;
            }
        }
    }

    /// <summary>
    /// Command sent to the bridge process
    /// </summary>
    public class BridgeCommand
    {
        public string Type { get; set; } = string.Empty;
        public string? Data { get; set; }
    }

    /// <summary>
    /// Response from the bridge process
    /// </summary>
    public class BridgeResponse
    {
        public bool Success { get; set; }
        public string? Data { get; set; }
        public string? Message { get; set; }
    }
}
