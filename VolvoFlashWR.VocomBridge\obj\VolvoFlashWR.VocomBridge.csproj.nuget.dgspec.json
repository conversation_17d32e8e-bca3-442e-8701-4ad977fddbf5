{"format": 1, "restore": {"D:\\Development\\S.A.H.VolvoFlashWR\\VolvoFlashWR.VocomBridge\\VolvoFlashWR.VocomBridge.csproj": {}}, "projects": {"D:\\Development\\S.A.H.VolvoFlashWR\\VolvoFlashWR.VocomBridge\\VolvoFlashWR.VocomBridge.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Development\\S.A.H.VolvoFlashWR\\VolvoFlashWR.VocomBridge\\VolvoFlashWR.VocomBridge.csproj", "projectName": "VolvoFlashWR.VocomBridge", "projectPath": "D:\\Development\\S.A.H.VolvoFlashWR\\VolvoFlashWR.VocomBridge\\VolvoFlashWR.VocomBridge.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Development\\S.A.H.VolvoFlashWR\\VolvoFlashWR.VocomBridge\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"HidSharp": {"target": "Package", "version": "[2.1.0, )"}, "InTheHand.Net.Bluetooth": {"target": "Package", "version": "[4.1.40, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Win32.Registry": {"target": "Package", "version": "[5.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Diagnostics.Process": {"target": "Package", "version": "[4.3.0, )"}, "System.IO.Pipes": {"target": "Package", "version": "[4.3.0, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.4, )"}, "System.Management": {"target": "Package", "version": "[9.0.5, )"}, "System.Net.NetworkInformation": {"target": "Package", "version": "[4.3.0, )"}, "System.Runtime.InteropServices": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-x86", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x86", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x86", "version": "[8.0.18, 8.0.18]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}}