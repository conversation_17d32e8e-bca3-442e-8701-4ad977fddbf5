using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Backup
{
    /// <summary>
    /// Implementation of the backup versioning service
    /// </summary>
    public class BackupVersioningService
    {
        #region Private Fields

        private readonly ILoggingService _logger;
        private readonly IBackupService _backupService;

        #endregion

        #region Events

        /// <summary>
        /// Event triggered when backup versions are merged
        /// </summary>
        public event EventHandler<BackupData> VersionsMerged;

        /// <summary>
        /// Event triggered when an error occurs during versioning operations
        /// </summary>
        public event EventHandler<string> VersioningError;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the BackupVersioningService class
        /// </summary>
        /// <param name="logger">The logging service</param>
        /// <param name="backupService">The backup service</param>
        public BackupVersioningService(ILoggingService logger, IBackupService backupService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));

            // Initialize events to empty handlers to avoid null reference exceptions
            VersionsMerged = (sender, data) => { };
            VersioningError = (sender, message) => { };
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets the version tree for a backup
        /// </summary>
        /// <param name="backupId">The ID of any version of the backup</param>
        /// <returns>The backup version tree</returns>
        public async Task<BackupVersionTree> GetVersionTreeAsync(string backupId)
        {
            try
            {
                _logger?.LogInformation($"Building version tree for backup {backupId}", "BackupVersioningService");

                if (string.IsNullOrEmpty(backupId))
                {
                    _logger?.LogError("Backup ID is null or empty", "BackupVersioningService");
                    VersioningError?.Invoke(this, "Backup ID is null or empty");
                    return null;
                }

                // Get all versions of the backup
                List<BackupData> versions = await _backupService.GetBackupVersionsAsync(backupId);
                if (versions == null || versions.Count == 0)
                {
                    _logger?.LogWarning($"No versions found for backup {backupId}", "BackupVersioningService");
                    return null;
                }

                // Create the version tree
                BackupVersionTree tree = BuildVersionTree(versions);
                _logger?.LogInformation($"Built version tree with {tree.VersionCount} versions for backup {backupId}", "BackupVersioningService");
                return tree;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error building version tree for backup {backupId}", "BackupVersioningService", ex);
                VersioningError?.Invoke(this, $"Error building version tree: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Compares two backup versions
        /// </summary>
        /// <param name="version1Id">The ID of the first version</param>
        /// <param name="version2Id">The ID of the second version</param>
        /// <returns>A dictionary of differences between the versions</returns>
        public async Task<Dictionary<string, (object Version1Value, object Version2Value)>> CompareVersionsAsync(string version1Id, string version2Id)
        {
            try
            {
                _logger?.LogInformation($"Comparing backup versions {version1Id} and {version2Id}", "BackupVersioningService");

                if (string.IsNullOrEmpty(version1Id) || string.IsNullOrEmpty(version2Id))
                {
                    _logger?.LogError("One or both version IDs are null or empty", "BackupVersioningService");
                    VersioningError?.Invoke(this, "One or both version IDs are null or empty");
                    return null;
                }

                // Get the backup versions
                var allBackups = await _backupService.GetAllBackupsAsync();
                var version1 = allBackups.FirstOrDefault(b => b.Id == version1Id);
                var version2 = allBackups.FirstOrDefault(b => b.Id == version2Id);

                if (version1 == null || version2 == null)
                {
                    _logger?.LogError("One or both versions not found", "BackupVersioningService");
                    VersioningError?.Invoke(this, "One or both versions not found");
                    return null;
                }

                // Compare the versions
                var differences = CompareBackups(version1, version2);
                _logger?.LogInformation($"Found {differences.Count} differences between versions {version1Id} and {version2Id}", "BackupVersioningService");
                return differences;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error comparing backup versions {version1Id} and {version2Id}", "BackupVersioningService", ex);
                VersioningError?.Invoke(this, $"Error comparing versions: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Merges two backup versions
        /// </summary>
        /// <param name="sourceVersionId">The ID of the source version</param>
        /// <param name="targetVersionId">The ID of the target version</param>
        /// <param name="mergeOptions">Options for the merge operation</param>
        /// <returns>The merged backup data</returns>
        public async Task<BackupData> MergeVersionsAsync(string sourceVersionId, string targetVersionId, BackupMergeOptions mergeOptions)
        {
            try
            {
                _logger?.LogInformation($"Merging backup versions {sourceVersionId} and {targetVersionId}", "BackupVersioningService");

                if (string.IsNullOrEmpty(sourceVersionId) || string.IsNullOrEmpty(targetVersionId))
                {
                    _logger?.LogError("One or both version IDs are null or empty", "BackupVersioningService");
                    VersioningError?.Invoke(this, "One or both version IDs are null or empty");
                    return null;
                }

                if (mergeOptions == null)
                {
                    mergeOptions = new BackupMergeOptions();
                    _logger?.LogWarning("Merge options not provided, using defaults", "BackupVersioningService");
                }

                // Get the backup versions
                var allBackups = await _backupService.GetAllBackupsAsync();
                var sourceVersion = allBackups.FirstOrDefault(b => b.Id == sourceVersionId);
                var targetVersion = allBackups.FirstOrDefault(b => b.Id == targetVersionId);

                if (sourceVersion == null || targetVersion == null)
                {
                    _logger?.LogError("One or both versions not found", "BackupVersioningService");
                    VersioningError?.Invoke(this, "One or both versions not found");
                    return null;
                }

                // Perform the merge
                var mergedBackup = await _backupService.MergeBackupVersionsAsync(sourceVersionId, targetVersionId, mergeOptions);
                if (mergedBackup != null)
                {
                    _logger?.LogInformation($"Successfully merged versions {sourceVersionId} and {targetVersionId}", "BackupVersioningService");
                    VersionsMerged?.Invoke(this, mergedBackup);
                }
                else
                {
                    _logger?.LogError($"Failed to merge versions {sourceVersionId} and {targetVersionId}", "BackupVersioningService");
                    VersioningError?.Invoke(this, $"Failed to merge versions {sourceVersionId} and {targetVersionId}");
                }

                return mergedBackup;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error merging backup versions {sourceVersionId} and {targetVersionId}", "BackupVersioningService", ex);
                VersioningError?.Invoke(this, $"Error merging versions: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Builds a version tree from a list of backup versions
        /// </summary>
        /// <param name="versions">The list of backup versions</param>
        /// <returns>The backup version tree</returns>
        private BackupVersionTree BuildVersionTree(List<BackupData> versions)
        {
            var tree = new BackupVersionTree();

            if (versions == null || versions.Count == 0)
            {
                return tree;
            }

            // Find the root backup (version 1)
            var rootBackup = versions.FirstOrDefault(v => v.Version == 1 || string.IsNullOrEmpty(v.ParentBackupId));
            if (rootBackup == null)
            {
                rootBackup = versions.OrderBy(v => v.Version).First();
            }

            tree.RootBackup = rootBackup;
            tree.AllVersions = versions.OrderBy(v => v.Version).ToList();
            tree.LatestVersion = versions.OrderByDescending(v => v.Version).First();

            // Build the tree structure
            var nodeMap = new Dictionary<string, BackupVersionNode>();

            // Create nodes for all versions
            foreach (var version in versions)
            {
                var node = new BackupVersionNode
                {
                    Backup = version,
                    Depth = 0
                };
                nodeMap[version.Id] = node;
            }

            // Connect the nodes
            foreach (var version in versions)
            {
                if (!string.IsNullOrEmpty(version.ParentBackupId) && nodeMap.ContainsKey(version.ParentBackupId))
                {
                    var parentNode = nodeMap[version.ParentBackupId];
                    var childNode = nodeMap[version.Id];

                    childNode.Parent = parentNode;
                    parentNode.Children.Add(childNode);
                }
            }

            // Find the root node
            var rootNode = nodeMap[rootBackup.Id];
            tree.VersionNodes.Add(rootNode);

            // Calculate depths
            CalculateNodeDepths(rootNode, 0);

            return tree;
        }

        /// <summary>
        /// Recursively calculates the depth of each node in the tree
        /// </summary>
        /// <param name="node">The current node</param>
        /// <param name="depth">The current depth</param>
        private void CalculateNodeDepths(BackupVersionNode node, int depth)
        {
            node.Depth = depth;
            foreach (var child in node.Children)
            {
                CalculateNodeDepths(child, depth + 1);
            }
        }

        /// <summary>
        /// Compares two backups and returns their differences
        /// </summary>
        /// <param name="backup1">The first backup</param>
        /// <param name="backup2">The second backup</param>
        /// <returns>A dictionary of differences</returns>
        private Dictionary<string, (object Version1Value, object Version2Value)> CompareBackups(BackupData backup1, BackupData backup2)
        {
            var differences = new Dictionary<string, (object, object)>();

            // Compare EEPROM data
            if (!AreByteArraysEqual(backup1.EEPROMData, backup2.EEPROMData))
            {
                differences["EEPROMData"] = (backup1.EEPROMData?.Length, backup2.EEPROMData?.Length);
            }

            // Compare microcontroller code
            if (!AreByteArraysEqual(backup1.MicrocontrollerCode, backup2.MicrocontrollerCode))
            {
                differences["MicrocontrollerCode"] = (backup1.MicrocontrollerCode?.Length, backup2.MicrocontrollerCode?.Length);
            }

            // Compare parameters
            CompareParameters(backup1, backup2, differences);

            // Compare metadata
            if (backup1.Description != backup2.Description)
            {
                differences["Description"] = (backup1.Description, backup2.Description);
            }

            if (backup1.Category != backup2.Category)
            {
                differences["Category"] = (backup1.Category, backup2.Category);
            }

            // Compare tags
            if (!AreTagsEqual(backup1.Tags, backup2.Tags))
            {
                differences["Tags"] = (string.Join(", ", backup1.Tags), string.Join(", ", backup2.Tags));
            }

            return differences;
        }

        /// <summary>
        /// Compares parameters between two backups
        /// </summary>
        /// <param name="backup1">The first backup</param>
        /// <param name="backup2">The second backup</param>
        /// <param name="differences">The dictionary to store differences</param>
        private void CompareParameters(BackupData backup1, BackupData backup2, Dictionary<string, (object, object)> differences)
        {
            // Get all parameter keys from both backups
            var allKeys = new HashSet<string>(backup1.Parameters.Keys.Concat(backup2.Parameters.Keys));

            foreach (var key in allKeys)
            {
                bool backup1HasKey = backup1.Parameters.ContainsKey(key);
                bool backup2HasKey = backup2.Parameters.ContainsKey(key);

                if (!backup1HasKey)
                {
                    differences[$"Parameter:{key}"] = (null, backup2.Parameters[key]);
                }
                else if (!backup2HasKey)
                {
                    differences[$"Parameter:{key}"] = (backup1.Parameters[key], null);
                }
                else if (!Equals(backup1.Parameters[key], backup2.Parameters[key]))
                {
                    differences[$"Parameter:{key}"] = (backup1.Parameters[key], backup2.Parameters[key]);
                }
            }
        }

        /// <summary>
        /// Checks if two byte arrays are equal
        /// </summary>
        /// <param name="array1">The first array</param>
        /// <param name="array2">The second array</param>
        /// <returns>True if the arrays are equal, false otherwise</returns>
        private bool AreByteArraysEqual(byte[] array1, byte[] array2)
        {
            if (array1 == null && array2 == null)
            {
                return true;
            }

            if (array1 == null || array2 == null)
            {
                return false;
            }

            if (array1.Length != array2.Length)
            {
                return false;
            }

            for (int i = 0; i < array1.Length; i++)
            {
                if (array1[i] != array2[i])
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Checks if two tag lists are equal
        /// </summary>
        /// <param name="tags1">The first tag list</param>
        /// <param name="tags2">The second tag list</param>
        /// <returns>True if the tag lists are equal, false otherwise</returns>
        private bool AreTagsEqual(List<string> tags1, List<string> tags2)
        {
            if (tags1 == null && tags2 == null)
            {
                return true;
            }

            if (tags1 == null || tags2 == null)
            {
                return false;
            }

            if (tags1.Count != tags2.Count)
            {
                return false;
            }

            var sortedTags1 = new List<string>(tags1);
            var sortedTags2 = new List<string>(tags2);
            sortedTags1.Sort();
            sortedTags2.Sort();

            for (int i = 0; i < sortedTags1.Count; i++)
            {
                if (sortedTags1[i] != sortedTags2[i])
                {
                    return false;
                }
            }

            return true;
        }

        #endregion
    }
}
