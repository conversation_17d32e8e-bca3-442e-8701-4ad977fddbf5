<?xml version="1.0" encoding="utf-8"?>
<ServiceLogManager xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <PreSetFullLevel>Default</PreSetFullLevel>
  <LogTechToolEnable>false</LogTechToolEnable>
  <LogGradeXEnable>false</LogGradeXEnable>
  <LogWcfTraceLevelAll>Off</LogWcfTraceLevelAll>
  <ComponentLogLevel>Off</ComponentLogLevel>
  <ServiceConfigs>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostController.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.ServiceHostController</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostController.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostController.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.ServiceHost</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostController.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostController.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.Utility</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostController.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.Common</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Common</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.StateProvider</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.Core.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.LoglevelReset</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.InternalLogCollector.UI</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.GX.StartUp</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Settings.DownloadPhonebook</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.TechTool2.LogCollector</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.ReportingService</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.ReportingService.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.Settings.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.Ui.Common</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.Utility</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.RMI.Uservalidation</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.About</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.AdministrateSoftware.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.AntivirusCheck</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ApciDbUpdate</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Authorization</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.BsActionControllers</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.BusinessRules</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.CampaignCheck.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ConcernReport.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.DiagnosticManualLink</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ErrorHandler</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ErrorReport</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.GradeXApplicationHost.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Initializers</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.LegalInformation</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.LoggedProductData.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Login.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ParameterProgramming.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.TechTool.ParameterService</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.PrintEngineLabel.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Printing</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ProductIdentification.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ProductHistory.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ReportHistory.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Settings.AdapterSettings</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Settings.CentralCommunicationSettings</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Settings.Common</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Settings.PersonalSettings</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Settings.ProxySettings</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.SupportInformation</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Ui.Common</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.UpdateInformation</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Utility</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.VersionReport</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>ApciProxy</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>McCommProxy</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>OepiProxy</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCADSPro.Data</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCAPCI</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCConn</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCError</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCFido</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCFlist</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCISAdm</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCJCard</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCMkernel</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCNams</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCNamsComm</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCOpData</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCProfile</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCServices</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCSettng</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCSI</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCSIProducts</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCUserNG</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCUtil</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VOGraphLib</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VOHCDART_SuspCal</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VOInfo</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VOIODisplay</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VOParTemplate</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VOTstCal</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VOVCDAVDA</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VCUI</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.TechTool.EnvironmentLog</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.TechTool.AdapterHandler</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.TechTool.BusinessOrchestrator</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.TechTool.DataAccess</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ProductProfileService</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ProductProfileValidater</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.TechTool.ServiceWrapper</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.Common</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Common</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.StateProvider</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.ServiceHostProcess</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.ServiceHost</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.BrokerServices</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.NetworkServices</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>ProductService</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ProductProfileService</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.InformationLinkService</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.BComService</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.ReportingService</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.LoggedProductData</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>Waf-VcadsPro</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ProductHistory</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ApciDbUpdate</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCUIService</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.Utility</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.BusinessRules</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Utility</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.VehicleCom</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.DiagnosticManualLink</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>ApciProxy</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>McCommProxy</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>OepiProxy</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCADSPro.Data</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCAPCI</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCConn</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCError</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCFido</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCFlist</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCISAdm</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCJCard</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCMkernel</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCNams</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCNamsComm</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCOpData</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCProfile</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCServices</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCSettng</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCSI</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCSIProducts</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCUserNG</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VCUtil</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VOGraphLib</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VOHCDART_SuspCal</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VOInfo</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VOIODisplay</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VOParTemplate</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VOTstCal</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VOVCDAVDA</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.EswManagement</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.TechTool.BusinessOrchestrator</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.TechTool.DataAccess</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ProductProfileValidater</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigFile>
      <Component>VolvoIt.TechTool.ServiceWrapper</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.ServiceHostProcess.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Waf.ReportingService.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.ReportingService.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Waf.ReportingService.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Waf.ReportingService.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.ReportingService</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Waf.ReportingService.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Waf.ReportingService.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.Utility</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Waf.ReportingService.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Waf.ReportingService.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Waf.Utility</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Waf.ReportingService.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Waf.ReportingService.Ui.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.Ui.Common</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Waf.ReportingService.Ui.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.CrashMessage.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.Core.Ui</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.CrashMessage.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
    <ServiceConfig>
      <ConfigFile>VolvoIt.Baf.Core.Ui.CrashMessage.log4net.xml</ConfigFile>
      <Component>VolvoIt.Baf.Utility</Component>
      <ConfigPath>C:\ProgramData\Tech Tool\Configuration\VolvoIt.Baf.Core.Ui.CrashMessage.log4net.xml</ConfigPath>
      <LogLevel>Default</LogLevel>
      <HasChanged>false</HasChanged>
    </ServiceConfig>
  </ServiceConfigs>
  <AppenderConfigs>
    <AppenderConfig>
      <AppenderName>RollingFileFlightRecorderAppender</AppenderName>
      <FileSize>5</FileSize>
      <NumberFiles>5</NumberFiles>
    </AppenderConfig>
    <AppenderConfig>
      <AppenderName>RollingFileDebugAppender</AppenderName>
      <FileSize>10</FileSize>
      <NumberFiles>5</NumberFiles>
    </AppenderConfig>
    <AppenderConfig>
      <AppenderName>StandardLogAppender</AppenderName>
      <FileSize>5</FileSize>
      <NumberFiles>5</NumberFiles>
    </AppenderConfig>
  </AppenderConfigs>
  <WcfLogLevel>
    <ItemList>
      <Name>Critical</Name>
      <IsChecked>false</IsChecked>
    </ItemList>
    <ItemList>
      <Name>Error</Name>
      <IsChecked>false</IsChecked>
    </ItemList>
    <ItemList>
      <Name>Warning</Name>
      <IsChecked>false</IsChecked>
    </ItemList>
    <ItemList>
      <Name>Information</Name>
      <IsChecked>false</IsChecked>
    </ItemList>
    <ItemList>
      <Name>Verbose</Name>
      <IsChecked>false</IsChecked>
    </ItemList>
    <ItemList>
      <Name>ActivityTracing</Name>
      <IsChecked>false</IsChecked>
    </ItemList>
  </WcfLogLevel>
  <BackupEnabled>false</BackupEnabled>
  <LogEntireMessage>false</LogEntireMessage>
  <LogMessagesAtServiceLevel>false</LogMessagesAtServiceLevel>
  <LogMessagesAtTransportLevel>false</LogMessagesAtTransportLevel>
  <LogMalformedMessages>false</LogMalformedMessages>
  <MaxMessagesToLog>10000</MaxMessagesToLog>
  <MaxSizeOfMessageToLog>1000000</MaxSizeOfMessageToLog>
  <IsSimulatorMode>false</IsSimulatorMode>
  <HasChassisSeriesKdtfgs>true</HasChassisSeriesKdtfgs>
  <LogApciEnable>false</LogApciEnable>
  <CollectSystemInformation>false</CollectSystemInformation>
  <LogCollectorVersion />
  <LogInstallerEnable>false</LogInstallerEnable>
</ServiceLogManager>