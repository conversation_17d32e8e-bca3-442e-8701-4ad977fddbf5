using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Moq;

using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Backup;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Backup
{
    [TestFixture]
    public class BackupVersioningIntegrationTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IECUCommunicationService> _mockEcuService;
        private BackupService _backupService;
        private BackupVersioningService _versioningService;
        private string _testBackupDirectory;
        private ECUDevice _testEcu;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILoggingService>();
            _mockEcuService = new Mock<IECUCommunicationService>();

            // Create a temporary directory for test backups
            _testBackupDirectory = Path.Combine(Path.GetTempPath(), "VolvoFlashWR_Tests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testBackupDirectory);

            // Create the backup service with the test directory
            _backupService = new BackupService(_mockLogger.Object, _testBackupDirectory);

            // Create the versioning service
            _versioningService = new BackupVersioningService(_mockLogger.Object, _backupService);

            // Initialize the backup service
            _backupService.InitializeAsync(_mockEcuService.Object).Wait();

            // Create a test ECU
            _testEcu = new ECUDevice
            {
                Id = "TestECU001",
                Name = "Test ECU",
                SerialNumber = "SN12345",
                HardwareVersion = "HW1.0",
                SoftwareVersion = "SW1.0",
                Parameters = new Dictionary<string, object>
                {
                    { "Param1", 100 },
                    { "Param2", "Value" }
                }
            };

            // Set up the ECU service mock to return the test ECU
            _mockEcuService.Setup(s => s.ReadEEPROMAsync(_testEcu))
                .ReturnsAsync(new byte[] { 1, 2, 3, 4 });

            _mockEcuService.Setup(s => s.ReadMicrocontrollerCodeAsync(_testEcu))
                .ReturnsAsync(new byte[] { 5, 6, 7, 8 });

            _mockEcuService.Setup(s => s.ReadParametersAsync(_testEcu))
                .ReturnsAsync(_testEcu.Parameters);

            _mockEcuService.Setup(s => s.ScanForECUsAsync())
                .ReturnsAsync(new List<ECUDevice> { _testEcu });
        }

        [TearDown]
        public void TearDown()
        {
            // Clean up the test directory
            if (Directory.Exists(_testBackupDirectory))
            {
                Directory.Delete(_testBackupDirectory, true);
            }
        }

        [Test]
        public async Task CreateAndRetrieveVersionTree_Success()
        {
            // Arrange - Create a backup
            var backup1 = await _backupService.CreateBackupAsync(_testEcu, "Initial backup");
            Assert.That(backup1, Is.Not.Null);

            // Modify the ECU parameters for version 2
            _testEcu.Parameters["Param1"] = 150;
            _mockEcuService.Setup(s => s.ReadParametersAsync(_testEcu))
                .ReturnsAsync(_testEcu.Parameters);

            // Create version 2
            var backup2 = await _backupService.CreateBackupVersionAsync(backup1, _testEcu, "Updated parameters");
            Assert.That(backup2, Is.Not.Null);

            // Modify the ECU parameters for version 3
            _testEcu.Parameters["Param1"] = 200;
            _testEcu.Parameters["Param3"] = true;
            _mockEcuService.Setup(s => s.ReadParametersAsync(_testEcu))
                .ReturnsAsync(_testEcu.Parameters);

            // Create version 3
            var backup3 = await _backupService.CreateBackupVersionAsync(backup2, _testEcu, "Added new parameter");
            Assert.That(backup3, Is.Not.Null);

            // Act - Get the version tree
            var versionTree = await _versioningService.GetVersionTreeAsync(backup1.Id);

            // Assert
            Assert.That(versionTree, Is.Not.Null);
            Assert.That(versionTree.VersionCount, Is.EqualTo(3));
            Assert.That(versionTree.RootBackup.Id, Is.EqualTo(backup1.Id));
            Assert.That(versionTree.LatestVersion.Id, Is.EqualTo(backup3.Id));

            // Check the version chain
            Assert.That(versionTree.RootBackup.Version, Is.EqualTo(1));
            Assert.That(backup2.Version, Is.EqualTo(2));
            Assert.That(backup3.Version, Is.EqualTo(3));

            // Check parent-child relationships
            Assert.That(backup1.ParentBackupId, Is.Null);
            Assert.That(backup2.ParentBackupId, Is.EqualTo(backup1.Id));
            Assert.That(backup3.ParentBackupId, Is.EqualTo(backup2.Id));

            Assert.That(backup1.ChildBackupIds.Contains(backup2.Id), Is.True);
            Assert.That(backup2.ChildBackupIds.Contains(backup3.Id), Is.True);
        }

        [Test]
        public async Task CompareVersions_ShowsDifferences()
        {
            // Arrange - Create a backup
            var backup1 = await _backupService.CreateBackupAsync(_testEcu, "Initial backup");
            Assert.That(backup1, Is.Not.Null);

            // Modify the ECU parameters for version 2
            _testEcu.Parameters["Param1"] = 150;
            _mockEcuService.Setup(s => s.ReadParametersAsync(_testEcu))
                .ReturnsAsync(_testEcu.Parameters);

            // Create version 2
            var backup2 = await _backupService.CreateBackupVersionAsync(backup1, _testEcu, "Updated parameters");
            Assert.That(backup2, Is.Not.Null);

            // Act - Compare the versions
            var differences = await _versioningService.CompareVersionsAsync(backup1.Id, backup2.Id);

            // Assert
            Assert.That(differences, Is.Not.Null);
            Assert.That(differences.ContainsKey("Parameter:Param1"), Is.True);
            var paramDiff = differences["Parameter:Param1"];
            Assert.That(paramDiff.Version1Value, Is.EqualTo(100));
            Assert.That(paramDiff.Version2Value, Is.EqualTo(150));
        }

        [Test]
        public async Task MergeVersions_CreatesNewVersion()
        {
            // Arrange - Create a backup
            var backup1 = await _backupService.CreateBackupAsync(_testEcu, "Initial backup");
            Assert.That(backup1, Is.Not.Null);

            // Modify the ECU parameters for version 2A (branch 1)
            _testEcu.Parameters["Param1"] = 150;
            _mockEcuService.Setup(s => s.ReadParametersAsync(_testEcu))
                .ReturnsAsync(_testEcu.Parameters);

            // Create version 2A
            var backup2A = await _backupService.CreateBackupVersionAsync(backup1, _testEcu, "Updated Param1");
            Assert.That(backup2A, Is.Not.Null);

            // Modify the ECU parameters for version 2B (branch 2)
            _testEcu.Parameters["Param1"] = 100; // Reset to original
            _testEcu.Parameters["Param2"] = "NewValue";
            _mockEcuService.Setup(s => s.ReadParametersAsync(_testEcu))
                .ReturnsAsync(_testEcu.Parameters);

            // Create version 2B
            var backup2B = await _backupService.CreateBackupVersionAsync(backup1, _testEcu, "Updated Param2");
            Assert.That(backup2B, Is.Not.Null);

            // Act - Merge the versions
            var mergeOptions = new BackupMergeOptions
            {
                MergeDescription = "Merged version",
                MergeNotes = "Merged 2A and 2B",
                CreateNewVersion = true
            };

            var mergedBackup = await _versioningService.MergeVersionsAsync(backup2A.Id, backup2B.Id, mergeOptions);

            // Assert
            Assert.That(mergedBackup, Is.Not.Null);
            Assert.That(mergedBackup.Description, Is.EqualTo("Merged version"));
            Assert.That(mergedBackup.Parameters["Param1"], Is.EqualTo(150)); // From 2A
            Assert.That(mergedBackup.Parameters["Param2"], Is.EqualTo("NewValue")); // From 2B
        }
    }
}

