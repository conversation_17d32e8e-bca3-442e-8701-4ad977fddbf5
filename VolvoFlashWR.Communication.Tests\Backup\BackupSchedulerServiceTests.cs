using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Moq;
using NUnit.Framework;
using NUnit.Framework.Legacy;
using VolvoFlashWR.Communication.Backup;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Tests.Backup
{
    [TestFixture]
    public class BackupSchedulerServiceTests
    {
        private Mock<ILoggingService> _mockLogger;
        private Mock<IBackupService> _mockBackupService;
        private Mock<IECUCommunicationService> _mockEcuService;
        private BackupSchedulerService _schedulerService;
        private string _testSchedulesDirectory;

        [SetUp]
        public void Setup()
        {
            // Create mocks
            _mockLogger = new Mock<ILoggingService>();
            _mockBackupService = new Mock<IBackupService>();
            _mockEcuService = new Mock<IECUCommunicationService>();

            // Create a temporary directory for test schedules
            _testSchedulesDirectory = Path.Combine(Path.GetTempPath(), "VolvoFlashWR_Tests", "Schedules");
            if (Directory.Exists(_testSchedulesDirectory))
            {
                Directory.Delete(_testSchedulesDirectory, true);
            }
            Directory.CreateDirectory(_testSchedulesDirectory);

            // Create the scheduler service with the test directory
            _schedulerService = new BackupSchedulerService(_mockLogger.Object);

            // Use reflection to set the schedules directory path
            var field = typeof(BackupSchedulerService).GetField("_schedulesDirectoryPath", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            field.SetValue(_schedulerService, _testSchedulesDirectory);

            // Set the schedules file path
            field = typeof(BackupSchedulerService).GetField("_schedulesFilePath", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            field.SetValue(_schedulerService, Path.Combine(_testSchedulesDirectory, "backup_schedules.json"));

            // Setup ECU service mock
            _mockEcuService.Setup(s => s.IsInitialized).Returns(true);
            _mockEcuService.Setup(s => s.ScanForECUsAsync()).ReturnsAsync(new List<ECUDevice>
            {
                new ECUDevice { Id = "ecu1", Name = "Test ECU 1" },
                new ECUDevice { Id = "ecu2", Name = "Test ECU 2" }
            });
            _mockEcuService.Setup(s => s.ConnectToECUAsync(It.IsAny<ECUDevice>())).ReturnsAsync(true);
            _mockEcuService.Setup(s => s.ConnectedECUs).Returns(new List<ECUDevice>());

            // Setup backup service mock
            _mockBackupService.Setup(s => s.CreateBackupAsync(
                It.IsAny<ECUDevice>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<List<string>>(),
                It.IsAny<bool>(),
                It.IsAny<bool>(),
                It.IsAny<bool>()
            )).ReturnsAsync((ECUDevice ecu, string desc, string cat, List<string> tags, bool eeprom, bool mc, bool param) =>
            {
                return new BackupData
                {
                    Id = Guid.NewGuid().ToString(),
                    ECUId = ecu.Id,
                    ECUName = ecu.Name,
                    Description = desc,
                    Category = cat,
                    Tags = tags,
                    CreationTime = DateTime.Now
                };
            });

            _mockBackupService.Setup(s => s.GetAllBackupsAsync()).ReturnsAsync(new List<BackupData>());
        }

        [TearDown]
        public void TearDown()
        {
            // Stop the scheduler if it's running
            if (_schedulerService.IsRunning)
            {
                _schedulerService.StopAsync().Wait();
            }

            // Clean up the test directory
            if (Directory.Exists(_testSchedulesDirectory))
            {
                Directory.Delete(_testSchedulesDirectory, true);
            }
        }

        [Test]
        public async Task InitializeAsync_ValidServices_ReturnsTrue()
        {
            // Act
            bool result = await _schedulerService.InitializeAsync(_mockBackupService.Object, _mockEcuService.Object);

            // Assert
            Assert.That(result, Is.True);
        }

        [Test]
        public async Task StartAsync_AfterInitialization_ReturnsTrue()
        {
            // Arrange
            await _schedulerService.InitializeAsync(_mockBackupService.Object, _mockEcuService.Object);

            // Act
            bool result = await _schedulerService.StartAsync();

            // Assert
            Assert.That(result, Is.True);
            Assert.That(_schedulerService.IsRunning, Is.True);
        }

        [Test]
        public async Task StopAsync_AfterStarting_ReturnsTrue()
        {
            // Arrange
            await _schedulerService.InitializeAsync(_mockBackupService.Object, _mockEcuService.Object);
            await _schedulerService.StartAsync();

            // Act
            bool result = await _schedulerService.StopAsync();

            // Assert
            Assert.That(result, Is.True);
            Assert.That(_schedulerService.IsRunning, Is.False);
        }

        [Test]
        public async Task CreateScheduleAsync_ValidSchedule_ReturnsSchedule()
        {
            // Arrange
            await _schedulerService.InitializeAsync(_mockBackupService.Object, _mockEcuService.Object);
            var schedule = new BackupSchedule
            {
                Name = "Test Schedule",
                Description = "Test Description",
                ECUId = "ecu1",
                ECUName = "Test ECU 1",
                FrequencyType = BackupFrequencyType.Daily,
                TimeOfDay = new TimeSpan(3, 0, 0)
            };

            // Act
            var result = await _schedulerService.CreateScheduleAsync(schedule);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(schedule.Id));
            Assert.That(result.Name, Is.EqualTo("Test Schedule"));
            Assert.That(result.NextExecutionTime, Is.GreaterThan(DateTime.Now));
        }

        [Test]
        public async Task GetAllSchedulesAsync_AfterCreatingSchedules_ReturnsAllSchedules()
        {
            // Arrange
            await _schedulerService.InitializeAsync(_mockBackupService.Object, _mockEcuService.Object);

            var schedule1 = new BackupSchedule
            {
                Name = "Test Schedule 1",
                ECUId = "ecu1",
                ECUName = "Test ECU 1"
            };

            var schedule2 = new BackupSchedule
            {
                Name = "Test Schedule 2",
                ECUId = "ecu2",
                ECUName = "Test ECU 2"
            };

            await _schedulerService.CreateScheduleAsync(schedule1);
            await _schedulerService.CreateScheduleAsync(schedule2);

            // Act
            var schedules = await _schedulerService.GetAllSchedulesAsync();

            // Assert
            Assert.That(schedules, Is.Not.Null);
            Assert.That(schedules.Count, Is.EqualTo(2));
        }

        [Test]
        public async Task GetSchedulesForECUAsync_WithMatchingSchedules_ReturnsFilteredSchedules()
        {
            // Arrange
            await _schedulerService.InitializeAsync(_mockBackupService.Object, _mockEcuService.Object);

            var schedule1 = new BackupSchedule
            {
                Name = "Test Schedule 1",
                ECUId = "ecu1",
                ECUName = "Test ECU 1"
            };

            var schedule2 = new BackupSchedule
            {
                Name = "Test Schedule 2",
                ECUId = "ecu1",
                ECUName = "Test ECU 1"
            };

            var schedule3 = new BackupSchedule
            {
                Name = "Test Schedule 3",
                ECUId = "ecu2",
                ECUName = "Test ECU 2"
            };

            await _schedulerService.CreateScheduleAsync(schedule1);
            await _schedulerService.CreateScheduleAsync(schedule2);
            await _schedulerService.CreateScheduleAsync(schedule3);

            // Act
            var schedules = await _schedulerService.GetSchedulesForECUAsync("ecu1");

            // Assert
            Assert.That(schedules, Is.Not.Null);
            Assert.That(schedules.Count, Is.EqualTo(2));
            Assert.That(schedules.All(s => s.ECUId == "ecu1"), Is.True);
        }

        [Test]
        public async Task ExecuteScheduleNowAsync_ValidSchedule_CreatesBackup()
        {
            // Arrange
            await _schedulerService.InitializeAsync(_mockBackupService.Object, _mockEcuService.Object);

            var schedule = new BackupSchedule
            {
                Name = "Test Schedule",
                ECUId = "ecu1",
                ECUName = "Test ECU 1",
                Category = "Test",
                Tags = new List<string> { "Test" }
            };

            var createdSchedule = await _schedulerService.CreateScheduleAsync(schedule);

            // Act
            var backup = await _schedulerService.ExecuteScheduleNowAsync(createdSchedule.Id);

            // Assert
            Assert.That(backup, Is.Not.Null);
            Assert.That(backup.ECUId, Is.EqualTo("ecu1"));
            Assert.That(backup.Category, Is.EqualTo("Test"));

            // Verify the backup service was called
            _mockBackupService.Verify(s => s.CreateBackupAsync(
                It.IsAny<ECUDevice>(),
                It.IsAny<string>(),
                It.Is<string>(c => c == "Test"),
                It.Is<List<string>>(t => t.Contains("Test")),
                It.IsAny<bool>(),
                It.IsAny<bool>(),
                It.IsAny<bool>()
            ), Times.Once);
        }

        [Test]
        public async Task ApplyRetentionPolicy_HourlySchedule_KeepsCorrectBackups()
        {
            // Arrange
            await _schedulerService.InitializeAsync(_mockBackupService.Object, _mockEcuService.Object);

            // Create test backups
            var testBackups = new List<BackupData>();
            for (int i = 0; i < 5; i++)
            {
                var creationTime = DateTime.Now.AddHours(-i);
                testBackups.Add(new BackupData
                {
                    Id = $"backup{i + 1}",
                    ECUId = "ecu1",
                    ECUName = "Test ECU 1",
                    CreationTime = creationTime,
                    LastModifiedTime = creationTime
                });
            }

            _mockBackupService.Setup(s => s.GetAllBackupsAsync()).ReturnsAsync(testBackups);

            var schedule = new BackupSchedule
            {
                Id = "schedule1",
                Name = "Hourly Backup",
                Description = "Test Hourly Backup",
                ECUId = "ecu1",
                ECUName = "Test ECU 1",
                FrequencyType = BackupFrequencyType.Hourly,
                Interval = 1,
                IsEnabled = true,
                MaxBackupsToKeep = 3,
                CreatedBackupIds = new List<string> { "backup1", "backup2", "backup3", "backup4", "backup5" }
            };

            await _schedulerService.CreateScheduleAsync(schedule);

            // Mock the DeleteBackupAsync method to track which backups are deleted
            List<string> deletedBackupIds = new List<string>();
            _mockBackupService.Setup(s => s.DeleteBackupAsync(It.IsAny<BackupData>()))
                .Callback<BackupData>(b => deletedBackupIds.Add(b.Id))
                .ReturnsAsync(true);

            // Act
            await _schedulerService.ExecuteScheduleNowAsync(schedule.Id);

            // Assert
            Assert.That(deletedBackupIds, Has.Count.GreaterThanOrEqualTo(2));
            Assert.That(deletedBackupIds, Contains.Item("backup4"));
            Assert.That(deletedBackupIds, Contains.Item("backup5"));
        }

        [Test]
        public async Task HandleBackupFailure_MaxRetriesExceeded_DisablesSchedule()
        {
            // Arrange
            await _schedulerService.InitializeAsync(_mockBackupService.Object, _mockEcuService.Object);

            var schedule = new BackupSchedule
            {
                Id = "schedule1",
                Name = "Test Schedule",
                Description = "Test Description",
                ECUId = "nonexistent-ecu",
                ECUName = "Nonexistent ECU",
                FrequencyType = BackupFrequencyType.Daily,
                Interval = 1,
                TimeOfDay = new TimeSpan(3, 0, 0),
                IsEnabled = true,
                MaxBackupsToKeep = 5,
                RetryCount = 2 // Already retried twice
            };

            await _schedulerService.CreateScheduleAsync(schedule);

            // Set up the ECU service to fail to find the ECU
            _mockEcuService.Setup(s => s.ScanForECUsAsync()).ReturnsAsync(new List<ECUDevice>());

            // Set up the field for max retry attempts using reflection
            var field = typeof(BackupSchedulerService).GetField("_maxRetryAttempts", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            field.SetValue(_schedulerService, 3); // Set max retries to 3

            // Act
            await _schedulerService.ExecuteScheduleNowAsync(schedule.Id);

            // Assert
            var updatedSchedule = await _schedulerService.GetScheduleAsync(schedule.Id);
            Assert.That(updatedSchedule.IsEnabled, Is.False);
            Assert.That(updatedSchedule.RetryCount, Is.EqualTo(3));
        }

        [Test]
        public async Task CustomRetentionPolicy_AppliesCorrectPolicy()
        {
            // Arrange
            await _schedulerService.InitializeAsync(_mockBackupService.Object, _mockEcuService.Object);

            // Create test backups
            var testBackups = new List<BackupData>();
            for (int i = 0; i < 10; i++)
            {
                var creationTime = DateTime.Now.AddDays(-i);
                testBackups.Add(new BackupData
                {
                    Id = $"backup{i + 1}",
                    ECUId = "ecu1",
                    ECUName = "Test ECU 1",
                    CreationTime = creationTime,
                    LastModifiedTime = creationTime
                });
            }

            _mockBackupService.Setup(s => s.GetAllBackupsAsync()).ReturnsAsync(testBackups);

            var schedule = new BackupSchedule
            {
                Id = "schedule1",
                Name = "Custom Backup",
                Description = "Test Custom Backup",
                ECUId = "ecu1",
                ECUName = "Test ECU 1",
                FrequencyType = BackupFrequencyType.Custom,
                CustomIntervalDays = 3, // Every 3 days
                IsEnabled = true,
                MaxBackupsToKeep = 4,
                CreatedBackupIds = testBackups.Select(b => b.Id).ToList()
            };

            await _schedulerService.CreateScheduleAsync(schedule);

            // Mock the DeleteBackupAsync method to track which backups are deleted
            List<string> deletedBackupIds = new List<string>();
            _mockBackupService.Setup(s => s.DeleteBackupAsync(It.IsAny<BackupData>()))
                .Callback<BackupData>(b => deletedBackupIds.Add(b.Id))
                .ReturnsAsync(true);

            // Act
            await _schedulerService.ExecuteScheduleNowAsync(schedule.Id);

            // Assert
            Assert.That(deletedBackupIds, Has.Count.GreaterThanOrEqualTo(6));
        }
    }
}

